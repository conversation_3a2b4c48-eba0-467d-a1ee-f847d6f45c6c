import typer
import asyncio
from typing import Dict

from src.services.vector_service import get_vector_service, KnowledgeTier

app = typer.Typer()

@app.command()
def migrate_chromadb(
    backup_path: str = typer.Option(..., "--backup-path", help="Path to the ChromaDB backup directory."),
    collection_mapping: str = typer.Option(..., "--collection-mapping", help="JSON string mapping collection names to knowledge tiers (e.g., '{\"public_docs\":\"public\",\"private_docs\":\"private\"}')")
):
    """
    Migrate data from a ChromaDB backup to Supabase pgvector.
    """
    async def main():
        import json

        try:
            mapping: Dict[str, KnowledgeTier] = {k: KnowledgeTier(v) for k, v in json.loads(collection_mapping).items()}
        except (json.JSONDecodeError, ValueError) as e:
            typer.echo(f"Invalid collection mapping: {e}")
            raise typer.Exit(code=1)

        vector_service = await get_vector_service()
        stats = await vector_service.migrate_chromadb_data(backup_path, mapping)
        typer.echo("ChromaDB migration completed.")
        typer.echo(f"Stats: {stats}")

    asyncio.run(main())

if __name__ == "__main__":
    app()
