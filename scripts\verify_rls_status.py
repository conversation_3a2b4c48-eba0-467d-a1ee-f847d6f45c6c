#!/usr/bin/env python3
"""
RLS Verification Script for Supabase Database

This script audits the Row Level Security (RLS) status of key tables in the database.
It verifies that RLS is enabled and that secure policies exist for multi-tenant security.

Usage:
    DATABASE_URL="your_connection_string" python scripts/verify_rls_status.py

Requirements:
    - psycopg2-binary
    - DATABASE_URL environment variable set
"""

import os
import sys
from typing import Tuple

import psycopg2


class RLSVerifier:
    """Handles RLS verification for database tables."""

    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.target_tables = [
            "projects",
            "documents",
            "document_sections",
            "agent_executions"
        ]

    def connect(self) -> psycopg2.extensions.connection:
        """Establish database connection."""
        try:
            return psycopg2.connect(self.connection_string)
        except psycopg2.Error as e:
            print(f" Database connection failed: {e}")
            sys.exit(1)

    def check_rls_enabled(self, conn: psycopg2.extensions.connection, table_name: str) -> bool:
        """Check if R<PERSON> is enabled for a table."""
        query = """
        SELECT relrowsecurity
        FROM pg_class c
        JOIN pg_namespace n ON c.relnamespace = n.oid
        WHERE c.relname = %s AND n.nspname = 'public'
        """
        with conn.cursor() as cursor:
            cursor.execute(query, (table_name,))
            result = cursor.fetchone()
            return result[0] if result else False

    def check_secure_policies(self, conn: psycopg2.extensions.connection, table_name: str) -> int:
        """Check number of secure policies for a table."""
        query = """
        SELECT COUNT(*)
        FROM pg_policies
        WHERE tablename = %s AND permissive = false AND qual IS NOT NULL
        """
        with conn.cursor() as cursor:
            cursor.execute(query, (table_name,))
            result = cursor.fetchone()
            return result[0] if result else 0

    def verify_table(self, conn: psycopg2.extensions.connection, table_name: str) -> Tuple[bool, bool]:
        """Verify RLS status for a single table."""
        rls_enabled = self.check_rls_enabled(conn, table_name)
        secure_policies = self.check_secure_policies(conn, table_name)
        return rls_enabled, secure_policies > 0

    def run_audit(self) -> bool:
        """Run the complete RLS audit."""
        print("--- RLS Verification Report ---")

        conn = self.connect()
        all_secure = True

        try:
            for table in self.target_tables:
                print(f"\nVerifying table: {table}...")
                rls_enabled, has_secure_policies = self.verify_table(conn, table)

                status_rls = "" if rls_enabled else ""
                status_policies = "" if has_secure_policies else ""

                print(f"[{status_rls}] RLS Enabled: {rls_enabled}")
                print(f"[{status_policies}] Secure Policies Found: {1 if has_secure_policies else 0}")

                if not rls_enabled or not has_secure_policies:
                    all_secure = False

        finally:
            conn.close()

        if not all_secure:
            print("\n AUDIT FAILED: 1 or more tables are not secure.")
            return False
        else:
            print("\n AUDIT PASSED: All tables are secure.")
            return True


def main():
    """Main entry point."""
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        print(" Error: DATABASE_URL environment variable not set")
        sys.exit(1)

    verifier = RLSVerifier(database_url)
    success = verifier.run_audit()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
