#!/usr/bin/env python3
"""Test script to verify database operations work correctly."""

import logging

from sqlalchemy import text
from src.models.database import create_db_and_tables as create_tables
from src.models.database import engine, get_database_info

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_database_operations():
    """Test database connection and operations."""
    print("Testing database operations...")
    try:
        # Test table creation
        print("Creating database tables...")
        create_tables()
        print("Tables created successfully!")

        # Test database info function
        info = get_database_info()
        print(f"Database info: {info}")

        # Test basic query
        with engine.connect() as conn:
            result = conn.execute(text("SELECT current_database(), current_user"))
            db_name, current_user = result.fetchone()
            print(f"Connected to database: {db_name} as user: {current_user}")

            # Test table existence
            result = conn.execute(
                text(
                    "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name"
                )
            )
            tables = [row[0] for row in result.fetchall()]
            print(f"Available tables: {tables}")

        print("All database operations completed successfully!")

    except Exception as e:
        print(f"Database operations failed: {e}")
        import traceback

        traceback.print_exc()
        return False

    return True


if __name__ == "__main__":
    success = test_database_operations()
    exit(0 if success else 1)
