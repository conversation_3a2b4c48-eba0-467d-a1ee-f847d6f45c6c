# **Priority Roadmap for CodingAgentTwo**
## 🏛️ **Stage 1: Foundational MVP (Minimum Viable Product)**

**Status: ✅ COMPLETE**
This stage covers the core infrastructure and agent framework. All systems are operational.

### **Phase 1: Environment & Database**
- [x] Full Docker Compose setup for all services.
- [x] PostgreSQL with pgvector and Redis containers operational with persistent volumes.
- [x] `code-server` development environment configured with live editing.

### **Phase 2: Backend Core & LLM Integration**
- [x] FastAPI-based `ai-orchestrator` container is operational.
- [x] Universal LLM service with multi-provider support (Ollama, OpenRouter) is integrated.
- [x] SQLAlchemy models and Alembic migrations are in place.

### **Phase 3: Core AI Agent Framework**
- [x] `BaseAgent` with a sequential, Redis-based locking mechanism is implemented.
- [x] All specialist agents (`Architect`, `Frontend`, `Backend`, `Shell`, `IssueFix`) are defined.
- [x] A persistent, database-backed task queue with a Planner/Dispatcher architecture is functional.

### **Phase 4: Core User Interface & Interaction**
- [x] Custom VS Code extension for AI chat is operational in `code-server`.
- [x] WebSocket communication for real-time chat with the `ArchitectAgent` is functional.
- [x] Admin Dashboard (`user-portal`) for LLM management is implemented.
- [x] Multi-tenant security via RLS and Redis namespacing is fully implemented.

---

## 🚀 **Stage 2: The Core User Journey (Idea to Live Site)**

**Status: IN PROGRESS**
This stage focuses on building the complete, end-to-end user story: taking a project from an idea, to a Git-managed codebase, to a live, user-approved website on a custom domain.

### **Phase 5: Project Ingestion & Management**
- [x] Project import via File Upload and Git Repository cloning.
- [x] Automated project structure detection and dependency installation.
- [ ] **Workspace Management:**
    - [ ] Support for multiple projects per user workspace.
    - [ ] Project switching and starter templates.
    - [ ] Resource limits (CPU/Memory) per project.

### **Phase 6: Preview & Atomic Deployment Workflow (MAINTENANCE)**
**Next Up: This is our current development epic.**
- [x] Automated `git init` for all new projects.
- [ ] **Preview Creation:**
    - [ ] Agent logic to create a new Git branch for changes.
    - [ ] Agent logic to build the project and copy artifacts to a unique preview directory.
    - [ ] Implement Traefik File Provider to dynamically create a live, temporary preview URL.
- [ ] **User Approval Loop:**
    - [ ] Agent sends preview URL to user for review via WebSocket.
    - [ ] API endpoint to handle user's "approve" command.
- [ ] **Atomic Deployment (Zero Downtime):**
    - [ ] Implement "release" directory structure.
    - [ ] Agent logic to perform an atomic symlink swap to make a new version live instantly.
    - [ ] Agent logic to merge the Git branch and clean up the preview route.

### **Phase 7: Multi-Platform Hosting & Custom Domains**
- [ ] **On-Platform Hosting:**
    - [ ] Implement the "Single Nginx Gateway" (`hosting-server`) to serve all user sites.
    - [ ] Implement the `hosted_sites_data` volume with the correct directory structure (`sites`, `releases`, `previews`).
- [ ] **Custom Domain Support:**
    - [ ] Agent logic to use the Docker Socket Proxy to dynamically update Traefik rules for user-provided custom domains.
    - [ ] UI in the `user-portal` for users to manage their domains and see DNS instructions.
- [ ] **External Deployments (Vercel, Netlify, etc.):**
    - [ ] Create `DeploymentAgent` logic to use stored credentials.
    - [ ] Implement deployment via Vercel CLI as the first provider.
    - [ ] Implement deployment integrations for Netlify and Railway.

---

## 🧠 **Stage 3: Platform Maturity & Intelligence**

**Status: Not Started**
This stage focuses on making the platform smarter, more resilient, and more powerful.

### **Phase 8: Intelligent Roadmap Enhancement (HYBRID MODEL)**
- [ ] **`IndexerAgent` Implementation:**
    - [ ] Create the `IndexerAgent` class.
    - [ ] Implement the transactional logic to version roadmaps, generate summaries, create embeddings (via `VectorStorageService`), and store source references in the database.
- [ ] **`ArchitectAgent` Enhancement:**
    - [ ] Implement the `improve_roadmap` method.
    - [ ] Integrate RAG service to pull context for roadmap improvement.
    - [ ] Implement logic to generate the structured `improved_roadmap_json`.
- [ ] **Orchestration & API:**
    - [ ] Create the `POST /roadmaps/{id}/improve` endpoint.
    - [ ] Implement the orchestration logic (lock, call Architect, call Indexer, unlock).
- [ ] **UI for Roadmap Versioning:**
    - [ ] UI in the `user-portal` to view roadmap history and diffs.

### **Phase 9: Advanced AI & Knowledge Management**
- [ ] Implement a project-specific knowledge base by embedding all project files.
- [ ] Enhance RAG service to use the full project context for all agent actions.
- [ ] Implement automated testing generation by a specialized `TestingAgent`.
- [ ] Implement security vulnerability scanning by a specialized `SecurityAgent`.

---

## 🏭 **Stage 4: Production, Security & Scale**

**Status: Not Started**
This stage focuses on making the platform ready for a large number of users and enterprise requirements.

### **Phase 10: Advanced Security & Isolation**
- [ ] Implement Docker-in-Docker setup for full user project isolation.
- [ ] Integrate a container vulnerability scanner like Trivy into the CI/CD pipeline.
- [ ] Implement secrets management with a dedicated tool like HashiCorp Vault.

### **Phase 11: Scalability & Performance**
- [ ] Develop Kubernetes manifests for deploying the platform to a K8s cluster.
- [ ] Implement horizontal scaling for stateless services (`ai-orchestrator`, `user-portal`).
- [ ] Set up automated database backups and a disaster recovery plan.
- [ ] Integrate a CDN (like Cloudflare) for serving hosted user sites.

### **Phase 12: Enhanced Monitoring & Observability**
- [ ] Integrate an Application Performance Monitoring (APM) tool (e.g., Sentry, DataDog).
- [ ] Create advanced Grafana dashboards for agent performance, task queue length, and LLM costs.
- [ ] Implement an automated alert system for critical infrastructure or application errors.

---

## 🌍 **Stage 5: Ecosystem & Future Vision**

**Status: Not Started**
This stage focuses on long-term growth, collaboration, and extensibility.

### **Phase 13: Multi-User Collaboration**
- [ ] Implement role-based project sharing (owner, editor, viewer).
- [ ] Create real-time collaborative editing in `code-server` (e.g., using Operational Transforms).
- [ ] Build a team management and project activity feed.

### **Phase 14: Extensibility & Community**
- [ ] Design and implement a plugin architecture for custom agents or tools.
- [ ] Create a marketplace for users to share project templates.
- [ ] Develop an SDK for third-party developers to build on the platform.

**[END OF NEW `priorityroadmap.md` FILE CONTENT]**