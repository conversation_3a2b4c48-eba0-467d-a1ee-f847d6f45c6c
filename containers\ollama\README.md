# Ollama LLM Container

Local LLM inference server for AI Coding Agent with GPU acceleration and model management.

## 🎯 Overview

This container provides Ollama as a local LLM inference service, enabling the AI Coding Agent to run large language models locally with optimized performance for code generation, analysis, and assistance tasks.

## 🏗️ Architecture

### Features

- **Local LLM Inference**: Run models without external API dependencies
- **GPU Acceleration**: CUDA and ROCm support for accelerated inference
- **Model Management**: Automatic model downloading and caching
- **REST API**: Compatible API for seamless integration
- **Multi-Model Support**: Run multiple models simultaneously
- **Quantization**: Optimized model sizes for efficient inference
- **Streaming Responses**: Real-time token streaming for better UX
- **Context Management**: Efficient context window handling

### Supported Models

- **Code Generation**: CodeLlama, DeepSeek-Coder, StarCoder
- **General Purpose**: Llama 2/3, Mistral, Phi-2
- **Specialized**: SQLCoder, MathCoder, MedicalCoder
- **Multilingual**: Support for multiple programming languages

## 🚀 Configuration

### Environment Variables

```bash
# Model Configuration
OLLAMA_MODEL=llama3:8b
OLLAMA_HOST=0.0.0.0
OLLAMA_PORT=11434
OLLAMA_MAX_LOADED_MODELS=3

# GPU Configuration
OLLAMA_GPU_LAYERS=35
OLLAMA_NUM_GPU=1
OLLAMA_NUM_THREAD=8

# Memory Management
OLLAMA_NUM_CTX=4096
OLLAMA_NUM_BATCH=512
OLLAMA_NUM_GQA=8

# Performance Tuning
OLLAMA_FLASH_ATTENTION=true
OLLAMA_ROPE_SCALING=linear
OLLAMA_COMPRESSION=auto
```

### Volume Mounts

```yaml
volumes:
  - ./volumes/ollama/models:/root/.ollama/models
  - ./volumes/ollama/cache:/tmp/ollama-cache
  - ./model-manifests:/model-manifests:ro
```

## 🔧 Usage

### Building the Container

```bash
# Build with CUDA support
docker build -t ai-coding-agent-ollama --build-arg GPU_TYPE=cuda .

# Build with ROCm support
docker build -t ai-coding-agent-ollama --build-arg GPU_TYPE=rocm .
```

### Running with Docker Compose

```bash
docker-compose up ollama
```

### Model Management

```bash
# Pull a model
docker exec ollama ollama pull llama3:8b

# List available models
docker exec ollama ollama list

# Remove a model
docker exec ollama ollama rm llama3:8b

# Show model information
docker exec ollama ollama show llama3:8b
```

## 🤖 Model Operations

### Code Generation

```bash
# Generate code with context
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "model": "codellama:7b",
    "prompt": "Write a Python function to calculate fibonacci numbers",
    "stream": false,
    "options": {
      "temperature": 0.1,
      "top_p": 0.9,
      "num_predict": 200
    }
  }'
```

### Chat Interface

```bash
# Interactive chat session
curl -X POST http://localhost:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama3:8b",
    "messages": [
      {"role": "user", "content": "Explain this Python code"}
    ],
    "stream": true
  }'
```

### Embeddings Generation

```bash
# Generate embeddings for semantic search
curl -X POST http://localhost:11434/api/embeddings \
  -H "Content-Type: application/json" \
  -d '{
    "model": "nomic-embed-text",
    "prompt": "def calculate_fibonacci(n): return n if n <= 1 else calculate_fibonacci(n-1) + calculate_fibonacci(n-2)"
  }'
```

## ⚡ Performance Optimization

### GPU Configuration

```yaml
# CUDA configuration
environment:
  - OLLAMA_GPU_LAYERS=35
  - CUDA_VISIBLE_DEVICES=0
  - OLLAMA_NUM_THREAD=8

# ROCm configuration
environment:
  - OLLAMA_GPU_LAYERS=35
  - HIP_VISIBLE_DEVICES=0
  - OLLAMA_NUM_THREAD=8
```

### Memory Optimization

```yaml
# Context window optimization
environment:
  - OLLAMA_NUM_CTX=4096
  - OLLAMA_NUM_BATCH=512
  - OLLAMA_ROPE_SCALING=linear

# Quantization settings
environment:
  - OLLAMA_COMPRESSION=q4_0  # Options: f16, q8_0, q4_0, q4_1
```

### Multi-Model Management

```bash
# Run multiple models
docker exec ollama ollama serve --model llama3:8b --port 11434 &
docker exec ollama ollama serve --model codellama:7b --port 11435 &

# Load balancing configuration
upstream ollama_backends {
    server localhost:11434;
    server localhost:11435;
}
```

## 📊 Monitoring & Health Checks

### Health Endpoints

```bash
# Basic health check
curl http://localhost:11434/api/tags

# Model loading status
curl http://localhost:11434/api/show -d '{"name": "llama3:8b"}'

# Performance metrics
curl http://localhost:11434/api/ps
```

### Resource Monitoring

```bash
# GPU memory usage
nvidia-smi --query-gpu=memory.used,memory.total --format=csv

# CPU and memory usage
docker stats ollama

# Model inference metrics
docker exec ollama ollama ps
```

### Logging Configuration

```yaml
# Structured logging
environment:
  - OLLAMA_DEBUG=true
  - OLLAMA_LOG_LEVEL=info

# Log rotation
volumes:
  - ./volumes/ollama/logs:/var/log/ollama
```

## 🔄 Model Management

### Custom Model Files

```bash
# Create custom model
cat > Modelfile << EOF
FROM llama3:8b
PARAMETER temperature 0.1
PARAMETER top_p 0.9
PARAMETER num_ctx 4096
SYSTEM "You are an expert Python developer. Provide clean, efficient, and well-documented code."
EOF

# Build and run custom model
docker exec ollama ollama create python-expert -f Modelfile
docker exec ollama ollama run python-expert
```

### Model Quantization

```bash
# Quantize existing model
docker exec ollama ollama run llama3:8b
docker exec ollama ollama cp llama3:8b llama3:8b-q4_0
docker exec ollama ollama rm llama3:8b
```

## 🛡️ Security Considerations

### Network Security

```yaml
# Bind to specific interface
environment:
  - OLLAMA_HOST=127.0.0.1

# Use reverse proxy
services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

### Access Control

```nginx
# Nginx configuration for Ollama
location /api/ {
    proxy_pass http://ollama:11434;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;

    # Rate limiting
    limit_req zone=ollama burst=10 nodelay;

    # Authentication
    auth_basic "Ollama API";
    auth_basic_user_file /etc/nginx/.htpasswd;
}
```

## 🐛 Troubleshooting

### Common Issues

1. **GPU Not Detected**
   - Verify GPU drivers are installed
   - Check CUDA/ROCm compatibility
   - Validate container GPU access

2. **Model Loading Failures**
   - Check available disk space
   - Verify model file integrity
   - Monitor memory usage

3. **Performance Issues**
   - Adjust GPU layers and threads
   - Optimize context window size
   - Use model quantization

### Debug Commands

```bash
# Check GPU status
docker exec ollama nvidia-smi

# View Ollama logs
docker logs ollama

# Test model inference
docker exec ollama ollama run llama3:8b "Hello, test message"

# Check model files
docker exec ollama ls -la /root/.ollama/models
```

## 📚 Additional Resources

- [Ollama Documentation](https://github.com/jmorganca/ollama)
- [Ollama API Reference](https://github.com/jmorganca/ollama/blob/main/docs/api.md)
- [Model Library](https://ollama.ai/library)
- [GPU Acceleration](https://github.com/jmorganca/ollama/blob/main/docs/gpu.md)

## 🤝 Contributing

When modifying this container:

1. Test model compatibility across different architectures
2. Validate GPU acceleration performance
3. Update model manifests for new releases
4. Test multi-model concurrent execution
5. Monitor resource usage and optimization opportunities
