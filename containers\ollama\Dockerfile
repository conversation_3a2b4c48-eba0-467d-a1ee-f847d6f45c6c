# syntax=docker/dockerfile:1
# Ollama Dockerfile with GPU support and optimized cache layering
# Uses official Ollama image with NVIDIA CUDA support

FROM ollama/ollama:0.3.12

# Set security and project labels (early for better caching)
LABEL org.opencontainers.image.title="AI Coding Agent - Ollama GPU" \
  org.opencontainers.image.description="Ollama LLM server with NVIDIA GPU support" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team" \
  security.non-root="true" \
  security.user="ollama"

# Create ollama user explicitly with specific UID/GID for security
USER root
RUN (getent group ollama || groupadd -r -g 1000 ollama) && \
  (getent passwd ollama || useradd -r -g ollama -u 1000 -d /home/<USER>/bin/bash ollama)

# Install system dependencies in a single layer for better caching
RUN apt-get update && apt-get install -y --no-install-recommends \
  curl \
  ca-certificates \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get clean

# Create necessary directories with proper permissions
RUN mkdir -p /home/<USER>/.ollama/models \
  && chown -R ollama:ollama /home/<USER>
  && chmod -R 755 /home/<USER>

# Switch to non-root user
USER ollama

# Set environment variables for GPU optimization
ENV OLLAMA_HOST=0.0.0.0 \
  OLLAMA_MODELS=/home/<USER>/.ollama/models \
  OLLAMA_KEEP_ALIVE=5m \
  OLLAMA_MAX_LOADED_MODELS=3 \
  OLLAMA_FLASH_ATTENTION=true \
  OLLAMA_NUM_PARALLEL=4

# Expose the port
EXPOSE 11434

# Health check for Ollama service with better timeout for GPU initialization
HEALTHCHECK --interval=30s --timeout=15s --start-period=60s --retries=5 \
  CMD curl -f http://localhost:11434/api/health || exit 1

# Start Ollama with proper path
CMD ["serve"]
