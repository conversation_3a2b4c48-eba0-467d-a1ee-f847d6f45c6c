server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: docker
    docker_sd_configs:
      - host: tcp://docker-proxy:2375
        refresh_interval: 5s
    relabel_configs:
      - source_labels: ['__meta_docker_container_name']
        regex: '/(.*)'
        target_label: 'container'
      - source_labels: ['__meta_docker_container_name']
        regex: '/(.*)'
        target_label: 'container_name'
      - source_labels: ['__meta_docker_container_label_com_docker_compose_service']
        target_label: 'service'
      - source_labels: ['__meta_docker_container_label_com_docker_compose_project']
        target_label: 'project'
      # Limit labels to prevent exceeding Loki's 15-label limit
      - action: labelmap
        regex: '__meta_docker_container_label_com_docker_compose_(.+)'
      - action: labeldrop
        regex: '__meta_docker_container_label_org_opencontainers_image_.*'
      - action: labeldrop
        regex: '__meta_docker_container_label_desktop_docker_io_.*'
      - action: labeldrop
        regex: '__meta_docker_container_label_com_docker_compose_config_hash'
      - action: labeldrop
        regex: '__meta_docker_container_label_com_docker_compose_project_config_files'
      - action: labeldrop
        regex: '__meta_docker_container_label_com_docker_compose_project_working_dir'
    pipeline_stages:
      - docker: {}
