"""
Supabase API Router for AI Coding Agent.

This module provides comprehensive API endpoints for Supabase integration
including authentication, user management, document operations, and RAG functionality
with proper permission controls and error handling.

Author: AI Coding Agent
Version: 1.0.0
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

# FastAPI imports
from fastapi import APIRouter, Depends, HTTPException, status, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field, field_validator

# Internal imports
from src.services.supabase_service import SupabaseService, get_supabase_service
from src.services.auth_service import (
    AuthService, get_auth_service, get_current_user,
    UserProfile, UserRole
)
from src.services.vector_service import VectorStorageService, get_vector_service
from src.services.rag_service import RAGService, get_rag_service, LLMProvider

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/supabase", tags=["supabase"])


# ==================================================================================
# PYDANTIC MODELS
# ==================================================================================

class UserRegistration(BaseModel):
    """User registration request."""
    email: str = Field(..., description="User email address")
    password: str = Field(..., min_length=8, description="User password")
    username: Optional[str] = Field(None, description="Optional username")
    full_name: Optional[str] = Field(None, description="Optional full name")

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        import re
        if not re.match(r'^[^@]+@[^@]+\.[^@]+$', v):
            raise ValueError('Invalid email format')
        return v.lower()


class UserLogin(BaseModel):
    """User login request."""
    email: str = Field(..., description="User email address")
    password: str = Field(..., description="User password")
    remember_me: bool = Field(False, description="Remember user session")


class UserResponse(BaseModel):
    """User profile response."""
    id: str
    email: str
    username: Optional[str]
    full_name: Optional[str]
    role: str
    avatar_url: Optional[str]
    preferences: Dict[str, Any]
    created_at: Optional[str]
    updated_at: Optional[str]
    email_verified: bool
    is_active: bool


class AuthResponse(BaseModel):
    """Authentication response."""
    user: UserResponse
    tokens: Dict[str, Any]
    message: str


class ProjectCreate(BaseModel):
    """Project creation request."""
    name: str = Field(..., min_length=1, max_length=100, description="Project name")
    description: Optional[str] = Field(None, max_length=500, description="Project description")
    settings: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Project settings")


class ProjectResponse(BaseModel):
    """Project response."""
    id: str
    name: str
    description: Optional[str]
    owner_id: str
    status: str
    settings: Dict[str, Any]
    created_at: str
    updated_at: str


class DocumentCreate(BaseModel):
    """Document creation request."""
    name: str = Field(..., min_length=1, max_length=200, description="Document name")
    content: Optional[str] = Field(None, description="Document content")
    file_type: Optional[str] = Field(None, description="File type")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Document metadata")


class DocumentResponse(BaseModel):
    """Document response."""
    id: str
    project_id: str
    name: str
    content: Optional[str]
    file_path: Optional[str]
    file_type: Optional[str]
    file_size: Optional[int]
    owner_id: str
    metadata: Dict[str, Any]
    created_at: str
    updated_at: str


class SearchRequest(BaseModel):
    """Document search request."""
    query: str = Field(..., min_length=1, description="Search query")
    project_id: Optional[str] = Field(None, description="Project filter")
    similarity_threshold: Optional[float] = Field(0.7, ge=0.0, le=1.0, description="Similarity threshold")
    max_results: Optional[int] = Field(10, ge=1, le=50, description="Maximum results")
    enable_hybrid_search: bool = Field(True, description="Enable hybrid search")


class RAGRequest(BaseModel):
    """RAG generation request."""
    query: str = Field(..., min_length=1, description="User query")
    project_id: Optional[str] = Field(None, description="Project filter")
    llm_provider: LLMProvider = Field(LLMProvider.OLLAMA, description="LLM provider")
    model_name: Optional[str] = Field(None, description="Specific model name")
    context_types: Optional[List[str]] = Field(None, description="Context types to include")


class TokenRefresh(BaseModel):
    """Token refresh request."""
    refresh_token: str = Field(..., description="Refresh token")


# ==================================================================================
# AUTHENTICATION ENDPOINTS
# ==================================================================================

@router.post("/auth/register", response_model=AuthResponse)
async def register_user(
    user_data: UserRegistration,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Register a new user.

    Creates a new user account with Supabase Auth and user profile.
    """
    try:
        user_profile, tokens = await auth_service.register_user(
            email=user_data.email,
            password=user_data.password,
            username=user_data.username,
            full_name=user_data.full_name
        )

        return AuthResponse(
            user=UserResponse(**user_profile.to_dict()),
            tokens={
                "access_token": tokens.access_token,
                "refresh_token": tokens.refresh_token,
                "token_type": tokens.token_type,
                "expires_in": tokens.expires_in
            },
            message="User registered successfully"
        )

    except Exception as e:
        logger.error(f"User registration failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/auth/login", response_model=AuthResponse)
async def login_user(
    login_data: UserLogin,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Login user.

    Authenticates user credentials and returns access tokens.
    """
    try:
        user_profile, tokens = await auth_service.login_user(
            email=login_data.email,
            password=login_data.password,
            remember_me=login_data.remember_me
        )

        return AuthResponse(
            user=UserResponse(**user_profile.to_dict()),
            tokens={
                "access_token": tokens.access_token,
                "refresh_token": tokens.refresh_token,
                "token_type": tokens.token_type,
                "expires_in": tokens.expires_in
            },
            message="Login successful"
        )

    except Exception as e:
        logger.error(f"User login failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )


@router.post("/auth/refresh")
async def refresh_token(
    token_data: TokenRefresh,
    auth_service: AuthService = Depends(get_auth_service)
):
    """Refresh access token using refresh token."""
    try:
        tokens = await auth_service.refresh_access_token(token_data.refresh_token)

        return {
            "access_token": tokens.access_token,
            "refresh_token": tokens.refresh_token,
            "token_type": tokens.token_type,
            "expires_in": tokens.expires_in,
            "message": "Token refreshed successfully"
        }

    except Exception as e:
        logger.error(f"Token refresh failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )


@router.post("/auth/logout")
async def logout_user(
    current_user: UserProfile = Depends(get_current_user),
    auth_service: AuthService = Depends(get_auth_service)
):
    """Logout user and invalidate tokens."""
    try:
        # Note: In a real implementation, you might want to maintain a token blacklist
        # For now, we just return success since JWT tokens are stateless

        return {"message": "Logout successful"}

    except Exception as e:
        logger.error(f"Logout failed: {str(e)}")
        return {"message": "Logout completed with warnings"}


# ==================================================================================
# USER MANAGEMENT ENDPOINTS
# ==================================================================================

@router.get("/user/profile", response_model=UserResponse)
async def get_user_profile(
    current_user: UserProfile = Depends(get_current_user)
):
    """Get current user's profile."""
    return UserResponse(**current_user.to_dict())


@router.put("/user/profile", response_model=UserResponse)
async def update_user_profile(
    profile_data: Dict[str, Any],
    current_user: UserProfile = Depends(get_current_user),
    supabase_service: SupabaseService = Depends(get_supabase_service)
):
    """Update user profile."""
    try:
        # Update user profile in database
        client = supabase_service.service_client

        # Filter allowed fields
        allowed_fields = ['username', 'full_name', 'avatar_url', 'preferences']
        update_data = {k: v for k, v in profile_data.items() if k in allowed_fields}
        update_data['updated_at'] = datetime.utcnow().isoformat()

        response = client.table('user_profiles').update(update_data).eq('id', current_user.id).execute()

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Profile update failed"
            )

        # Return updated profile
        updated_profile = current_user.to_dict()
        updated_profile.update(update_data)

        return UserResponse(**updated_profile)

    except Exception as e:
        logger.error(f"Profile update failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


# ==================================================================================
# PROJECT MANAGEMENT ENDPOINTS
# ==================================================================================

@router.post("/projects", response_model=ProjectResponse)
async def create_project(
    project_data: ProjectCreate,
    current_user: UserProfile = Depends(get_current_user),
    supabase_service: SupabaseService = Depends(get_supabase_service)
):
    """Create a new project."""
    try:
        client = supabase_service.service_client

        project_record = {
            'name': project_data.name,
            'description': project_data.description,
            'owner_id': current_user.id,
            'settings': project_data.settings
        }

        response = client.table('projects').insert(project_record).execute()

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Project creation failed"
            )

        return ProjectResponse(**response.data[0])

    except Exception as e:
        logger.error(f"Project creation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/projects", response_model=List[ProjectResponse])
async def list_projects(
    current_user: UserProfile = Depends(get_current_user),
    supabase_service: SupabaseService = Depends(get_supabase_service)
):
    """List user's projects."""
    try:
        client = supabase_service.get_client()  # Use user's client for RLS

        response = client.table('projects').select('*').eq('owner_id', current_user.id).execute()

        return [ProjectResponse(**project) for project in response.data]

    except Exception as e:
        logger.error(f"Project listing failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/projects/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: str,
    current_user: UserProfile = Depends(get_current_user),
    supabase_service: SupabaseService = Depends(get_supabase_service)
):
    """Get project by ID."""
    try:
        client = supabase_service.get_client()

        response = client.table('projects').select('*').eq('id', project_id).eq('owner_id', current_user.id).execute()

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        return ProjectResponse(**response.data[0])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Project retrieval failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


# ==================================================================================
# DOCUMENT MANAGEMENT ENDPOINTS
# ==================================================================================

@router.post("/projects/{project_id}/documents", response_model=DocumentResponse)
async def create_document(
    project_id: str,
    document_data: DocumentCreate,
    current_user: UserProfile = Depends(get_current_user),
    supabase_service: SupabaseService = Depends(get_supabase_service)
):
    """Create a document in a project."""
    try:
        client = supabase_service.service_client

        # Verify project ownership
        project_response = client.table('projects').select('id').eq('id', project_id).eq('owner_id', current_user.id).execute()
        if not project_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        # Create document
        document_record = {
            'project_id': project_id,
            'name': document_data.name,
            'content': document_data.content,
            'file_type': document_data.file_type,
            'owner_id': current_user.id,
            'metadata': document_data.metadata
        }

        response = client.table('documents').insert(document_record).execute()

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Document creation failed"
            )

        return DocumentResponse(**response.data[0])

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Document creation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/documents/{document_id}/process")
async def process_document(
    document_id: str,
    overwrite: bool = False,
    current_user: UserProfile = Depends(get_current_user),
    vector_service: VectorStorageService = Depends(get_vector_service),
    supabase_service: SupabaseService = Depends(get_supabase_service)
):
    """Process document for vector search."""
    try:
        # Get document content
        client = supabase_service.service_client
        doc_response = client.table('documents').select('*').eq('id', document_id).execute()

        if not doc_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )

        document = doc_response.data[0]

        # Verify ownership through project
        project_response = client.table('projects').select('id').eq('id', document['project_id']).eq('owner_id', current_user.id).execute()
        if not project_response.data:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # Process document
        chunk_ids = await vector_service.process_document(
            document_id=document_id,
            content=document['content'] or '',
            user_id=current_user.id,
            metadata=document.get('metadata', {}),
            overwrite=overwrite
        )

        return {
            "message": "Document processed successfully",
            "document_id": document_id,
            "chunks_created": len(chunk_ids),
            "chunk_ids": chunk_ids
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Document processing failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


# ==================================================================================
# SEARCH AND RAG ENDPOINTS
# ==================================================================================

@router.post("/search")
async def search_documents(
    search_request: SearchRequest,
    current_user: UserProfile = Depends(get_current_user),
    vector_service: VectorStorageService = Depends(get_vector_service)
):
    """Search documents using vector similarity."""
    try:
        if search_request.enable_hybrid_search:
            results = await vector_service.hybrid_search(
                query=search_request.query,
                user_id=current_user.id,
                project_id=search_request.project_id,
                similarity_threshold=search_request.similarity_threshold,
                max_results=search_request.max_results
            )
        else:
            results = await vector_service.search_documents(
                query=search_request.query,
                user_id=current_user.id,
                project_id=search_request.project_id,
                similarity_threshold=search_request.similarity_threshold,
                max_results=search_request.max_results
            )

        return {
            "query": search_request.query,
            "results": [result.to_dict() for result in results],
            "total_results": len(results),
            "search_type": "hybrid" if search_request.enable_hybrid_search else "vector"
        }

    except Exception as e:
        logger.error(f"Document search failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/rag/generate")
async def generate_with_rag(
    rag_request: RAGRequest,
    current_user: UserProfile = Depends(get_current_user),
    rag_service: RAGService = Depends(get_rag_service)
):
    """Generate code or content using RAG."""
    try:
        # Convert context_types to enum if provided
        context_types = None
        if rag_request.context_types:
            from src.services.rag_service import ContextType
            context_types = [ContextType(ct) for ct in rag_request.context_types]

        response = await rag_service.generate_code_with_context(
            query=rag_request.query,
            user_profile=current_user,
            project_id=rag_request.project_id,
            context_types=context_types,
            llm_provider=rag_request.llm_provider,
            model_name=rag_request.model_name
        )

        return {
            "response": response.response,
            "context_used": response.context_used,
            "context_tokens": response.context_tokens,
            "response_tokens": response.response_tokens,
            "similarity_scores": response.similarity_scores,
            "llm_provider": response.llm_provider,
            "processing_time_ms": response.processing_time_ms,
            "query": response.query,
            "metadata": response.metadata
        }

    except Exception as e:
        logger.error(f"RAG generation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/rag/question")
async def answer_question(
    question: str = Form(...),
    project_id: Optional[str] = Form(None),
    llm_provider: LLMProvider = Form(LLMProvider.OLLAMA),
    current_user: UserProfile = Depends(get_current_user),
    rag_service: RAGService = Depends(get_rag_service)
):
    """Answer question using document context."""
    try:
        response = await rag_service.answer_question_with_context(
            question=question,
            user_profile=current_user,
            project_id=project_id,
            llm_provider=llm_provider
        )

        return {
            "question": question,
            "answer": response.response,
            "context_used": response.context_used,
            "context_tokens": response.context_tokens,
            "processing_time_ms": response.processing_time_ms,
            "llm_provider": response.llm_provider
        }

    except Exception as e:
        logger.error(f"Question answering failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


# ==================================================================================
# STATISTICS AND HEALTH ENDPOINTS
# ==================================================================================

@router.get("/stats/user")
async def get_user_stats(
    current_user: UserProfile = Depends(get_current_user),
    vector_service: VectorStorageService = Depends(get_vector_service)
):
    """Get user's document and usage statistics."""
    try:
        stats = await vector_service.get_user_stats(current_user.id)

        return {
            "user_id": current_user.id,
            "total_projects": stats.total_documents,  # Projects handled separately
            "total_documents": stats.total_documents,
            "total_chunks": stats.total_chunks,
            "total_embeddings": stats.total_embeddings,
            "avg_chunks_per_document": stats.avg_chunks_per_document,
            "storage_size_mb": stats.storage_size_mb,
            "last_updated": stats.last_updated.isoformat() if stats.last_updated else None
        }

    except Exception as e:
        logger.error(f"Stats retrieval failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/health")
async def health_check(
    supabase_service: SupabaseService = Depends(get_supabase_service)
):
    """Health check for Supabase integration."""
    try:
        health_status = await supabase_service.health_check()

        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "supabase": health_status,
            "features": {
                "authentication": True,
                "vector_search": True,
                "rag": True,
                "row_level_security": True
            }
        }

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }
        )


# ==================================================================================
# ADMIN ENDPOINTS (Require admin role)
# ==================================================================================

@router.get("/admin/users")
async def list_all_users(
    current_user: UserProfile = Depends(get_current_user),
    supabase_service: SupabaseService = Depends(get_supabase_service)
):
    """List all users (admin only)."""
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )

    try:
        client = supabase_service.service_client
        response = client.table('user_profiles').select('*').execute()

        return {
            "users": response.data,
            "total_count": len(response.data)
        }

    except Exception as e:
        logger.error(f"User listing failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/admin/refresh-indexes")
async def refresh_vector_indexes(
    current_user: UserProfile = Depends(get_current_user),
    vector_service: VectorStorageService = Depends(get_vector_service)
):
    """Refresh vector indexes (admin only)."""
    if current_user.role not in [UserRole.ADMIN, UserRole.SUPER_ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )

    try:
        success = await vector_service.refresh_vector_index()

        return {
            "message": "Vector indexes refreshed successfully" if success else "Index refresh failed",
            "success": success,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Index refresh failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )