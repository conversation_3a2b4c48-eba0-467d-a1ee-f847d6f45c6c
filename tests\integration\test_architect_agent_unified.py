"""
Comprehensive Integration Test for Architect<PERSON>gent Interviewer.

This test validates the new stateful ArchitectAgent interviewer functionality,
including state machine transitions, conversation management, and roadmap generation.

Test Scenarios:
1. test_start_new_interview - Test creating new interview sessions
2. test_submit_response_and_get_next_question - Test response submission and question flow
3. test_full_interview_flow_to_roadmap - Test complete interview to roadmap generation
4. test_invalid_session_id - Test error handling for invalid sessions

Author: AI Coding Agent
Version: 1.0.0
"""

import json
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch

from src.models import InterviewState, InterviewSession, ConversationHistory, User, Project
from src.repository.conversation_repository import ConversationRepository
from src.agents.architect_agent import ArchitectAgent
from src.models.database import create_db_and_tables, SessionLocal


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """Create a test database session with proper cleanup."""
    # This function is now synchronous
    create_db_and_tables()

    # Create a new session
    session = SessionLocal()

    # Clean up any existing test data
    try:
        # Clean up in reverse dependency order
        session.query(ConversationHistory).delete()
        session.query(InterviewSession).delete()
        session.query(Project).delete()
        session.query(User).delete()
        session.commit()
    except Exception:
        session.rollback()

    yield session

    # Cleanup after test
    try:
        session.query(ConversationHistory).delete()
        session.query(InterviewSession).delete()
        session.query(Project).delete()
        session.query(User).delete()
        session.commit()
    except Exception:
        session.rollback()
    finally:
        session.close()


@pytest.fixture
def test_user(db_session):
    """Create a test user for the tests."""
    from src.models.user import UserProfile
    import uuid
    user = UserProfile(
        email="<EMAIL>",
        username="testuser",
        full_name="Test User",
        hashed_password="testpassword",
        supabase_user_id=str(uuid.uuid4())
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_project(db_session, test_user):
    """Create a test project for the tests."""
    project = Project(
        name="Test Project",
        description="A test project for ArchitectAgent interviewer",
        owner_id=test_user.supabase_user_id,
        status="active"
    )
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project


@pytest.fixture
def mock_llm_service():
    """Mock LLM service for testing."""
    mock_service = AsyncMock()

    # Mock the generate method to return a structured response
    mock_response = Mock()
    mock_response.content = json.dumps({
        "overview": "Test project overview",
        "technology_stack": ["Python", "FastAPI"],
        "phases": [
            {
                "name": "Planning",
                "duration": "1 week",
                "description": "Project planning phase",
                "deliverables": ["Requirements document"],
                "key_features": ["User authentication"]
            }
        ],
        "key_features": ["User authentication", "API endpoints"],
        "risks": ["Technical challenges"],
        "timeline": "4 weeks"
    })
    mock_response.model = "test-model"
    mock_response.provider = Mock()
    mock_response.provider.value = "test-provider"

    mock_service.generate.return_value = mock_response
    return mock_service


@pytest.fixture
def mock_conversation_repo():
    """Mock ConversationRepository instance for testing."""
    repo_mock = Mock(spec=ConversationRepository)
    repo_mock.create_interview_session = AsyncMock()
    repo_mock.get_interview_session = AsyncMock()
    repo_mock.get_next_unanswered_question = AsyncMock()
    repo_mock.get_session_history = AsyncMock()
    repo_mock.update_interview_session_state = AsyncMock()
    repo_mock.update_response = AsyncMock()

    # Mock session creation
    mock_session = Mock()
    mock_session.session_id = "test-session-id"
    mock_session.current_state = InterviewState.GREETING.value
    mock_session.project_id = 1
    mock_session.user_id = "test-user-id"
    repo_mock.create_interview_session.return_value = mock_session
    repo_mock.get_interview_session.return_value = mock_session

    # Mock conversation history
    mock_conversation = Mock()
    mock_conversation.id = 1
    mock_conversation.question_key = "project_name"
    mock_conversation.question_text = "What is the name of your project?"
    mock_conversation.sequence_order = 1
    mock_conversation.is_followup = False
    mock_conversation.user_response = None
    repo_mock.get_next_unanswered_question.return_value = mock_conversation
    repo_mock.get_session_history.return_value = [mock_conversation]

    return repo_mock


@pytest.fixture
def architect_agent(mock_llm_service, mock_conversation_repo):
    """Create an ArchitectAgent instance with mocked dependencies."""
    agent = ArchitectAgent(llm_service=mock_llm_service)
    agent._conversation_repo = mock_conversation_repo
    return agent


class TestArchitectAgent:
    """Test suite for ArchitectAgent unified functionality."""

    @pytest.mark.asyncio
    async def test_start_new_interview(self, architect_agent, test_project, test_user, mock_conversation_repo):
        """Test starting a new interview session."""
        # Execute the agent with just project_id and user_id (no session_id)
        result = await architect_agent.execute({
            "project_id": test_project.id,
            "user_id": test_user.id
        })

        # Verify the result structure
        assert result["agent"] == "architect"
        assert result["status"] == "session_created"
        assert "session_id" in result
        assert result["message"] == "Welcome! Let's start your project interview."
        assert result["next_action"] == "greeting"

        # Verify that create_interview_session was called
        mock_conversation_repo.create_interview_session.assert_called_once()
        call_args = mock_conversation_repo.create_interview_session.call_args
        assert call_args[1]["project_id"] == test_project.id
        assert call_args[1]["user_id"] == test_user.id

    @pytest.mark.asyncio
    async def test_submit_response_and_get_next_question(self, architect_agent, test_project, test_user, mock_conversation_repo):
        """Test submitting a response and getting the next question."""
        session_id = "test-session-id"

        # Mock the session and conversation for the response flow
        mock_session = Mock()
        mock_session.session_id = session_id
        mock_session.current_state = InterviewState.ASKING_QUESTIONS.value

        mock_conversation_repo.get_interview_session.return_value = mock_session

        # Mock unanswered question
        mock_question = Mock()
        mock_question.id = 1
        mock_question.question_key = "project_name"
        mock_question.question_text = "What is the name of your project?"
        mock_question.sequence_order = 1
        mock_question.is_followup = False
        mock_conversation_repo.get_next_unanswered_question.return_value = mock_question

        # Execute the agent with session_id and user_response
        result = await architect_agent.execute({
            "project_id": test_project.id,
            "user_id": test_user.id,
            "session_id": session_id,
            "user_response": "My Awesome Project"
        })

        # Verify the result structure
        assert result["agent"] == "architect"
        assert result["status"] == "asking_question"
        assert result["session_id"] == session_id
        assert "question" in result
        assert result["question"]["key"] == "project_name"
        assert result["question"]["text"] == "What is the name of your project?"
        assert result["next_action"] == "wait_for_response"

        # Verify that update_response was called
        mock_conversation_repo.update_response.assert_called_once_with(
            db=mock_conversation_repo.update_response.call_args[0][0],
            conversation_id=1,
            user_response="My Awesome Project"
        )

    @pytest.mark.asyncio
    async def test_full_interview_flow_to_roadmap(self, architect_agent, test_project, test_user, mock_conversation_repo, mock_llm_service):
        """Test complete interview flow from start to roadmap generation."""
        session_id = "test-session-id"

        # Mock session
        mock_session = Mock()
        mock_session.session_id = session_id
        mock_session.current_state = InterviewState.ASKING_QUESTIONS.value
        mock_conversation_repo.get_interview_session.return_value = mock_session

        # Mock all questions answered (no more unanswered questions)
        mock_conversation_repo.get_next_unanswered_question.return_value = None

        # Mock conversation history for roadmap generation
        mock_responses = {
            "project_name": "Test Project",
            "project_type": "Web Application",
            "target_audience": "Developers",
            "key_features": "User authentication, API endpoints",
            "technology_stack": "Python, FastAPI",
            "timeline": "4 weeks",
            "budget": "$10,000",
            "existing_code": "None"
        }

        mock_conversation_repo.get_session_history.return_value = [
            Mock(question_key=key, user_response=value)
            for key, value in mock_responses.items()
        ]

        # Execute the agent to trigger roadmap generation
        result = await architect_agent.execute({
            "project_id": test_project.id,
            "user_id": test_user.id,
            "session_id": session_id
        })

        # Verify the result structure
        assert result["agent"] == "architect"
        assert result["status"] == "roadmap_generated"
        assert result["session_id"] == session_id
        assert "roadmap" in result
        assert result["message"] == "Your project roadmap has been generated successfully!"
        assert result["next_action"] == "interview_complete"

        # Verify roadmap structure
        roadmap = result["roadmap"]
        assert "overview" in roadmap
        assert "technology_stack" in roadmap
        assert "phases" in roadmap
        assert isinstance(roadmap["phases"], list)

        # Verify state transition to GENERATING_ROADMAP then COMPLETE
        assert mock_conversation_repo.update_interview_session_state.call_count >= 2

    @pytest.mark.asyncio
    async def test_invalid_session_id(self, architect_agent, test_project, test_user, mock_conversation_repo):
        """Test error handling for invalid session ID."""
        # Mock repository to return None for invalid session
        mock_conversation_repo.get_interview_session.return_value = None

        # Execute the agent with invalid session_id
        result = await architect_agent.execute({
            "project_id": test_project.id,
            "user_id": test_user.id,
            "session_id": "invalid-session-id"
        })

        # Verify error response
        assert result["agent"] == "architect"
        assert result["status"] == "error"
        assert "Session invalid-session-id not found" in result["error"]

    @pytest.mark.asyncio
    async def test_missing_required_parameters(self, architect_agent, test_user):
        """Test error handling for missing required parameters."""
        # The 'execute' method is what's being tested for this legacy input structure.
        # Test missing project_id
        result = await architect_agent.execute({
            "user_id": test_user.id
        })

        assert result["status"] == "invalid_input"
        assert "project_id" in result["error"].lower()

        # Test missing user_id
        result = await architect_agent.execute({
            "project_id": 1
        })

        assert result["status"] == "invalid_input"
        assert "user_id" in result["error"].lower()

    @pytest.mark.asyncio
    async def test_greeting_state_transition(self, architect_agent, test_project, test_user, mock_conversation_repo):
        """Test the greeting state transition to asking questions."""
        session_id = "test-session-id"

        # Mock session in GREETING state
        mock_session = Mock()
        mock_session.session_id = session_id
        mock_session.current_state = InterviewState.GREETING.value
        mock_conversation_repo.get_interview_session.return_value = mock_session

        # Execute the agent
        result = await architect_agent.execute({
            "project_id": test_project.id,
            "user_id": test_user.id,
            "session_id": session_id
        })

        # Verify greeting response
        assert result["agent"] == "architect"
        assert result["status"] == "greeting"
        assert result["session_id"] == session_id
        assert "Welcome" in result["message"]
        assert result["next_action"] == "ask_next_question"

        # Verify state transition to ASKING_QUESTIONS
        mock_conversation_repo.update_interview_session_state.assert_called_once()
        call_args = mock_conversation_repo.update_interview_session_state.call_args
        assert call_args[1]["new_state"] == InterviewState.ASKING_QUESTIONS

    @pytest.mark.asyncio
    async def test_complete_state_response(self, architect_agent, test_project, test_user, mock_conversation_repo):
        """Test the complete state response."""
        session_id = "test-session-id"

        # Mock session in COMPLETE state
        mock_session = Mock()
        mock_session.session_id = session_id
        mock_session.current_state = InterviewState.COMPLETE.value
        mock_conversation_repo.get_interview_session.return_value = mock_session

        # Execute the agent
        result = await architect_agent.execute({
            "project_id": test_project.id,
            "user_id": test_user.id,
            "session_id": session_id
        })

        # Verify complete response
        assert result["agent"] == "architect"
        assert result["status"] == "interview_complete"
        assert result["session_id"] == session_id
        assert "complete" in result["message"]
        assert result["next_action"] == "finished"


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
