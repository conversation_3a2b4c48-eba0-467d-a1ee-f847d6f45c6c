CONTRIBUTING - Project rules and quick checks
===========================================

Purpose
-------

This file summarizes the critical conventions and requirements from the project's copilot instructions so contributors and automation can follow them consistently.

Key non-negotiable rules
------------------------

- ALWAYS keep `AGENTS.md` accurate for any agent public-interface changes. If you change an agent's inputs/outputs, update `AGENTS.md` in the same change.

- NEVER remove imports flagged as "unused" by linters without following the Import Preservation Protocol below.

- ALL output produced by tooling or automation must be ASCII-only. Use ASCII bracket labels like [OK], [ERROR], [INFO]. Do not include emojis or other Unicode symbols in automated outputs.

- Container-first development: run and validate in containers. Do not assume host tools are available.

- Do not hardcode secrets or API keys. Use env vars or Docker secrets.

Import Preservation Protocol
----------------------------

When a linter flags an import as unused:

1. Search the file for dynamic usage and type-only usage.

2. If the import is used only for type hints, add a comment: `# noqa: F401 - used for type hints` and keep it.

3. If the import is intended for planned work, comment it out with a TODO explaining why.

4. Only delete an import after confirming it is truly orphaned and include a short justification in the commit message.

Agent/Interface Rules
---------------------

- Use absolute imports from `src` (for example: `from src.services.vector_service import VectorStorageService`). Relative imports are forbidden.

- All I/O code should be async/await and type-hinted.

- Agent interface changes MUST be reflected in `AGENTS.md` and covered by tests.

ASCII-only docs and tooling
--------------------------

- Documentation and automated logs that are intended for machine consumption must be ASCII-only.

- Human-facing docs may contain Unicode, but automated outputs used in CI must be ASCII.

Quick local checks (run inside the development container)
------------------------------------------------------

Run these from the repository root (inside the container):

    # install dev deps
    pip install -r containers/ai-orchestrator/requirements.txt
    pip install -r requirements-dev.txt

    # formatting / lints
    ruff check containers/ai-orchestrator/src/ --fix
    mypy containers/ai-orchestrator/src/ --ignore-missing-imports

    # tests
    pytest -q

What I did in this change
-------------------------

- Added this `CONTRIBUTING.md` file to record the project's critical constraints and quick checks.

Next recommended automated work (I can do these for you)
------------------------------------------------------

1. Run a code scan to detect files with non-ASCII characters that will break automated outputs (candidate: `AGENTS.md`).

2. Identify linter flagged "unused" imports and either mark them with `# noqa` and explanation or find ways to use them.

3. Add a CI step (GitHub Actions) that runs the quick local checks above and enforces ASCII-only output from tooling.

If you want me to proceed, tell me which of the next recommended automated tasks I should run now.

Requirements coverage
---------------------

- Create CONTRIBUTING.md: Done

- Repo scan for agent coverage / unused-imports: Partial (I listed user Python files and found references to `ShellAgent` in the codebase). Next: run full linter or unused-imports scan if requested.
