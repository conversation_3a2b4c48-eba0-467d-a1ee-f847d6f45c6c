#!/usr/bin/env python3
"""
Test Configuration Setup

This script sets up environment variables for testing to ensure 100% test pass rate.
"""

import os
from pathlib import Path

def setup_test_environment():
    """Set up all required environment variables for testing."""

    # Database configuration
    os.environ.setdefault('DATABASE_URL', 'postgresql://postgres:testpassword@localhost:5432/ai_coding_agent')
    os.environ.setdefault('POSTGRES_HOST', 'localhost')
    os.environ.setdefault('POSTGRES_PORT', '5432')
    os.environ.setdefault('POSTGRES_DB', 'ai_coding_agent')
    os.environ.setdefault('POSTGRES_USER', 'postgres')
    os.environ.setdefault('POSTGRES_PASSWORD', 'testpassword')

    # Redis configuration
    os.environ.setdefault('REDIS_URL', 'redis://localhost:6379/0')
    os.environ.setdefault('REDIS_HOST', 'localhost')
    os.environ.setdefault('REDIS_PORT', '6379')
    os.environ.setdefault('REDIS_DB', '0')

    # JWT and security
    os.environ.setdefault('JWT_SECRET', 'test-jwt-secret-key-for-development-only')
    os.environ.setdefault('JWT_ALGORITHM', 'HS256')
    os.environ.setdefault('JWT_EXPIRE_MINUTES', '30')

    # Supabase configuration (optional for testing)
    os.environ.setdefault('SUPABASE_URL', 'https://test-project.supabase.co')
    os.environ.setdefault('SUPABASE_KEY', 'test-anon-key')
    os.environ.setdefault('SUPABASE_ANON_KEY', 'test-anon-key')
    os.environ.setdefault('SUPABASE_SERVICE_KEY', 'test-service-key')

    # Additional Supabase variables that might be needed
    os.environ.setdefault('SUPABASE_JWT_SECRET', 'test-jwt-secret')
    os.environ.setdefault('SUPABASE_PROJECT_ID', 'test-project-id')

    # LLM provider configuration
    os.environ.setdefault('OLLAMA_BASE_URL', 'http://host.docker.internal:11434')
    os.environ.setdefault('OPENROUTER_API_KEY', 'test-openrouter-key')
    os.environ.setdefault('OPENAI_API_KEY', 'test-openai-key')
    os.environ.setdefault('ANTHROPIC_API_KEY', 'test-anthropic-key')

    # Application configuration
    os.environ.setdefault('LOG_LEVEL', 'INFO')
    os.environ.setdefault('DEBUG', 'true')
    os.environ.setdefault('ENVIRONMENT', 'development')

    # Export feature configuration
    os.environ.setdefault('EXPORT_TEMP_DIR', '/tmp/exports')
    os.environ.setdefault('EXPORT_DOWNLOAD_DIR', '/downloads/exports')
    os.environ.setdefault('EXPORT_MAX_SIZE_MB', '1000')
    os.environ.setdefault('EXPORT_RETENTION_DAYS', '7')

    # Workspace configuration
    os.environ.setdefault('WORKSPACE_ROOT', '/workspace')
    os.environ.setdefault('USER_WORKSPACE_ROOT', '/workspace/users')

    # Security configuration
    os.environ.setdefault('ALLOWED_HOSTS', 'localhost,127.0.0.1')
    os.environ.setdefault('CORS_ORIGINS', '["http://localhost:3000","http://localhost:8000"]')
    os.environ.setdefault('MAX_UPLOAD_SIZE_MB', '100')

    # Testing configuration
    os.environ.setdefault('TEST_MODE', 'true')
    os.environ.setdefault('SKIP_AUTH_IN_TESTS', 'true')
    os.environ.setdefault('MOCK_EXTERNAL_SERVICES', 'true')

    print("✅ Test environment variables configured")

def load_env_file():
    """Load environment variables from .env file if it exists."""
    env_file = Path(__file__).parent / '.env'
    if env_file.exists():
        try:
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ.setdefault(key.strip(), value.strip())
            print(f"✅ Loaded environment variables from {env_file}")
        except Exception as e:
            print(f"⚠️  Warning: Could not load .env file: {e}")
    else:
        print(f"ℹ️  No .env file found at {env_file}")

if __name__ == "__main__":
    print("🔧 Setting up test environment...")
    load_env_file()
    setup_test_environment()
    print("🎯 Test environment ready!")
