# AI Coding Agent - Validation Framework

This directory contains the foundational validation framework and error recovery system for the AI Coding Agent project, implementing the specifications from `RoadmapEnforcement&ValidationSystem.md`.

## Architecture Overview

The validation framework provides comprehensive task validation, error recovery, and checkpoint management capabilities:

```
┌─────────────────────────────────────────────────────────────┐
│                    Architect Agent                          │
│  (Master Coordinator with Validation Gates)                 │
└─────────────────┬───────────────────────────────────────────┘
                  │
                  ├── Task Validator
                  │   ├── File System Validation
                  │   ├── Code Syntax Validation
                  │   ├── Functional Testing
                  │   └── Integration Validation
                  │
                  ├── Error Recovery System
                  │   ├── Error Classification
                  │   ├── Automatic Fixing (LLM-assisted)
                  │   ├── Dependency Resolution
                  │   └── Configuration Recovery
                  │
                  └── Checkpoint Manager
                      ├── State Snapshots
                      ├── File Backup/Restore
                      ├── Database Backup/Restore
                      └── Rollback Capability
```

## Core Components

### 1. Base Agent Architecture (`agents/base_agent.py`)

Provides the foundation for all AI agents with built-in validation and error recovery:

- **Sequential Execution**: Resource locking ensures only one agent runs at a time
- **Validation Gates**: Pre and post-execution validation for all tasks
- **Error Recovery**: Automatic retry with intelligent recovery strategies
- **Status Reporting**: Comprehensive monitoring and logging

```python
from agents.base_agent import BaseAgent
from models.validation_models import AgentType

class MyAgent(BaseAgent):
    def __init__(self):
        super().__init__(AgentType.BACKEND)

    async def _execute_core_task(self, task):
        # Implement task-specific logic
        return TaskResult(success=True, output="Task completed")
```

### 2. Task Validation System (`services/task_validator.py`)

Comprehensive validation with multiple verification methods:

- **File System Validation**: Verify expected files exist and are accessible
- **Code Syntax Validation**: Language-specific syntax checking (Python, JS, HTML, CSS)
- **Functional Testing**: Execute test commands and verify results
- **Integration Validation**: Check API endpoints, database connections, etc.

```python
from services.task_validator import TaskValidator

validator = TaskValidator()
validation_result = await validator.validate_task_completion(task, result)

if validation_result.is_valid:
    print("Task validation passed!")
else:
    print(f"Validation failed: {validation_result.error}")
```

### 3. Error Recovery System (`services/error_recovery.py`)

Intelligent error detection and automatic fixing:

- **Error Classification**: Categorize errors for targeted recovery
- **Syntax Error Recovery**: LLM-assisted code fixing
- **Import Error Recovery**: Automatic package installation
- **Configuration Recovery**: Default config file creation
- **Network Recovery**: Retry with exponential backoff

```python
from services.error_recovery import ErrorRecoverySystem

recovery_system = ErrorRecoverySystem()
recovery_result = await recovery_system.handle_task_failure(task, error)

if recovery_result.success:
    print(f"Recovery successful: {recovery_result.actions_taken}")
```

### 4. Checkpoint Management (`services/checkpoint_manager.py`)

State management for rollback capability:

- **Project Snapshots**: Create compressed backups of project state
- **Database Backups**: Backup and restore database state
- **Rollback Capability**: Restore to any previous checkpoint
- **Cleanup Management**: Automatic cleanup of old checkpoints

```python
from services.checkpoint_manager import CheckpointManager

checkpoint_manager = CheckpointManager()

# Create checkpoint
checkpoint_id = await checkpoint_manager.create_checkpoint(
    roadmap_id="my_roadmap",
    checkpoint_type="phase_start"
)

# Rollback if needed
success = await checkpoint_manager.rollback_to_checkpoint(checkpoint_id)
```

### 5. Enhanced Architect Agent (`agents/architect.py`)

Master coordinator with comprehensive validation and user oversight:

- **Roadmap Execution**: Sequential phase and step execution
- **Validation Gates**: Multi-level validation (task → step → phase)
- **User Approval Workflows**: Mandatory approvals at critical points
- **Error Recovery Integration**: Automatic recovery with escalation
- **Checkpoint Integration**: Automatic checkpoint creation and rollback

```python
from agents.architect_agent import ArchitectAgent
from models.validation_models import Roadmap

architect = ArchitectAgent()

# Execute roadmap with full validation
success = await architect.execute_roadmap_with_strict_validation(
    roadmap, user_id="developer"
)
```

## Data Models (`models/validation_models.py`)

Comprehensive data models supporting the validation framework:

- **Execution Models**: `Task`, `Step`, `Phase`, `Roadmap`
- **Validation Models**: `ValidationResult`, `TaskResult`, `RecoveryResult`
- **Status Models**: `ExecutionStatus`, `ApprovalStatus`, `ErrorType`
- **System Models**: `Checkpoint`, `ApprovalRequest`, `HealthCheckResult`

## Usage Examples

### Running the Demonstration

The framework includes a comprehensive demonstration script:

```bash
# From the project root directory
python scripts/demo_validation_framework.py
```

This demonstrates:
- Task validation with syntax checking
- Error recovery with automatic fixing
- Checkpoint management with rollback
- Full roadmap execution structure

### Basic Task Validation

```python
from models.validation_models import Task, TaskType, AgentType
from services.task_validator import TaskValidator

# Create a task
task = Task(
    title="Create Python Module",
    description="Create a sample Python module",
    type=TaskType.CREATE_API_ENDPOINT,
    agent_type=AgentType.BACKEND,
    expected_files=["my_module.py"],
    code_files=["my_module.py"],
    test_command="python -m py_compile my_module.py"
)

# Validate task completion
validator = TaskValidator()
result = await validator.validate_task_completion(task, task_result)
```

### Error Recovery Example

```python
from services.error_recovery import ErrorRecoverySystem

recovery_system = ErrorRecoverySystem()

try:
    # Execute task that might fail
    result = await execute_task(task)
except Exception as error:
    # Attempt automatic recovery
    recovery_result = await recovery_system.handle_task_failure(task, error)

    if recovery_result.retry_recommended:
        # Retry the task
        result = await execute_task(task)
```

### Checkpoint Management Example

```python
from services.checkpoint_manager import CheckpointManager

checkpoint_manager = CheckpointManager()

# Create checkpoint before risky operation
checkpoint_id = await checkpoint_manager.create_checkpoint(
    roadmap_id="development",
    checkpoint_type="before_deployment",
    description="State before deploying new feature"
)

try:
    # Perform risky operation
    await deploy_feature()
except Exception:
    # Rollback on failure
    await checkpoint_manager.rollback_to_checkpoint(checkpoint_id)
```

## Configuration

### Environment Variables

- `PROJECT_ROOT`: Project root directory (default: `/workspace`)
- `BACKUP_ROOT`: Checkpoint backup directory (default: `/workspace/.checkpoints`)
- `LOG_LEVEL`: Logging level (default: `INFO`)

### Validation Configuration

The framework can be configured through the task and roadmap models:

```python
# Configure strict validation
roadmap = Roadmap(
    title="My Project",
    strict_validation=True,     # Enable comprehensive validation
    auto_recovery_enabled=True, # Enable automatic error recovery
    user_id="developer"
)

# Configure task validation requirements
task = Task(
    title="My Task",
    expected_files=["file1.py", "file2.py"],  # Files that must exist
    code_files=["file1.py"],                  # Files to syntax check
    test_command="pytest tests/",             # Tests to run
    integration_checks=["http://localhost:8000/health"]  # Integration checks
)
```

## Implementation Status

### ✅ Completed Features

- **Foundational Validation Framework**: Complete task validation system
- **Error Recovery Patterns**: Automatic error detection and recovery
- **Base Agent Architecture**: Sequential execution with validation gates
- **Task-Level Validation**: Multi-method validation infrastructure
- **Checkpoint Management**: State snapshots and rollback capability
- **Architect Agent**: Master coordinator with comprehensive oversight

### ⚠️ Placeholder/Integration Needed

- **LLM Integration**: Requires LLM service setup for syntax error fixing
- **Database Integration**: Needs specific database backup/restore implementation
- **User Approval System**: Requires WebSocket/UI integration
- **Production Configuration**: Environment-specific validation rules

## Next Steps

1. **LLM Service Integration**: Implement actual LLM calls for error recovery
2. **Database Integration**: Add PostgreSQL/SQLite backup/restore capabilities
3. **WebSocket Integration**: Real-time status updates and approval workflows
4. **Additional Agent Types**: Implement Frontend, Backend, Shell, and IssueFix agents
5. **Production Hardening**: Add security validation and performance optimization

## Testing

Run the demonstration script to verify the framework:

```bash
# From the project root directory
python scripts/demo_validation_framework.py
```

The demo will:
1. Create sample projects with validation scenarios
2. Demonstrate error recovery capabilities
3. Show checkpoint creation and rollback
4. Display comprehensive system status

## Architecture Benefits

- **Fail-Fast Validation**: Catches issues immediately at each level
- **Automatic Error Recovery**: Fixes common problems without human intervention
- **User Control**: Mandatory approvals at critical decision points
- **Rollback Capability**: Can undo changes if things go wrong
- **Production Ready**: Ensures proper configuration for deployment
- **Comprehensive Testing**: Validates functionality, not just code existence

This foundational validation framework provides the reliability and safety needed for autonomous AI agent development workflows.