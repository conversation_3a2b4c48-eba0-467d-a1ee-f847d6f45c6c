"""
Comprehensive Integration Test for Sequential Agent Workflow.

This test validates the entire end-to-end agent workflow from task creation
through final completion, ensuring all components work together correctly.

Test Flow:
1. Create test user and project in database
2. Create high-level task for ArchitectAgent
3. Execute ArchitectAgent to create plan and sub-tasks
4. Use Dispatcher to execute all sub-tasks until completion
5. Verify all tasks are completed with proper output

Author: AI Coding Agent
Version: 1.0.0
"""

import os
import sys
import uuid
import json
import pytest
import asyncio
from typing import Dict, Any, List, Optional
from unittest.mock import AsyncMock, Mock, patch
from pathlib import Path

from src.models.database import create_db_and_tables, SessionLocal
from src.models import User, Project, Task
from src.models.conversation import ConversationHistory
from src.models.roadmap import RoadmapItem
from src.agents.architect_agent import ArchitectAgent
from src.services.dispatcher import Dispatcher
from src.repository.task_repository import TaskRepository
from src.services.lock_manager import LockManager


class MockLockManager:
    """Mock LockManager for testing without Redis dependency."""

    def __init__(self):
        self._locks: Dict[int, Dict[str, Any]] = {}

    async def acquire_lock(
        self,
        project_id: int,
        agent_role: str,
        task_id: int,
        timeout_seconds: int = 600,
    ) -> bool:
        """Mock lock acquisition - always succeeds for testing."""
        self._locks[project_id] = {
            "agent_role": agent_role,
            "task_id": task_id,
            "acquired_at": "2024-01-01T00:00:00Z"
        }
        return True

    async def release_lock(self, project_id: int) -> None:
        """Mock lock release."""
        if project_id in self._locks:
            del self._locks[project_id]


class MockLLMService:
    """Mock LLM service that returns predictable planning responses."""

    async def generate(self, request, user_id: str = "test"):
        """Generate mock planning response."""
        mock_plan = {
            "plan": [
                {
                    "step": 1,
                    "agent_role": "backend",
                    "task_description": "Create FastAPI application structure with main.py and basic routing"
                },
                {
                    "step": 2,
                    "agent_role": "backend",
                    "task_description": "Implement hello world endpoint at /api/hello with JSON response"
                },
                {
                    "step": 3,
                    "agent_role": "shell",
                    "task_description": "Create requirements.txt with FastAPI and uvicorn dependencies"
                }
            ]
        }

        # Mock LLM response object
        mock_response = Mock()
        mock_response.content = json.dumps(mock_plan)
        mock_response.model = "mock-llm"
        mock_response.provider = Mock()
        mock_response.provider.value = "mock"

        return mock_response


class MockAgent:
    """Mock specialist agent for testing that returns task_input for inspection."""

    def __init__(self, agent_role: str):
        self.agent_role = agent_role

    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Mock agent execution that returns the input for inspection."""
        # Return the task_input so we can inspect context_briefing
        if self.agent_role == "backend":
            return {
                "agent": self.agent_role,
                "success": True,
                "task_input": task_input,  # Include the full input for verification
                "message": f"Successfully executed {self.agent_role} task",
                "files_created": ["main.py", "requirements.txt"],
                "code_generated": "FastAPI application with hello world endpoint"
            }
        elif self.agent_role == "shell":
            return {
                "agent": self.agent_role,
                "success": True,
                "task_input": task_input,  # Include the full input for verification
                "message": f"Successfully executed {self.agent_role} task",
                "files_created": ["requirements.txt"],
                "commands_executed": ["pip install fastapi uvicorn"]
            }
        else:
            return {
                "agent": self.agent_role,
                "success": True,
                "task_input": task_input,  # Include the full input for verification
                "message": f"Successfully executed {self.agent_role} task"
            }


@pytest.fixture(scope="session", autouse=True)
def setup_test_database():
    """Create test database tables once per session."""
    create_db_and_tables()


@pytest.fixture
def db_session():
    """Provide a clean database session for each test."""
    session = SessionLocal()
    try:
        yield session
    finally:
        # Clean up test data
        session.query(Task).delete()
        session.query(Project).delete()
        session.query(User).delete()
        session.commit()
        session.close()


@pytest.fixture
def test_user(db_session) -> User:
    """Create a test user in the database."""
    user = User.create_from_supabase_user(
        supabase_user_id=uuid.uuid4(),
        email="<EMAIL>",
        username="testuser",
        full_name="Test User"
    )
    user.hashed_password = "test_hash"
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_project(db_session, test_user) -> Project:
    """Create a test project in the database."""
    project = Project(
        name="test_fastapi_project",
        description="Test project for agent workflow integration"
    )
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project


@pytest.fixture
def mock_lock_manager():
    """Provide mock lock manager for testing."""
    return MockLockManager()


@pytest.fixture
def mock_llm_service():
    """Provide mock LLM service for testing."""
    return MockLLMService()


@pytest.fixture
def agent_registry():
    """Create agent registry with mock agents."""
    return {
        "backend": lambda: MockAgent("backend"),
        "frontend": lambda: MockAgent("frontend"),
        "shell": lambda: MockAgent("shell"),
        "architect": lambda: MockAgent("architect")
    }


@pytest.fixture
def mock_conversation_history(db_session, test_project, test_user):
    """Create mock conversation history for RAG testing."""
    conversations = [
        ConversationHistory(
            project_id=test_project.id,
            user_id=str(test_user.id),
            session_id="test_session_1",
            question_key="website_type",
            question_text="What type of website are you building?",
            user_response="I want to build a modern web application with a clean, professional design using React for the frontend and FastAPI for the backend.",
            sequence_order=1
        ),
        ConversationHistory(
            project_id=test_project.id,
            user_id=str(test_user.id),
            session_id="test_session_1",
            question_key="tech_stack",
            question_text="What technologies do you prefer?",
            user_response="I prefer Python for backend, React for frontend, and PostgreSQL for database. I want to use modern tools and best practices.",
            sequence_order=2
        ),
        ConversationHistory(
            project_id=test_project.id,
            user_id=str(test_user.id),
            session_id="test_session_1",
            question_key="features",
            question_text="What are the main features you need?",
            user_response="I need user authentication, a dashboard, API endpoints for data management, and a responsive design that works on mobile devices.",
            sequence_order=3
        )
    ]

    for conv in conversations:
        db_session.add(conv)
    db_session.commit()

    return conversations


@pytest.fixture
def mock_roadmap_items(db_session, test_project):
    """Create mock roadmap items for RAG testing."""
    roadmap_items = [
        RoadmapItem(
            project_id=test_project.id,
            parent_id=None,
            level=1,
            sequence_order=1,
            title="Project Setup Phase",
            description="Set up the basic project structure with FastAPI backend, React frontend, and PostgreSQL database configuration.",
            item_type="phase",
            status="completed"
        ),
        RoadmapItem(
            project_id=test_project.id,
            parent_id=None,
            level=1,
            sequence_order=2,
            title="Authentication System",
            description="Implement user authentication with JWT tokens, password hashing, and secure login/logout endpoints.",
            item_type="phase",
            status="in_progress"
        ),
        RoadmapItem(
            project_id=test_project.id,
            parent_id=None,
            level=2,
            sequence_order=1,
            title="Backend API Development",
            description="Create RESTful API endpoints for user management, data operations, and business logic implementation.",
            item_type="step",
            status="in_progress"
        ),
        RoadmapItem(
            project_id=test_project.id,
            parent_id=None,
            level=3,
            sequence_order=1,
            title="Implement User Registration Endpoint",
            description="Create POST /api/auth/register endpoint with input validation, password hashing, and user creation logic.",
            item_type="task",
            status="pending",
            agent_role="backend"
        )
    ]

    for item in roadmap_items:
        db_session.add(item)
    db_session.commit()

    return roadmap_items


@pytest.mark.asyncio
class TestAgentWorkflowIntegration:
    """Integration tests for the complete agent workflow."""

    async def test_complete_agent_workflow_end_to_end(
        self,
        db_session,
        test_user,
        test_project,
        mock_lock_manager,
        mock_llm_service,
        agent_registry,
        mock_conversation_history,
        mock_roadmap_items
    ):
        """
        Test the complete agent workflow from task creation to completion.

        This test validates:
        1. ArchitectAgent creates a plan and sub-tasks
        2. Dispatcher processes all sub-tasks with RAG context integration
        3. All tasks complete successfully with proper output and context briefing
        4. RAG system provides relevant conversation history and roadmap context
        """

        # ARRANGE: Set up the test environment
        task_input = {
            "goal": "create a basic hello world FastAPI endpoint",
            "project_id": test_project.id,
            "description": "Build a simple FastAPI application with a hello world endpoint"
        }

        # ACT PART 1: Execute ArchitectAgent to create plan
        with patch('src.router.llm_router.get_llm_service', return_value=mock_llm_service):
            architect = ArchitectAgent()
            architect_result = await architect.execute(task_input)

        # ASSERT PART 1: Verify ArchitectAgent results
        assert architect_result["success"] == True, f"Architect failed: {architect_result}"
        assert "plan" in architect_result, "Architect should return a plan"
        assert len(architect_result["plan"]) == 3, "Plan should have 3 steps"
        assert "created_task_ids" in architect_result, "Architect should return created task IDs"
        assert len(architect_result["created_task_ids"]) == 3, "Should create 3 tasks"

        # Verify tasks were created in database
        created_tasks = db_session.query(Task).filter(
            Task.project_id == test_project.id,
            Task.status == "pending"
        ).all()
        assert len(created_tasks) == 3, "Should have 3 pending tasks in database"

        # Verify task details
        task_roles = [task.agent_role for task in created_tasks]
        assert "backend" in task_roles, "Should have backend tasks"
        assert "shell" in task_roles, "Should have shell tasks"

        # ACT PART 2: Execute Dispatcher to process all tasks
        dispatcher = Dispatcher(
            lock_manager=mock_lock_manager,
            session_factory=lambda: db_session
        )

        with patch.object(dispatcher, '_get_agent_factory') as mock_factory, \
             patch('src.services.vector_service.get_vector_service') as mock_vector:
            # Return MockAgent for all roles
            mock_factory.side_effect = lambda role: lambda: MockAgent(role)

            # Mock vector service
            mock_vector_service = Mock()
            mock_vector.return_value = mock_vector_service

            # Run dispatcher until no more pending tasks
            max_iterations = 10  # Safety limit
            iteration = 0

            while iteration < max_iterations:
                # Check for pending tasks
                pending_tasks = db_session.query(Task).filter(
                    Task.project_id == test_project.id,
                    Task.status.in_(["pending", "running"])
                ).all()

                if not pending_tasks:
                    break

                # Run one dispatcher cycle
                await dispatcher.run_once()
                iteration += 1

                # Refresh session to see updates
                db_session.commit()        # ASSERT PART 2: Verify all tasks completed successfully
        final_tasks = db_session.query(Task).filter(
            Task.project_id == test_project.id
        ).all()

        assert len(final_tasks) == 3, "Should still have 3 tasks total"

        # Verify all tasks are completed
        completed_tasks = [task for task in final_tasks if task.status == "completed"]
        assert len(completed_tasks) == 3, f"All tasks should be completed, got statuses: {[t.status for t in final_tasks]}"

        # Verify all tasks have output data
        for task in completed_tasks:
            assert task.output_data is not None, f"Task {task.id} should have output data"
            assert task.output_data.get("success") == True, f"Task {task.id} should have successful status"
            assert "message" in task.output_data, f"Task {task.id} should have a message"

        # Verify specific task outputs based on agent roles
        backend_tasks = [task for task in completed_tasks if task.agent_role == "backend"]
        shell_tasks = [task for task in completed_tasks if task.agent_role == "shell"]

        assert len(backend_tasks) >= 1, "Should have at least one backend task"
        assert len(shell_tasks) >= 1, "Should have at least one shell task"

        # Verify backend task outputs
        for backend_task in backend_tasks:
            output = backend_task.output_data
            assert "files_created" in output, "Backend task should report files created"
            assert "code_generated" in output, "Backend task should report code generation"

        # Verify shell task outputs
        for shell_task in shell_tasks:
            output = shell_task.output_data
            assert "files_created" in output, "Shell task should report files created"
            assert "commands_executed" in output, "Shell task should report commands executed"

        # ASSERT PART 3: Verify RAG Context Integration
        # Check that each completed task received context briefing from RAG
        for task in completed_tasks:
            output = task.output_data
            assert "task_input" in output, f"Task {task.id} should have task_input in output"

            task_input = output["task_input"]
            assert "context_briefing" in task_input, f"Task {task.id} should have context_briefing in task_input"

            context_briefing = task_input["context_briefing"]
            assert isinstance(context_briefing, str), f"Context briefing should be a string for task {task.id}"
            assert len(context_briefing) > 0, f"Context briefing should not be empty for task {task.id}"

            # Verify context briefing contains expected elements
            assert "=== CONTEXT BRIEFING ===" in context_briefing, "Context briefing should have header"
            assert "=== END CONTEXT BRIEFING ===" in context_briefing, "Context briefing should have footer"

            # Verify conversation history content
            assert "💬 RELEVANT INTERVIEW ANSWERS:" in context_briefing, "Should include interview answers section"
            # Check that at least one interview question is found (relevance varies by task)
            has_interview_content = (
                "What type of website are you building?" in context_briefing or
                "What technologies do you prefer?" in context_briefing or
                "What are the main features you need?" in context_briefing
            )
            assert has_interview_content, f"Should include at least one relevant interview question, found: {context_briefing}"

            # Verify roadmap content
            assert "🎯 CURRENT GOALS:" in context_briefing, "Should include roadmap goals section"
            assert "Authentication System" in context_briefing, "Should include roadmap items"
            # Note: Backend API Development may not match due to relevance filtering
            print(f"✅ Context briefing contains roadmap goals: {'Backend API Development' in context_briefing}")

            print(f"✅ RAG Context Integration verified for task {task.id}")
            print(f"   - Context briefing length: {len(context_briefing)} characters")
            print(f"   - Contains interview answers: {'website_type' in context_briefing}")
            print(f"   - Contains roadmap goals: {'Authentication System' in context_briefing}")

        print(f"✅ Complete agent workflow test with RAG integration passed!")
        print(f"   - ArchitectAgent created {len(architect_result['plan'])} planned steps")
        print(f"   - {len(completed_tasks)} tasks completed successfully")
        print(f"   - All tasks have proper output data")
        print(f"   - All tasks received context briefing from RAG system")

    async def test_architect_agent_invalid_input_handling(self, mock_llm_service):
        """Test ArchitectAgent handles invalid input gracefully."""

        # Test missing project_id
        with patch('src.agents.architect_agent.get_llm_service', return_value=mock_llm_service):
            architect = ArchitectAgent()
            result = await architect.execute({"goal": "test goal"})

        assert result["status"] == "invalid_input"
        assert "project_id" in result["error"]

        # Test non-integer project_id
        result = await architect.execute({"goal": "test goal", "project_id": "invalid"})
        assert result["status"] == "invalid_input"
        assert "project_id" in result["error"]

    async def test_dispatcher_handles_no_pending_tasks(self, db_session, mock_lock_manager, agent_registry):
        """Test Dispatcher handles case with no pending tasks."""

        dispatcher = Dispatcher(
            lock_manager=mock_lock_manager,
            session_factory=lambda: db_session
        )

        # Should complete without error when no tasks exist
        await dispatcher.run_once()

        # Verify no tasks were created
        tasks = db_session.query(Task).all()
        assert len(tasks) == 0

    async def test_task_repository_operations(self, db_session, test_project):
        """Test TaskRepository CRUD operations work correctly."""

        # Test create_task
        input_data = {"feature": "test feature", "goal": "test goal"}
        task = await TaskRepository.create_task(
            db_session,
            project_id=test_project.id,
            agent_role="backend",
            input_data=input_data
        )

        assert task.id is not None
        assert task.project_id == test_project.id
        assert task.agent_role == "backend"
        assert task.status == "pending"
        assert task.input_data == input_data

        # Test get_next_pending_task_for_project
        next_task = await TaskRepository.get_next_pending_task_for_project(
            db_session, test_project.id
        )
        assert next_task is not None
        assert next_task.id == task.id

        # Test update_task_status
        updated_task = await TaskRepository.update_task_status(
            db_session, task.id, "completed"
        )
        assert updated_task.status == "completed"

        # Test record_task_output
        output_data = {"status": "success", "message": "Task completed"}
        await TaskRepository.record_task_output(
            db_session, task.id, output_data
        )

        # Refresh and verify output was saved
        db_session.refresh(updated_task)
        assert updated_task.output_data == output_data

    async def test_workflow_with_failed_task(
        self,
        db_session,
        test_project,
        mock_lock_manager,
        mock_llm_service
    ):
        """Test workflow handles failed tasks appropriately."""

        # Create a failing agent
        class FailingAgent:
            def __init__(self):
                self.agent_role = "failing"

            async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
                return {
                    "agent": "failing",
                    "success": False,
                    "error": "Simulated failure for testing"
                }

        failing_agent_registry = {
            "backend": lambda: FailingAgent(),
            "shell": lambda: MockAgent("shell")
        }

        # Create a task manually that will fail
        task = await TaskRepository.create_task(
            db_session,
            project_id=test_project.id,
            agent_role="backend",
            input_data={"feature": "failing task"}
        )

        # Run dispatcher
        dispatcher = Dispatcher(
            session_factory=lambda: db_session,
            lock_manager=mock_lock_manager,
            agent_registry=failing_agent_registry
        )

        await dispatcher.run_once()

        # Verify task was processed and has output (even though it failed)
        db_session.refresh(task)
        assert task.status == "completed"  # Dispatcher marks as completed regardless
        assert task.output_data is not None
        assert task.output_data.get("status") == "error"

    async def test_multiple_projects_isolation(
        self,
        db_session,
        test_user,
        mock_lock_manager,
        agent_registry
    ):
        """Test that tasks from different projects are properly isolated."""

        # Create two projects
        project1 = Project(name="project1", description="First project")
        project2 = Project(name="project2", description="Second project")
        db_session.add(project1)
        db_session.add(project2)
        db_session.commit()
        db_session.refresh(project1)
        db_session.refresh(project2)

        # Create tasks for each project
        task1 = await TaskRepository.create_task(
            db_session,
            project_id=project1.id,
            agent_role="backend",
            input_data={"feature": "project1 task"}
        )

        task2 = await TaskRepository.create_task(
            db_session,
            project_id=project2.id,
            agent_role="backend",
            input_data={"feature": "project2 task"}
        )

        # Run dispatcher - should process both projects
        dispatcher = Dispatcher(
            session_factory=lambda: db_session,
            lock_manager=mock_lock_manager,
            agent_registry=agent_registry
        )

        # Run multiple cycles to ensure both projects are processed
        for _ in range(3):
            await dispatcher.run_once()

        # Verify both tasks completed
        db_session.refresh(task1)
        db_session.refresh(task2)

        assert task1.status == "completed"
        assert task2.status == "completed"
        assert task1.output_data.get("feature") == "project1 task"
        assert task2.output_data.get("feature") == "project2 task"
