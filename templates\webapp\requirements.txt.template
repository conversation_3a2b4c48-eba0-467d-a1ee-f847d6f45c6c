# __PROJECT_NAME__ Python Dependencies
# Production-ready FastAPI application requirements

# Core FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Redis and caching
redis[hiredis]==5.0.1
aioredis==2.0.1

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP client and requests
httpx==0.25.2
aiohttp==3.9.1

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Logging and monitoring
structlog==23.2.0
python-json-logger==2.0.7

# Environment and configuration
python-dotenv==1.0.0

# Development and testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Production WSGI server (alternative to uvicorn)
gunicorn==21.2.0
