from datetime import timedelta
import logging
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel

from src.core.config import settings
from src.core.rate_limiting import apply_rate_limit
from src.utils.auth import create_access_token, get_current_user, get_supabase

logger = logging.getLogger(__name__)

# Dependency to check if auth is configured and enabled
async def check_auth_configured():
    if not settings.supabase_available:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service is not configured.",
        )

router = APIRouter(
    prefix="/auth",
    tags=["authentication"],
    dependencies=[Depends(check_auth_configured)]
)

class UserCreate(BaseModel):
    email: str
    password: str
    username: Optional[str] = None

class UserLogin(BaseModel):
    email: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class UserRegistrationResponse(BaseModel):
    message: str
    user_id: str
    email: str

@router.post("/register", response_model=UserRegistrationResponse, status_code=status.HTTP_201_CREATED)
@apply_rate_limit("10/minute")
async def register_user(user: UserCreate, request: Request):
    """Register a new user."""
    supabase = get_supabase()
    try:
        response = supabase.auth.sign_up({
            "email": user.email,
            "password": user.password,
            "options": {"data": {"username": user.username}},
        })
        if not response.user:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to create user")

        logger.info(f"User {response.user.email} registered successfully. Awaiting email confirmation.")
        return {
            "message": "Registration successful. Please check your email to confirm your account.",
            "user_id": response.user.id,
            "email": response.user.email,
        }
    except Exception as e:
        logger.error(f"Registration failed for email {user.email}: {e}", exc_info=True)
        # Check for specific GoTrue errors if possible, otherwise a generic message
        if "User already registered" in str(e):
             raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="User with this email already exists.")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Registration failed.")

@router.post("/login", response_model=Token)
@apply_rate_limit("20/minute")
async def login_user(user: UserLogin, request: Request):
    """Login user and return JWT token."""
    supabase = get_supabase()
    try:
        response = supabase.auth.sign_in_with_password({
            "email": user.email,
            "password": user.password
        })
        if not response.user:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")

        access_token_expires = timedelta(minutes=settings.auth.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": response.user.id, "email": response.user.email},
            expires_delta=access_token_expires
        )
        return {"access_token": access_token, "token_type": "bearer"}
    except Exception as e:
        logger.error(f"Login failed for email {user.email}: {e}")
        if "Email not confirmed" in str(e):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Email not confirmed.")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials.")

@router.post("/logout")
async def logout_user(current_user=Depends(get_current_user)):
    """Logout current user."""
    supabase = get_supabase()
    try:
        supabase.auth.sign_out()
        return {"message": "Successfully logged out"}
    except Exception as e:
        logger.error(f"Logout failed for user {current_user.email}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Logout failed.")

@router.get("/me", summary="Get Current User Info")
async def get_current_user_info(current_user=Depends(get_current_user)):
    """Retrieves information for the currently authenticated user."""
    return {
        "user_id": current_user.id,
        "email": current_user.email,
        "username": getattr(current_user, 'username', None),
    }

@router.get("/config", summary="Get Authentication Configuration")
async def get_auth_config():
    """Returns the status of the authentication service configuration."""
    # This endpoint will only be reachable if the dependency check passes.
    return {"auth_enabled": True}
