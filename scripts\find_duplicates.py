import os
import hashlib
import ast
from collections import defaultdict

def find_duplicate_files(root_dir, exclude_dirs=None):
    if exclude_dirs is None:
        exclude_dirs = []

    file_hashes = defaultdict(list)
    for dirpath, dirnames, filenames in os.walk(root_dir):
        # Exclude specified directories
        dirnames[:] = [d for d in dirnames if d not in exclude_dirs]

        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            try:
                if os.path.getsize(file_path) > 0:
                    with open(file_path, 'rb') as f:
                        file_hash = hashlib.md5(f.read()).hexdigest()
                    file_hashes[file_hash].append(file_path)
            except (IOError, OSError):
                continue
    return {hash_val: paths for hash_val, paths in file_hashes.items() if len(paths) > 1}

def find_duplicate_functions(root_dir, exclude_dirs=None):
    if exclude_dirs is None:
        exclude_dirs = []

    function_hashes = defaultdict(list)
    for dirpath, dirnames, filenames in os.walk(root_dir):
        # Exclude specified directories
        dirnames[:] = [d for d in dirnames if d not in exclude_dirs]

        for filename in filenames:
            if filename.endswith('.py'):
                file_path = os.path.join(dirpath, filename)
                try:
                    if os.path.getsize(file_path) > 0:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            source = f.read()
                        tree = ast.parse(source)
                        for node in ast.walk(tree):
                            if isinstance(node, ast.FunctionDef):
                                function_source = ast.get_source_segment(source, node)
                                function_hash = hashlib.md5(function_source.encode('utf-8')).hexdigest()
                                function_hashes[function_hash].append(f"{file_path}:{node.name}")
                except (IOError, OSError, SyntaxError):
                    continue
    return {hash_val: paths for hash_val, paths in function_hashes.items() if len(paths) > 1}

if __name__ == "__main__":
    project_root = '.'
    exclude_directories = ['.venv', '.git']

    with open("findings.txt", "w") as report_file:
        report_file.write("Duplicate File Analysis Report\n")
        report_file.write("==============================\n\n")

        print("Finding duplicate files...")
        duplicate_files = find_duplicate_files(project_root, exclude_dirs=exclude_directories)
        if duplicate_files:
            report_file.write("Duplicate Files Found:\n")
            report_file.write("----------------------\n")
            for hash_val, paths in duplicate_files.items():
                report_file.write(f"Hash: {hash_val}\n")
                for path in paths:
                    report_file.write(f"  - {path}\n")
                report_file.write("\n")
            print("Found duplicate files. See findings.txt for details.")
        else:
            report_file.write("No duplicate files found.\n")
            print("No duplicate files found.")

        report_file.write("\n\nDuplicate Function Analysis Report\n")
        report_file.write("==================================\n\n")

        print("\nFinding duplicate functions...")
        duplicate_functions = find_duplicate_functions(project_root, exclude_dirs=exclude_directories)
        if duplicate_functions:
            report_file.write("Duplicate Functions Found:\n")
            report_file.write("--------------------------\n")
            for hash_val, paths in duplicate_functions.items():
                report_file.write(f"Hash: {hash_val}\n")
                for path in paths:
                    report_file.write(f"  - {path}\n")
                report_file.write("\n")
            print("Found duplicate functions. See findings.txt for details.")
        else:
            report_file.write("No duplicate functions found.\n")
            print("No duplicate functions found.")
