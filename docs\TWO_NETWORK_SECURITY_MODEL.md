# Two-Network Security Model Implementation

## Overview

This document describes the implementation of a dual-network security architecture for the AI Coding Agent project. This model provides enhanced security through network segmentation, implementing the principle of least privilege at the network level.

## Network Architecture

### Web Network (DMZ - Demilitarized Zone)
**Network Name:** `ai-coding-agent_web_network`
**Purpose:** Public-facing services accessible via Traefik reverse proxy

**Members:**
- `traefik` - Reverse proxy and load balancer (entry point)
- `ai-orchestrator` - Main API service (bridge to internal)
- `user-portal` - Frontend web application
- `code-server` instances - User development environments
- `prometheus` - Monitoring dashboard (web access)
- `grafana` - Observability dashboard (web access)

### Internal Network (Secure Backend)
**Network Name:** `ai-coding-agent_internal_network`
**Purpose:** Secure backend services with no external exposure

**Members:**
- `ai-orchestrator` - Bridge service (dual network access)
- `redis` - Cache and session store
- `ollama` - Local LLM inference service
- `docker-proxy` - Secure Docker socket access
- `postgresql` - Database (when containerized)

### Monitoring Network
**Network Name:** `monitoring-network`
**Purpose:** Isolated observability stack

**Members:**
- `prometheus` - Metrics collection
- `grafana` - Visualization
- `loki` - Log aggregation
- `promtail` - Log collection

## Security Benefits

### 1. Attack Surface Reduction
- **Before:** All services on single network - compromise of any container gives access to database
- **After:** Database/Redis only accessible from ai-orchestrator - attackers must compromise the orchestrator to reach critical data

### 2. Network Isolation
- User code-server instances cannot directly access Redis or database
- Frontend (user-portal) cannot directly access backend services
- Monitoring stack is isolated from core application data

### 3. Principle of Least Privilege
- Each service only has access to networks it absolutely needs
- Clear separation between public-facing and internal services
- Simplified security model - network access equals trust level

## Implementation Details

### Dual-Network Services

#### AI Orchestrator (Bridge Service)
```yaml
networks:
  - web       # Receives traffic from Traefik
  - internal  # Accesses Redis, Ollama, PostgreSQL
```

The ai-orchestrator is the **only** service with dual network access, making it the controlled bridge between the DMZ and secure backend.

### Network-Specific Services

#### Web Network Only (DMZ Services)
```yaml
# Services that only need external access via Traefik
services:
  traefik:
    networks: [web]
  user-portal:
    networks: [web]
  code-server:
    networks: [web]
```

#### Internal Network Only (Secure Backend)
```yaml
# Services that should never be externally accessible
services:
  redis:
    networks: [internal]
  ollama:
    networks: [internal]
  docker-proxy:
    networks: [internal]
```

## Traefik Configuration Updates

All Traefik labels have been updated to use the new web network:

```yaml
labels:
  - "traefik.docker.network=ai-coding-agent_web_network"
```

This ensures Traefik routes traffic correctly within the DMZ.

## Migration from Legacy Networks

### Deprecated Networks
The following networks are marked as deprecated and will be removed:
- `ai-coding-agent-network` (legacy catch-all network)
- `traefik-network` (replaced by `web` network)

### Gradual Migration
The deprecated networks are kept temporarily for backward compatibility but should not be used for new services.

## Traffic Flow

```
Internet → Traefik (web) → ai-orchestrator (web) → ai-orchestrator (internal) → Redis/Ollama/DB
```

1. **External Request:** User makes HTTP request to domain
2. **Reverse Proxy:** Traefik receives request on web network
3. **API Gateway:** Traefik routes to ai-orchestrator on web network
4. **Backend Access:** ai-orchestrator accesses Redis/Ollama on internal network
5. **Response:** Data flows back through the same path

## Development Environment

The development override (`docker-compose.dev.yml`) maintains the same network structure:

- Adds localhost port bindings for development access
- Preserves network isolation in development
- Allows debugging while maintaining security model

## Operational Commands

### Starting the Stack
```bash
# Production
docker-compose up -d

# Development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch
```

### Network Inspection
```bash
# List networks
docker network ls

# Inspect web network
docker network inspect ai-coding-agent_web_network

# Inspect internal network
docker network inspect ai-coding-agent_internal_network

# Check service network connections
docker inspect <container_name> | jq '.[0].NetworkSettings.Networks'
```

### Verification

#### Network Connectivity Testing
```bash
# Test internal network isolation
docker-compose exec user-portal ping redis  # Should fail
docker-compose exec ai-orchestrator ping redis  # Should succeed

# Test web network connectivity
docker-compose exec traefik ping ai-orchestrator  # Should succeed
docker-compose exec traefik ping redis  # Should fail
```

#### Service Health Checks
```bash
# Verify all services are healthy
docker-compose ps

# Check specific service health
docker-compose exec ai-orchestrator curl http://redis:6379  # Should succeed
```

## Troubleshooting

### Common Issues

1. **Service Cannot Connect to Database**
   - Verify service is on `internal` network
   - Check ai-orchestrator has dual network access

2. **Traefik Cannot Route to Service**
   - Verify service is on `web` network
   - Check Traefik labels use correct network name

3. **Development Environment Issues**
   - Ensure dev overrides maintain network structure
   - Check localhost port bindings are correct

### Logs and Debugging
```bash
# View network-related logs
docker-compose logs traefik | grep network
docker-compose logs ai-orchestrator | grep connection

# Debug network connectivity
docker-compose exec ai-orchestrator nslookup redis
docker-compose exec traefik nslookup ai-orchestrator
```

## Security Considerations

### Network Policies
- No direct communication between web and internal networks except through ai-orchestrator
- All external access must go through Traefik
- Internal services are completely isolated from internet

### Monitoring
- Network traffic monitoring through Prometheus
- Connection attempt logging in Traefik
- Health check monitoring for all network bridges

### Future Enhancements
- Consider implementing Kubernetes NetworkPolicies for container orchestration
- Add network-level encryption for sensitive data flows
- Implement network monitoring and intrusion detection

## Compliance and Auditing

This two-network model helps meet various compliance requirements:

- **PCI DSS:** Network segmentation requirements
- **SOC 2:** Security controls and monitoring
- **GDPR:** Data protection through isolation
- **HIPAA:** Administrative safeguards (if applicable)

The clear network boundaries make it easier to audit and demonstrate security controls during compliance reviews.
