# AI Coding Agent - Development Container Build Script
# Systematic approach to building containers with proper error handling

Write-Host "🚀 AI Coding Agent - Development Container Build" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Function to check if command succeeded
function Test-CommandSuccess {
  param($ExitCode, $ServiceName)
  if ($ExitCode -eq 0) {
    Write-Host "✅ $ServiceName - SUCCESS" -ForegroundColor Green
    return $true
  }
  else {
    Write-Host "❌ $ServiceName - FAILED (Exit Code: $ExitCode)" -ForegroundColor Red
    return $false
  }
}

# Function to build and test individual service
function Build-Service {
  param($ServiceName, $Description)

  Write-Host "`n🔨 Building $ServiceName ($Description)..." -ForegroundColor Yellow
  Write-Host "================================" -ForegroundColor Yellow

  # Build the service
  docker-compose -f docker-compose.yml -f docker-compose.dev.yml build $ServiceName
  $buildResult = $LASTEXITCODE

  if (Test-CommandSuccess $buildResult $ServiceName) {
    Write-Host "📋 Checking $ServiceName configuration..." -ForegroundColor Cyan

    # Validate the service configuration
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml config --services | Where-Object { $_ -eq $ServiceName }
    $configResult = $LASTEXITCODE

    if (Test-CommandSuccess $configResult "$ServiceName Config") {
      return $true
    }
  }

  return $false
}

# Step 1: Network Setup
Write-Host "`n🌐 Setting up networks..." -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

$networks = @(
  "ai-coding-agent_web_network",
  "ai-coding-agent_internal_network"
)

foreach ($network in $networks) {
  Write-Host "Creating network: $network" -ForegroundColor White
  docker network create $network --driver bridge 2>$null
  if ($LASTEXITCODE -eq 0 -or $LASTEXITCODE -eq 1) {
    Write-Host "✅ Network $network ready" -ForegroundColor Green
  }
  else {
    Write-Host "❌ Failed to create network $network" -ForegroundColor Red
  }
}

# Step 2: Build Core Infrastructure Services (no dependencies)
Write-Host "`n🏗️  Phase 1: Core Infrastructure" -ForegroundColor Magenta
Write-Host "=================================" -ForegroundColor Magenta

$coreServices = @(
  @{Name = "docker-proxy"; Description = "Docker Socket Proxy" },
  @{Name = "redis"; Description = "Redis Cache Database" }
)

$coreSuccess = $true
foreach ($service in $coreServices) {
  if (-not (Build-Service $service.Name $service.Description)) {
    $coreSuccess = $false
    Write-Host "⚠️  Core service $($service.Name) failed - stopping build" -ForegroundColor Red
    break
  }
}

if (-not $coreSuccess) {
  Write-Host "`n❌ Core infrastructure build failed. Check errors above." -ForegroundColor Red
  exit 1
}

# Step 3: Build AI/ML Services
Write-Host "`n🤖 Phase 2: AI/ML Services" -ForegroundColor Magenta
Write-Host "=========================" -ForegroundColor Magenta

$aiServices = @(
  @{Name = "ollama-chromadb"; Description = "Ollama LLM + ChromaDB Vector Store" }
)

foreach ($service in $aiServices) {
  if (-not (Build-Service $service.Name $service.Description)) {
    Write-Host "⚠️  AI service $($service.Name) failed but continuing..." -ForegroundColor Yellow
  }
}

# Step 4: Build Application Services
Write-Host "`n💻 Phase 3: Application Services" -ForegroundColor Magenta
Write-Host "===============================" -ForegroundColor Magenta

$appServices = @(
  @{Name = "ai-orchestrator"; Description = "FastAPI Backend Service" },
  @{Name = "user-portal"; Description = "Next.js Frontend Service" },
  @{Name = "code-server"; Description = "VS Code Server" }
)

foreach ($service in $appServices) {
  if (-not (Build-Service $service.Name $service.Description)) {
    Write-Host "⚠️  Application service $($service.Name) failed but continuing..." -ForegroundColor Yellow
  }
}

# Step 5: Build Monitoring Services (Optional)
Write-Host "`n📊 Phase 4: Monitoring Services (Optional)" -ForegroundColor Magenta
Write-Host "==========================================" -ForegroundColor Magenta

$monitoringServices = @(
  @{Name = "traefik"; Description = "Reverse Proxy & Load Balancer" },
  @{Name = "prometheus"; Description = "Metrics Collection" },
  @{Name = "grafana"; Description = "Metrics Visualization" },
  @{Name = "loki"; Description = "Log Aggregation" }
)

foreach ($service in $monitoringServices) {
  Write-Host "🔧 Building optional service: $($service.Name)" -ForegroundColor Cyan
  if (-not (Build-Service $service.Name $service.Description)) {
    Write-Host "⚠️  Optional service $($service.Name) failed - this is OK for development" -ForegroundColor Yellow
  }
}

# Step 6: Final Validation
Write-Host "`n🔍 Final System Validation" -ForegroundColor Magenta
Write-Host "=========================" -ForegroundColor Magenta

Write-Host "Validating complete configuration..." -ForegroundColor Cyan
docker-compose -f docker-compose.yml -f docker-compose.dev.yml config --quiet
$finalResult = $LASTEXITCODE

if (Test-CommandSuccess $finalResult "Complete Configuration") {
  Write-Host "`n🎉 Container build completed successfully!" -ForegroundColor Green
  Write-Host "You can now run: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up" -ForegroundColor Green
}
else {
  Write-Host "`n❌ Configuration validation failed. Check docker-compose files for errors." -ForegroundColor Red
}

# Show next steps
Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Review any failed services above" -ForegroundColor White
Write-Host "2. To start core services: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up redis docker-proxy ollama-chromadb" -ForegroundColor White
Write-Host "3. To start all services: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up" -ForegroundColor White
Write-Host "4. To start with watch mode: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch" -ForegroundColor White
