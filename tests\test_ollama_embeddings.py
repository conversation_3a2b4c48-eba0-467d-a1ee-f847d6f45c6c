#!/usr/bin/env python3
"""
Test script for Ollama embedding functionality.
This script demonstrates the RAG workflow using Ollama embeddings as described in the documentation.
"""

import asyncio
import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.config import settings
from src.services.vector_service import VectorStorageService as VectorService


async def test_ollama_embeddings():
    """Test Ollama embedding generation and basic RAG workflow."""

    print("🔍 Testing Ollama Embedding Setup")
    print("=" * 50)

    # Initialize vector service
    vector_service = VectorService()

    try:
        # Test 1: Basic embedding generation
        print("\n📝 Test 1: Generating embeddings for sample text")
        test_text = "Llamas are members of the camelid family"
        print(f"Input text: '{test_text}'")

        embedding = await vector_service._generate_ollama_embedding(test_text)
        print("✅ Embedding generated successfully!")
        print(f"   - Dimensions: {len(embedding)}")
        print(f"   - Sample values: {embedding[:5]}...")

        # Test 2: Multiple embeddings for RAG workflow
        print("\n📚 Test 2: RAG Workflow - Document Processing")
        documents = [
            "Llamas are members of the camelid family meaning they're pretty closely related to vicuñas and camels",
            "Llamas were first domesticated and used as pack animals 4,000 to 5,000 years ago in the Peruvian highlands",
            "Llamas can grow as much as 6 feet tall though the average llama between 5 feet 6 inches and 5 feet 9 inches tall",
            "Llamas weigh between 280 and 450 pounds and can carry 25 to 30 percent of their body weight",
            "Llamas are vegetarians and have very efficient digestive systems",
            "Llamas live to be about 20 years old, though some only live for 15 years and others live to be 30 years old",
        ]

        print(f"Processing {len(documents)} documents...")
        embeddings = []
        for i, doc in enumerate(documents):
            emb = await vector_service._generate_ollama_embedding(doc)
            embeddings.append(emb)
            print(f"   - Document {i+1}: {len(emb)} dimensions")

        # Test 3: Similarity search simulation
        print("\n🔍 Test 3: Similarity Search Simulation")
        query = "What animals are llamas related to?"
        print(f"Query: '{query}'")

        query_embedding = await vector_service._generate_ollama_embedding(query)

        # Simple cosine similarity (in production, use pgvector)
        similarities = []
        for i, doc_emb in enumerate(embeddings):
            # Calculate dot product
            dot_product = sum(a * b for a, b in zip(query_embedding, doc_emb))
            # Calculate magnitudes
            query_mag = sum(x**2 for x in query_embedding)**0.5
            doc_mag = sum(x**2 for x in doc_emb)**0.5
            # Calculate cosine similarity
            similarity = dot_product / (query_mag * doc_mag) if query_mag * doc_mag > 0 else 0
            similarities.append((i, similarity))

        # Sort by similarity
        similarities.sort(key=lambda x: x[1], reverse=True)
        best_match_idx, best_similarity = similarities[0]

        print(f"   - Best match: '{documents[best_match_idx][:100]}...'")
        print(f"   - Similarity score: {best_similarity:.3f}")

        print("\n🎉 All tests passed! Ollama embeddings are working correctly.")
        print("\n📋 Configuration Summary:")
        print(f"   - Provider: {settings.EMBEDDING_PROVIDER}")
        print(f"   - Model: {settings.OLLAMA_EMBEDDING_MODEL}")
        print(f"   - Dimensions: {settings.EMBEDDING_DIMENSION}")
        print(f"   - Ollama URL: {settings.OLLAMA_BASE_URL}")

    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

    return True


if __name__ == "__main__":
    success = asyncio.run(test_ollama_embeddings())
    sys.exit(0 if success else 1)
