# syntax=docker/dockerfile:1
# Optimized Nginx Dockerfile for reverse proxy and load balancing
# Uses official nginx image with security best practices and performance optimizations

FROM nginx:1.25-alpine

# Labels for better container management
LABEL org.opencontainers.image.title="AI Coding Agent - Nginx Reverse Proxy" \
  org.opencontainers.image.description="Optimized Nginx reverse proxy and load balancer" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team" \
  security.non-root="true" \
  security.user="nginx" \
  version="1.0.0"

# Install security updates and required packages in single layer
# Combine RUN commands to reduce layers and improve caching
RUN apk update && apk upgrade && \
  apk add --no-cache \
  curl \
  tzdata && \
  # Clean up package manager cache to reduce image size
  rm -rf /var/cache/apk/* && \
  # Create necessary directories with proper permissions
  mkdir -p /var/cache/nginx /var/log/nginx /etc/nginx/ssl /var/www/certbot && \
  # Set proper ownership (nginx user already exists in nginx:alpine)
  chown -R nginx:nginx /var/cache/nginx /var/log/nginx /etc/nginx/ssl /var/www/certbot && \
  # Create nginx pid file
  touch /var/run/nginx.pid && \
  chown nginx:nginx /var/run/nginx.pid && \
  # Set restrictive permissions on sensitive directories
  chmod 755 /var/cache/nginx /var/log/nginx && \
  chmod 700 /etc/nginx/ssl

# Copy nginx configuration with validation
COPY nginx.conf /etc/nginx/nginx.conf

# Validate nginx configuration syntax
RUN nginx -t -c /etc/nginx/nginx.conf

# SSL certificates and Let's Encrypt directories are mounted as volumes in production
# via docker-compose volumes:
# volumes:
#   - ./ssl:/etc/nginx/ssl:ro
#   - ./letsencrypt:/etc/letsencrypt:ro

# Switch to non-root user (nginx user already exists in nginx:alpine base image)
USER nginx

# Expose ports
EXPOSE 80 443

# Health check with proper nginx validation
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f -H "Host: localhost" http://localhost/health || exit 1

# Default environment variables for flexible configuration
ENV NGINX_WORKER_PROCESSES=auto \
  NGINX_WORKER_CONNECTIONS=4096 \
  NGINX_CLIENT_MAX_BODY_SIZE=64M \
  NGINX_KEEPALIVE_TIMEOUT=65

# Start nginx with daemon off for container compatibility
CMD ["nginx", "-g", "daemon off;"]
