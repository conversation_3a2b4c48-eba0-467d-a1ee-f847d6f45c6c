"""
WebSocket Connection Manager for AI Coding Agent Chat.

This module provides WebSocket connection management for real-time chat communication
between users and the Architect Agent, following the established architectural patterns.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Set, Any, Optional, Callable
from fastapi import WebSocket
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)


class MessageType(str, Enum):
    """WebSocket message types for chat communication."""
    USER_MESSAGE = "user_message"
    AGENT_RESPONSE = "agent_response"
    SYSTEM_MESSAGE = "system_message"
    PING = "ping"
    PONG = "pong"
    ERROR = "error"
    TYPING = "typing"
    TERMINAL_OUTPUT = "terminal_output"


class ChatMessage:
    """Represents a chat message in the system."""

    def __init__(
        self,
        message_id: str,
        user_id: str,
        content: str,
        message_type: MessageType,
        timestamp: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.message_id = message_id
        self.user_id = user_id
        self.content = content
        self.message_type = message_type
        # Fix for line 44 error: Ensure timestamp is always a datetime object
        self.timestamp: datetime = timestamp if timestamp is not None else datetime.utcnow()
        self.metadata: Dict[str, Any] = metadata if metadata is not None else {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for JSON serialization."""
        return {
            "message_id": self.message_id,
            "user_id": self.user_id,
            "content": self.content,
            "type": self.message_type.value,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata
        }


class WebSocketConnection:
    """Represents a WebSocket connection with user context."""

    def __init__(self, websocket: WebSocket, user_id: str, connection_id: str):
        self.websocket = websocket
        self.user_id = user_id
        self.connection_id = connection_id
        self.connected_at = datetime.utcnow()
        self.last_ping = datetime.utcnow()
        self.is_active = True

    async def send_message(self, message: Dict[str, Any]) -> bool:
        """
        Send message to WebSocket connection.

        Args:
            message: Message dictionary to send

        Returns:
            bool: True if message sent successfully, False otherwise
        """
        try:
            await self.websocket.send_text(json.dumps(message))
            return True
        except Exception as e:
            logger.error(f"Failed to send message to {self.connection_id}: {e}")
            self.is_active = False
            return False

    async def ping(self) -> bool:
        """Send ping to check connection health."""
        ping_message = {
            "type": MessageType.PING.value,
            "timestamp": datetime.utcnow().isoformat()
        }
        success = await self.send_message(ping_message)
        if success:
            self.last_ping = datetime.utcnow()
        return success


class WebSocketChatManager:
    """
    Manages WebSocket connections for chat functionality.

    Provides connection management, message routing, and user session tracking
    for real-time chat communication with the Architect Agent.
    """

    def __init__(self):
        """Initialize the WebSocket chat manager."""
        # Active connections by connection ID
        self.connections: Dict[str, WebSocketConnection] = {}

        # User to connections mapping (users can have multiple connections)
        self.user_connections: Dict[str, Set[str]] = {}

        # Chat message history (in-memory for MVP, could be moved to Redis/DB)
        self.message_history: List[ChatMessage] = []

        # Message handlers
        self.message_handlers: Dict[MessageType, List[Callable]] = {
            MessageType.USER_MESSAGE: [],
            MessageType.PING: [],
        }

        # Background tasks
        self._background_tasks: Set[asyncio.Task] = set()
        self._health_check_running = False

    async def connect(self, websocket: WebSocket, user_id: Optional[str] = None) -> str:
        """
        Accept a new WebSocket connection.

        Args:
            websocket: WebSocket instance
            user_id: Optional user identifier

        Returns:
            str: Generated connection ID
        """
        await websocket.accept()

        connection_id = str(uuid.uuid4())
        # Fix for line 136 error: Ensure user_id is always a string
        effective_user_id: str = user_id if user_id is not None else f"anonymous_{connection_id[:8]}"

        # Create connection object
        connection = WebSocketConnection(websocket, effective_user_id, connection_id)

        # Store connection
        self.connections[connection_id] = connection

        # Map user to connection
        if effective_user_id not in self.user_connections:
            self.user_connections[effective_user_id] = set()
        self.user_connections[effective_user_id].add(connection_id)

        logger.info(f"WebSocket connected: {connection_id} (user: {effective_user_id})")

        # Send welcome message
        welcome_message = {
            "type": MessageType.SYSTEM_MESSAGE.value,
            "content": "Connected to AI Coding Agent. Type your message to start chatting!",
            "timestamp": datetime.utcnow().isoformat(),
            "connection_id": connection_id
        }
        await connection.send_message(welcome_message)

        # Start health check if not running
        if not self._health_check_running:
            asyncio.create_task(self._start_health_check())

        return connection_id

    async def disconnect(self, connection_id: str) -> None:
        """
        Handle WebSocket disconnection.

        Args:
            connection_id: ID of the connection to disconnect
        """
        if connection_id not in self.connections:
            return

        connection = self.connections[connection_id]
        user_id = connection.user_id

        # Remove from connections
        del self.connections[connection_id]

        # Remove from user connections
        if user_id in self.user_connections:
            self.user_connections[user_id].discard(connection_id)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]

        logger.info(f"WebSocket disconnected: {connection_id} (user: {user_id})")

    async def send_to_user(self, user_id: str, message: Dict[str, Any]) -> int:
        """
        Send message to all connections for a specific user.

        Args:
            user_id: Target user ID
            message: Message to send

        Returns:
            int: Number of successful sends
        """
        if user_id not in self.user_connections:
            return 0

        successful_sends = 0
        failed_connections = []

        for connection_id in self.user_connections[user_id].copy():
            connection = self.connections.get(connection_id)
            if connection and connection.is_active:
                success = await connection.send_message(message)
                if success:
                    successful_sends += 1
                else:
                    failed_connections.append(connection_id)
            else:
                failed_connections.append(connection_id)

        # Clean up failed connections
        for connection_id in failed_connections:
            await self.disconnect(connection_id)

        return successful_sends

    async def send_to_connection(self, connection_id: str, message: Dict[str, Any]) -> bool:
        """
        Send message to specific connection.

        Args:
            connection_id: Target connection ID
            message: Message to send

        Returns:
            bool: True if sent successfully
        """
        connection = self.connections.get(connection_id)
        if connection and connection.is_active:
            return await connection.send_message(message)
        return False

    async def broadcast_to_all(self, message: Dict[str, Any]) -> int:
        """
        Broadcast message to all active connections.

        Args:
            message: Message to broadcast

        Returns:
            int: Number of successful sends
        """
        successful_sends = 0
        failed_connections = []

        for connection_id, connection in self.connections.items():
            if connection.is_active:
                success = await connection.send_message(message)
                if success:
                    successful_sends += 1
                else:
                    failed_connections.append(connection_id)
            else:
                failed_connections.append(connection_id)

        # Clean up failed connections
        for connection_id in failed_connections:
            await self.disconnect(connection_id)

        return successful_sends

    async def handle_message(self, connection_id: str, raw_message: str) -> None:
        """
        Handle incoming WebSocket message.

        Args:
            connection_id: Source connection ID
            raw_message: Raw message string
        """
        connection = self.connections.get(connection_id)
        if not connection:
            logger.warning(f"Message from unknown connection: {connection_id}")
            return

        try:
            message_data = json.loads(raw_message)
            message_type = MessageType(message_data.get("type", MessageType.USER_MESSAGE))

            # Create chat message object
            chat_message = ChatMessage(
                message_id=str(uuid.uuid4()),
                user_id=connection.user_id,
                content=message_data.get("content", ""),
                message_type=message_type,
                metadata=message_data.get("metadata", {})
            )

            # Store in history
            self.message_history.append(chat_message)

            # Handle specific message types
            if message_type == MessageType.PING:
                await self._handle_ping(connection_id)
            elif message_type == MessageType.USER_MESSAGE:
                await self._handle_user_message(connection_id, chat_message)

            # Notify registered handlers
            for handler in self.message_handlers.get(message_type, []):
                try:
                    await handler(connection_id, chat_message)
                except Exception as e:
                    logger.error(f"Message handler error: {e}")

        except json.JSONDecodeError:
            logger.error(f"Invalid JSON message from {connection_id}: {raw_message}")
            await self._send_error(connection_id, "Invalid message format")
        except ValueError as e:
            logger.error(f"Invalid message type from {connection_id}: {e}")
            await self._send_error(connection_id, "Invalid message type")
        except Exception as e:
            logger.error(f"Error handling message from {connection_id}: {e}")
            await self._send_error(connection_id, "Internal error processing message")

    async def _handle_ping(self, connection_id: str) -> None:
        """Handle ping message with pong response."""
        pong_message = {
            "type": MessageType.PONG.value,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_to_connection(connection_id, pong_message)

    async def _handle_user_message(self, connection_id: str, chat_message: ChatMessage) -> None:
        """Handle user message - this will be connected to ArchitectAgent."""
        # For now, send typing indicator
        typing_message = {
            "type": MessageType.TYPING.value,
            "content": "Architect Agent is thinking...",
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_to_connection(connection_id, typing_message)

        # This will be replaced with actual ArchitectAgent integration
        logger.info(f"User message from {chat_message.user_id}: {chat_message.content}")

    async def _send_error(self, connection_id: str, error_message: str) -> None:
        """Send error message to connection."""
        error_msg = {
            "type": MessageType.ERROR.value,
            "content": error_message,
            "timestamp": datetime.utcnow().isoformat()
        }
        await self.send_to_connection(connection_id, error_msg)

    def register_message_handler(self, message_type: MessageType, handler: Callable) -> None:
        """Register a message handler for specific message types."""
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []
        self.message_handlers[message_type].append(handler)

    async def _start_health_check(self) -> None:
        """Start background health check for connections."""
        if self._health_check_running:
            return

        self._health_check_running = True
        task = asyncio.create_task(self._health_check_loop())
        self._background_tasks.add(task)
        task.add_done_callback(self._background_tasks.discard)

    async def _health_check_loop(self) -> None:
        """Background loop to check connection health."""
        while self._health_check_running and self.connections:
            try:
                current_time = datetime.utcnow()
                stale_connections = []

                # Check for stale connections (no ping for 60 seconds)
                for connection_id, connection in self.connections.items():
                    time_since_ping = (current_time - connection.last_ping).total_seconds()
                    if time_since_ping > 60:
                        stale_connections.append(connection_id)

                # Disconnect stale connections
                for connection_id in stale_connections:
                    logger.info(f"Disconnecting stale connection: {connection_id}")
                    await self.disconnect(connection_id)

                # Send ping to all active connections
                for connection in self.connections.values():
                    if connection.is_active:
                        await connection.ping()

                # Wait 30 seconds before next health check
                await asyncio.sleep(30)

            except Exception as e:
                logger.error(f"Health check error: {e}")
                await asyncio.sleep(30)  # Continue checking even after error

        self._health_check_running = False

    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        return {
            "total_connections": len(self.connections),
            "connected_users": len(self.user_connections),
            "message_history_count": len(self.message_history),
            "active_connections": sum(1 for conn in self.connections.values() if conn.is_active),
            "health_check_running": self._health_check_running,
            "connection_details": [
                {
                    "connection_id": conn_id,
                    "user_id": connection.user_id,
                    "connected_at": connection.connected_at.isoformat(),
                    "is_active": connection.is_active,
                    "last_ping": connection.last_ping.isoformat()
                }
                for conn_id, connection in self.connections.items()
            ]
        }

    async def cleanup(self) -> None:
        """Cleanup resources and stop background tasks."""
        self._health_check_running = False

        # Cancel all background tasks
        for task in self._background_tasks:
            if not task.done():
                task.cancel()

        # Wait for tasks to complete
        if self._background_tasks:
            await asyncio.gather(*self._background_tasks, return_exceptions=True)

        # Clear connections
        self.connections.clear()
        self.user_connections.clear()

        logger.info("WebSocketChatManager cleanup completed")


# Global instance for singleton pattern
chat_manager = WebSocketChatManager()


async def get_chat_manager() -> WebSocketChatManager:
    """Get the global chat manager instance.

    This function provides dependency injection for FastAPI endpoints
    and ensures a singleton pattern for the WebSocket chat manager.

    Returns:
        WebSocketChatManager: The global chat manager instance
    """
    return chat_manager
