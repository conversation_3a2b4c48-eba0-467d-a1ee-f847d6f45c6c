// Utility functions for Project Importer Extension

import * as vscode from 'vscode';
import { FileData } from './api';

/**
 * Extracts the project name from a Git repository URL
 * @param url - The Git repository URL
 * @returns The project name or null if invalid
 */
export function extractProjectName(url: string): string | null {
    try {
        // Remove .git suffix if present
        const cleanUrl = url.replace(/\.git$/, '');

        // Handle different Git URL formats
        const patterns = [
            /github\.com\/[^\/]+\/([^\/]+)$/,
            /gitlab\.com\/[^\/]+\/([^\/]+)$/,
            /bitbucket\.org\/[^\/]+\/([^\/]+)$/,
            /git@.*:.*\/([^\/]+)\.git$/,
            /https?:\/\/.*\/.*\/([^\/]+)$/
        ];

        for (const pattern of patterns) {
            const match = cleanUrl.match(pattern);
            if (match && match[1]) {
                return match[1];
            }
        }

        // Fallback: extract from path
        const urlParts = cleanUrl.split('/');
        const lastPart = urlParts[urlParts.length - 1];
        if (lastPart && lastPart.trim()) {
            return lastPart;
        }

        return null;
    } catch (error) {
        console.error('Error extracting project name:', error);
        return null;
    }
}

/**
 * Validates if a string is a valid Git repository URL
 * @param url - The URL to validate
 * @returns True if valid, false otherwise
 */
export function isValidGitUrl(url: string): boolean {
    const gitUrlPattern = /^(https?|git|ssh):\/\/[^\s\/$.?#].[^\s]*$/i;
    const sshPattern = /^git@.*:.*\.git$/;

    return gitUrlPattern.test(url) || sshPattern.test(url);
}

/**
 * Reads the contents of files from the given URIs
 * @param fileUris - Array of VS Code URIs to read
 * @returns Promise resolving to array of FileData objects
 */
export async function readFilesFromUris(fileUris: vscode.Uri[]): Promise<FileData[]> {
    const files: FileData[] = [];

    for (const uri of fileUris) {
        try {
            const stat = await vscode.workspace.fs.stat(uri);
            if (stat.type === vscode.FileType.File) {
                const content = await vscode.workspace.fs.readFile(uri);
                const fileName = vscode.workspace.asRelativePath(uri);

                files.push({
                    name: fileName,
                    path: uri.fsPath,
                    content: content.toString(),
                    size: stat.size
                });
            }
        } catch (error) {
            console.error(`Error reading file ${uri.fsPath}:`, error);
            // Continue with other files
        }
    }

    return files;
}
