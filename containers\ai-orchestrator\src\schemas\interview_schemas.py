# Project: AI Coding Agent
# Purpose: Pydantic schemas for conversation and roadmap models

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class InterviewStatus(str, Enum):
    """Status of an architect interview session."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"


class RoadmapItemType(str, Enum):
    """Types of roadmap items."""
    PHASE = "phase"
    STEP = "step"
    TASK = "task"


class RoadmapItemStatus(str, Enum):
    """Status values for roadmap items."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    BLOCKED = "blocked"


class EffortLevel(str, Enum):
    """Estimated effort levels for roadmap items."""
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"


# Conversation History Schemas

class ConversationHistoryBase(BaseModel):
    """Base schema for conversation history."""
    project_id: int = Field(..., description="ID of the project")
    user_id: str = Field(..., description="ID of the user")
    session_id: str = Field(..., description="Unique interview session identifier")
    question_key: str = Field(..., description="Key identifier for the question")
    question_text: str = Field(..., description="The question text presented to user")
    user_response: Optional[str] = Field(None, description="User's response to the question")
    sequence_order: int = Field(..., description="Order in the interview sequence")
    is_followup: bool = Field(False, description="Whether this is a follow-up question")


class ConversationHistoryCreate(BaseModel):
    """Schema for creating conversation history records."""
    project_id: int
    user_id: str
    session_id: str
    question_key: str
    question_text: str
    user_response: Optional[str] = None
    sequence_order: int
    is_followup: bool = False


class ConversationHistoryUpdate(BaseModel):
    """Schema for updating conversation history records."""
    user_response: str = Field(..., description="Updated user response")


class ConversationHistoryResponse(ConversationHistoryBase):
    """Schema for conversation history API responses."""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        """Pydantic configuration."""
        from_attributes = True


class ConversationSessionSummary(BaseModel):
    """Summary of a conversation session."""
    session_id: str
    total_questions: int
    answered_questions: int
    responses: Dict[str, Dict[str, Any]]


# Roadmap Item Schemas

class RoadmapItemBase(BaseModel):
    """Base schema for roadmap items."""
    project_id: int = Field(..., description="ID of the project")
    parent_id: Optional[int] = Field(None, description="Parent roadmap item ID")
    level: int = Field(..., description="Hierarchy level (1=Phase, 2=Step, 3=Task)")
    sequence_order: int = Field(..., description="Order within the same level")
    title: str = Field(..., description="Title of the roadmap item")
    description: Optional[str] = Field(None, description="Description of the roadmap item")
    item_type: RoadmapItemType = Field(..., description="Type of roadmap item")
    status: RoadmapItemStatus = Field(RoadmapItemStatus.PENDING, description="Current status")
    agent_role: Optional[str] = Field(None, description="Agent role for task items")
    estimated_effort: Optional[EffortLevel] = Field(None, description="Estimated effort level")
    dependencies: List[int] = Field(default_factory=list, description="List of dependent item IDs")


class RoadmapItemCreate(BaseModel):
    """Schema for creating roadmap items."""
    project_id: int
    parent_id: Optional[int] = None
    level: int
    sequence_order: int
    title: str
    description: Optional[str] = None
    item_type: RoadmapItemType
    agent_role: Optional[str] = None
    estimated_effort: Optional[EffortLevel] = None
    dependencies: List[int] = Field(default_factory=list)


class RoadmapItemUpdate(BaseModel):
    """Schema for updating roadmap items."""
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[RoadmapItemStatus] = None
    agent_role: Optional[str] = None
    estimated_effort: Optional[EffortLevel] = None
    dependencies: Optional[List[int]] = None


class RoadmapItemResponse(RoadmapItemBase):
    """Schema for roadmap item API responses."""
    id: int
    created_at: datetime

    class Config:
        """Pydantic configuration."""
        from_attributes = True


# Hierarchical Roadmap Schemas

class RoadmapTaskSchema(BaseModel):
    """Schema for task items within roadmap structure."""
    title: str
    description: str
    agent_role: str
    estimated_effort: EffortLevel


class RoadmapStepSchema(BaseModel):
    """Schema for step items within roadmap structure."""
    title: str
    description: str
    tasks: List[RoadmapTaskSchema]


class RoadmapPhaseSchema(BaseModel):
    """Schema for phase items within roadmap structure."""
    title: str
    description: str
    steps: List[RoadmapStepSchema]


class RoadmapStructureSchema(BaseModel):
    """Schema for complete roadmap structure."""
    phases: List[RoadmapPhaseSchema]


class RoadmapGenerationRequest(BaseModel):
    """Request schema for roadmap generation."""
    project_id: int
    conversation_session_id: str


class RoadmapGenerationResponse(BaseModel):
    """Response schema for roadmap generation."""
    success: bool
    roadmap_id: Optional[str] = None
    message: str
    created_items: int = 0
    phases_count: int = 0
    steps_count: int = 0
    tasks_count: int = 0


# Interview Session Schemas

class InterviewQuestionSchema(BaseModel):
    """Schema for interview questions."""
    key: str
    question: str
    options: Optional[List[str]] = None
    required: bool = True
    followup_trigger: bool = False


class InterviewResponseSchema(BaseModel):
    """Schema for user responses during interview."""
    question_key: str
    response: str
    session_id: str


class InterviewStateResponse(BaseModel):
    """Schema for current interview state."""
    session_id: str
    status: InterviewStatus
    current_question: Optional[InterviewQuestionSchema] = None
    progress: Dict[str, Any]
    completed_questions: int
    total_questions: int


# Progress and Analytics Schemas

class RoadmapProgressResponse(BaseModel):
    """Schema for roadmap progress statistics."""
    total_items: int
    completed_items: int
    in_progress_items: int
    pending_items: int
    completion_percentage: float
    phases: Dict[str, int]
    steps: Dict[str, int]
    tasks: Dict[str, int]


class ProjectRoadmapSummary(BaseModel):
    """Summary of a project's roadmap."""
    project_id: int
    has_roadmap: bool
    total_phases: int
    total_steps: int
    total_tasks: int
    completion_percentage: float
    interview_status: InterviewStatus
    last_updated: Optional[datetime] = None


# WebSocket Message Schemas

class WebSocketInterviewMessage(BaseModel):
    """Schema for WebSocket interview messages."""
    type: str  # 'start_interview', 'answer', 'get_status'
    session_id: Optional[str] = None
    question_key: Optional[str] = None
    response: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class WebSocketInterviewResponse(BaseModel):
    """Schema for WebSocket interview responses."""
    type: str  # 'question', 'status', 'complete', 'error'
    session_id: str
    data: Dict[str, Any]
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


# Validation

class RoadmapValidationError(BaseModel):
    """Schema for roadmap validation errors."""
    field: str
    message: str
    item_id: Optional[int] = None


class RoadmapValidationResponse(BaseModel):
    """Schema for roadmap validation results."""
    is_valid: bool
    errors: List[RoadmapValidationError] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
