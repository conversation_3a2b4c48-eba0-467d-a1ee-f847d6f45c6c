#!/usr/bin/env python3
"""
Database Schema Verification Script

This script verifies that the consolidated schema file (000_consolidated_initial_schema.sql)
contains all the necessary components from the individual migration files (001-006).
"""

import re
from pathlib import Path
from typing import Dict, List

def read_sql_file(file_path: str) -> str:
    """Read SQL file content."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

def extract_schema_components(sql_content: str) -> Dict[str, List[str]]:
    """Extract schema components from SQL content."""
    components = {
        'tables': [],
        'indexes': [],
        'functions': [],
        'policies': [],
        'triggers': [],
        'grants': [],
        'comments': []
    }

    # Split by major sections
    sections = re.split(r'-- =+[\s\S]*?=+\s*', sql_content)

    for section in sections:
        section = section.strip()
        if not section:
            continue

        # Extract CREATE TABLE statements
        table_matches = re.findall(r'CREATE TABLE (?:IF NOT EXISTS )?([\w\._]+)', section, re.IGNORECASE)
        components['tables'].extend(table_matches)

        # Extract CREATE INDEX statements
        index_matches = re.findall(r'CREATE INDEX (?:IF NOT EXISTS )?([\w\._]+)', section, re.IGNORECASE)
        components['indexes'].extend(index_matches)

        # Extract CREATE FUNCTION statements
        function_matches = re.findall(r'CREATE (?:OR REPLACE )?FUNCTION ([\w\._]+)', section, re.IGNORECASE)
        components['functions'].extend(function_matches)

        # Extract CREATE POLICY statements
        policy_matches = re.findall(r'CREATE POLICY ([\w\._]+)', section, re.IGNORECASE)
        components['policies'].extend(policy_matches)

        # Extract CREATE TRIGGER statements
        trigger_matches = re.findall(r'CREATE TRIGGER ([\w\._]+)', section, re.IGNORECASE)
        components['triggers'].extend(trigger_matches)

        # Extract GRANT statements - normalize by removing parameter details
        grant_matches = re.findall(r'GRANT (?:[^;]*?ON (?:FUNCTION|TABLE|SCHEMA|ALL SEQUENCES)[^;]*);', section, re.IGNORECASE)
        # Normalize grants by removing parameter details for comparison
        normalized_grants = []
        for grant in grant_matches:
            # Remove parameter details from function grants for comparison
            normalized_grant = re.sub(r'\([^)]*\)', '', grant)
            normalized_grants.append(normalized_grant.strip())
        components['grants'].extend(normalized_grants)

        # Extract COMMENT statements - normalize by removing parameter details
        comment_matches = re.findall(r'COMMENT (?:ON [^;]*);', section, re.IGNORECASE)
        # Normalize comments by removing parameter details for comparison
        normalized_comments = []
        for comment in comment_matches:
            # Remove parameter details from function comments for comparison
            normalized_comment = re.sub(r'\([^)]*\)', '', comment)
            normalized_comments.append(normalized_comment.strip())
        components['comments'].extend(normalized_comments)

    return components

def compare_components(consolidated: Dict[str, List[str]], migrations: Dict[str, List[str]]) -> Dict[str, Dict[str, List[str]]]:
    """Compare components between consolidated and individual migrations."""
    comparison = {}

    for component_type in consolidated.keys():
        consolidated_set = set(consolidated[component_type])
        migrations_set = set(migrations[component_type])

        # For grants and comments, we need to handle function signature differences
        if component_type in ['grants', 'comments']:
            # Create a mapping of function names to their entries
            consolidated_funcs = {}
            for item in consolidated[component_type]:
                func_name = re.search(r'FUNCTION (\w+)', item)
                if func_name:
                    consolidated_funcs[func_name.group(1)] = item

            migrations_funcs = {}
            for item in migrations[component_type]:
                func_name = re.search(r'FUNCTION (\w+)', item)
                if func_name:
                    migrations_funcs[func_name.group(1)] = item

            # Compare based on function names rather than exact strings
            missing = []
            extra = []
            common = []

            for func_name, migration_item in migrations_funcs.items():
                if func_name not in consolidated_funcs:
                    missing.append(migration_item)
                else:
                    common.append(migration_item)

            for func_name, consolidated_item in consolidated_funcs.items():
                if func_name not in migrations_funcs:
                    extra.append(consolidated_item)

            comparison[component_type] = {
                'missing_in_consolidated': missing,
                'extra_in_consolidated': extra,
                'common': common
            }
        else:
            # For other components, use exact string comparison
            missing_in_consolidated = list(migrations_set - consolidated_set)
            extra_in_consolidated = list(consolidated_set - migrations_set)
            common = list(consolidated_set & migrations_set)

            comparison[component_type] = {
                'missing_in_consolidated': missing_in_consolidated,
                'extra_in_consolidated': extra_in_consolidated,
                'common': common
            }

    return comparison

def main():
    """Main verification function."""
    print(" Starting Database Schema Verification")
    print("=" * 50)

    # Paths
    supabase_dir = Path('supabase/migrations')
    consolidated_file = supabase_dir / '000_consolidated_initial_schema.sql'
    migration_files = [
        supabase_dir / '001_initial_schema.sql',
        supabase_dir / '002_vector_functions.sql',
        supabase_dir / '003_secure_rls_policies.sql',
        supabase_dir / '004_incremental_scanning.sql',
        supabase_dir / '005_hybrid_roadmap_schema.sql',
        supabase_dir / '006_explicit_user_context.sql'
    ]

    # Verify files exist
    if not consolidated_file.exists():
        print(f" Consolidated schema file not found: {consolidated_file}")
        return False

    missing_migrations = [f for f in migration_files if not f.exists()]
    if missing_migrations:
        print(f" Missing migration files: {missing_migrations}")
        return False

    print(f" Found consolidated file: {consolidated_file}")
    print(f" Found all {len(migration_files)} migration files")

    # Read and parse consolidated schema
    print("\n Reading consolidated schema...")
    consolidated_content = read_sql_file(str(consolidated_file))
    consolidated_components = extract_schema_components(consolidated_content)

    # Read and parse all migration files
    print(" Reading individual migration files...")
    migration_components = {
        'tables': [],
        'indexes': [],
        'functions': [],
        'policies': [],
        'triggers': [],
        'grants': [],
        'comments': []
    }

    for migration_file in migration_files:
        content = read_sql_file(str(migration_file))
        components = extract_schema_components(content)
        for key in migration_components:
            migration_components[key].extend(components[key])

    # Compare components
    print(" Comparing components...")
    comparison = compare_components(consolidated_components, migration_components)

    # Debug: Print component counts for verification
    print("\n Component Counts:")
    for component_type in consolidated_components:
        cons_count = len(consolidated_components[component_type])
        mig_count = len(migration_components[component_type])
        print(f"   {component_type}: consolidated={cons_count}, migrations={mig_count}")

    # Print results and determine overall status
    verification_passed = True
    has_warnings = False

    print("\n" + "="*60)
    print("DATABASE SCHEMA VERIFICATION RESULTS")
    print("="*60)

    for component_type, results in comparison.items():
        print(f"\n{component_type.upper():<15}")
        print("-" * 30)

        missing = results['missing_in_consolidated']
        extra = results['extra_in_consolidated']
        common = results['common']

        if missing:
            print(f" Missing in consolidated: {len(missing)}")
            for item in sorted(missing):
                print(f"   - {item}")
            verification_passed = False  # Only missing components cause failure
        else:
            print(" No missing components")

        if extra:
            print(f"  Extra in consolidated: {len(extra)}")
            for item in sorted(extra):
                print(f"   - {item}")
            has_warnings = True  # Extra components are warnings, not failures
        else:
            print(" No extra components")

        print(f" Common components: {len(common)}")

    # Summary with clear logic
    print("\n" + "="*60)
    print("VERIFICATION SUMMARY")
    print("="*60)

    if verification_passed:
        print(" VERIFICATION PASSED")
        print("    All components from individual migrations are present in consolidated schema")
        if has_warnings:
            print("     Note: Some extra components found in consolidated schema (treated as warnings)")
        print("\n   Status: Ready for Two-Tiered RAG Service implementation")
        return True
    else:
        print(" VERIFICATION FAILED")
        print("    Some components from individual migrations are missing in consolidated schema")
        print("    Consolidated schema is incomplete")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
