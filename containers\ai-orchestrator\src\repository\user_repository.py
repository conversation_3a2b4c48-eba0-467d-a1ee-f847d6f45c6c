# Project: AI Coding Agent
# Purpose: User repository implementing data access patterns
# Author: AI Coding Agent Team

"""
User Repository for AI Coding Agent.

This module provides the essential data access layer that bridges Supabase authentication
with our custom user management system. It implements the Repository pattern to ensure
clean separation between business logic and data persistence.

Key Components:
- UserRepository: Main repository class with CRUD operations
- Supabase integration for user synchronization
- Project relationship management
- FastAPI dependency injection support
"""

import json
import logging
from typing import Optional, List
from datetime import datetime
from uuid import UUID

# SQLAlchemy imports
from sqlalchemy import select, insert, delete, and_, or_, func
from sqlalchemy.orm import Session, selectinload
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from src.models.project import Project
from src.models.user import UserProfile
from src.models.association import user_project_association
from src.schemas.user_schemas import (
    SupabaseUser,
    UserProfileUpdateSchema,
    UserResponse,
    UserListResponse,
    UserCreateResponse
)

# FastAPI imports
from fastapi import Depends

# Configure logging
logger = logging.getLogger(__name__)


class UserRepositoryError(Exception):
    """Base exception for user repository operations."""
    pass


class UserNotFoundError(UserRepositoryError):
    """Exception raised when a user is not found."""
    pass


class UserAlreadyExistsError(UserRepositoryError):
    """Exception raised when trying to create a user that already exists."""
    pass


class UserRepository:
    """
    User repository implementing the Repository pattern for data access.

    This class provides a clean interface between the application logic and the
    database layer, specifically designed to work with FastAPI's dependency
    injection system and handle Supabase Auth integration.

    Features:
    - Supabase Auth synchronization
    - CRUD operations with comprehensive error handling
    - Project relationship management
    - Pagination and filtering support
    - Transaction management
    """

    def __init__(self):
        """
        Initialize the UserRepository.

        The repository is designed to be stateless and receive database
        sessions through dependency injection.
        """
        logger.info("UserRepository initialized")

    # ==================================================================================
    # CORE SUPABASE INTEGRATION
    # ==================================================================================

    def get_or_create_user_from_supabase(
        self,
        db: Session,
        supabase_user: SupabaseUser
    ) -> UserCreateResponse:
        """
        Get or create user from Supabase Auth data.

        This is the most critical method that bridges Supabase authentication
        with our custom user management system. It ensures every authenticated
        user has a corresponding record in our users table.

        Args:
            db: Database session
            supabase_user: Supabase user data from auth

        Returns:
            UserCreateResponse: Created or found user with metadata

        Raises:
            UserRepositoryError: If operation fails
            SQLAlchemyError: If database operation fails
        """
        try:
            logger.info(f"Getting or creating user for Supabase ID: {supabase_user.id}")

            # First, try to find existing user by supabase_user_id
            existing_user = db.query(UserProfile).filter(
                UserProfile.supabase_user_id == supabase_user.id
            ).first()

            if existing_user:
                logger.info(f"Found existing user: {existing_user.username}")

                # Update last login timestamp
                existing_user.last_login = datetime.utcnow()
                db.commit()
                db.refresh(existing_user)

                return UserCreateResponse(
                    user=UserResponse.from_user_model(existing_user),
                    created_from_supabase=False,
                    sync_status="found_existing",
                    metadata={
                        "supabase_user_id": str(supabase_user.id),
                        "last_login_updated": True,
                        "operation": "get_existing"
                    }
                )

            # User doesn't exist, create a new one
            logger.info(f"Creating new user for email: {supabase_user.email}")

            # Check for username conflicts and generate unique username
            base_username = supabase_user.username
            username = self._ensure_unique_username(db, base_username)

            # Check for email conflicts (shouldn't happen with Supabase, but be safe)
            if self._email_exists(db, supabase_user.email):
                raise UserAlreadyExistsError(
                    f"User with email {supabase_user.email} already exists"
                )

            # Create new user instance
            new_user = UserProfile.create_from_supabase_user(
                supabase_user_id=supabase_user.id,
                email=supabase_user.email,
                username=username,
                full_name=supabase_user.full_name,
                is_active=True,
                last_login=datetime.utcnow()
            )

            # Add additional metadata from Supabase if available
            profile_metadata = {
                "supabase_created_at": supabase_user.created_at.isoformat(),
                "supabase_metadata": supabase_user.user_metadata,
                "email_confirmed": supabase_user.email_confirmed_at is not None,
                "sync_source": "supabase_auth"
            }

            new_user.profile_data = json.dumps(profile_metadata)

            # Save to database
            db.add(new_user)
            db.commit()
            db.refresh(new_user)

            logger.info(f"Successfully created user: {new_user.username} (ID: {new_user.id})")

            return UserCreateResponse(
                user=UserResponse.from_user_model(new_user),
                created_from_supabase=True,
                sync_status="created_new",
                metadata={
                    "supabase_user_id": str(supabase_user.id),
                    "username_adjusted": username != base_username,
                    "original_username": base_username,
                    "operation": "create_new"
                }
            )

        except IntegrityError as e:
            db.rollback()
            logger.error(f"Integrity error creating user: {str(e)}")
            raise UserRepositoryError(f"User creation failed due to data constraint: {str(e)}")

        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Database error in get_or_create_user_from_supabase: {str(e)}")
            raise UserRepositoryError(f"Database operation failed: {str(e)}")

        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error in get_or_create_user_from_supabase: {str(e)}")
            raise UserRepositoryError(f"Unexpected error: {str(e)}")

    # ==================================================================================
    # STANDARD CRUD OPERATIONS
    # ==================================================================================

    def get_by_id(
        self,
        db: Session,
        user_id: int
    ) -> Optional[UserProfile]:
        """
        Retrieve user by internal primary key.

        Args:
            db: Database session
            user_id: Internal user ID

        Returns:
            User model instance or None if not found
        """
        try:
            user = db.query(UserProfile).filter(UserProfile.id == user_id).first()
            if user:
                logger.debug(f"Found user by ID {user_id}: {user.username}")
            else:
                logger.debug(f"No user found with ID {user_id}")
            return user

        except SQLAlchemyError as e:
            logger.error(f"Database error in get_by_id: {str(e)}")
            raise UserRepositoryError(f"Failed to retrieve user by ID: {str(e)}")

    def get_by_supabase_id(
        self,
        db: Session,
        supabase_user_id: UUID
    ) -> Optional[UserProfile]:
        """
        Retrieve user by Supabase user ID.

        Args:
            db: Database session
            supabase_user_id: Supabase user UUID

        Returns:
            User model instance or None if not found
        """
        try:
            user = db.query(UserProfile).filter(UserProfile.supabase_user_id == supabase_user_id).first()
            if user:
                logger.debug(f"Found user by Supabase ID {supabase_user_id}: {user.username}")
            return user

        except SQLAlchemyError as e:
            logger.error(f"Database error in get_by_supabase_id: {str(e)}")
            raise UserRepositoryError(f"Failed to retrieve user by Supabase ID: {str(e)}")

    def get_by_email(
        self,
        db: Session,
        email: str
    ) -> Optional[UserProfile]:
        """
        Retrieve user by email address.

        Args:
            db: Database session
            email: User email address

        Returns:
            User model instance or None if not found
        """
        try:
            user = db.query(UserProfile).filter(UserProfile.email == email).first()
            if user:
                logger.debug(f"Found user by email {email}: {user.username}")
            return user

        except SQLAlchemyError as e:
            logger.error(f"Database error in get_by_email: {str(e)}")
            raise UserRepositoryError(f"Failed to retrieve user by email: {str(e)}")

    def get_by_username(
        self,
        db: Session,
        username: str
    ) -> Optional[UserProfile]:
        """
        Retrieve user by username.

        Args:
            db: Database session
            username: Username to search for

        Returns:
            User model instance or None if not found
        """
        try:
            user = db.query(UserProfile).filter(UserProfile.username == username).first()
            if user:
                logger.debug(f"Found user by username: {username}")
            return user

        except SQLAlchemyError as e:
            logger.error(f"Database error in get_by_username: {str(e)}")
            raise UserRepositoryError(f"Failed to retrieve user by username: {str(e)}")

    def update_profile(
        self,
        db: Session,
        user: UserProfile,
        profile_data: UserProfileUpdateSchema
    ) -> UserProfile:
        """
        Update user profile information.

        Args:
            db: Database session
            user: User model instance to update
            profile_data: Profile update data

        Returns:
            Updated User model instance

        Raises:
            UserRepositoryError: If update fails
            UserAlreadyExistsError: If username/email conflicts occur
        """
        try:
            logger.info(f"Updating profile for user: {user.username}")

            # Track what fields are being updated
            updated_fields = []

            # Update username if provided and different
            if profile_data.username and profile_data.username != user.username:
                if self._username_exists_excluding_user(db, profile_data.username, user.id):
                    raise UserAlreadyExistsError(f"Username '{profile_data.username}' already exists")
                user.username = profile_data.username
                updated_fields.append("username")

            # Update full name if provided
            if profile_data.full_name is not None:
                user.full_name = profile_data.full_name if profile_data.full_name.strip() else None
                updated_fields.append("full_name")

            # Update active status if provided (admin only)
            if profile_data.is_active is not None:
                user.is_active = profile_data.is_active
                updated_fields.append("is_active")

            # Update last login if provided
            if profile_data.last_login is not None:
                user.last_login = profile_data.last_login
                updated_fields.append("last_login")

            # Handle profile data updates
            if profile_data.profile_data is not None or profile_data.preferences is not None:
                current_profile = {}
                if user.profile_data:
                    try:
                        current_profile = json.loads(user.profile_data)
                    except (json.JSONDecodeError, TypeError):
                        current_profile = {}

                # Merge additional profile data
                if profile_data.profile_data:
                    current_profile.update(profile_data.profile_data)
                    updated_fields.append("profile_data")

                # Merge preferences
                if profile_data.preferences:
                    current_profile["preferences"] = profile_data.preferences
                    updated_fields.append("preferences")

                # Add update timestamp
                current_profile["last_profile_update"] = datetime.utcnow().isoformat()

                user.profile_data = json.dumps(current_profile)

            # Update the updated_at timestamp
            user.updated_at = datetime.utcnow()

            # Commit changes
            db.commit()
            db.refresh(user)

            logger.info(f"Successfully updated user {user.username}. Fields: {', '.join(updated_fields)}")
            return user

        except IntegrityError as e:
            db.rollback()
            logger.error(f"Integrity error updating user profile: {str(e)}")
            raise UserRepositoryError(f"Profile update failed due to data constraint: {str(e)}")

        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Database error in update_profile: {str(e)}")
            raise UserRepositoryError(f"Profile update failed: {str(e)}")

    def get_users_paginated(
        self,
        db: Session,
        page: int = 1,
        page_size: int = 20,
        search: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> UserListResponse:
        """
        Get paginated list of users with optional filtering.

        Args:
            db: Database session
            page: Page number (1-based)
            page_size: Number of users per page
            search: Optional search term for username/email/full_name
            is_active: Optional filter by active status

        Returns:
            UserListResponse with paginated results
        """
        try:
            # Build base query
            query = db.query(UserProfile)

            # Apply filters
            if search:
                search_term = f"%{search.lower()}%"
                query = query.filter(
                    or_(
                        func.lower(UserProfile.username).like(search_term),
                        func.lower(UserProfile.email).like(search_term),
                        func.lower(User.full_name).like(search_term)
                    )
                )

            if is_active is not None:
                query = query.filter(User.is_active == is_active)

            # Get total count
            total = query.count()

            # Apply pagination
            offset = (page - 1) * page_size
            users = query.offset(offset).limit(page_size).all()

            logger.debug(f"Retrieved {len(users)} users (page {page}/{page_size}, total: {total})")

            return UserListResponse.create_paginated_response(
                users=users,
                total=total,
                page=page,
                page_size=page_size
            )

        except SQLAlchemyError as e:
            logger.error(f"Database error in get_users_paginated: {str(e)}")
            raise UserRepositoryError(f"Failed to retrieve users: {str(e)}")

    # ==================================================================================
    # PROJECT RELATIONSHIP MANAGEMENT
    # ==================================================================================

    def get_user_projects(
        self,
        db: Session,
        user_id: int
    ) -> List[Project]:
        """
        Get all projects for a specific user.

        Args:
            db: Database session
            user_id: User ID to get projects for

        Returns:
            List[Project]: List of projects associated with the user
        """
        try:
            # Use selectinload to eagerly load projects
            stmt = select(User).options(
                selectinload(User.projects)
            ).where(User.id == user_id)

            result = db.execute(stmt)
            user = result.scalar_one_or_none()

            if not user:
                return []

            return user.projects
        except Exception:
            raise

    def add_user_to_project(
        self,
        db: Session,
        user_id: int,
        project_id: int,
        role: str = "member"
    ) -> bool:
        """
        Add a user to a project with specified role.

        Args:
            db: Database session
            user_id: User ID
            project_id: Project ID
            role: User role in project (default: "member")

        Returns:
            bool: True if successful, False if relationship already exists
        """
        try:
            # Check if relationship already exists
            stmt = select(user_project_association).where(
                and_(
                    user_project_association.c.user_id == user_id,
                    user_project_association.c.project_id == project_id
                )
            )
            result = db.execute(stmt)
            if result.fetchone():
                return False  # Relationship already exists

            # Insert new relationship
            stmt = insert(user_project_association).values(
                user_id=user_id,
                project_id=project_id,
                role=role,
                created_at=datetime.utcnow()
            )
            db.execute(stmt)
            db.commit()
            return True
        except Exception:
            db.rollback()
            raise

    def remove_user_from_project(
        self,
        db: Session,
        user_id: int,
        project_id: int
    ) -> bool:
        """
        Remove a user from a project.

        Args:
            db: Database session
            user_id: User ID
            project_id: Project ID

        Returns:
            bool: True if successful, False if relationship doesn't exist
        """
        try:
            stmt = delete(user_project_association).where(
                and_(
                    user_project_association.c.user_id == user_id,
                    user_project_association.c.project_id == project_id
                )
            )
            result = db.execute(stmt)
            db.commit()
            return result.rowcount > 0
        except Exception:
            db.rollback()
            raise

    # ==================================================================================
    # UTILITY METHODS
    # ==================================================================================

    def _ensure_unique_username(self, db: Session, base_username: str) -> str:
        """
        Ensure username is unique by appending numbers if necessary.

        Args:
            db: Database session
            base_username: Base username to make unique

        Returns:
            str: Unique username
        """
        username = base_username
        counter = 1

        while self._username_exists(db, username):
            username = f"{base_username}{counter}"
            counter += 1

            # Prevent infinite loops
            if counter > 1000:
                username = f"{base_username}_{datetime.utcnow().timestamp()}"
                break

        return username

    def _username_exists(self, db: Session, username: str) -> bool:
        """Check if username already exists."""
        return db.query(User).filter(User.username == username).first() is not None

    def _username_exists_excluding_user(self, db: Session, username: str, user_id: int) -> bool:
        """Check if username exists excluding a specific user."""
        return db.query(User).filter(
            and_(User.username == username, User.id != user_id)
        ).first() is not None

    def _email_exists(self, db: Session, email: str) -> bool:
        """Check if email already exists."""
        return db.query(User).filter(User.email == email).first() is not None


# ==================================================================================
# DEPENDENCY INJECTION HELPERS
# ==================================================================================

def get_user_repository() -> UserRepository:
    """
    FastAPI dependency for UserRepository.

    Returns:
        UserRepository: Repository instance
    """
    return UserRepository()


# Type alias for dependency injection
UserRepositoryDep = Depends(get_user_repository)