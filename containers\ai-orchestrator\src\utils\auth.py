"""
Authentication utilities for AI Orchestrator.

Provides secure authentication with Supabase integration and JWT fallback,
following the project's security-first approach with proper error handling.
"""

from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Any, Dict, Union
from datetime import datetime, timedelta, timezone
import logging
import uuid

# Optional dependencies with graceful fallback
try:
    from passlib.context import CryptContext
    PASSLIB_AVAILABLE = True
except ImportError:
    PASSLIB_AVAILABLE = False
    CryptContext = None  # type: ignore

# Import centralized configuration
try:
    from src.core.config import settings
    # Import secrets utility for secure configuration
    from src.utils.secrets import read_secret
except ImportError:
    # Fallback to absolute imports for direct module execution
    from src.core.config import settings
    from utils.secrets import read_secret

try:
    from supabase import create_client, Client  # type: ignore[import-untyped]
    SUPABASE_AVAILABLE = True
    ClientType = Client
except ImportError:
    SUPABASE_AVAILABLE = False
    ClientType = None  # type: ignore
    def create_client(*args, **kwargs):
        raise NotImplementedError("Supabase not available")

from jose import JWTError, jwt  # type: ignore[import-untyped]
from pydantic import BaseModel

logger = logging.getLogger(__name__)

# Export SECRET_KEY for backward compatibility with tests
SECRET_KEY = settings.JWT_SECRET
ALGORITHM = getattr(settings, 'JWT_ALGORITHM', 'HS256')

class Token(BaseModel):
    """JWT Token response model."""
    access_token: str
    token_type: str

# Initialize Supabase client with secrets support
SUPABASE_URL = read_secret("supabase_url", "SUPABASE_URL")
SUPABASE_KEY = read_secret("supabase_key", "SUPABASE_ANON_KEY")
SUPABASE_SERVICE_KEY = read_secret("supabase_service_key", "SUPABASE_SERVICE_KEY")

supabase: Optional[Any] = None

def init_supabase():
    """Initialize Supabase client with proper error handling and fallback support."""
    global supabase

    # Check if Supabase is enabled in configuration
    if not settings.USE_SUPABASE:
        logger.info("Supabase integration disabled - using local JWT fallback")
        return

    if not SUPABASE_AVAILABLE:
        logger.warning("Supabase client not available - using local JWT fallback")
        return

    if not SUPABASE_URL or not SUPABASE_KEY:
        logger.warning(
            "Supabase configuration incomplete. Using local JWT fallback. "
            "Set SUPABASE_URL and SUPABASE_ANON_KEY environment variables for full functionality."
        )
        return

    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        logger.info("Supabase client initialized successfully")
    except Exception as e:
        logger.warning(f"Failed to initialize Supabase client: {e}. Using local JWT fallback.")
        supabase = None

# Initialize at module level for proper startup
def _initialize_auth():
    """Initialize authentication components at module startup."""
    init_supabase()

# Call initialization when module is imported
try:
    _initialize_auth()
except Exception as e:
    logger.warning(f"Auth initialization failed: {e}")

# The insecure local fallback user management system has been removed.
# Authentication is now handled by Supabase or standard JWT validation.

def get_supabase() -> Optional[Any]:
    """Get Supabase client instance with proper validation and fallback support."""
    # Check if Supabase is enabled in configuration
    if not settings.USE_SUPABASE:
        logger.debug("Supabase integration disabled, using local fallback")
        return None

    if not SUPABASE_AVAILABLE:
        logger.debug("Supabase not available, using local fallback")
        return None

    if supabase is None:
        init_supabase()

    return supabase  # Can be None, which triggers fallback


def is_supabase_available() -> bool:
    """Check if Supabase is available and properly configured."""
    return SUPABASE_AVAILABLE and bool(SUPABASE_URL and SUPABASE_KEY) and supabase is not None


# JWT configuration using centralized settings
security = HTTPBearer()


def validate_token_payload(payload: Dict[str, Any]) -> Union[Dict[str, str], None]:
    """
    Validate and extract standardized user information from JWT payload.

    Args:
        payload: JWT payload dictionary

    Returns:
        Union[Dict[str, str], None]: Standardized user data or None if invalid
    """
    if not isinstance(payload, dict):
        return None

    # Extract user identifier (try multiple standard claims)
    user_id = payload.get("sub") or payload.get("user_id") or payload.get("id")
    if not user_id:
        return None

    # Build standardized user data dictionary
    user_data: Dict[str, str] = {
        "user_id": str(user_id),
        "email": payload.get("email", ""),
        "username": payload.get("username", payload.get("preferred_username", "")),
        "role": payload.get("role", payload.get("user_role", "user"))
    }

    return user_data


def merge_auth_contexts(
    supabase_context: Optional[Dict[str, Any]],
    jwt_context: Optional[Dict[str, Any]]
) -> Dict[str, Union[str, bool, None]]:
    """
    Merge authentication contexts from different sources with priority handling.

    Args:
        supabase_context: Authentication data from Supabase
        jwt_context: Authentication data from JWT payload

    Returns:
        Dict[str, Union[str, bool, None]]: Merged authentication context
    """
    merged_context: Dict[str, Union[str, bool, None]] = {
        "user_id": None,
        "email": None,
        "username": None,
        "is_supabase_auth": False,
        "auth_source": "unknown"
    }

    # Supabase takes priority if available
    if supabase_context:
        merged_context.update({
            "user_id": supabase_context.get("id"),
            "email": supabase_context.get("email"),
            "username": supabase_context.get("username"),
            "is_supabase_auth": True,
            "auth_source": "supabase"
        })
    elif jwt_context:
        merged_context.update({
            "user_id": jwt_context.get("user_id"),
            "email": jwt_context.get("email"),
            "username": jwt_context.get("username"),
            "is_supabase_auth": False,
            "auth_source": "jwt"
        })

    return merged_context


class TokenData:
    """Token data container for authentication information."""

    def __init__(self, username: Optional[str] = None, user_id: Optional[str] = None,
                 email: Optional[str] = None, role: str = "user", is_local: bool = False):
        self.username = username
        self.user_id = user_id
        self.email = email
        self.role = role
        self.is_local = is_local  # Flag to indicate local vs Supabase auth

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenData:
    """
    Verify JWT token, trying Supabase first and falling back to python-jose.

    Args:
        credentials: HTTP Bearer token credentials.

    Returns:
        TokenData: User information from the token.

    Raises:
        HTTPException: If the token is invalid or verification fails.
    """
    token = credentials.credentials

    # 1. Try Supabase authentication first
    if is_supabase_available():
        try:
            supabase_client = get_supabase()
            user = supabase_client.auth.get_user(token)
            if user and user.user:
                # Extract role from user metadata if available
                role = user.user.user_metadata.get("role", "user")
                return TokenData(
                    username=user.user.email,
                    user_id=user.user.id,
                    email=user.user.email,
                    role=role,
                    is_local=False
                )
        except Exception as e:
            logger.warning(f"Supabase token verification failed: {e}. Trying standard JWT verification.")

    # 2. Fallback to standard JWT verification using python-jose
    try:
        # Ensure JWT_SECRET is available for token verification
        jwt_secret = settings.JWT_SECRET
        if not jwt_secret:
            raise JWTError("JWT_SECRET not configured")

        payload = jwt.decode(
            token,
            jwt_secret,
            algorithms=[settings.JWT_ALGORITHM]
        )

        user_id = payload.get("sub") or payload.get("user_id")
        if not user_id:
            raise JWTError("Token is missing a 'sub' or 'user_id' claim.")

        # If the token was not validated by Supabase, we can still create
        # a representation of the user from the token claims.
        return TokenData(
            username=payload.get("email"),
            user_id=user_id,
            email=payload.get("email"),
            role=payload.get("role", "user"),
            is_local=True  # Indicates this token was not validated by Supabase
        )
    except JWTError as e:
        logger.error(f"JWT verification failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"An unexpected error occurred during token verification: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token using the python-jose library.

    Args:
        data: The data to encode in the token.
        expires_delta: Optional expiration time delta.

    Returns:
        The encoded JWT token.

    Raises:
        HTTPException: If token creation fails.
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({
        "exp": int(expire.timestamp()),
        "iat": int(datetime.now(timezone.utc).timestamp()),
        "jti": str(uuid.uuid4())  # Add a unique token identifier
    })

    try:
        # Ensure JWT_SECRET is available for token creation
        jwt_secret = settings.JWT_SECRET
        if not jwt_secret:
            logger.error("JWT_SECRET not configured for token creation")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="JWT configuration error."
            )

        encoded_jwt = jwt.encode(to_encode, jwt_secret, algorithm=settings.JWT_ALGORITHM)
        return encoded_jwt
    except Exception as e:
        logger.error(f"Failed to create access token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not create access token."
        )

# The insecure local fallback user management functions have been removed.


# =====================================================================================
# MAIN AUTHENTICATION FUNCTIONS
# =====================================================================================

async def get_current_user(token_data: TokenData = Depends(verify_token)) -> TokenData:
    """
    Get current authenticated user with fallback support.

    Args:
        token_data: Verified token data

    Returns:
        TokenData: Current user information
    """
    return token_data


async def get_current_admin_user(current_user: TokenData = Depends(get_current_user)) -> TokenData:
    """
    Dependency to ensure the current user is an admin.

    Raises HTTP 403 if the user does not have the admin role. This is a small,
    explicit helper used by routers that require admin privileges.
    """
    if not current_user or getattr(current_user, 'role', None) != "admin":
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin privileges required")
    return current_user


# Password hashing context using passlib with bcrypt (optional)
pwd_context = None
if PASSLIB_AVAILABLE and CryptContext:
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def hash_password(password: str) -> str:
    """
    Hash password for secure storage using bcrypt.

    Args:
        password: Plain text password

    Returns:
        str: Hashed password

    Raises:
        NotImplementedError: If passlib is not available
    """
    if not PASSLIB_AVAILABLE or not pwd_context:
        raise NotImplementedError("Password hashing requires passlib to be installed")
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify password against a hashed password.

    Args:
        plain_password: Plain text password to verify
        hashed_password: Stored hashed password

    Returns:
        bool: True if password matches

    Raises:
        NotImplementedError: If passlib is not available
    """
    if not PASSLIB_AVAILABLE or not pwd_context:
        raise NotImplementedError("Password verification requires passlib to be installed")
    return pwd_context.verify(plain_password, hashed_password)




def get_auth_status() -> dict:
    """
    Get comprehensive authentication service status for health checks.

    Returns:
        dict: Authentication status information
    """
    return {
        "supabase_available": SUPABASE_AVAILABLE,
        "jwt_library": "python-jose",
        "supabase_configured": bool(SUPABASE_URL and SUPABASE_KEY),
        "supabase_initialized": supabase is not None,
        "supabase_ready": is_supabase_available(),
        "auth_ready": is_supabase_available() or settings.JWT_SECRET is not None,
        "jwt_algorithm": settings.JWT_ALGORITHM,
        "token_expire_minutes": settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
    }

def create_service_account_token(service_name: str, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT for service-to-service communication.

    Args:
        service_name: The name of the service account.
        expires_delta: Optional expiration time delta.

    Returns:
        The encoded JWT token.
    """
    to_encode = {
        "sub": service_name,
        "is_service_account": True,
    }
    return create_access_token(to_encode, expires_delta)
