# Supabase Google OAuth Setup Script for Windows
# Run this script from the project root directory

Write-Host "=== AI Coding Agent - Supabase Google OAuth Setup ===" -ForegroundColor Green
Write-Host ""

# Function to check if command exists
function Test-Command($command) {
  try {
    if (Get-Command $command -ErrorAction SilentlyContinue) {
      return $true
    }
  }
  catch {
    return $false
  }
  return $false
}

# Check prerequisites
Write-Host "1. Checking prerequisites..." -ForegroundColor Yellow
if (-not (Test-Command "docker")) {
  Write-Host "[FAIL] Docker is not installed or not in PATH" -ForegroundColor Red
  exit 1
}

if (-not (Test-Command "docker-compose")) {
  Write-Host "[FAIL] Docker Compose is not installed or not in PATH" -ForegroundColor Red
  exit 1
}

Write-Host "[OK] Docker and Docker Compose are available" -ForegroundColor Green
Write-Host ""

# Check if environment file exists
Write-Host "2. Checking environment configuration..." -ForegroundColor Yellow
if (-not (Test-Path "containers\user-portal\.env.local")) {
  Write-Host "[WARN] .env.local file not found" -ForegroundColor Yellow
  Write-Host "       Creating from template..."

  if (Test-Path "containers\user-portal\.env.example") {
    Copy-Item "containers\user-portal\.env.example" "containers\user-portal\.env.local"
    Write-Host "[OK] Environment template copied to .env.local" -ForegroundColor Green
    Write-Host "[ACTION REQUIRED] Please edit containers\user-portal\.env.local and add your credentials" -ForegroundColor Yellow
  }
  else {
    Write-Host "[FAIL] .env.example template not found" -ForegroundColor Red
    exit 1
  }
}
else {
  Write-Host "[OK] Environment file exists" -ForegroundColor Green
}
Write-Host ""

# Rebuild containers with new dependencies
Write-Host "3. Rebuilding containers with new dependencies..." -ForegroundColor Yellow
Write-Host "   This may take a few minutes..."

& docker-compose -f docker-compose.yml -f docker-compose.dev.yml down --remove-orphans
if ($LASTEXITCODE -ne 0) {
  Write-Host "[FAIL] Failed to stop containers" -ForegroundColor Red
  exit 1
}

# Build with no cache to ensure dependencies are installed
& docker-compose -f docker-compose.yml -f docker-compose.dev.yml build --no-cache user-portal
if ($LASTEXITCODE -ne 0) {
  Write-Host "[FAIL] Failed to build user-portal container" -ForegroundColor Red
  exit 1
}

Write-Host "[OK] Container rebuild completed" -ForegroundColor Green
Write-Host ""

# Start containers
Write-Host "4. Starting containers..." -ForegroundColor Yellow
& docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
if ($LASTEXITCODE -ne 0) {
  Write-Host "[FAIL] Failed to start containers" -ForegroundColor Red
  exit 1
}

Write-Host "[OK] Containers started" -ForegroundColor Green
Write-Host ""

# Wait for services to be ready
Write-Host "5. Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check container health
Write-Host "6. Checking container status..." -ForegroundColor Yellow
& docker-compose -f docker-compose.yml -f docker-compose.dev.yml ps

Write-Host ""
Write-Host "=== Setup Complete! ===" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Configure Supabase and Google OAuth (see docs\SUPABASE_GOOGLE_OAUTH_SETUP.md)"
Write-Host "2. Edit containers\user-portal\.env.local with your credentials"
Write-Host "3. Restart containers: docker-compose down && docker-compose up -d"
Write-Host "4. Test login at: http://portal.localhost/login"
Write-Host ""
Write-Host "For detailed implementation guide, see: docs\SUPABASE_GOOGLE_OAUTH_IMPLEMENTATION.md" -ForegroundColor Cyan
