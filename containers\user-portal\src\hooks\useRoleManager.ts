/**
 * useRoleManager Hook
 * Custom hook for managing role configurations with enhanced state management
 */

import { useCallback, useEffect, useState } from "react";
import { roleApi } from "@/lib/api";
import { LLMProvider } from "../types/role";
import type {
  ApiError,
  RoleConfiguration,
  RoleConfigurationUpdate,
} from "../types/role";
import { LoadingState } from "../types/api";

interface UseRoleManagerReturn {
  // State
  roles: Record<string, RoleConfiguration>;
  activeRole: string;
  loading: Record<string, LoadingState>;
  errors: Record<string, ApiError | null>;
  showCreateForm: boolean;
  showDeleteModal: string | null;
  newRoleName: string;

  // Actions
  setActiveRole: (roleName: string) => void;
  refreshRoles: () => Promise<void>;
  createRole: (roleName: string) => Promise<boolean>;
  updateRole: (
    roleName: string,
    updates: RoleConfigurationUpdate,
  ) => Promise<boolean>;
  deleteRole: (roleName: string) => Promise<boolean>;
  saveRole: (roleName: string) => Promise<boolean>;
  clearError: (roleName: string) => void;

  // UI Actions
  openCreateForm: () => void;
  closeCreateForm: () => void;
  openDeleteModal: (roleName: string) => void;
  closeDeleteModal: () => void;
  setNewRoleName: (name: string) => void;

  // Utilities
  getRoleNames: () => string[];
  getRoleByName: (roleName: string) => RoleConfiguration | null;
  isRoleEnabled: (roleName: string) => boolean;
  getRoleDisplayName: (roleName: string) => string;
  canDeleteRole: (roleName: string) => boolean;
}

/**
 * Custom hook for role management with enhanced state management
 */
export const useRoleManager = (): UseRoleManagerReturn => {
  const [roles, setRoles] = useState<Record<string, RoleConfiguration>>({});
  const [activeRole, setActiveRole] = useState<string>("");
  const [loading, setLoading] = useState<Record<string, LoadingState>>({});
  const [errors, setErrors] = useState<Record<string, ApiError | null>>({});
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState<string | null>(null);
  const [newRoleName, setNewRoleName] = useState("");

  /**
   * Load all roles from the API
   */
  const refreshRoles = useCallback(async (): Promise<void> => {
    setLoading((prev) => ({ ...prev, global: LoadingState.LOADING }));
    setErrors((prev) => ({ ...prev, global: null }));

    try {
      const response = await roleApi.getRoles();

      if (response.error) {
        setLoading((prev) => ({ ...prev, global: LoadingState.ERROR }));
        setErrors((prev) => ({ ...prev, global: response.error! }));
        return;
      }

      const roleData = response.data?.roles || {};
      setRoles(roleData);

      // Set active role to first role if none is active
      if (!activeRole && Object.keys(roleData).length > 0) {
        setActiveRole(Object.keys(roleData)[0]);
      }

      setLoading((prev) => ({ ...prev, global: LoadingState.SUCCESS }));
    } catch (error) {
      setLoading((prev) => ({ ...prev, global: LoadingState.ERROR }));
      setErrors((prev) => ({
        ...prev,
        global: {
          message: "Failed to fetch roles",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
    }
  }, [activeRole]);

  /**
   * Create a new role
   */
  const createRole = useCallback(async (roleName: string): Promise<boolean> => {
    if (!roleName.trim()) return false;

    const normalizedName = roleName.trim().toLowerCase().replace(/\s+/g, "_");

    if (roles[normalizedName]) {
      setErrors((prev) => ({
        ...prev,
        create: { message: "Role name already exists" },
      }));
      return false;
    }

    setLoading((prev) => ({ ...prev, create: LoadingState.LOADING }));

    try {
      const newRole: Omit<RoleConfiguration, "created_at" | "updated_at"> = {
        provider: LLMProvider.OPENROUTER,
        available_models: ["anthropic/claude-3-sonnet"],
        selected_model: "anthropic/claude-3-sonnet",
        api_key: "",
        cost_limit: 50.0,
        max_tokens: 4096,
        temperature: 0.7,
        enabled: true,
      };

      const response = await roleApi.createRole(normalizedName, newRole);

      if (response.error) {
        setErrors((prev) => ({ ...prev, create: response.error! }));
        return false;
      }

      // Add the new role to local state
      if (response.data?.configuration) {
        setRoles((prev) => ({
          ...prev,
          [normalizedName]: response.data!.configuration!,
        }));
        setActiveRole(normalizedName);
        setNewRoleName("");
        setShowCreateForm(false);
        setErrors((prev) => ({ ...prev, create: null }));
      }

      return true;
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        create: {
          message: "Failed to create role",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
      return false;
    } finally {
      setLoading((prev) => ({ ...prev, create: LoadingState.IDLE }));
    }
  }, [roles]);

  /**
   * Update an existing role
   */
  const updateRole = useCallback(async (
    roleName: string,
    updates: RoleConfigurationUpdate,
  ): Promise<boolean> => {
    try {
      const response = await roleApi.updateRole(roleName, updates);

      if (response.error) {
        setErrors((prev) => ({ ...prev, [roleName]: response.error! }));
        return false;
      }

      // Update the role in local state
      if (response.data?.configuration) {
        setRoles((prev) => ({
          ...prev,
          [roleName]: {
            ...prev[roleName],
            ...response.data!.configuration!,
            updated_at: new Date().toISOString(),
          },
        }));
        setErrors((prev) => ({ ...prev, [roleName]: null }));
      }

      return true;
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        [roleName]: {
          message: "Failed to update role",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
      return false;
    }
  }, []);

  /**
   * Delete a role
   */
  const deleteRole = useCallback(async (roleName: string): Promise<boolean> => {
    if (Object.keys(roles).length <= 1) {
      setErrors((prev) => ({
        ...prev,
        delete: { message: "Cannot delete the last role" },
      }));
      return false;
    }

    setLoading((prev) => ({ ...prev, delete: LoadingState.LOADING }));

    try {
      const response = await roleApi.deleteRole(roleName);

      if (response.error) {
        setErrors((prev) => ({ ...prev, delete: response.error! }));
        return false;
      }

      // Remove from local state
      const newRoles = { ...roles };
      delete newRoles[roleName];
      setRoles(newRoles);

      // Switch to first remaining role if deleted role was active
      if (activeRole === roleName) {
        setActiveRole(Object.keys(newRoles)[0]);
      }

      setShowDeleteModal(null);
      setErrors((prev) => ({ ...prev, delete: null }));

      return true;
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        delete: {
          message: "Failed to delete role",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
      return false;
    } finally {
      setLoading((prev) => ({ ...prev, delete: LoadingState.IDLE }));
    }
  }, [roles, activeRole]);

  /**
   * Save role configuration
   */
  const saveRole = useCallback(async (roleName: string): Promise<boolean> => {
    setLoading((prev) => ({ ...prev, [roleName]: LoadingState.LOADING }));
    setErrors((prev) => ({ ...prev, [roleName]: null }));

    try {
      const response = await roleApi.updateRole(roleName, roles[roleName]);

      if (response.error) {
        setErrors((prev) => ({ ...prev, [roleName]: response.error! }));
        return false;
      }

      // Update the role's updated timestamp
      setRoles((prev) => ({
        ...prev,
        [roleName]: {
          ...prev[roleName],
          updated_at: new Date().toISOString(),
        },
      }));

      return true;
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        [roleName]: {
          message: "Failed to save configuration",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
      return false;
    } finally {
      setLoading((prev) => ({ ...prev, [roleName]: LoadingState.IDLE }));
    }
  }, [roles]);

  /**
   * Clear error for a specific role
   */
  const clearError = useCallback((roleName: string) => {
    setErrors((prev) => ({ ...prev, [roleName]: null }));
  }, []);

  /**
   * UI action handlers
   */
  const openCreateForm = useCallback(() => {
    setShowCreateForm(true);
    setNewRoleName("");
    setErrors((prev) => ({ ...prev, create: null }));
  }, []);

  const closeCreateForm = useCallback(() => {
    setShowCreateForm(false);
    setNewRoleName("");
    setErrors((prev) => ({ ...prev, create: null }));
  }, []);

  const openDeleteModal = useCallback((roleName: string) => {
    setShowDeleteModal(roleName);
    setErrors((prev) => ({ ...prev, delete: null }));
  }, []);

  const closeDeleteModal = useCallback(() => {
    setShowDeleteModal(null);
    setErrors((prev) => ({ ...prev, delete: null }));
  }, []);

  /**
   * Utility functions
   */
  const getRoleNames = useCallback(() => {
    return Object.keys(roles);
  }, [roles]);

  const getRoleByName = useCallback((roleName: string) => {
    return roles[roleName] || null;
  }, [roles]);

  const isRoleEnabled = useCallback((roleName: string) => {
    return roles[roleName]?.enabled ?? false;
  }, [roles]);

  const getRoleDisplayName = useCallback((roleName: string) => {
    return roleName
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }, []);

  const canDeleteRole = useCallback((_roleName: string) => {
    return Object.keys(roles).length > 1;
  }, [roles]);

  // Initial load
  useEffect(() => {
    refreshRoles();
  }, [refreshRoles]);

  return {
    roles,
    activeRole,
    loading,
    errors,
    showCreateForm,
    showDeleteModal,
    newRoleName,
    setActiveRole,
    refreshRoles,
    createRole,
    updateRole,
    deleteRole,
    saveRole,
    clearError,
    openCreateForm,
    closeCreateForm,
    openDeleteModal,
    closeDeleteModal,
    setNewRoleName,
    getRoleNames,
    getRoleByName,
    isRoleEnabled,
    getRoleDisplayName,
    canDeleteRole,
  };
};
