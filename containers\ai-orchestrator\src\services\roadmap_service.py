"""
Roadmap Service for AI Coding Agent.

This module provides business logic for managing versioned roadmaps,
including creating new versions, managing summaries, and handling source references.
"""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import select, and_, desc

from src.models.roadmap import Roadmap, RoadmapItem, RoadmapSummary, RoadmapSourceReference
from src.models.project import Project

logger = logging.getLogger(__name__)


class RoadmapService:
    """
    Service for managing versioned roadmaps with AI-generated summaries and source references.

    Provides functionality for:
    - Creating and versioning roadmaps
    - Managing roadmap summaries with embeddings
    - Tracking source document references
    - Multi-tenant data access control
    """

    def __init__(self, db: Session):
        """
        Initialize the RoadmapService.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db

    async def create_new_version(
        self,
        original_roadmap_id: UUID,
        user_id: UUID
    ) -> Roadmap:
        """
        Create a new versioned copy of an existing roadmap.

        Args:
            original_roadmap_id: UUID of the original roadmap to version
            user_id: UUID of the user creating the new version

        Returns:
            Roadmap: The newly created roadmap version

        Raises:
            ValueError: If original roadmap not found or access denied
        """
        # Fetch the original roadmap
        original_roadmap = self.db.query(Roadmap).filter(
            and_(
                Roadmap.id == original_roadmap_id,
                Roadmap.owner_id == user_id
            )
        ).first()

        if not original_roadmap:
            raise ValueError(f"Roadmap {original_roadmap_id} not found or access denied")

        # Create the new version
        new_version_number = original_roadmap.version + 1

        new_roadmap = Roadmap(
            project_id=original_roadmap.project_id,
            title=f"{original_roadmap.title} (v{new_version_number})",
            content=original_roadmap.content.copy(),  # Deep copy the content
            owner_id=user_id,
            version=new_version_number,
            parent_id=original_roadmap_id,
            status="draft"
        )

        self.db.add(new_roadmap)
        self.db.flush()  # Get the new roadmap ID

        # Deep copy associated roadmap items
        await self._copy_roadmap_items(original_roadmap_id, new_roadmap.id, user_id)

        # Deep copy summaries and source references
        await self._copy_summaries_and_references(original_roadmap_id, new_roadmap.id, user_id)

        self.db.commit()

        logger.info(f"Created new roadmap version {new_version_number} for roadmap {original_roadmap_id}")
        return new_roadmap

    async def _copy_roadmap_items(
        self,
        original_roadmap_id: UUID,
        new_roadmap_id: UUID,
        user_id: UUID
    ) -> None:
        """
        Deep copy all roadmap items from original to new roadmap.

        Args:
            original_roadmap_id: UUID of the original roadmap
            new_roadmap_id: UUID of the new roadmap
            user_id: UUID of the user performing the operation
        """
        # Get the project ID from the new roadmap
        new_roadmap = self.db.query(Roadmap).filter(Roadmap.id == new_roadmap_id).first()
        if not new_roadmap:
            return

        # Get all roadmap items for the original roadmap's project
        roadmap_items = self.db.query(RoadmapItem).filter(
            RoadmapItem.project_id == new_roadmap.project_id
        ).all()

        # Create a mapping of old IDs to new IDs for parent relationships
        id_mapping = {}

        # First pass: Create all items without parent relationships
        for item in roadmap_items:
            new_item = RoadmapItem(
                project_id=item.project_id,
                parent_id=None,  # Will set this in second pass
                level=item.level,
                sequence_order=item.sequence_order,
                title=item.title,
                description=item.description,
                item_type=item.item_type,
                status="pending",  # Reset status for new version
                agent_role=item.agent_role,
                estimated_effort=item.estimated_effort,
                dependencies=item.dependencies.copy() if item.dependencies else [],
                priority=item.priority
            )

            self.db.add(new_item)
            self.db.flush()
            id_mapping[item.id] = new_item.id

        # Second pass: Update parent relationships
        for old_item in roadmap_items:
            if old_item.parent_id and old_item.parent_id in id_mapping:
                new_item_id = id_mapping[old_item.id]
                new_item = self.db.query(RoadmapItem).filter(RoadmapItem.id == new_item_id).first()
                if new_item:
                    new_item.parent_id = id_mapping[old_item.parent_id]

        logger.info(f"Copied {len(roadmap_items)} roadmap items for new version {new_roadmap_id}")

    async def _copy_summaries_and_references(
        self,
        original_roadmap_id: UUID,
        new_roadmap_id: UUID,
        user_id: UUID
    ) -> None:
        """
        Deep copy summaries and source references from original to new roadmap.

        Args:
            original_roadmap_id: UUID of the original roadmap
            new_roadmap_id: UUID of the new roadmap
            user_id: UUID of the user performing the operation
        """
        # Copy roadmap summaries
        summaries = self.db.query(RoadmapSummary).filter(
            RoadmapSummary.roadmap_id == original_roadmap_id
        ).all()

        for summary in summaries:
            new_summary = RoadmapSummary(
                roadmap_id=new_roadmap_id,
                phase_title=summary.phase_title,
                summary_text=summary.summary_text,
                embedding=summary.embedding,  # Copy the embedding vector
                owner_id=user_id
            )
            self.db.add(new_summary)

        # Copy source references
        references = self.db.query(RoadmapSourceReference).filter(
            RoadmapSourceReference.roadmap_id == original_roadmap_id
        ).all()

        for ref in references:
            new_ref = RoadmapSourceReference(
                roadmap_id=new_roadmap_id,
                source_document_id=ref.source_document_id,
                excerpt=ref.excerpt,
                retrieval_score=ref.retrieval_score,
                owner_id=user_id
            )
            self.db.add(new_ref)

        logger.info(f"Copied {len(summaries)} summaries and {len(references)} references for new version {new_roadmap_id}")

    async def get_roadmap_versions(
        self,
        project_id: UUID,
        user_id: UUID
    ) -> List[Roadmap]:
        """
        Get all versions of roadmaps for a project.

        Args:
            project_id: UUID of the project
            user_id: UUID of the user

        Returns:
            List[Roadmap]: All roadmap versions for the project
        """
        roadmaps = self.db.query(Roadmap).filter(
            and_(
                Roadmap.project_id == project_id,
                Roadmap.owner_id == user_id
            )
        ).order_by(desc(Roadmap.version)).all()

        return roadmaps

    async def get_roadmap_by_id(
        self,
        roadmap_id: UUID,
        user_id: UUID
    ) -> Optional[Roadmap]:
        """
        Get a specific roadmap by ID with access control.

        Args:
            roadmap_id: UUID of the roadmap
            user_id: UUID of the user

        Returns:
            Optional[Roadmap]: The roadmap if found and accessible
        """
        roadmap = self.db.query(Roadmap).filter(
            and_(
                Roadmap.id == roadmap_id,
                Roadmap.owner_id == user_id
            )
        ).first()

        return roadmap

    async def update_roadmap_status(
        self,
        roadmap_id: UUID,
        user_id: UUID,
        status: str
    ) -> bool:
        """
        Update the status of a roadmap.

        Args:
            roadmap_id: UUID of the roadmap
            user_id: UUID of the user
            status: New status ('draft', 'active', 'archived')

        Returns:
            bool: True if update was successful
        """
        # First verify the roadmap exists and belongs to the user
        stmt = select(Roadmap).where(
            and_(
                Roadmap.id == roadmap_id,
                Roadmap.owner_id == user_id
            )
        )
        roadmap = self.db.execute(stmt).scalar_one_or_none()

        if not roadmap:
            return False

        # Update the roadmap status
        roadmap.status = status
        roadmap.updated_at = datetime.utcnow()
        self.db.commit()
        return True

    async def add_roadmap_summary(
        self,
        roadmap_id: UUID,
        user_id: UUID,
        phase_title: str,
        summary_text: str,
        embedding: Optional[List[float]] = None
    ) -> RoadmapSummary:
        """
        Add an AI-generated summary to a roadmap.

        Args:
            roadmap_id: UUID of the roadmap
            user_id: UUID of the user
            phase_title: Title of the phase being summarized
            summary_text: AI-generated summary text
            embedding: Vector embedding for the summary

        Returns:
            RoadmapSummary: The created summary

        Raises:
            ValueError: If roadmap not found or access denied
        """
        # Verify roadmap ownership
        roadmap = await self.get_roadmap_by_id(roadmap_id, user_id)
        if not roadmap:
            raise ValueError(f"Roadmap {roadmap_id} not found or access denied")

        summary = RoadmapSummary(
            roadmap_id=roadmap_id,
            phase_title=phase_title,
            summary_text=summary_text,
            embedding=embedding,
            owner_id=user_id
        )

        self.db.add(summary)
        self.db.commit()

        logger.info(f"Added summary for roadmap {roadmap_id}, phase: {phase_title}")
        return summary

    async def add_source_reference(
        self,
        roadmap_id: UUID,
        user_id: UUID,
        source_document_id: UUID,
        excerpt: str,
        retrieval_score: Optional[float] = None
    ) -> RoadmapSourceReference:
        """
        Add a source document reference to a roadmap.

        Args:
            roadmap_id: UUID of the roadmap
            user_id: UUID of the user
            source_document_id: UUID of the source document
            excerpt: Text excerpt from the source
            retrieval_score: Retrieval score for the reference

        Returns:
            RoadmapSourceReference: The created reference

        Raises:
            ValueError: If roadmap not found or access denied
        """
        # Verify roadmap ownership
        roadmap = await self.get_roadmap_by_id(roadmap_id, user_id)
        if not roadmap:
            raise ValueError(f"Roadmap {roadmap_id} not found or access denied")

        reference = RoadmapSourceReference(
            roadmap_id=roadmap_id,
            source_document_id=source_document_id,
            excerpt=excerpt,
            retrieval_score=retrieval_score,
            owner_id=user_id
        )

        self.db.add(reference)
        self.db.commit()

        logger.info(f"Added source reference for roadmap {roadmap_id}, document: {source_document_id}")
        return reference

    async def get_roadmap_statistics(
        self,
        project_id: UUID,
        user_id: UUID
    ) -> Dict[str, Any]:
        """
        Get comprehensive statistics for roadmaps in a project.

        Args:
            project_id: UUID of the project
            user_id: UUID of the user

        Returns:
            Dict[str, Any]: Statistics including version count, summaries count, etc.
        """
        # Verify project access by checking if any roadmaps exist for this user
        project_roadmaps = await self.get_roadmap_versions(project_id, user_id)

        if not project_roadmaps:
            return {
                "project_id": str(project_id),
                "total_versions": 0,
                "active_versions": 0,
                "draft_versions": 0,
                "archived_versions": 0,
                "total_summaries": 0,
                "total_references": 0,
                "latest_version": None,
                "created_at": None,
                "last_updated": None
            }

        # Calculate statistics
        total_versions = len(project_roadmaps)
        active_versions = len([r for r in project_roadmaps if r.status == "active"])
        draft_versions = len([r for r in project_roadmaps if r.status == "draft"])
        archived_versions = len([r for r in project_roadmaps if r.status == "archived"])

        # Get latest version (first in desc order)
        latest_roadmap = project_roadmaps[0] if project_roadmaps else None

        # Count summaries across all versions
        total_summaries = sum(len(r.roadmap_summaries or []) for r in project_roadmaps)
        total_references = sum(len(r.roadmap_source_references or []) for r in project_roadmaps)

        return {
            "project_id": str(project_id),
            "total_versions": total_versions,
            "active_versions": active_versions,
            "draft_versions": draft_versions,
            "archived_versions": archived_versions,
            "total_summaries": total_summaries,
            "total_references": total_references,
            "latest_version": latest_roadmap.version if latest_roadmap else None,
            "created_at": latest_roadmap.created_at.isoformat() if latest_roadmap else None,
            "last_updated": latest_roadmap.updated_at.isoformat() if latest_roadmap else None
        }

    async def get_project_roadmap_overview(
        self,
        project_id: UUID,
        user_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """
        Get comprehensive overview of project with its roadmap information.

        Args:
            project_id: UUID of the project
            user_id: UUID of the user

        Returns:
            Optional[Dict[str, Any]]: Project and roadmap overview or None if not found
        """
        # Get project information
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            return None

        # Get roadmap statistics
        roadmap_stats = await self.get_roadmap_statistics(project_id, user_id)

        return {
            "project": {
                "id": str(project.id),
                "name": project.name,
                "description": project.description,
                "created_at": project.created_at.isoformat() if project.created_at else None
            },
            "roadmap_stats": roadmap_stats
        }
