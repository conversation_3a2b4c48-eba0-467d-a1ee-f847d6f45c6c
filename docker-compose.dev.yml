# =============================================================================
# Development Override Configuration
#
# Optimized for cache layering and hot-reload development.
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build --watch
#
# Key Features:
# - Proper Docker layer caching
# - Hot-reload without breaking dependency layers
# - Development debugging capabilities
# - Optimized build performance
# =============================================================================

services:
  # ============================================================================
  # OLLAMA SERVICE - Development Configuration
  # ============================================================================
  ollama-cpu:
    # CPU-only configuration for development
    environment:
      - OLLAMA_NUM_GPU=0
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_DEBUG=1
    ports:
      - "127.0.0.1:11434:11434"
    # Add development models volume for persistence
    volumes:
      - ollama_models_dev:/root/.ollama
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:11434/api/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # ============================================================================
  # TRAEFIK - Development Dashboard
  # ============================================================================
  traefik:
    command:
      - "--api.dashboard=true"
      - "--api.insecure=true"
      - "--log.level=INFO"
      - "--accesslog=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
    ports:
      - "127.0.0.1:8080:8080" # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro

  # ============================================================================
  # AI ORCHESTRATOR - Development with Hot Reload
  # ============================================================================
  ai-orchestrator:
    build:
      context: ./containers/ai-orchestrator
      dockerfile: Dockerfile
      target: builder
      args:
        BUILDKIT_INLINE_CACHE: 1

    # Development command with hot reload
    command: [ "python", "-m", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--reload-dir", "/app/src", "--log-level", "debug" ]

    ports:
      - "127.0.0.1:8000:8000" # API server
      - "127.0.0.1:5678:5678" # Debug port

    # Optimized volume mounts - preserve dependency layers
    volumes:
      # Only mount source code, not entire app directory
      - ./containers/ai-orchestrator/src:/app/src:cached
      - ./containers/ai-orchestrator/tests:/app/tests:cached
      - ./containers/ai-orchestrator/alembic:/app/alembic:cached
      - ./containers/ai-orchestrator/alembic.ini:/app/alembic.ini:ro
      # Preserve installed packages by NOT mounting these:
      # - ./containers/ai-orchestrator:/app (This would break cache layers)

    environment:
      - ENVIRONMENT=development
      - LOG_LEVEL=debug
      - DEBUG=true
      - PYTHONPATH=/app/src
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1

    depends_on:
      redis:
        condition: service_healthy
      # postgres:
      #   condition: service_healthy
      # ollama-cpu dependency removed - using profiles, dependency handled at runtime

      # Hot reload configuration
    develop:
      watch:
        # Sync source code changes
        - action: sync
          path: ./containers/ai-orchestrator/src
          target: /app/src
          ignore:
            - "**/__pycache__"
            - "**/*.pyc"

        # Sync test files
        - action: sync
          path: ./containers/ai-orchestrator/tests
          target: /app/tests

        # Rebuild on dependency changes
        - action: rebuild
          path: ./containers/ai-orchestrator/requirements*.txt

        # Sync configuration changes
        - action: sync
          path: ./containers/ai-orchestrator/alembic
          target: /app/alembic

  # ============================================================================
  # USER PORTAL - Development with Hot Reload
  # ============================================================================
  user-portal:
    build:
      context: ./containers/user-portal
      dockerfile: Dockerfile.dev
      args:
        BUILDKIT_INLINE_CACHE: 1

    command: [ "npm", "run", "dev" ]

    ports:
      - "127.0.0.1:3000:3000"

    # Optimized volume mounts for Next.js
    volumes:
      # Mount source directories only
      - ./containers/user-portal/src:/app/src:cached
      - ./containers/user-portal/app:/app/app:cached
      - ./containers/user-portal/public:/app/public:cached
      - ./containers/user-portal/components:/app/components:cached
      - ./containers/user-portal/styles:/app/styles:cached

      # Mount config files
      - ./containers/user-portal/next.config.js:/app/next.config.js:ro
      - ./containers/user-portal/tailwind.config.js:/app/tailwind.config.js:ro
      - ./containers/user-portal/tsconfig.json:/app/tsconfig.json:ro

      # Preserve node_modules with named volume
      - user_portal_node_modules:/app/node_modules

    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true

    # Hot reload configuration
    develop:
      watch:
        # Sync source code
        - action: sync
          path: ./containers/user-portal/src
          target: /app/src

        - action: sync
          path: ./containers/user-portal/app
          target: /app/app

        - action: sync
          path: ./containers/user-portal/components
          target: /app/components

        - action: sync
          path: ./containers/user-portal/styles
          target: /app/styles

        # Rebuild on package.json changes
        - action: rebuild
          path: ./containers/user-portal/package*.json

  # ============================================================================
  # CODE SERVER - Development IDE
  # ============================================================================
  code-server-template:
    ports:
      - "127.0.0.1:8443:8080"

    # Mount project files for editing
    volumes:
      - .:/home/<USER>/project:cached
      - code_server_data_dev:/home/<USER>/.local/share/code-server

    environment:
      - PASSWORD=development
      - SUDO_PASSWORD=development

  # ============================================================================
  # DATABASE SERVICES - Development Configuration
  # ============================================================================
  # The postgres service is removed from dev to align with the production setup,
  # which relies on an external Supabase instance managed by the Supabase CLI.
  # This ensures consistency between environments.
  # To run tests or develop locally, ensure Supabase is running (`supabase start`).

  redis:
    ports:
      - "127.0.0.1:6379:6379"

    # Development Redis configuration
    volumes:
      - redis_data_dev:/data

    # Enhanced health check
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 60s

# ============================================================================
# DEVELOPMENT VOLUMES
# ============================================================================
volumes:
  # Development data persistence
  postgres_data_dev:
    name: ai_agent_postgres_dev
    driver: local

  redis_data_dev:
    name: ai_agent_redis_dev
    driver: local

  ollama_models_dev:
    name: ai_agent_ollama_models_dev
    driver: local

  # Preserve node_modules for faster rebuilds
  user_portal_node_modules:
    name: ai_agent_user_portal_node_modules_dev
    driver: local

  # Code server development data
  code_server_data_dev:
    name: ai_agent_code_server_dev
    driver: local

# ============================================================================
# DEVELOPMENT NETWORKS
# ============================================================================
networks:
  default:
    name: ai_agent_dev
    driver: bridge
