#!/usr/bin/env python3
"""
Test Supabase Database Connection
=================================

This script tests the connection to the local Supabase database
and verifies that the schema is properly set up.
"""

import os
import sys
import asyncio
import asyncpg
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_database_connection():
    """Test basic database connectivity."""
    print("🔍 Testing Supabase Database Connection...")

    # Get database URL from environment
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ DATABASE_URL not found in environment variables")
        return False

    print(f"📍 Database URL: {database_url}")

    try:
        # Connect to database
        conn = await asyncpg.connect(database_url)
        print("✅ Database connection successful!")

        # Test basic query
        result = await conn.fetchval("SELECT version()")
        print(f"📊 PostgreSQL Version: {result}")

        # Check if vector extension is available
        vector_check = await conn.fetchval(
            "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector')"
        )
        print(f"🔧 Vector extension available: {'✅' if vector_check else '❌'}")

        # List all tables in the public schema
        tables = await conn.fetch("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)

        print(f"\n📋 Available tables ({len(tables)}):")
        for table in tables:
            print(f"   • {table['table_name']}")

        # Test each main table
        main_tables = ['projects', 'documents', 'document_sections', 'embeddings', 'user_profiles']
        print("\n🧪 Testing main tables:")

        for table_name in main_tables:
            try:
                count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
                print(f"   ✅ {table_name}: {count} rows")
            except Exception as e:
                print(f"   ❌ {table_name}: Error - {e}")

        # Test vector functions
        print("\n🔬 Testing vector functions:")
        try:
            # Test cosine similarity function
            similarity = await conn.fetchval("""
                SELECT cosine_similarity(
                    '[1,0,0]'::vector,
                    '[0,1,0]'::vector
                )
            """)
            print(f"   ✅ cosine_similarity function: {similarity}")
        except Exception as e:
            print(f"   ❌ cosine_similarity function: {e}")

        await conn.close()
        print("\n🎉 Database connection test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

async def test_supabase_api():
    """Test Supabase REST API connection."""
    print("\n🌐 Testing Supabase REST API...")

    import requests

    supabase_url = os.getenv('SUPABASE_URL')
    supabase_key = os.getenv('SUPABASE_KEY')

    if not supabase_url or not supabase_key:
        print("❌ SUPABASE_URL or SUPABASE_KEY not found")
        return False

    try:
        # Test API endpoint
        response = requests.get(
            f"{supabase_url}/rest/v1/projects",
            headers={
                "apikey": supabase_key,
                "Authorization": f"Bearer {supabase_key}"
            }
        )

        if response.status_code == 200:
            print("✅ Supabase REST API connection successful!")
            projects = response.json()
            print(f"📊 Projects found: {len(projects)}")
            return True
        else:
            print(f"❌ API request failed: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ API connection failed: {e}")
        return False

def test_environment_variables():
    """Test that all required environment variables are set."""
    print("⚙️  Testing Environment Variables...")

    required_vars = [
        'DATABASE_URL',
        'SUPABASE_URL',
        'SUPABASE_KEY',
        'SUPABASE_SERVICE_KEY',
        'JWT_SECRET'
    ]

    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Truncate sensitive values for display
            display_value = value[:20] + "..." if len(value) > 20 else value
            print(f"   ✅ {var}: {display_value}")
        else:
            print(f"   ❌ {var}: Not set")
            missing_vars.append(var)

    if missing_vars:
        print(f"\n❌ Missing environment variables: {', '.join(missing_vars)}")
        return False

    print("✅ All environment variables are set!")
    return True

async def main():
    """Main test function."""
    print("🚀 Supabase Project Link Test")
    print("=" * 50)

    # Test environment variables
    env_ok = test_environment_variables()

    # Test database connection
    db_ok = await test_database_connection() if env_ok else False

    # Test API connection
    api_ok = await test_supabase_api() if env_ok else False

    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   Environment Variables: {'✅' if env_ok else '❌'}")
    print(f"   Database Connection: {'✅' if db_ok else '❌'}")
    print(f"   REST API Connection: {'✅' if api_ok else '❌'}")

    if env_ok and db_ok and api_ok:
        print("\n🎉 Your project is successfully linked to Supabase!")
        print("🔗 You can now use the database in your application.")
        print("🌐 Access Supabase Studio at: http://127.0.0.1:54323")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
