"""add storage management and ingestion error tracking

Revision ID: add_storage_mgmt
Revises: add_proj_mgmt_fields
Create Date: 2025-09-01 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'add_storage_mgmt'
down_revision: Union[str, None] = 'add_proj_mgmt_fields'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Add storage_usage_bytes to user_profiles table
    op.add_column('user_profiles', sa.Column('storage_usage_bytes', sa.Integer(), server_default='0', nullable=False))

    # Create ingestion_errors table
    op.create_table('ingestion_errors',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True, comment='Unique error identifier'),
        sa.Column('project_id', sa.Integer(), sa.<PERSON>('projects.id', ondelete='CASCADE'), nullable=False, comment='Project where error occurred'),
        sa.Column('user_id', sa.UUID(), sa.ForeignKey('user_profiles.supabase_user_id', ondelete='CASCADE'), nullable=False, comment='User who initiated the ingestion'),
        sa.Column('file_path', sa.String(length=500), nullable=False, comment='Relative path of the file that failed'),
        sa.Column('error_type', sa.String(length=100), nullable=False, comment='Type of error (e.g., processing, embedding, storage)'),
        sa.Column('error_message', sa.Text(), nullable=False, comment='Detailed error message'),
        sa.Column('stack_trace', sa.Text(), nullable=True, comment='Full stack trace if available'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Timestamp when error occurred')
    )

    # Create indexes for ingestion_errors table
    op.create_index('ix_ingestion_errors_project_id', 'ingestion_errors', ['project_id'], unique=False)
    op.create_index('ix_ingestion_errors_user_id', 'ingestion_errors', ['user_id'], unique=False)
    op.create_index('ix_ingestion_errors_error_type', 'ingestion_errors', ['error_type'], unique=False)
    op.create_index('ix_ingestion_errors_created_at', 'ingestion_errors', ['created_at'], unique=False)

    # Add scanning fields to projects table
    op.add_column('projects', sa.Column('scan_started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('projects', sa.Column('scan_completed_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('projects', sa.Column('total_files_scanned', sa.Integer(), server_default='0', nullable=False))
    op.add_column('projects', sa.Column('total_files_indexed', sa.Integer(), server_default='0', nullable=False))
    op.add_column('projects', sa.Column('scan_error_message', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Remove scanning fields from projects table
    op.drop_column('projects', 'scan_error_message')
    op.drop_column('projects', 'total_files_indexed')
    op.drop_column('projects', 'total_files_scanned')
    op.drop_column('projects', 'scan_completed_at')
    op.drop_column('projects', 'scan_started_at')

    # Drop ingestion_errors table
    op.drop_index('ix_ingestion_errors_created_at', table_name='ingestion_errors')
    op.drop_index('ix_ingestion_errors_error_type', table_name='ingestion_errors')
    op.drop_index('ix_ingestion_errors_user_id', table_name='ingestion_errors')
    op.drop_index('ix_ingestion_errors_project_id', table_name='ingestion_errors')
    op.drop_table('ingestion_errors')

    # Remove storage_usage_bytes from user_profiles table
    op.drop_column('user_profiles', 'storage_usage_bytes')

    # ### end Alembic commands ###
