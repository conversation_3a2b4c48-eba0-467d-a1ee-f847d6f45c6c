"""
Project Repository for AI Coding Agent.

This module provides the data access layer for project management operations,
implementing the Repository pattern to ensure clean separation between
business logic and data persistence. It handles all interactions with the
filesystem for project storage and Git operations.

Key Components:
- ProjectRepository: Main repository class with CRUD-like operations for projects.
- Filesystem operations for creating, deleting, and listing projects.
- Git repository cloning and management.
- Integration with FastAPI's dependency injection system.
"""

import asyncio
import inspect
import logging
import os
import re
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

from fastapi import Depends
from sqlalchemy.orm import Session
from src.core.config import settings
from src.models import Project, UserProfile
from src.models.project import ProjectExport
from src.models.deployment_integration import DeploymentIntegration
from src.services.redis_service import ProjectCache, get_project_cache

logger = logging.getLogger(__name__)


class ProjectRepositoryError(Exception):
    """Base exception for project repository operations."""


class ProjectNotFoundError(ProjectRepositoryError):
    """Exception raised when a project is not found on the filesystem."""


class ProjectAlreadyExistsError(ProjectRepositoryError):
    """Exception raised when trying to create a project that already exists."""


class ProjectRepository:
    """
    Manages project data and filesystem operations.
    NOTE: This class violates SRP and should be refactored into smaller services.
    """

    def __init__(self, project_cache: ProjectCache):
        """Initializes the ProjectRepository."""
        self.project_cache = project_cache
        logger.info("ProjectRepository initialized")

    async def get_user_workspace_path(self, user_id: str) -> Path:
        """Constructs the path to a user's workspace directory."""
        # Sanitize user_id to prevent path traversal with os.path.basename
        safe_user_id = os.path.basename(user_id)
        return settings.paths.WORKSPACE_PATH / f"user_{safe_user_id}"

    async def get_workspace_info(self, user_id: str) -> Dict[str, Any]:
        """
        Retrieves information about the user's workspace.

        Args:
            user_id: The user's ID.

        Returns:
            A dictionary containing details about the workspace, including
            paths and existence status.
        """
        try:
            workspace_path = settings.WORKSPACE_PATH
            user_dir = await self.get_user_workspace_path(user_id)
            if inspect.isawaitable(user_dir):
                user_dir = await user_dir

            return {
                "workspace_path": str(workspace_path),
                "user_workspace": str(user_dir),
                "workspace_exists": workspace_path.exists(),
                "user_workspace_exists": user_dir.exists(),
            }
        except Exception as e:
            logger.error(f"Failed to get workspace info for user {user_id}: {e}")
            raise ProjectRepositoryError(f"Failed to get workspace info: {str(e)}")

    async def upload_project_files(
        self, user_id: str, files: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Uploads a list of files to the user's workspace in batches.

        This method will create the necessary directory structure for each file
        and process uploads concurrently for better performance.

        Args:
            user_id: The user's ID.
            files: A list of dictionaries, each representing a file with
                   'name', 'path', and 'content' keys.

        Returns:
            A dictionary with the result of the upload operation.
        """
        if not files:
            raise ProjectRepositoryError("No files provided for upload.")

        user_dir = await self.get_user_workspace_path(user_id)
        if inspect.isawaitable(user_dir):
            user_dir = await user_dir

        user_dir.mkdir(parents=True, exist_ok=True)

        BATCH_SIZE = 20
        files_processed = 0
        total_files = len(files)
        errors = []

        async def _write_file(file_data: Dict[str, Any]) -> None:
            try:
                # Security: Ensure the file path is relative and within the user's directory
                file_path = user_dir.joinpath(file_data["path"]).resolve()
                if not str(file_path).startswith(str(user_dir.resolve())):
                    logger.warning(
                        f"Skipping file upload due to path traversal attempt: {file_data['path']}"
                    )
                    errors.append(f"Path traversal attempt: {file_data['path']}")
                    return

                file_path.parent.mkdir(parents=True, exist_ok=True)

                import aiofiles

                async with aiofiles.open(file_path, "w", encoding="utf-8") as f:
                    await f.write(file_data["content"])

                logger.info(f"Successfully uploaded file: {file_path}")
            except Exception as file_error:
                logger.error(f"Failed to upload file '{file_data.get('path')}': {file_error}")
                errors.append(f"Failed to upload {file_data.get('path')}: {file_error}")
                # This will be caught by asyncio.gather and handled below

        for i in range(0, total_files, BATCH_SIZE):
            batch = files[i : i + BATCH_SIZE]
            tasks = [_write_file(file_data) for file_data in batch]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            for result in results:
                if result is None:
                    files_processed += 1
                elif isinstance(result, Exception):
                    # Errors are already logged in _write_file
                    pass

        if files_processed == 0 and total_files > 0:
            raise ProjectRepositoryError(
                f"Failed to upload any of the provided files. Errors: {'; '.join(errors)}"
            )

        return {
            "message": f"Successfully uploaded {files_processed} of {total_files} files.",
            "path": str(user_dir),
            "files_processed": files_processed,
            "errors": errors,
        }

    async def clone_git_repository(
        self,
        user_id: str,
        repository_url: str,
        target_directory: Optional[str] = None,
        branch: Optional[str] = None,
        db: Optional[Session] = None,
        user: Optional[UserProfile] = None,
    ) -> Dict[str, Any]:
        """
        Clones a Git repository into the user's workspace.

        If a database session and user object are provided, it also creates a
        corresponding `Project` record in the database.

        Args:
            user_id: The user's ID.
            repository_url: The URL of the Git repository.
            target_directory: Optional name for the directory where the repo will be cloned.
            branch: Optional Git branch to clone.
            db: Optional SQLAlchemy session for database operations.
            user: Optional User object for creating project associations.

        Returns:
            A dictionary with the result of the clone operation.
        """
        try:
            user_dir = await self.get_user_workspace_path(user_id)
            if inspect.isawaitable(user_dir):
                user_dir = await user_dir

            user_dir.mkdir(parents=True, exist_ok=True)

            if target_directory:
                # Sanitize target directory to prevent path traversal and other attacks
                sanitized_directory = re.sub(r"[^a-zA-Z0-9_.-]", "", target_directory)
                if (
                    not sanitized_directory
                    or ".." in sanitized_directory
                    or sanitized_directory.startswith("/")
                ):
                    raise ProjectRepositoryError("Invalid target directory name")
                target_path = user_dir / sanitized_directory
            else:
                repo_name = repository_url.rstrip("/").split("/")[-1]
                if repo_name.endswith(".git"):
                    repo_name = repo_name[:-4]
                target_path = user_dir / repo_name

            if target_path.exists():
                raise ProjectAlreadyExistsError(
                    f"Directory '{target_path.name}' already exists in workspace."
                )

            git_command = ["git", "clone", repository_url, str(target_path)]
            if branch:
                git_command.extend(["--branch", branch])

            process = await asyncio.create_subprocess_exec(
                *git_command,
                cwd=str(user_dir),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )
            stdout, stderr = await process.communicate()

            if process.returncode != 0:
                error_message = (
                    stderr.decode() if stderr else "Git clone failed with no error message."
                )
                logger.error(f"Git clone failed for {repository_url}: {error_message}")
                raise ProjectRepositoryError(f"Failed to clone repository: {error_message}")

            logger.info(f"Successfully cloned {repository_url} to {target_path}")

            if db and user:
                try:
                    project = Project(
                        name=target_path.name,
                        description=f"Cloned from {repository_url}",
                        owner_id=user.supabase_user_id,
                    )
                    db.add(project)
                    db.commit()
                    db.refresh(project)
                except Exception:
                    db.rollback()
                    logger.exception("Project cloned but database persistence failed.")

            return {
                "message": "Successfully cloned repository.",
                "path": str(target_path),
                "repository_url": repository_url,
            }
        except (ProjectAlreadyExistsError, ProjectRepositoryError) as e:
            logger.warning(f"Clone validation failed for user {user_id}: {e}")
            raise
        except Exception as e:
            logger.error(
                f"Repository cloning failed unexpectedly for user {user_id}: {e}", exc_info=True
            )
            raise ProjectRepositoryError(f"An unexpected error occurred during cloning: {str(e)}")

    async def list_user_projects(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Lists all projects in a user's workspace directory.

        Args:
            user_id: The user's ID.

        Returns:
            A list of dictionaries, each representing a project.
        """
        cached_projects = await self.project_cache.get_project_list(user_id)
        if cached_projects is not None:
            return cached_projects

        try:
            user_dir = await self.get_user_workspace_path(user_id)
            if inspect.isawaitable(user_dir):
                user_dir = await user_dir

            if not user_dir.exists():
                return []

            projects = []
            for item in user_dir.iterdir():
                if item.is_dir() and not item.name.startswith("."):
                    try:
                        stat_info = item.stat()
                        project_info = {
                            "name": item.name,
                            "path": str(item.relative_to(user_dir)),
                            "is_git_repo": (item / ".git").exists(),
                            "created": datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                            "modified": datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                        }
                        projects.append(project_info)
                    except FileNotFoundError:
                        # File might have been deleted during iteration
                        continue

            projects.sort(key=lambda x: x["created"], reverse=True)
            await self.project_cache.set_project_list(user_id, projects)
            return projects
        except Exception as e:
            logger.error(f"Failed to list projects for user {user_id}: {e}")
            raise ProjectRepositoryError(f"Failed to list projects: {str(e)}")

    async def delete_project(self, user_id: str, project_name: str) -> Dict[str, str]:
        """
        Deletes a project directory from a user's workspace.

        Args:
            user_id: The user's ID.
            project_name: The name of the project to delete.

        Returns:
            A confirmation message.
        """
        try:
            if ".." in project_name or "/" in project_name:
                raise ProjectRepositoryError("Invalid project name format.")

            user_dir = await self.get_user_workspace_path(user_id)
            if inspect.isawaitable(user_dir):
                user_dir = await user_dir

            project_path = user_dir / project_name

            if not project_path.exists():
                raise ProjectNotFoundError(f"Project '{project_name}' not found.")

            if not project_path.is_dir():
                raise ProjectRepositoryError(f"'{project_name}' is not a valid project directory.")

            shutil.rmtree(project_path)
            await self.project_cache.invalidate_project_list(user_id)
            logger.info(f"Successfully deleted project: {project_path}")
            return {"message": f"Successfully deleted project '{project_name}'."}
        except (ProjectNotFoundError, ProjectRepositoryError) as e:
            logger.warning(f"Project deletion failed for user {user_id}: {e}")
            raise
        except Exception as e:
            logger.error(
                f"Unexpected error deleting project '{project_name}' for user {user_id}: {e}",
                exc_info=True,
            )
            raise ProjectRepositoryError(
                f"An unexpected error occurred while deleting project: {str(e)}"
            )

    async def create_project(
        self,
        *,
        db: Session,
        user: UserProfile,
        project_name: str,
        description: Optional[str] = None,
        template_type: str = "webapp",
    ) -> Project:
        """
        Creates a project directory from a template and persists a Project record.

        This enhanced method integrates with various services for resource management,
        rollback capabilities, and port allocation.

        Args:
            db: The SQLAlchemy database session.
            user: The user for whom the project is being created.
            project_name: The name of the new project.
            description: An optional description for the project.
            template_type: The type of template to use for the project.

        Returns:
            The created Project database object.
        """
        from src.services.port_manager import get_port_manager
        from src.services.resource_manager import get_resource_manager
        from src.services.rollback_manager import RollbackStage, get_rollback_manager
        from src.services.template_registry import TemplateType, get_template_registry

        template_registry = get_template_registry()
        port_manager = get_port_manager()
        resource_manager = get_resource_manager()
        rollback_manager = get_rollback_manager()

        checkpoint_id = None
        project_path = None
        allocated_port = None
        schema_name = None

        try:
            if ".." in project_name or "/" in project_name:
                raise ProjectRepositoryError("Invalid project name.")

            user_dir = await self.get_user_workspace_path(str(user.id))
            if inspect.isawaitable(user_dir):
                user_dir = await user_dir

            project_path = user_dir / project_name
            if project_path.exists():
                raise ProjectAlreadyExistsError(f"Directory '{project_name}' already exists.")

            checkpoint_id = await rollback_manager.create_checkpoint(
                project_name,
                str(user.id),
                RollbackStage.DIRECTORY_CREATION,
                {"project_path": str(project_path)},
            )

            allocated_port = await port_manager.allocate_port(
                str(user.id), project_name, template_type
            )
            if not allocated_port:
                raise ProjectRepositoryError("Failed to allocate port for project.")

            project_path.mkdir(parents=True, exist_ok=False)

            template_enum = (
                TemplateType(template_type)
                if template_type in [t.value for t in TemplateType]
                else TemplateType.WEBAPP
            )
            template_metadata = template_registry.get_template_metadata(template_enum)
            resource_limits = resource_manager.get_default_resource_limits(template_type)

            await self._provision_project_from_template(
                project_path, project_name, template_type, allocated_port, resource_limits
            )

            checkpoint_id = await rollback_manager.create_checkpoint(
                project_name,
                str(user.id),
                RollbackStage.DATABASE_RECORD,
                {"project_path": str(project_path), "allocated_port": allocated_port},
            )

            project = Project(name=project_name, description=description, owner_id=user.id)
            db.add(project)
            db.commit()
            db.refresh(project)

            from src.services.provisioning_service import ProvisioningService

            provisioning_service = ProvisioningService()
            schema_name, connection_url = await provisioning_service.create_project_schema(
                db, project.id
            )

            project.db_schema_name = schema_name
            project.db_connection_url = connection_url
            db.commit()
            db.refresh(project)

            checkpoint_id = await rollback_manager.create_checkpoint(
                project_name,
                str(user.id),
                RollbackStage.USER_ASSOCIATION,
                {
                    "project_path": str(project_path),
                    "project_id": str(project.id),
                    "user_id": str(user.id),
                    "allocated_port": allocated_port,
                    "schema_name": schema_name,
                },
            )

            # The owner is already associated with the project via owner_id, so no need to do anything here.
            pass

            if checkpoint_id:
                await rollback_manager.commit_checkpoint(checkpoint_id)

            await self.project_cache.invalidate_project_list(str(user.id))
            logger.info(
                f"Successfully created project '{project_name}' for user {user.id} on port {allocated_port}."
            )
            return project
        except Exception as e:
            db.rollback()
            logger.error(f"Project creation failed for '{project_name}': {e}", exc_info=True)

            if checkpoint_id:
                logger.info(f"Executing rollback for failed project creation: {project_name}")
                await rollback_manager.rollback_to_checkpoint(checkpoint_id)
            else:
                try:
                    if allocated_port:
                        await port_manager.release_port(str(user.id), project_name)
                    if project_path and project_path.exists():
                        shutil.rmtree(project_path)
                    if schema_name:
                        from sqlalchemy import text

                        db.execute(text(f'DROP SCHEMA IF EXISTS "{schema_name}" CASCADE'))
                        db.commit()
                except Exception:
                    logger.exception("Failed to manually cleanup project resources after error.")
            raise

    async def _provision_project_from_template(
        self,
        project_path: Path,
        project_name: str,
        template_type: str,
        allocated_port: int,
        resource_limits: Any,
    ) -> None:
        """
        Provisions a new project from a template.

        Args:
            project_path: The path to the new project directory.
            project_name: The name of the project.
            template_type: The type of template to use.
            allocated_port: The port allocated for the project.
            resource_limits: The resource limits for the project.
        """
        try:
            template_dir = settings.TEMPLATES_PATH / template_type
            if not template_dir.exists():
                raise ProjectRepositoryError(f"Template directory not found: {template_dir}")

            logger.info(f"Copying template '{template_type}' from {template_dir} to {project_path}")

            placeholders = {
                "__PROJECT_NAME__": project_name,
                "__PROJECT_PORT__": str(allocated_port),
                "__CPU_LIMIT__": resource_limits.cpu_limit if resource_limits else "1.0",
                "__MEMORY_LIMIT__": resource_limits.memory_limit if resource_limits else "512m",
            }

            await self._copy_template_files_enhanced(template_dir, project_path, placeholders)

            (project_path / "logs").mkdir(exist_ok=True)
            (project_path / "data").mkdir(exist_ok=True)
            (project_path / "src").mkdir(exist_ok=True)

            if resource_limits:
                await self._apply_resource_limits_to_compose(project_path, resource_limits)

            logger.info(f"Template provisioning completed for '{project_name}'.")
        except Exception as e:
            logger.error(f"Failed to provision project from template: {e}")
            raise ProjectRepositoryError(f"Template provisioning failed: {e}")

    async def _copy_template_files_enhanced(
        self, template_dir: Path, project_path: Path, placeholders: dict
    ) -> None:
        """
        Copies template files and replaces placeholders.

        Args:
            template_dir: The source template directory.
            project_path: The destination project directory.
            placeholders: A dictionary of placeholders and their values.
        """
        for template_file in template_dir.rglob("*"):
            if template_file.is_file():
                relative_path = template_file.relative_to(template_dir)
                dest_filename = str(relative_path).removesuffix(".template")
                dest_path = project_path / dest_filename
                dest_path.parent.mkdir(parents=True, exist_ok=True)

                try:
                    content = template_file.read_text(encoding="utf-8")
                    for placeholder, replacement in placeholders.items():
                        content = content.replace(placeholder, replacement)
                    dest_path.write_text(content, encoding="utf-8")
                    logger.debug(f"Processed template file: {relative_path} -> {dest_filename}")
                except Exception as e:
                    logger.error(f"Failed to process template file {template_file}: {e}")
                    raise

    async def _apply_resource_limits_to_compose(
        self, project_path: Path, resource_limits: Any
    ) -> None:
        """
        Applies resource limits to the docker-compose.yml file.

        Args:
            project_path: The path to the project directory.
            resource_limits: The resource limits to apply.
        """
        try:
            compose_file = project_path / "docker-compose.yml"
            if not compose_file.exists():
                return

            import yaml

            with open(compose_file, "r") as f:
                compose_data = yaml.safe_load(f)

            if "services" in compose_data:
                for service in compose_data["services"].values():
                    service["deploy"] = {
                        "resources": {
                            "limits": {
                                "cpus": str(resource_limits.cpu_limit),
                                "memory": resource_limits.memory_limit,
                            },
                            "reservations": {
                                "cpus": str(float(resource_limits.cpu_limit) * 0.5),
                                "memory": f"{int(resource_limits.memory_limit.rstrip('mMgG')) // 2}{resource_limits.memory_limit[-1]}",
                            },
                        }
                    }

            with open(compose_file, "w") as f:
                yaml.dump(compose_data, f, default_flow_style=False, sort_keys=False)

            logger.info("Applied resource limits to docker-compose.yml")
        except Exception as e:
            logger.warning(f"Failed to apply resource limits to compose file: {e}")

    # ... (rest of the file remains the same) ...
    async def create_export_task(self, export_task: Dict[str, Any], db: Session) -> Dict[str, Any]:
        """
        Create export task and delegate to ArchitectAgent.

        Args:
            export_task: Export task parameters containing export_id, project_id, user_id, etc.

        Returns:
            Dict containing the export result and task information

        Raises:
            ProjectRepositoryError: If export task creation fails
        """
        try:
            # Store export record in database
            export_record = {
                "export_id": export_task["export_id"],
                "project_id": export_task["project_id"],
                "user_id": export_task["user_id"],
                "project_name": export_task["project_name"],
                "status": "initiated",
                "export_format": export_task.get("export_format", "zip"),
                "include_database": export_task.get("include_database", True),
                "include_files": export_task.get("include_files", True),
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow() + timedelta(days=7),
            }

            # Insert into database using raw SQL (adapt to your DB setup)
            await self._store_export_record(export_record, db)

            # Delegate to ArchitectAgent
            from src.agents.architect_agent import ArchitectAgent

            architect = ArchitectAgent()
            result = await architect.handle_export_request(export_task)

            # Update export record with task IDs if successful
            if result.get("success"):
                await self._update_export_record(
                    export_task["export_id"],
                    {
                        "task_ids": result.get("task_ids", []),
                        "status": "in_progress",
                        "metadata": {"plan": result.get("plan")},
                    },
                    db,
                )

            return result

        except Exception as e:
            logger.error(f"Export task creation failed: {e}")
            # Update export record with error
            try:
                await self._update_export_record(
                    export_task["export_id"], {"status": "failed", "error_message": str(e)}, db
                )
            except Exception:
                logger.exception("Failed to update export record with error")

            raise ProjectRepositoryError(f"Failed to create export task: {str(e)}")

    async def get_export_status(self, user_id: str, export_id: str, db: Session) -> Dict[str, Any]:
        """
        Get export status and details.

        Args:
            user_id: User identifier
            export_id: Export identifier

        Returns:
            Dict containing export status information

        Raises:
            ProjectRepositoryError: If export not found or access denied
        """
        try:
            # Query export record from database
            export_record = await self._get_export_record(export_id, user_id, db)

            if not export_record:
                raise ProjectRepositoryError("Export not found or access denied")

            # Build download URL if completed
            download_url = None
            if export_record["status"] == "completed" and export_record.get("file_path"):
                download_url = (
                    f"/api/v1/projects/{export_record['project_id']}/export/{export_id}/download"
                )

            return {
                "export_id": export_record["export_id"],
                "project_id": export_record["project_id"],
                "project_name": export_record["project_name"],
                "status": export_record["status"],
                "progress_percentage": export_record.get("progress_percentage"),
                "current_step": export_record.get("current_step"),
                "steps_completed": export_record.get("steps_completed", []),
                "steps_remaining": export_record.get("steps_remaining", []),
                "export_format": export_record["export_format"],
                "include_database": export_record["include_database"],
                "include_files": export_record["include_files"],
                "file_size": export_record.get("file_size"),
                "filename": export_record.get("filename"),
                "download_url": download_url,
                "created_at": export_record["created_at"].isoformat()
                if export_record.get("created_at")
                else None,
                "completed_at": export_record["completed_at"].isoformat()
                if export_record.get("completed_at")
                else None,
                "expires_at": export_record["expires_at"].isoformat()
                if export_record.get("expires_at")
                else None,
                "error_message": export_record.get("error_message"),
                "message": self._get_status_message(export_record["status"]),
            }

        except Exception as e:
            logger.error(f"Export status retrieval failed: {e}")
            raise ProjectRepositoryError(f"Failed to get export status: {str(e)}")

    async def get_export_file_path(
        self, user_id: str, export_id: str, db: Session
    ) -> Dict[str, Any]:
        """
        Get export file path for download.

        Args:
            user_id: User identifier
            export_id: Export identifier

        Returns:
            Dict containing file path and metadata

        Raises:
            ProjectRepositoryError: If export not found or not ready
        """
        try:
            export_record = await self._get_export_record(export_id, user_id, db)

            if not export_record:
                raise ProjectRepositoryError("Export not found or access denied")

            if export_record["status"] != "completed":
                raise ProjectRepositoryError("Export is not completed yet")

            if not export_record.get("file_path") or not Path(export_record["file_path"]).exists():
                raise ProjectRepositoryError("Export file not found")

            return {
                "file_path": export_record["file_path"],
                "filename": export_record["filename"],
                "file_size": export_record.get("file_size"),
                "export_format": export_record["export_format"],
            }

        except Exception as e:
            logger.error(f"Export file path retrieval failed: {e}")
            raise ProjectRepositoryError(f"Failed to get export file path: {str(e)}")

    async def update_export_progress(
        self, export_id: str, progress_data: Dict[str, Any], db: Session
    ) -> None:
        """
        Update export progress information.

        Args:
            export_id: Export identifier
            progress_data: Progress update data
        """
        try:
            await self._update_export_record(export_id, progress_data, db)
        except Exception as e:
            logger.error(f"Export progress update failed: {e}")
            # Don't raise exception for progress updates to avoid breaking the export process

    # =====================================================================================
    # PRIVATE HELPER METHODS FOR EXPORT OPERATIONS
    # =====================================================================================

    async def _store_export_record(self, export_record: Dict[str, Any], db: Session) -> None:
        """Store export record in database."""
        try:
            # Create ProjectExport instance from the export record
            project_export = ProjectExport(
                export_id=export_record["export_id"],
                project_id=export_record["project_id"],
                user_id=export_record["user_id"],
                format=export_record.get("format", "zip"),
                status=export_record.get("status", "pending"),
                file_path=export_record.get("file_path"),
                file_size=export_record.get("file_size"),
                progress_percentage=export_record.get("progress_percentage", 0.0),
                current_step=export_record.get("current_step"),
                total_steps=export_record.get("total_steps", 1),
                export_metadata=export_record.get("export_metadata", {}),
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )

            # Add to database session
            db.add(project_export)
            db.commit()
            db.refresh(project_export)

            logger.info(f"Successfully stored export record: {export_record['export_id']}")

        except Exception as e:
            db.rollback()
            logger.error(f"Failed to store export record: {str(e)}")
            raise ProjectRepositoryError(f"Failed to store export record: {str(e)}")

    async def _get_export_record(
        self, export_id: str, user_id: str, db: Session
    ) -> Optional[Dict[str, Any]]:
        """Retrieve export record from database."""
        try:
            # Query the database for the export record
            from sqlalchemy import select

            stmt = select(ProjectExport).where(
                ProjectExport.export_id == export_id, ProjectExport.user_id == user_id
            )

            result = db.execute(stmt)
            export_record = result.scalars().first()

            if not export_record:
                logger.info(f"Export record not found: {export_id} for user: {user_id}")
                return None

            # Convert to dictionary format
            export_data = {
                "id": export_record.id,
                "export_id": export_record.export_id,
                "project_id": export_record.project_id,
                "user_id": export_record.user_id,
                "format": export_record.format,
                "status": export_record.status,
                "file_path": export_record.file_path,
                "file_size": export_record.file_size,
                "progress_percentage": export_record.progress_percentage,
                "current_step": export_record.current_step,
                "total_steps": export_record.total_steps,
                "error_message": export_record.error_message,
                "export_metadata": export_record.export_metadata,
                "created_at": export_record.created_at.isoformat()
                if export_record.created_at
                else None,
                "updated_at": export_record.updated_at.isoformat()
                if export_record.updated_at
                else None,
                "completed_at": export_record.completed_at.isoformat()
                if export_record.completed_at
                else None,
            }

            logger.info(f"Successfully retrieved export record: {export_id}")
            return export_data

        except Exception as e:
            logger.error(f"Failed to retrieve export record: {str(e)}")
            return None

    async def _update_export_record(
        self, export_id: str, update_data: Dict[str, Any], db: Session
    ) -> None:
        """Update export record in database."""
        try:
            # Query the database for the export record
            from sqlalchemy import select

            stmt = select(ProjectExport).where(ProjectExport.export_id == export_id)
            result = db.execute(stmt)
            export_record = result.scalars().first()

            if not export_record:
                logger.warning(f"Export record not found for update: {export_id}")
                return

            # Update the record with the provided data
            for key, value in update_data.items():
                if hasattr(export_record, key):
                    setattr(export_record, key, value)

            # Always update the updated_at timestamp
            export_record.updated_at = datetime.now()

            # Set completed_at if status is completed
            if update_data.get("status") == "completed":
                export_record.completed_at = datetime.now()

            # Commit the changes
            db.commit()

            logger.info(f"Successfully updated export record: {export_id} with data: {update_data}")

        except Exception as e:
            db.rollback()
            logger.error(f"Failed to update export record: {str(e)}")
            raise ProjectRepositoryError(f"Failed to update export record: {str(e)}")

    def _get_status_message(self, status: str) -> str:
        """Get human-readable status message."""
        status_messages = {
            "initiated": "Export has been initiated and is queued for processing",
            "in_progress": "Export is currently being processed",
            "completed": "Export completed successfully and is ready for download",
            "failed": "Export failed due to an error",
            "expired": "Export has expired and is no longer available",
        }
        return status_messages.get(status, f"Unknown status: {status}")

    async def get_project_by_id(
        self, user_id: str, project_id: str, db: Session
    ) -> Optional[Project]:
        """
        Get project by ID with user ownership validation.

        Args:
            user_id: User identifier
            project_id: Project identifier
            db: The SQLAlchemy database session.

        Returns:
            Project object if found and user has access, None otherwise
        """
        try:
            from sqlalchemy import select

            stmt = select(Project).where(Project.id == project_id, Project.owner_id == user_id)
            result = db.execute(stmt)
            project = result.scalars().first()

            if not project:
                logger.info(
                    f"Project not found or access denied for project {project_id}, user {user_id}"
                )
                return None

            return project

        except Exception as e:
            logger.error(f"Project retrieval failed for project {project_id}: {e}")
            raise ProjectRepositoryError(f"Failed to retrieve project: {str(e)}")

    async def get_deployment_integration_by_id(
        self, deployment_integration_id: str, user_id: str, db: Session
    ) -> Optional[DeploymentIntegration]:
        """
        Get deployment integration by ID with user ownership validation.
        """
        try:
            from sqlalchemy import select

            stmt = select(DeploymentIntegration).where(
                DeploymentIntegration.id == deployment_integration_id,
                DeploymentIntegration.user_id == user_id,
            )
            result = db.execute(stmt)
            integration = result.scalars().first()

            if not integration:
                logger.info(
                    f"Deployment integration not found or access denied for integration {deployment_integration_id}, user {user_id}"
                )
                return None

            return integration

        except Exception as e:
            logger.error(
                f"Deployment integration retrieval failed for integration {deployment_integration_id}: {e}"
            )
            raise ProjectRepositoryError(
                f"Failed to retrieve deployment integration: {str(e)}"
            )

    async def get_user_project_stats(self, project_id: str, db: Session) -> Dict[str, Any]:
        """
        Get statistics for a specific project.

        Args:
            project_id: The ID of the project.
            db: The SQLAlchemy database session.

        Returns:
            A dictionary with project statistics.
        """
        logger.info(f"Fetching stats for project_id: {project_id}")
        try:
            from sqlalchemy import text

            query = text("""
                SELECT
                    (SELECT COUNT(*) FROM documents WHERE project_id = :project_id) as file_count,
                    status
                FROM projects
                WHERE id = :project_id;
            """)
            result = db.execute(query, {"project_id": project_id}).fetchone()

            if result:
                return dict(result)
            else:
                return {"files": 0, "status": "not_found"}
        except Exception as e:
            logger.error(f"Error fetching project stats for {project_id}: {e}")
            raise ProjectRepositoryError(f"Could not fetch stats for project {project_id}")


# =====================================================================================
# DEPENDENCY FUNCTIONS FOR FASTAPI
# =====================================================================================


async def get_project_repository(
    project_cache: ProjectCache = Depends(get_project_cache),
) -> ProjectRepository:
    """
    FastAPI dependency for Project repository.

    Returns:
        ProjectRepository: The project repository instance
    """
    return ProjectRepository(project_cache)
