import { Role, Roles } from '@/types/roles';

export const mockRoles: Roles = {
  architect: {
    provider: 'openrouter',
    available_models: ['anthropic/claude-3-sonnet', 'openai/gpt-4'],
    selected_model: 'anthropic/claude-3-sonnet',
    api_key: 'sk-or-v1-xxxxxxxxxxxxxxxxxxxxxxxx',
    cost_limit: 100.0,
    max_tokens: 4096,
    temperature: 0.7,
    enabled: true,
    created_at: '2024-01-01T00:00:00.000000',
    updated_at: '2024-01-01T00:00:00.000000',
  },
  backend: {
    provider: 'ollama',
    available_models: ['codellama:13b', 'deepseek-coder:6.7b'],
    selected_model: 'codellama:13b',
    api_key: null,
    cost_limit: null,
    max_tokens: 4096,
    temperature: 0.3,
    enabled: true,
    created_at: '2024-01-01T00:00:00.000000',
    updated_at: '2024-01-01T00:00:00.000000',
  },
  frontend: {
    provider: 'openai',
    available_models: ['gpt-4o', 'gpt-3.5-turbo'],
    selected_model: 'gpt-4o',
    api_key: 'sk-xxxxxxxxxxxxxxxxxxxxxxxx',
    cost_limit: 75.0,
    max_tokens: 2048,
    temperature: 0.5,
    enabled: false,
    created_at: '2024-01-01T00:00:00.000000',
    updated_at: '2024-01-01T00:00:00.000000',
  },
};
