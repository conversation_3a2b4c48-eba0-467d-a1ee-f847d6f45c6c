from fastapi import APIRouter, HTTPException, Depends, status, Body, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse, Response
from pydantic import BaseModel, validator
from typing import List, Dict, Any, Optional
import logging
import uuid
from datetime import datetime
from pathlib import Path
import os
import mimetypes
from sqlalchemy.orm import Session
import httpx
from uuid import UUID

from src.utils.auth import get_current_user
from src.repository.project_repository import ProjectRepository, get_project_repository, ProjectRepositoryError
from src.models.database import get_db
from src.models.project import Project, ProjectStatus
from src.schemas.project_schemas import (
    ProjectExportRequest,
    ProjectExportResponse,
    ProjectExportStatusResponse,
    FileData,
    ProjectUploadResponse,
    GitCloneRequest,
    GitCloneResponse,
    CustomDomainRequest,
    LaunchProjectResponse,
    ProjectEnvironmentListResponse
)
from src.services.supabase_service import SupabaseService, get_supabase_service
from src.services.vector_service import VectorStorageService, get_vector_service
from src.services.websocket_manager import WebSocketChatManager, get_chat_manager
from src.services.project_service import ProjectService
from src.services.docker_service import DockerWorkspaceService, get_docker_service
from src.agents.shell_agent import ShellAgent
from src.agents.deployment_agent import DeploymentAgent
from src.agents.frontend_agent import FrontendAgent
from src.agents.architect_agent import ArchitectAgent
from src.approval.approval_manager import ApprovalManager
from src.approval.approval_models import ApprovalStatus
from src.schemas.project_schemas import ApproveChangesRequest
from src.core.config import settings
import re

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/projects", tags=["projects"])

# Service dependency for ProjectService
def get_project_service(db: Session = Depends(get_db)) -> ProjectService:
    """Dependency injection for ProjectService with Docker client."""
    return ProjectService(db_session=db, docker_proxy_url=f"tcp://{settings.DOCKER_PROXY_HOST}:2375")


# =====================================================================================
# AGENT DEPENDENCIES & CHANGE REQUEST MODELS
# =====================================================================================

class RequestChangesRequest(BaseModel):
    prompt: str

class RequestChangesResponse(BaseModel):
    message: str
    branch_name: str
    commit_hash: str
    preview_url: str

def get_shell_agent() -> ShellAgent:
    """Dependency injection for ShellAgent."""
    return ShellAgent()

def get_deployment_agent() -> DeploymentAgent:
    """Dependency injection for DeploymentAgent."""
    return DeploymentAgent(traefik_config_path=settings.TRAEFIK_CONFIG_PATH)

def get_frontend_agent() -> FrontendAgent:
    """Dependency injection for FrontendAgent."""
    return FrontendAgent()

def get_architect_agent(
    frontend_agent: FrontendAgent = Depends(get_frontend_agent)
) -> ArchitectAgent:
    """Dependency injection for ArchitectAgent."""
    return ArchitectAgent(frontend_agent=frontend_agent)


def get_approval_manager() -> ApprovalManager:
    """Dependency injection for ApprovalManager."""
    # This will be a singleton if the ApprovalManager is implemented as such.
    # For now, we create a new instance each time.
    return ApprovalManager()


@router.post("/{project_id}/request-changes", response_model=RequestChangesResponse)
async def request_changes(
    project_id: UUID,
    request: RequestChangesRequest,
    current_user: dict = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository),
    shell_agent: ShellAgent = Depends(get_shell_agent),
    frontend_agent: FrontendAgent = Depends(get_frontend_agent),
    deployment_agent: DeploymentAgent = Depends(get_deployment_agent),
) -> RequestChangesResponse:
    """
    Orchestrates the end-to-end workflow for requesting code changes and creating a preview deployment.
    """
    try:
        # Step 1: Fetch Project Details
        user_id = current_user.supabase_user_id
        project = await project_repo.get_project_by_id(project_id, user_id)
        if not project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")

        # Ensure project_name is a string before passing it to the repository
        project_name_str = str(project.project_name)
        project_workspace_path = project_repo.get_project_workspace_path(user_id, project_name_str)

        # Step 2: Create a New Git Branch
        prompt_slug = re.sub(r'[^a-z0-9]+', '-', request.prompt.lower()).strip('-')[:30]
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M")
        branch_name = f"feature/{prompt_slug}-{timestamp}"

        # First, ensure we are on the main branch and have the latest changes
        await shell_agent.execute({"command": ["git", "checkout", "main"], "cwd": project_workspace_path})
        await shell_agent.execute({"command": ["git", "pull"], "cwd": project_workspace_path})

        git_checkout_result = await shell_agent.execute({
            "command": ["git", "checkout", "-b", branch_name],
            "cwd": project_workspace_path,
        })
        if git_checkout_result.get("returncode") != 0:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to create new branch: {git_checkout_result.get('stderr')}")

        # Step 3: Dispatch to Specialist Agent (FrontendAgent)
        code_modification_result = await frontend_agent.execute({
            "prompt": request.prompt,
            "project_id": str(project_id),
            "cwd": project_workspace_path,
            "user_id": user_id,
        })
        if not code_modification_result.get("success"):
            # Attempt to clean up by switching back to the main branch
            await shell_agent.execute({"command": ["git", "checkout", "main"], "cwd": project_workspace_path})
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Code modification failed: {code_modification_result.get('error', 'Unknown error from agent')}")

        # Step 4: Commit the Changes
        commit_message = f"feat: Implement changes for '{request.prompt[:50]}...'"
        git_add_result = await shell_agent.execute({
            "command": ["git", "add", "."], "cwd": project_workspace_path
        })
        if git_add_result.get("returncode") != 0:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to stage changes: {git_add_result.get('stderr')}")

        git_commit_result = await shell_agent.execute({
            "command": ["git", "commit", "-m", commit_message], "cwd": project_workspace_path
        })
        # A return code of 1 from commit often means "nothing to commit"
        if git_commit_result.get("returncode") not in [0, 1]:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to commit changes: {git_commit_result.get('stderr')}")

        # Step 5: Get the Commit Hash
        git_rev_parse_result = await shell_agent.execute({
            "command": ["git", "rev-parse", "HEAD"], "cwd": project_workspace_path
        })
        if git_rev_parse_result.get("returncode") != 0:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to get commit hash: {git_rev_parse_result.get('stderr')}")
        commit_hash = git_rev_parse_result.get("stdout", "").strip()

        # Step 6: Create Preview Deployment
        deployment_result = await deployment_agent.create_preview_deployment(
            project_id=str(project_id),
            project_hostname=str(project.project_hostname),
            branch_name=branch_name,
            commit_hash=commit_hash,
            user_id=user_id,
        )

        if not deployment_result.get("success"):
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to create preview deployment: {deployment_result.get('error', 'Unknown deployment error')}")

        # Step 7: Return the Preview URL
        preview_url = deployment_result.get("preview_url")
        if not preview_url:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Deployment succeeded but no preview URL was returned.")

        return RequestChangesResponse(
            message="Successfully requested changes and created preview deployment.",
            branch_name=branch_name,
            commit_hash=commit_hash,
            preview_url=preview_url,
        )
    except ProjectRepositoryError as e:
        logger.error(f"Project repository error in request_changes: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    except HTTPException:
        # Re-raise HTTP exceptions to avoid them being caught by the generic one
        raise
    except Exception as e:
        logger.error(f"An unexpected error occurred in request_changes for project {project_id}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected internal error occurred.")


@router.get("/templates", response_model=List[str], summary="List Project Templates")
async def list_project_templates():
    """
    Scans the filesystem for available project templates and returns a list of their names.
    These templates can be used to bootstrap new projects.
    """
    templates_dir = Path("templates/project-templates")
    if not templates_dir.is_dir():
        logger.error(f"Templates directory not found at: {templates_dir}")
        return []
    try:
        templates = [d.name for d in os.scandir(templates_dir) if d.is_dir()]
        return templates
    except Exception as e:
        logger.error(f"Error scanning templates directory '{templates_dir}': {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while retrieving project templates."
        )


@router.get("/workspace/info")
async def get_workspace_info(
    current_user = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository)
) -> Dict[str, Any]:
    """Get workspace information"""
    try:
        user_id = current_user.supabase_user_id
        workspace_info = await project_repo.get_workspace_info(user_id)
        return workspace_info
    except ProjectRepositoryError as e:
        logger.error(f"Failed to get workspace info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get workspace info: {str(e)}"
        )

@router.post("/upload", response_model=ProjectUploadResponse)
async def upload_project(
    files: List[FileData] = Body(...),
    current_user = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository)
) -> ProjectUploadResponse:
    """Upload project files to user workspace"""
    try:
        user_id = current_user.supabase_user_id

        # Convert FileData objects to dictionaries for the repository
        file_dicts = [
            {
                "name": file_data.name,
                "path": file_data.path,
                "content": file_data.content,
                "type": file_data.type
            }
            for file_data in files
        ]

        result = await project_repo.upload_project_files(user_id, file_dicts)
        return ProjectUploadResponse(**result)

    except ProjectRepositoryError as e:
        logger.error(f"Project upload failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload project: {str(e)}"
        )

@router.post("/clone", response_model=GitCloneResponse)
async def clone_repository(
    request: GitCloneRequest,
    current_user = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository)
) -> GitCloneResponse:
    """Clone a Git repository to user workspace"""
    try:
        user_id = current_user.supabase_user_id

        result = await project_repo.clone_git_repository(
            user_id=user_id,
            repository_url=request.repository_url,
            target_directory=request.target_directory,
            branch=request.branch
        )

        return GitCloneResponse(**result)

    except ProjectRepositoryError as e:
        logger.error(f"Repository cloning failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clone repository: {str(e)}"
        )

@router.get("/list")
async def list_user_projects(
    current_user = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository)
) -> Dict[str, Any]:
    """List all projects in user workspace"""
    try:
        user_id = current_user.supabase_user_id
        projects = await project_repo.list_user_projects(user_id)
        return {"projects": projects}
    except ProjectRepositoryError as e:
        logger.error(f"Failed to list projects: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list projects: {e}"
        )


@router.post("/{project_id}/approve-changes")
async def approve_changes(
    project_id: UUID,
    request: ApproveChangesRequest,
    current_user: dict = Depends(get_current_user),
    approval_manager: ApprovalManager = Depends(get_approval_manager),
    project_repo: ProjectRepository = Depends(get_project_repository),
    deployment_agent: DeploymentAgent = Depends(get_deployment_agent),
    shell_agent: ShellAgent = Depends(get_shell_agent),
):
    """
    Approves a set of changes, merges the feature branch into main, and triggers the final deployment.
    """
    # Step 1: Validate Project and User
    # Note: In `get_project_by_id`, user_id needs to be a UUID, not a string.
    # The `get_current_user` dependency should return a user model with a UUID id.
    # Assuming `current_user.id` is the UUID.
    project = await project_repo.get_project_by_id(project_id, current_user.id)
    if not project:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found or you don't have access to it.")

    # Step 2: Validate Approval Token
    token = request.approval_token
    user_id_str = str(current_user.id)
    approval_request = await approval_manager.get_approval_request(token)

    if not approval_request or approval_request.user_id != user_id_str:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired approval token.",
        )

    # Step 3: Process the Approval
    success = await approval_manager.respond_to_approval(
        approval_id=token,
        user_id=user_id_str,
        decision=ApprovalStatus.APPROVED,
        comments="User approved preview deployment.",
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to process approval. The token may have been used already.",
        )

    # Step 4: Git Merge Logic
    branch_name = approval_request.preview_data.get('branch_name')
    commit_hash = approval_request.preview_data.get('commit_hash')
    if not branch_name:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Branch name not found in approval request.")

    try:
        project_workspace_path = project_repo.get_project_workspace_path(current_user.id, str(project.project_name))

        # Checkout main branch
        checkout_main_result = await shell_agent.execute({"command": ["git", "checkout", "main"], "cwd": project_workspace_path})
        if checkout_main_result.get("returncode") != 0:
            raise HTTPException(status_code=500, detail=f"Failed to checkout main branch: {checkout_main_result.get('stderr')}")

        # Merge feature branch
        merge_result = await shell_agent.execute({"command": ["git", "merge", branch_name], "cwd": project_workspace_path})
        if merge_result.get("returncode") != 0:
            # Handle merge conflicts if necessary, for now, we fail
            raise HTTPException(status_code=500, detail=f"Failed to merge branch '{branch_name}': {merge_result.get('stderr')}")

        # Optional: Push to remote
        # push_result = await shell_agent.execute({"command": ["git", "push", "origin", "main"], "cwd": project_workspace_path})
        # if push_result.get("returncode") != 0:
        #     logger.warning(f"Failed to push to remote for project {project_id}: {push_result.get('stderr')}")

        # Delete local feature branch
        delete_branch_result = await shell_agent.execute({"command": ["git", "branch", "-d", branch_name], "cwd": project_workspace_path})
        if delete_branch_result.get("returncode") != 0:
            logger.warning(f"Failed to delete local branch '{branch_name}': {delete_branch_result.get('stderr')}")

    except ProjectRepositoryError as e:
        raise HTTPException(status_code=500, detail=f"Failed to get project workspace: {e}")
    except Exception as e:
        # Log the exception and return a generic error
        logger.error(f"An error occurred during Git merge for project {project_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="An internal error occurred during the merge process.")


    # Step 5: Dispatch to DeploymentAgent to begin the final deployment.
    deployment_result = await deployment_agent.deploy_main_branch(
        project_id=str(project_id),
        project_hostname=project.project_hostname,
        commit_hash=commit_hash,
        project_workspace_path=project_workspace_path,
    )

    if not deployment_result.get("success"):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start final deployment: {deployment_result.get('error', 'Unknown deployment error')}",
        )

    logger.info(
        f"Final deployment started for project {project_id} after merging branch {branch_name}"
    )

    return {
        "status": "success",
        "message": "Approval received, branch merged, and final deployment started.",
        "deployment_id": deployment_result.get("deployment_id"),
    }


@router.post("/{project_id}/custom-domain")
async def add_custom_domain(
    project_id: UUID,
    request: CustomDomainRequest,
    current_user: dict = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository),
    deployment_agent: DeploymentAgent = Depends(get_deployment_agent),
):
    """
    Adds a custom domain to a project by creating a new Traefik route.
    """
    try:
        user_id = current_user.supabase_user_id
        project = await project_repo.get_project_by_id(project_id, user_id)
        if not project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")

        if not project.project_hostname:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Project does not have a hostname configured.")

        # Dispatch to the DeploymentAgent to handle the Traefik config creation
        result = await deployment_agent.add_custom_domain(
            project_hostname=project.project_hostname,
            custom_domain=request.custom_domain
        )

        if not result.get("success"):
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to add custom domain: {result.get('error', 'Unknown agent error')}")

        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "message": f"Custom domain '{request.custom_domain}' added successfully.",
                "router_name": result.get("router_name"),
            }
        )

    except ProjectRepositoryError as e:
        logger.error(f"Project repository error when adding custom domain for project {project_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    except HTTPException:
        # Re-raise HTTP exceptions to avoid them being caught by the generic one
        raise
    except Exception as e:
        logger.error(f"An unexpected error occurred while adding custom domain for project {project_id}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected internal error occurred.")


@router.post("/{project_id}/launch", response_model=LaunchProjectResponse)
async def launch_project_environment(
    project_id: UUID,
    current_user: dict = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository),
    docker_service: DockerWorkspaceService = Depends(get_docker_service),
):
    """
    Launches a dedicated code-server environment for a specific project.
    """
    try:
        user_id = current_user.id
        project = await project_repo.get_project_by_id(project_id, user_id)
        if not project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")

        if not project.docker_volume_name:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Project path not configured.")

        result = await docker_service.launch_project_environment(
            user_id=str(user_id),
            project_id=str(project.id),
            project_path=project.docker_volume_name
        )
        return LaunchProjectResponse(**result)

    except ProjectRepositoryError as e:
        logger.error(f"Project repository error when launching environment for project {project_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    except Exception as e:
        logger.error(f"An unexpected error occurred while launching environment for project {project_id}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected internal error occurred.")


@router.get("/environments", response_model=ProjectEnvironmentListResponse)
async def list_project_environments(
    current_user: dict = Depends(get_current_user),
    docker_service: DockerWorkspaceService = Depends(get_docker_service),
):
    """
    Lists all currently running project environments for the authenticated user.
    """
    try:
        user_id = current_user.id
        environments = await docker_service.list_project_environments(user_id=str(user_id))
        return ProjectEnvironmentListResponse(environments=environments)
    except Exception as e:
        logger.error(f"An unexpected error occurred while listing environments for user {current_user.id}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected internal error occurred.")


class DeployRequest(BaseModel):
    deployment_integration_id: UUID


@router.post("/{project_id}/deploy", status_code=status.HTTP_202_ACCEPTED)
async def deploy_project(
    project_id: UUID,
    request: DeployRequest,
    current_user: dict = Depends(get_current_user),
    project_repo: ProjectRepository = Depends(get_project_repository),
    deployment_agent: DeploymentAgent = Depends(get_deployment_agent),
    db: Session = Depends(get_db),
):
    """
    Triggers a new deployment for a project using a specified deployment integration.
    """
    try:
        user_id = current_user.supabase_user_id
        project = await project_repo.get_project_by_id(project_id, user_id)
        if not project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")

        integration = await project_repo.get_deployment_integration_by_id(
            str(request.deployment_integration_id), user_id, db
        )
        if not integration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Deployment integration not found"
            )

        project_workspace_path = project_repo.get_project_workspace_path(user_id, str(project.project_name))

        task_input = {
            "action": "external_deployment",
            "project_path": project_workspace_path,
            "integration": integration,
        }

        result = await deployment_agent.execute(task_input)

        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Deployment failed to start: {result.get('error')}",
            )

        return {"message": "Deployment started successfully", "url": result.get("data", {}).get("url")}

    except ProjectRepositoryError as e:
        logger.error(f"Project repository error during deployment for project {project_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    except Exception as e:
        logger.error(f"An unexpected error occurred during deployment for project {project_id}: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected internal error occurred.")
