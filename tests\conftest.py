"""
Consolidated pytest configuration for the project tests.

Provides:
- a session-scoped asyncio event loop (deterministic across platforms)
- conservative default timeout configuration (if pytest-timeout plugin installed)
- PYTHONPATH tweaks so tests can import the ai-orchestrator sources
- sensible default environment variables for fast unit tests
- a minimal FastAPI mock app and fixtures helpful for API-related tests
"""

import asyncio
import os
import sys
from pathlib import Path
from unittest.mock import Mock, patch

import pytest
from fastapi import FastAPI
from fastapi.responses import JSONResponse

# --- Path adjustments so tests can import library modules directly ---
project_root = Path(__file__).parent.parent
ai_orchestrator_path = project_root / "containers" / "ai-orchestrator"
src_path = ai_orchestrator_path / "src"

# Prepend to sys.path so tests resolve imports consistently
sys.path.insert(0, str(ai_orchestrator_path))
sys.path.insert(0, str(src_path))


# --- Environment defaults used during tests ---
os.environ.setdefault("TESTING", "1")
os.environ.setdefault("ENVIRONMENT", "testing")
os.environ.setdefault("USE_SUPABASE", "false")
os.environ.setdefault("ENABLE_ROLE_CONFIG_INIT", "false")
os.environ.setdefault("ENABLE_WEBSOCKET_CHAT", "false")
os.environ.setdefault("DATABASE_URL", "sqlite:///./test.db")
os.environ.setdefault("REDIS_URL", "redis://localhost:6379")
os.environ.setdefault("OLLAMA_BASE_URL", "http://localhost:11434")
os.environ.setdefault("CORS_ORIGINS", '["http://localhost:3000"]')
os.environ.setdefault("CORS_ALLOW_CREDENTIALS", "true")
os.environ.setdefault("CORS_ALLOW_METHODS", '["*"]')
os.environ.setdefault("CORS_ALLOW_HEADERS", '["*"]')

# Dummy secrets (ensure any secret length requirements are met)
os.environ.setdefault("POSTGRES_PASSWORD", "test_password")
os.environ.setdefault("JWT_SECRET", "test_jwt_secret_key_for_testing_only_32_chars_minimum")
os.environ.setdefault("SUPABASE_URL", "https://test.supabase.co")
os.environ.setdefault("SUPABASE_KEY", "test_supabase_key")
os.environ.setdefault("SUPABASE_ANON_KEY", "test_supabase_anon_key")
os.environ.setdefault("SUPABASE_SERVICE_KEY", "test_supabase_service_key")


@pytest.fixture(scope="session")
def event_loop():
    """Create a fresh event loop for the test session.

    Some test runners or Windows environments can reuse the wrong event loop
    which leads to deadlocks; creating a fresh loop per session is more
    deterministic.
    """
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


def pytest_configure(config):
    """Set a reasonable default timeout when pytest-timeout is installed.

    If pytest-timeout isn't present, this will silently no-op.
    """
    try:
        if hasattr(config, "option") and hasattr(config.option, "timeout"):
            # Only set if the user didn't pass a timeout explicitly
            if config.option.timeout is None:
                config.option.timeout = 30
    except Exception:
        # Be conservative: don't break test discovery if plugin not present
        pass


# --- Minimal FastAPI mock app and useful fixtures for tests ---
mock_app = FastAPI()


@mock_app.get("/health")
async def health():
    return {"status": "ok"}


@mock_app.get("/")
async def root():
    return {
        "message": "AI Coding Agent API",
        "version": "1.0.0",
        "status": "running",
    }


@mock_app.get("/api/agents")
async def agents():
    return JSONResponse(status_code=401, content={"detail": "Authentication required"})


@mock_app.get("/api/models")
async def models():
    return JSONResponse(status_code=401, content={"detail": "Authentication required"})


@mock_app.post("/auth/register")
async def auth_register():
    """Consolidated pytest configuration for the project tests.

    Provides:
    - a session-scoped asyncio event loop (deterministic across platforms)
    - conservative default timeout configuration (if pytest-timeout plugin installed)
    - PYTHONPATH tweaks so tests can import the ai-orchestrator sources
    - sensible default environment variables for fast unit tests
    - a minimal FastAPI mock app and fixtures helpful for API-related tests
    """

    import asyncio
    import os
    import sys
    from pathlib import Path
    from unittest.mock import Mock, patch

    import pytest
    from fastapi import FastAPI
    from fastapi.responses import JSONResponse

    # --- Path adjustments so tests can import library modules directly ---
    project_root = Path(__file__).parent.parent
    ai_orchestrator_path = project_root / "containers" / "ai-orchestrator"
    src_path = ai_orchestrator_path / "src"

    # Prepend to sys.path so tests resolve imports consistently
    sys.path.insert(0, str(ai_orchestrator_path))
    sys.path.insert(0, str(src_path))

    # --- Environment defaults used during tests ---
    os.environ.setdefault("TESTING", "1")
    os.environ.setdefault("ENVIRONMENT", "testing")
    os.environ.setdefault("USE_SUPABASE", "false")
    os.environ.setdefault("ENABLE_ROLE_CONFIG_INIT", "false")
    os.environ.setdefault("ENABLE_WEBSOCKET_CHAT", "false")
    os.environ.setdefault("DATABASE_URL", "sqlite:///./test.db")
    os.environ.setdefault("REDIS_URL", "redis://localhost:6379")
    os.environ.setdefault("OLLAMA_BASE_URL", "http://localhost:11434")
    os.environ.setdefault("CORS_ORIGINS", '["http://localhost:3000"]')
    os.environ.setdefault("CORS_ALLOW_CREDENTIALS", "true")
    os.environ.setdefault("CORS_ALLOW_METHODS", '["*"]')
    os.environ.setdefault("CORS_ALLOW_HEADERS", '["*"]')

    # Dummy secrets (ensure any secret length requirements are met)
    os.environ.setdefault("POSTGRES_PASSWORD", "test_password")
    os.environ.setdefault("JWT_SECRET", "test_jwt_secret_key_for_testing_only_32_chars_minimum")
    os.environ.setdefault("SUPABASE_URL", "https://test.supabase.co")
    os.environ.setdefault("SUPABASE_KEY", "test_supabase_key")
    os.environ.setdefault("SUPABASE_ANON_KEY", "test_supabase_anon_key")
    os.environ.setdefault("SUPABASE_SERVICE_KEY", "test_supabase_service_key")

    @pytest.fixture(scope="session")
    def event_loop():
        """Create a fresh event loop for the test session.

        Some test runners or Windows environments can reuse the wrong event loop
        which leads to deadlocks; creating a fresh loop per session is more
        deterministic.
        """
        loop = asyncio.get_event_loop_policy().new_event_loop()
        yield loop
        loop.close()

    def pytest_configure(config):
        """Set a reasonable default timeout when pytest-timeout is installed.

        If pytest-timeout isn't present, this will silently no-op.
        """
        try:
            if hasattr(config, "option") and hasattr(config.option, "timeout"):
                # Only set if the user didn't pass a timeout explicitly
                if config.option.timeout is None:
                    config.option.timeout = 30
        except Exception:
            # Be conservative: don't break test discovery if plugin not present
            pass

    # --- Minimal FastAPI mock app and useful fixtures for tests ---
    mock_app = FastAPI()

    @mock_app.get("/health")
    async def health():
        return {"status": "ok"}

    @mock_app.get("/")
    async def root():
        return {
            "message": "AI Coding Agent API",
            "version": "1.0.0",
            "status": "running",
        }

    @mock_app.get("/api/agents")
    async def agents():
        return JSONResponse(status_code=401, content={"detail": "Authentication required"})

    @mock_app.get("/api/models")
    async def models():
        return JSONResponse(status_code=401, content={"detail": "Authentication required"})

    @mock_app.post("/auth/register")
    async def auth_register():
        return {"message": "User registered successfully"}

    @mock_app.post("/auth/login")
    async def auth_login():
        return {"access_token": "test_token", "token_type": "bearer"}

    @mock_app.get("/auth/protected")
    async def auth_protected():
        return {"message": "Protected endpoint"}

    app = mock_app

    @pytest.fixture
    def client():
        """Create a test client for the FastAPI app"""
        from fastapi.testclient import TestClient

        client = TestClient(app)
        yield client

    @pytest.fixture
    def mock_supabase():
        """Mock Supabase client for testing"""
        with patch("src.utils.auth.supabase") as mock:
            yield mock

    @pytest.fixture
    def mock_current_user():
        """Mock current user for authentication tests"""
        user_mock = Mock()
        user_mock.id = "test-user-id"
        user_mock.email = "<EMAIL>"
        return user_mock


@mock_app.post("/auth/login")
async def auth_login():
    return {"access_token": "test_token", "token_type": "bearer"}


@mock_app.get("/auth/protected")
async def auth_protected():
    return {"message": "Protected endpoint"}


app = mock_app


@pytest.fixture
def client():
    """Create a test client for the FastAPI app"""
    from fastapi.testclient import TestClient

    client = TestClient(app)
    yield client


@pytest.fixture
def mock_supabase():
    """Mock Supabase client for testing"""
    with patch("src.utils.auth.supabase") as mock:
        yield mock


@pytest.fixture
def mock_current_user():
    """Mock current user for authentication tests"""
    user_mock = Mock()
    user_mock.id = "test-user-id"
    user_mock.email = "<EMAIL>"
    return user_mock
