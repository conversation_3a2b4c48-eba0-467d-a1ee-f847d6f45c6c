# Docker Proxy Container

HAProxy-based Docker API proxy for secure container management in AI Coding Agent.

## 🎯 Overview

This container provides a secure proxy for Docker API operations, enabling the AI Coding Agent to manage containers, networks, and volumes through a controlled interface with authentication and authorization.

## 🏗️ Architecture

### Features

- **Docker API Proxy**: Secure access to Docker daemon
- **Authentication**: JWT-based authentication for API access
- **Authorization**: Role-based access control for operations
- **Load Balancing**: Distribute requests across Docker nodes
- **SSL/TLS**: Encrypted communication with Docker daemon
- **Rate Limiting**: Prevent abuse and ensure fair usage
- **Audit Logging**: Complete audit trail of all operations
- **Health Monitoring**: Docker daemon and service health checks

### Supported Operations

- **Container Management**: Create, start, stop, remove containers
- **Image Management**: Pull, build, and manage Docker images
- **Network Management**: Create and manage Docker networks
- **Volume Management**: Handle persistent data volumes
- **Service Discovery**: Automatic service registration and discovery

## 🚀 Configuration

### Environment Variables

```bash
# Docker Connection
DOCKER_HOST=tcp://docker:2376
DOCKER_TLS_VERIFY=1
DOCKER_CERT_PATH=/etc/docker/certs

# Proxy Configuration
PROXY_PORT=2375
PROXY_SSL_PORT=2376
PROXY_MAX_CONNECTIONS=100

# Authentication
JWT_SECRET=your-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Authorization
ADMIN_USERS=<EMAIL>,<EMAIL>
READONLY_USERS=<EMAIL>

# Security
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
CLIENT_TIMEOUT=30
```

### HAProxy Configuration

```haproxy
global
    log /dev/log local0
    log /dev/log local1 notice
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin expose-fd listeners
    stats timeout 30s
    user haproxy
    group haproxy
    daemon

defaults
    log global
    mode tcp
    option tcplog
    timeout connect 5000
    timeout client 50000
    timeout server 50000

frontend docker_api
    bind *:2375
    bind *:2376 ssl crt /etc/ssl/certs/proxy.pem
    mode tcp
    option tcplog

    # Rate limiting
    stick-table type ip size 100k expire 1m store gpc0
    tcp-request connection track-sc0 src
    tcp-request connection reject if { sc0_gpc0 gt 10 }

    default_backend docker_daemon

backend docker_daemon
    mode tcp
    option tcplog
    server docker docker:2376 check ssl verify none
```

## 🔧 Usage

### Building the Container

```bash
docker build -t ai-coding-agent-docker-proxy .
```

### Running with Docker Compose

```bash
docker-compose up docker-proxy
```

### Testing the Proxy

```bash
# Test basic connectivity
curl -H "Authorization: Bearer <jwt-token>" http://localhost:2375/_ping

# List containers
curl -H "Authorization: Bearer <jwt-token>" http://localhost:2375/containers/json

# Get container logs
curl -H "Authorization: Bearer <jwt-token>" http://localhost:2375/containers/mycontainer/logs?stdout=1
```

## 🔒 Security Features

### JWT Authentication

```python
# Authentication middleware
def authenticate_request(request):
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None

    token = auth_header.split(' ')[1]
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload.get('sub')
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None
```

### Role-Based Authorization

```python
# Authorization matrix
PERMISSIONS = {
    'admin': ['*'],  # All permissions
    'developer': [
        'GET /containers/*',
        'POST /containers/*/start',
        'POST /containers/*/stop',
        'GET /images/*',
        'POST /images/create'
    ],
    'viewer': [
        'GET /containers/json',
        'GET /images/json',
        'GET /networks',
        'GET /volumes'
    ]
}
```

### Audit Logging

```python
# Audit log entry
audit_entry = {
    'timestamp': datetime.utcnow().isoformat(),
    'user': authenticated_user,
    'method': request.method,
    'path': request.path,
    'query': dict(request.query_params),
    'client_ip': request.client.host,
    'user_agent': request.headers.get('User-Agent'),
    'response_status': response.status_code,
    'response_size': len(response.body) if response.body else 0
}
```

## 📊 Monitoring & Health Checks

### Health Endpoints

```bash
# Proxy health
curl http://localhost:2375/_ping

# Docker daemon health
curl -H "Authorization: Bearer <jwt-token>" http://localhost:2375/_ping

# Detailed health check
curl http://localhost:2375/health
```

### Metrics Collection

```python
# Prometheus metrics
METRICS = {
    'requests_total': Counter('proxy_requests_total', 'Total requests', ['method', 'endpoint', 'status']),
    'request_duration': Histogram('proxy_request_duration_seconds', 'Request duration', ['method', 'endpoint']),
    'active_connections': Gauge('proxy_active_connections', 'Active connections'),
    'rate_limit_hits': Counter('proxy_rate_limit_hits', 'Rate limit hits', ['client_ip']),
    'auth_failures': Counter('proxy_auth_failures', 'Authentication failures', ['reason'])
}
```

### Logging Configuration

```python
# Structured logging
logging.config.dictConfig({
    'version': 1,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        },
        'json': {
            'format': '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "message": "%(message)s"}'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'detailed',
            'level': 'INFO'
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': '/var/log/docker-proxy/audit.log',
            'formatter': 'json',
            'level': 'INFO'
        }
    }
})
```

## ⚖️ Load Balancing & High Availability

### Multi-Node Setup

```yaml
version: '3.8'
services:
  docker-proxy:
    image: ai-coding-agent-docker-proxy
    ports:
      - "2375:2375"
      - "2376:2376"
    environment:
      - DOCKER_HOST=tcp://docker-swarm:2376
    networks:
      - docker-swarm
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure

networks:
  docker-swarm:
    external: true
```

### Failover Configuration

```haproxy
backend docker_cluster
    mode tcp
    balance roundrobin
    option tcplog

    server docker-01 docker-01:2376 check ssl verify none
    server docker-02 docker-02:2376 check ssl verify none backup
    server docker-03 docker-03:2376 check ssl verify none backup
```

## 🐛 Troubleshooting

### Common Issues

1. **Connection Refused**
   - Verify Docker daemon is running
   - Check network connectivity
   - Validate SSL certificates

2. **Authentication Failures**
   - Verify JWT token validity
   - Check user permissions
   - Review audit logs for details

3. **Rate Limiting**
   - Monitor request patterns
   - Adjust rate limit settings
   - Implement exponential backoff

### Debug Commands

```bash
# Check proxy logs
docker logs docker-proxy

# Test Docker connectivity
docker exec docker-proxy nc -zv docker 2376

# Validate configuration
docker exec docker-proxy haproxy -c -f /etc/haproxy/haproxy.cfg

# Check active connections
docker exec docker-proxy ss -tlnp | grep :2375
```

## 📚 Additional Resources

- [HAProxy Documentation](https://www.haproxy.org/)
- [Docker API Reference](https://docs.docker.com/engine/api/)
- [JWT Authentication](https://jwt.io/)
- [Docker Swarm](https://docs.docker.com/engine/swarm/)

## 🤝 Contributing

When modifying this container:

1. Test authentication and authorization thoroughly
2. Validate Docker API compatibility
3. Update audit logging for new operations
4. Test load balancing with multiple Docker nodes
5. Monitor security implications of configuration changes
