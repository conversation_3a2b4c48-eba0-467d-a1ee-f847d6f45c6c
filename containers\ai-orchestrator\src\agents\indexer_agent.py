"""
Sequential Agents - IndexerAgent

Handles the "store & index" part of the "Architect-thinks, Indexer-stores" pattern.
Persists improved roadmaps and generates embeddings for efficient RAG retrieval.
"""
from __future__ import annotations

import uuid
import logging
from typing import Dict, Any

import orjson
from src.agents.base_agent import BaseAgent, ValidationResult
from src.services.supabase_service import SupabaseService
from src.services.vector_service import VectorStorageService

logger = logging.getLogger(__name__)


class IndexerAgent(BaseAgent):
    """
    Agent responsible for storing improved roadmaps and generating embeddings.

    This agent handles:
    - Creating new roadmap versions
    - Generating concise summaries for each phase
    - Creating embeddings for RAG retrieval
    - Storing source references for audit trail
    """

    def __init__(
        self,
        supabase_service: SupabaseService,
        vector_service: VectorStorageService,
    ) -> None:
        """
        Initialize IndexerAgent with dependency injection.
        """
        super().__init__()
        self.supabase_service = supabase_service
        self.vector_service = vector_service

    async def _execute_core(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Store improved roadmap and generate embeddings within a single transaction.

        Args:
            task_input: Dictionary containing:
                - roadmap_id: UUID of the parent roadmap. Can be None for a new roadmap.
                - improved_roadmap: JSON structure from Architect
                - source_refs: List of source references
                - owner_id: UUID of the owner
                - project_id: UUID of the project (required if roadmap_id is None)

        Returns:
            Dict with success status and new_version number
        """
        roadmap_id = task_input.get("roadmap_id")
        improved_roadmap = task_input.get("improved_roadmap")
        source_refs = task_input.get("source_refs", [])
        owner_id = task_input.get("owner_id")

        try:
            async with self.supabase_service.get_db_connection() as conn:
                async with conn.transaction():
                    # Step 1: Determine Project ID and Version
                    parent_id = None
                    if roadmap_id:
                        existing_roadmap = await conn.fetchrow(
                            'SELECT project_id, version FROM roadmaps WHERE id = $1 AND owner_id = $2',
                            roadmap_id, owner_id
                        )
                        if not existing_roadmap:
                            return {"success": False, "error": f"Roadmap {roadmap_id} not found or access denied."}

                        project_id = existing_roadmap['project_id']
                        new_version = existing_roadmap.get('version', 1) + 1
                        parent_id = roadmap_id
                    else:
                        project_id = task_input.get("project_id")
                        if not project_id:
                            raise ValueError("project_id is required for a new roadmap.")
                        new_version = 1

                    # Step 2: Create a new version of the roadmap
                    new_roadmap_id = str(uuid.uuid4())
                    await conn.execute(
                        """
                        INSERT INTO roadmaps (id, project_id, parent_id, owner_id, title, content, version, status)
                        VALUES ($1, $2, $3, $4, $5, $6, $7, 'active')
                        """,
                        new_roadmap_id,
                        project_id,
                        parent_id,
                        owner_id,
                        improved_roadmap.get("title", f"Roadmap v{new_version}"),
                        orjson.dumps(improved_roadmap),
                        new_version,
                    )

                    # Step 3: Generate and insert summaries and embeddings
                    phases = improved_roadmap.get('phases', [])
                    for phase in phases:
                        phase_title = phase.get('title', 'Untitled Phase')
                        summary_text = await self._generate_phase_summary(phase_title, phase.get('summary', ''))

                        embedding = await self.vector_service.generate_embedding(summary_text)

                        await conn.execute(
                            """
                            INSERT INTO roadmap_summaries (id, roadmap_id, owner_id, phase_title, summary_text, embedding)
                            VALUES ($1, $2, $3, $4, $5, $6)
                            """,
                            str(uuid.uuid4()),
                            new_roadmap_id,
                            owner_id,
                            phase_title,
                            summary_text,
                            embedding,
                        )

                    # Step 4: Insert source references
                    for ref in source_refs:
                        await conn.execute(
                            """
                            INSERT INTO roadmap_source_references (id, roadmap_id, owner_id, source_document_id, excerpt, retrieval_score)
                            VALUES ($1, $2, $3, $4, $5, $6)
                            """,
                            str(uuid.uuid4()),
                            new_roadmap_id,
                            owner_id,
                            ref.get('document_id'),
                            ref.get('excerpt'),
                            ref.get('score'),
                        )

            # Step 5: Return success response
            return {
                "success": True,
                "new_version": new_version,
                "roadmap_id": new_roadmap_id,
                "message": f"Successfully indexed roadmap version {new_version}"
            }

        except Exception as e:
            logger.error(f"IndexerAgent execution failed: {str(e)}", exc_info=True)
            return {"success": False, "error": f"Indexing failed: {str(e)}"}

    async def _generate_phase_summary(self, title: str, content: str) -> str:
        """
        Generate a concise summary for a roadmap phase.
        """
        # This can be enhanced with an LLM call in the future
        return f"Phase: {title}. Summary: {content}"

    async def _validate_agent_specific_prerequisites(self, task_input: Dict[str, Any]) -> ValidationResult:
        """Validate IndexerAgent specific prerequisites."""
        if not task_input.get("owner_id"):
            return ValidationResult.failure("Missing required field: owner_id")

        if not task_input.get("roadmap_id") and not task_input.get("project_id"):
            return ValidationResult.failure("Missing required field: roadmap_id or project_id")

        improved_roadmap = task_input.get("improved_roadmap")
        if not isinstance(improved_roadmap, dict) or 'phases' not in improved_roadmap:
            return ValidationResult.failure("improved_roadmap must be a dict with a 'phases' key.")

        return ValidationResult.success("IndexerAgent prerequisites validated")

    async def _validate_agent_specific_completion(self, task_input: Dict[str, Any], result: Dict[str, Any]) -> ValidationResult:
        """Validate IndexerAgent specific completion."""
        if not result.get("success"):
            return ValidationResult.failure(f"Indexing operation failed: {result.get('error')}")

        if "new_version" not in result or "roadmap_id" not in result:
            return ValidationResult.failure("Required fields 'new_version' or 'roadmap_id' not in result.")

        return ValidationResult.success("Indexing completed successfully")
