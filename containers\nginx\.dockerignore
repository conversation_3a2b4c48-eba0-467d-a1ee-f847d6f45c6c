# Nginx Reverse Proxy .dockerignore
# Exclude unnecessary files to reduce build context and improve security

# Version control
.git
.gitignore
.github

# Documentation
*.md
docs/
README*
CHANGELOG*
CONTRIBUTING*

# Development files
.env*
*.log
*.tmp
.cache/
.vscode/
.idea/

# Docker files (avoid recursive copying)
Dockerfile*
docker-compose*
.dockerignore

# Testing files
test*/
tests/
*.test.*
*_test.*
pytest.ini
conftest.py

# Python-specific (if any)
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Node.js specific (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.old
*.orig
*.swp
*.swo
*~

# SSL certificates (should be mounted as volumes)
ssl/
certbot/

# Temporary files
tmp/
temp/
