import NextAuth, { Account, NextAuthOptions, Session, User } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import axios from "axios";
import nextAuthOptions from "@/lib/nextAuth";

// Ensure Node.js runtime for this route (avoids Edge runtime issues)
export const runtime = "nodejs";

/**
 * Runtime environment variable validation helper
 * Ensures critical authentication secrets are present at startup
 */
function requireEnv(name: string): string {
  const value = process.env[name];
  if (!value) {
    throw new Error(`FATAL: Missing required environment variable: ${name}`);
  }
  return value;
}

// Validate critical environment variables in production
if (process.env.NODE_ENV === "production") {
  requireEnv("NEXTAUTH_SECRET");
  requireEnv("NEXTAUTH_URL");

  // Only require Google OAuth env vars if both are provided (indicating intent to use Google)
  if (process.env.GOOGLE_CLIENT_ID || process.env.GOOGLE_CLIENT_SECRET) {
    requireEnv("GOOGLE_CLIENT_ID");
    requireEnv("GOOGLE_CLIENT_SECRET");
  }

  // Only require GitHub OAuth env vars if both are provided (indicating intent to use GitHub)
  if (process.env.GITHUB_CLIENT_ID || process.env.GITHUB_CLIENT_SECRET) {
    requireEnv("GITHUB_CLIENT_ID");
    requireEnv("GITHUB_CLIENT_SECRET");
  }
}

// Backend base URL (container-to-container or public). Prefer internal first.
const API_BASE_URL = process.env.API_BASE_URL ||
  process.env.NEXT_PUBLIC_API_BASE_URL ||
  "http://ai-orchestrator:8000";

// Lightweight flexible object type to avoid `any` in casts
type LooseObject = Record<string, unknown>;

// Base providers from centralized options (typed to avoid `any`)
const baseProviders: any[] = Array.isArray(nextAuthOptions.providers)
  ? (nextAuthOptions.providers as any[])
  : [];

// Build a complete authOptions object by merging the centralized options
// with server-specific providers (Credentials) and callbacks used for
// backend token exchange and session shaping.
export const authOptions: NextAuthOptions = {
  ...nextAuthOptions,
  providers: [
    // Preserve any providers defined centrally
    ...baseProviders,
    // Add Credentials provider for email/password auth against backend
    Credentials({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, _req): Promise<User | null> {
        if (!credentials?.email || !credentials.password) return null;
        try {
          const response = await axios.post(
            `${API_BASE_URL}/token`,
            {
              username: credentials.email,
              password: credentials.password,
            },
            { timeout: 8000 },
          );

          const data = response.data;
          if (!data?.access_token) return null;

          return {
            id: data.user?.id || credentials.email,
            email: credentials.email,
            name: data.user?.name || credentials.email.split("@")[0],
            accessToken: data.access_token,
            tokenType: data.token_type || "bearer",
            raw: data.user || null,
          } as User;
        } catch (err) {
          return null;
        }
      },
    }),
  ],
  callbacks: {
    // Preserve any centrally defined callbacks, then extend/override
    ...(nextAuthOptions.callbacks || {}),
    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      try {
        const urlObj = new URL(url);
        const baseUrlObj = new URL(baseUrl);
        if (urlObj.origin === baseUrlObj.origin) {
          return url;
        }
        return `${baseUrl}/dashboard`;
      } catch {
        return `${baseUrl}/dashboard`;
      }
    },
    async jwt(
      { token, user, account }: {
        token: LooseObject;
        user?: User | null;
        account?: Account | null;
      },
    ) {
      // Google
      if (account?.provider === "google" && account.access_token) {
        (token as unknown as LooseObject).accessToken = account
          .access_token as unknown;
        (token as unknown as LooseObject).refreshToken = account
          .refresh_token as unknown;
        (token as unknown as LooseObject).provider = "google" as unknown;
        (token as unknown as LooseObject).googleUser =
          user as unknown as LooseObject;

        // Best-effort backend sync
        try {
          const backendResponse = await axios.post(
            `${API_BASE_URL}/auth/google`,
            {
              access_token: account.access_token,
              user,
              google_account: account,
            },
            {
              timeout: 8000,
              headers: { Authorization: `Bearer ${account.access_token}` },
            },
          );
          if (backendResponse.data?.success) {
            (token as unknown as LooseObject).backendUser = backendResponse.data
              .user as unknown;
            (token as unknown as LooseObject).backendToken = backendResponse
              .data.access_token as unknown;
          }
        } catch {
          // ignore
        }
      }

      // GitHub
      if (account?.provider === "github" && account.access_token) {
        (token as unknown as LooseObject).accessToken = account
          .access_token as unknown;
        (token as unknown as LooseObject).refreshToken = account
          .refresh_token as unknown;
        (token as unknown as LooseObject).provider = "github" as unknown;
        (token as unknown as LooseObject).githubUser =
          user as unknown as LooseObject;
        try {
          const backendResponse = await axios.post(
            `${API_BASE_URL}/auth/github`,
            {
              access_token: account.access_token,
              user,
              github_account: account,
            },
            {
              timeout: 8000,
              headers: { Authorization: `Bearer ${account.access_token}` },
            },
          );
          if (backendResponse.data?.success) {
            (token as unknown as LooseObject).backendUser = backendResponse.data
              .user as unknown;
            (token as unknown as LooseObject).backendToken = backendResponse
              .data.access_token as unknown;
          }
        } catch {
          // ignore
        }
      }

      // Credentials
      if (user && account?.provider === "credentials") {
        (token as unknown as LooseObject).accessToken =
          (user as unknown as LooseObject).accessToken as unknown;
        (token as unknown as LooseObject).tokenType =
          (user as unknown as LooseObject).tokenType as unknown;
        (token as unknown as LooseObject).rawUser =
          (user as unknown as LooseObject).raw as unknown;
        (token as unknown as LooseObject).provider = "credentials" as unknown;
      }

      return token;
    },
    async session(
      { session, token }: { session: Session; token: LooseObject },
    ) {
      (session as unknown as LooseObject).provider =
        (token as unknown as LooseObject).provider;

      if ((token as unknown as LooseObject).provider === "google") {
        (session as unknown as LooseObject).accessToken =
          (token as unknown as LooseObject).backendToken ||
          (token as unknown as LooseObject).accessToken;
        (session as unknown as LooseObject).refreshToken =
          (token as unknown as LooseObject).refreshToken;
        (session as unknown as LooseObject).tokenType = "bearer" as unknown;
        (session as unknown as LooseObject).user =
          (token as unknown as LooseObject).backendUser ||
          (token as unknown as LooseObject).googleUser || session.user;
      }

      if ((token as unknown as LooseObject).provider === "github") {
        (session as unknown as LooseObject).accessToken =
          (token as unknown as LooseObject).backendToken ||
          (token as unknown as LooseObject).accessToken;
        (session as unknown as LooseObject).refreshToken =
          (token as unknown as LooseObject).refreshToken;
        (session as unknown as LooseObject).tokenType = "bearer" as unknown;
        (session as unknown as LooseObject).user =
          (token as unknown as LooseObject).backendUser ||
          (token as unknown as LooseObject).githubUser || session.user;
      }

      if ((token as unknown as LooseObject).provider === "credentials") {
        (session as unknown as LooseObject).accessToken =
          (token as unknown as LooseObject).accessToken;
        (session as unknown as LooseObject).tokenType =
          (token as unknown as LooseObject).tokenType;
        (session as unknown as LooseObject).user =
          (token as unknown as LooseObject).rawUser
            ? (token as unknown as LooseObject).rawUser
            : session.user;
      }

      return session;
    },
    async signIn() {
      return true;
    },
  },
  cookies: process.env.NODE_ENV === "production"
    ? {
      sessionToken: {
        name: `__Secure-next-auth.session-token`,
        options: { httpOnly: true, sameSite: "lax", path: "/", secure: true },
      },
    }
    : {},
};

const handler = NextAuth(authOptions as NextAuthOptions);
export { handler as GET, handler as POST };
