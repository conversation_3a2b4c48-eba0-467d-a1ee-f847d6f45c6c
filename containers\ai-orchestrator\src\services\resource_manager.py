"""
Resource Management Service for AI Coding Agent

Manages resource limits, cleanup, and monitoring for isolated project environments.
"""

import asyncio
import logging
import docker
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import psutil

logger = logging.getLogger(__name__)


@dataclass
class ResourceLimits:
    """Resource limits for a project."""
    cpu_limit: str = "1.0"  # CPU cores
    memory_limit: str = "512m"  # Memory limit
    disk_limit: str = "2g"  # Disk space limit
    max_containers: int = 5  # Max containers per project
    max_networks: int = 2  # Max networks per project
    max_volumes: int = 3  # Max volumes per project


@dataclass
class ProjectResources:
    """Current resource usage for a project."""
    project_name: str
    user_id: str
    cpu_usage: float
    memory_usage: int  # bytes
    disk_usage: int  # bytes
    container_count: int
    network_count: int
    volume_count: int
    last_activity: datetime
    status: str  # 'active', 'idle', 'abandoned'


class ResourceManager:
    """Manages resources for project environments."""
    
    def __init__(self):
        self.docker_client = None
        self.resource_limits = ResourceLimits()
        self.project_resources: Dict[str, ProjectResources] = {}
        self.cleanup_lock = asyncio.Lock()
        
        # Initialize Docker client
        try:
            self.docker_client = docker.from_env()
        except Exception as e:
            logger.warning(f"Docker client initialization failed: {e}")
    
    def get_default_resource_limits(self, template_type: str = "webapp") -> ResourceLimits:
        """Get default resource limits based on template type."""
        limits_map = {
            "webapp": ResourceLimits(cpu_limit="1.0", memory_limit="512m", disk_limit="2g"),
            "microservice": ResourceLimits(cpu_limit="0.5", memory_limit="256m", disk_limit="1g"),
            "react-frontend": ResourceLimits(cpu_limit="0.5", memory_limit="256m", disk_limit="1g"),
            "nextjs-app": ResourceLimits(cpu_limit="0.8", memory_limit="512m", disk_limit="1.5g"),
        }
        return limits_map.get(template_type, self.resource_limits)
    
    def generate_docker_compose_resources(self, limits: ResourceLimits) -> Dict[str, Any]:
        """Generate Docker Compose resource configuration."""
        return {
            "deploy": {
                "resources": {
                    "limits": {
                        "cpus": limits.cpu_limit,
                        "memory": limits.memory_limit
                    },
                    "reservations": {
                        "cpus": str(float(limits.cpu_limit) * 0.5),
                        "memory": str(int(limits.memory_limit.rstrip('mMgG')) // 2) + limits.memory_limit[-1]
                    }
                }
            }
        }
    
    async def monitor_project_resources(self, user_id: str, project_name: str) -> Optional[ProjectResources]:
        """Monitor current resource usage for a project."""
        if not self.docker_client:
            return None
        
        try:
            project_prefix = f"{project_name}-"
            containers = self.docker_client.containers.list(
                filters={"name": project_prefix}
            )
            
            total_cpu = 0.0
            total_memory = 0
            container_count = len(containers)
            
            for container in containers:
                try:
                    stats = container.stats(stream=False)
                    
                    # Calculate CPU usage
                    cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                               stats['precpu_stats']['cpu_usage']['total_usage']
                    system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                                  stats['precpu_stats']['system_cpu_usage']
                    
                    if system_delta > 0:
                        cpu_percent = (cpu_delta / system_delta) * 100.0
                        total_cpu += cpu_percent
                    
                    # Memory usage
                    memory_usage = stats['memory_stats'].get('usage', 0)
                    total_memory += memory_usage
                    
                except Exception as e:
                    logger.warning(f"Failed to get stats for container {container.name}: {e}")
            
            # Get disk usage
            disk_usage = await self._get_project_disk_usage(user_id, project_name)
            
            # Count networks and volumes
            networks = self.docker_client.networks.list(
                filters={"name": f"{project_name}-network"}
            )
            volumes = self.docker_client.volumes.list(
                filters={"name": project_name}
            )
            
            resources = ProjectResources(
                project_name=project_name,
                user_id=user_id,
                cpu_usage=total_cpu,
                memory_usage=total_memory,
                disk_usage=disk_usage,
                container_count=container_count,
                network_count=len(networks),
                volume_count=len(volumes),
                last_activity=datetime.utcnow(),
                status="active" if container_count > 0 else "idle"
            )
            
            self.project_resources[f"{user_id}:{project_name}"] = resources
            return resources
            
        except Exception as e:
            logger.error(f"Failed to monitor project resources: {e}")
            return None
    
    async def _get_project_disk_usage(self, user_id: str, project_name: str) -> int:
        """Get disk usage for a project directory."""
        try:
            # This would integrate with ProjectRepository to get project path
            from src.repository.project_repository import ProjectRepository
            repo = ProjectRepository()
            user_workspace = await repo.get_user_workspace_path(user_id)
            project_path = user_workspace / project_name
            
            if project_path.exists():
                total_size = 0
                for file_path in project_path.rglob("*"):
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
                return total_size
            return 0
            
        except Exception as e:
            logger.warning(f"Failed to get disk usage: {e}")
            return 0
    
    async def check_resource_limits(self, user_id: str, project_name: str, limits: ResourceLimits) -> Dict[str, bool]:
        """Check if project is within resource limits."""
        resources = await self.monitor_project_resources(user_id, project_name)
        if not resources:
            return {"within_limits": True}
        
        checks = {
            "cpu_within_limit": resources.cpu_usage <= float(limits.cpu_limit) * 100,
            "memory_within_limit": resources.memory_usage <= self._parse_memory_limit(limits.memory_limit),
            "disk_within_limit": resources.disk_usage <= self._parse_disk_limit(limits.disk_limit),
            "container_count_ok": resources.container_count <= limits.max_containers,
            "network_count_ok": resources.network_count <= limits.max_networks,
            "volume_count_ok": resources.volume_count <= limits.max_volumes
        }
        
        checks["within_limits"] = all(checks.values())
        return checks
    
    def _parse_memory_limit(self, limit: str) -> int:
        """Parse memory limit string to bytes."""
        limit = limit.lower()
        if limit.endswith('g'):
            return int(limit[:-1]) * 1024 * 1024 * 1024
        elif limit.endswith('m'):
            return int(limit[:-1]) * 1024 * 1024
        elif limit.endswith('k'):
            return int(limit[:-1]) * 1024
        else:
            return int(limit)
    
    def _parse_disk_limit(self, limit: str) -> int:
        """Parse disk limit string to bytes."""
        return self._parse_memory_limit(limit)
    
    async def cleanup_abandoned_projects(self, max_idle_days: int = 7) -> List[str]:
        """Clean up projects that have been idle for too long."""
        async with self.cleanup_lock:
            cleaned_projects = []
            cutoff_time = datetime.utcnow() - timedelta(days=max_idle_days)
            
            for project_key, resources in list(self.project_resources.items()):
                if resources.last_activity < cutoff_time and resources.status == "idle":
                    user_id, project_name = project_key.split(":", 1)
                    
                    try:
                        # Stop and remove containers
                        await self._cleanup_project_containers(project_name)
                        
                        # Remove networks
                        await self._cleanup_project_networks(project_name)
                        
                        # Remove volumes (optional - may want to preserve data)
                        # await self._cleanup_project_volumes(project_name)
                        
                        # Mark as abandoned
                        resources.status = "abandoned"
                        cleaned_projects.append(project_name)
                        
                        logger.info(f"Cleaned up abandoned project: {project_name}")
                        
                    except Exception as e:
                        logger.error(f"Failed to cleanup project {project_name}: {e}")
            
            return cleaned_projects
    
    async def _cleanup_project_containers(self, project_name: str):
        """Stop and remove all containers for a project."""
        if not self.docker_client:
            return
        
        containers = self.docker_client.containers.list(
            all=True,
            filters={"name": f"{project_name}-"}
        )
        
        for container in containers:
            try:
                container.stop(timeout=10)
                container.remove()
                logger.info(f"Removed container: {container.name}")
            except Exception as e:
                logger.warning(f"Failed to remove container {container.name}: {e}")
    
    async def _cleanup_project_networks(self, project_name: str):
        """Remove networks for a project."""
        if not self.docker_client:
            return
        
        networks = self.docker_client.networks.list(
            filters={"name": f"{project_name}-network"}
        )
        
        for network in networks:
            try:
                network.remove()
                logger.info(f"Removed network: {network.name}")
            except Exception as e:
                logger.warning(f"Failed to remove network {network.name}: {e}")
    
    async def _cleanup_project_volumes(self, project_name: str):
        """Remove volumes for a project."""
        if not self.docker_client:
            return
        
        volumes = self.docker_client.volumes.list(
            filters={"name": project_name}
        )
        
        for volume in volumes:
            try:
                volume.remove()
                logger.info(f"Removed volume: {volume.name}")
            except Exception as e:
                logger.warning(f"Failed to remove volume {volume.name}: {e}")
    
    def get_system_resource_stats(self) -> Dict[str, Any]:
        """Get overall system resource statistics."""
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent
            },
            "active_projects": len([r for r in self.project_resources.values() if r.status == "active"])
        }


# Global resource manager instance
resource_manager = ResourceManager()


def get_resource_manager() -> ResourceManager:
    """Get the global resource manager instance."""
    return resource_manager
