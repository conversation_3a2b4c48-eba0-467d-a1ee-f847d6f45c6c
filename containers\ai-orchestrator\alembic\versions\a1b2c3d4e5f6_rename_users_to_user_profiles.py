"""rename_users_to_user_profiles

Revision ID: a1b2c3d4e5f6
Revises: add_storage_mgmt
Create Date: 2025-09-05 17:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a1b2c3d4e5f6'
down_revision: Union[str, None] = 'add_storage_mgmt'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Rename users table to user_profiles
    op.rename_table('users', 'user_profiles')


def downgrade() -> None:
    # Rename user_profiles table back to users
    op.rename_table('user_profiles', 'users')
