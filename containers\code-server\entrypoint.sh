#!/bin/bash
set -e

# Enhanced entrypoint script for code-server with better initialization
# Author: AI Coding Agent Team
# Version: 1.0.0

echo "[INFO] Starting code-server initialization..."

# Function to log with timestamp
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Function to handle graceful shutdown
cleanup() {
    log "INFO: Received shutdown signal, cleaning up..."
    # Kill any background processes
    jobs -p | xargs -r kill
    log "INFO: Cleanup completed"
    exit 0
}

# Set up signal handlers for graceful shutdown
trap cleanup SIGTERM SIGINT

# Ensure proper permissions on home directory
log "INFO: Setting up directory permissions..."
mkdir -p /home/<USER>/.config/code-server
mkdir -p /home/<USER>/.local/share/code-server
mkdir -p /home/<USER>/workspace

# Set proper ownership (in case volumes are mounted)
CURRENT_UID=$(id -u)
CURRENT_GID=$(id -g)
CURRENT_USER=$(whoami)

log "INFO: Running as user: $CURRENT_USER (UID: $CURRENT_UID, GID: $CURRENT_GID)"

# If running as root, switch to coder user (if gosu is available)
if [ "$CURRENT_UID" = "0" ]; then
    log "WARNING: Running as root"
    if command -v gosu >/dev/null 2>&1; then
        log "INFO: Switching to coder user with gosu..."
        chown -R coder:coder /home/<USER>
        exec gosu coder "$0" "$@"
    else
        log "WARNING: gosu not available, continuing as root (not recommended)"
    fi
else
    log "INFO: Running as non-root user, proceeding..."
fi

# Handle password configuration
if [ -n "$PASSWORD_FILE" ] && [ -f "$PASSWORD_FILE" ]; then
    log "INFO: Using password from secrets file"
    export PASSWORD=$(cat "$PASSWORD_FILE")
elif [ -z "$PASSWORD" ]; then
    log "WARNING: No password configured, using default"
    export PASSWORD="secure-default-password"
fi

# Verify code-server installation
if ! command -v code-server >/dev/null 2>&1; then
    log "ERROR: code-server not found in PATH"
    exit 1
fi

# Install additional extensions if extensions.json is present
if [ -f "/home/<USER>/extensions.json" ]; then
    log "INFO: Installing VS Code extensions..."
    if [ -f "/home/<USER>/install-extensions.sh" ]; then
        bash /home/<USER>/install-extensions.sh || log "WARNING: Extension installation failed"
    fi
fi

# Create a health check endpoint
create_healthz() {
    cat > /tmp/healthz.sh << 'EOF'
#!/bin/bash
# Simple health check for code-server
if curl -f -s http://localhost:8080/login >/dev/null 2>&1; then
    echo "OK"
    exit 0
else
    echo "FAIL"
    exit 1
fi
EOF
    chmod +x /tmp/healthz.sh
}

create_healthz

log "INFO: Starting code-server with configuration:"
log "INFO: - Bind address: ${CODE_SERVER_BIND_ADDR:-0.0.0.0:8080}"
log "INFO: - Auth method: ${CODE_SERVER_AUTH:-password}"
log "INFO: - User: $(whoami) ($(id))"
log "INFO: - Home: $HOME"
log "INFO: - Workspace: /home/<USER>/workspace"

# Start code-server in background to handle signals properly
log "INFO: Starting code-server..."
exec code-server \
    --bind-addr "${CODE_SERVER_BIND_ADDR:-0.0.0.0:8080}" \
    --auth "${CODE_SERVER_AUTH:-password}" \
    --disable-telemetry \
    --disable-update-check \
    --disable-getting-started-override \
    --user-data-dir /home/<USER>/.local/share/code-server \
    --config /home/<USER>/.config/code-server/config.yaml \
    /home/<USER>/workspace \
    "$@"
