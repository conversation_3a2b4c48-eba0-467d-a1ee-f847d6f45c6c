#!/usr/bin/env python3
"""
Mock classes and functions for testing without external dependencies.
"""

import uuid
from typing import Dict, Any, Optional
from datetime import datetime
from unittest.mock import Mock


class MockProject:
    """Mock project class for testing."""

    def __init__(self, project_id: str = None, name: str = "test-project"):
        self.id = project_id or str(uuid.uuid4())
        self.name = name
        self.description = "Test project for export testing"
        self.owner_id = "test-user-123"
        self.status = "active"
        self.created_at = datetime.now()
        self.updated_at = datetime.now()


class MockUser:
    """Mock user class for testing."""

    def __init__(self, user_id: str = None):
        self.id = user_id or "test-user-123"
        self.email = "<EMAIL>"
        self.name = "Test User"


class MockTaskRepository:
    """Mock task repository for testing."""

    def __init__(self):
        self.tasks = []

    async def create_task(self, task_data: Dict[str, Any]) -> Mock:
        """Create a mock task."""
        task = Mock()
        task.id = str(uuid.uuid4())
        task.title = task_data.get('title', 'Mock Task')
        task.description = task_data.get('description', 'Mock task description')
        task.status = 'created'
        task.created_at = datetime.now()

        self.tasks.append(task)
        return task

    async def get_task(self, task_id: str) -> Optional[Mock]:
        """Get a mock task by ID."""
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None


class MockSessionLocal:
    """Mock database session for testing."""

    def __init__(self):
        self.closed = False

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.closed = True

    def close(self):
        self.closed = True


class MockLLMService:
    """Mock LLM service for testing."""

    async def generate(self, request) -> Mock:
        """Generate a mock LLM response."""
        response = Mock()
        response.content = "Mock LLM response for testing"
        response.model = "mock-model"
        response.provider = Mock()
        response.provider.value = "mock-provider"
        response.usage = Mock()
        response.usage.prompt_tokens = 10
        response.usage.completion_tokens = 20
        response.usage.total_tokens = 30
        return response


def setup_test_mocks():
    """Set up all test mocks."""

    # Mock external services
    mocks = {}

    # Mock database session
    mocks['SessionLocal'] = MockSessionLocal

    # Mock task repository
    mocks['TaskRepository'] = MockTaskRepository

    # Mock LLM service
    mocks['get_llm_service'] = lambda: MockLLMService()

    return mocks


class MockArchitectAgent:
    """Mock ArchitectAgent for testing without full dependencies."""

    async def handle_export_request(self, export_task: Dict[str, Any]) -> Dict[str, Any]:
        """Mock export request handling."""
        return {
            "success": True,
            "export_id": export_task['export_id'],
            "plan": {
                "export_type": "project_export",
                "project_name": export_task['project_name'],
                "steps": [
                    {"step": "database_export", "description": "Export database data"},
                    {"step": "filesystem_export", "description": "Copy project files"},
                    {"step": "package_export", "description": "Create archive"},
                    {"step": "cleanup_export", "description": "Clean up temporary files"}
                ],
                "total_steps": 4
            },
            "task_ids": [str(uuid.uuid4()) for _ in range(4)],
            "estimated_duration": "5-10 minutes"
        }

    async def _create_export_plan(self, export_task: Dict[str, Any]) -> Dict[str, Any]:
        """Create a mock export plan."""
        return {
            "export_type": "project_export",
            "project_name": export_task['project_name'],
            "export_format": export_task.get('export_format', 'zip'),
            "include_database": export_task.get('include_database', True),
            "include_files": export_task.get('include_files', True),
            "steps": [
                {"step": "database_export", "description": "Export database data"},
                {"step": "filesystem_export", "description": "Copy project files"},
                {"step": "package_export", "description": "Create archive"},
                {"step": "cleanup_export", "description": "Clean up temporary files"}
            ],
            "total_steps": 4,
            "created_at": datetime.now().isoformat()
        }


class MockProjectRepository:
    """Mock ProjectRepository for testing without database."""

    def __init__(self):
        self.projects = {}
        self.exports = {}

    async def get_project_by_id(self, user_id: str, project_id: str) -> Optional[MockProject]:
        """Get a mock project."""
        if project_id in self.projects:
            return self.projects[project_id]
        # Create a default mock project
        project = MockProject(project_id, "test-project")
        self.projects[project_id] = project
        return project

    async def create_export_task(self, export_task: Dict[str, Any]) -> Dict[str, Any]:
        """Create a mock export task."""
        export_id = export_task['export_id']

        # Store export record
        self.exports[export_id] = {
            'export_id': export_id,
            'project_id': export_task['project_id'],
            'user_id': export_task['user_id'],
            'project_name': export_task['project_name'],
            'status': 'initiated',
            'export_format': export_task.get('export_format', 'zip'),
            'include_database': export_task.get('include_database', True),
            'include_files': export_task.get('include_files', True),
            'created_at': datetime.now(),
            'expires_at': datetime.now()
        }

        # Use mock architect agent
        architect = MockArchitectAgent()
        result = await architect.handle_export_request(export_task)

        return result

    async def get_export_status(self, user_id: str, export_id: str) -> Dict[str, Any]:
        """Get mock export status."""
        if export_id not in self.exports:
            return None

        export_record = self.exports[export_id]
        return {
            'export_id': export_record['export_id'],
            'project_id': export_record['project_id'],
            'project_name': export_record['project_name'],
            'status': 'completed',  # Mock as completed
            'progress_percentage': 100,
            'current_step': 'completed',
            'steps_completed': ['database_export', 'filesystem_export', 'package_export', 'cleanup_export'],
            'steps_remaining': [],
            'export_format': export_record['export_format'],
            'include_database': export_record['include_database'],
            'include_files': export_record['include_files'],
            'file_size': 1024000,
            'filename': f"{export_record['project_name']}_export.zip",
            'download_url': f"/api/v1/projects/{export_record['project_id']}/export/{export_id}/download",
            'created_at': export_record['created_at'].isoformat(),
            'completed_at': datetime.now().isoformat(),
            'expires_at': export_record['expires_at'].isoformat(),
            'error_message': None,
            'message': 'Export completed successfully and is ready for download'
        }

    async def get_export_file_path(self, user_id: str, export_id: str) -> Dict[str, Any]:
        """Get mock export file path."""
        if export_id not in self.exports:
            return None

        export_record = self.exports[export_id]

        # Create a temporary mock file
        import tempfile
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.zip')
        temp_file.write(b"Mock export file content")
        temp_file.close()

        return {
            'file_path': temp_file.name,
            'filename': f"{export_record['project_name']}_export.zip",
            'file_size': 25,
            'export_format': export_record['export_format']
        }

    def _get_status_message(self, status: str) -> str:
        """Get status message."""
        messages = {
            'initiated': 'Export has been initiated and is queued for processing',
            'in_progress': 'Export is currently being processed',
            'completed': 'Export completed successfully and is ready for download',
            'failed': 'Export failed due to an error',
            'expired': 'Export has expired and is no longer available'
        }
        return messages.get(status, f'Unknown status: {status}')

    async def _store_export_record(self, export_record: Dict[str, Any]) -> None:
        """Store export record (mock implementation)."""
        export_id = export_record['export_id']
        self.exports[export_id] = export_record

    async def _get_export_record(self, export_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get export record (mock implementation)."""
        return self.exports.get(export_id)

    async def _update_export_record(self, export_id: str, update_data: Dict[str, Any]) -> None:
        """Update export record (mock implementation)."""
        if export_id in self.exports:
            self.exports[export_id].update(update_data)

    async def update_export_progress(self, export_id: str, progress_data: Dict[str, Any]) -> None:
        """Update export progress (mock implementation)."""
        await self._update_export_record(export_id, progress_data)


def get_mock_current_user():
    """Get a mock current user."""
    return MockUser()


def get_mock_project_repository():
    """Get a mock project repository."""
    return MockProjectRepository()
