#!/usr/bin/env python3
"""
Requirements verification script for AI Orchestrator.
This script checks that all required packages are installed and can be imported.
"""

import sys
import importlib
from typing import List, Tuple

# Required packages and their import names
REQUIRED_PACKAGES = {
    # Core FastAPI and web framework
    'fastapi': ['fastapi'],
    'uvicorn': ['uvicorn'],
    'python-multipart': ['multipart'],

    # Database and ORM
    'sqlalchemy': ['sqlalchemy'],
    'alembic': ['alembic'],
    'psycopg2-binary': ['psycopg2'],
    'asyncpg': ['asyncpg'],
    'pgvector': ['pgvector'],

    # Redis and caching
    'redis': ['redis'],

    # Configuration and validation
    'pydantic': ['pydantic', 'pydantic_settings'],
    'python-dotenv': ['dotenv'],

    # Authentication and security
    'python-jose': ['jose'],
    'passlib': ['passlib'],
    'PyJWT': ['jwt'],
    'cryptography': ['cryptography'],
    'bcrypt': ['bcrypt'],

    # Supabase
    'supabase': ['supabase'],

    # AI/ML packages
    'langchain': ['langchain', 'langchain_community', 'langchain_openai', 'langchain_core'],
    'openai': ['openai'],
    'anthropic': ['anthropic'],
    'sentence-transformers': ['sentence_transformers'],
    'transformers': ['transformers'],
    'torch': ['torch'],
    'scikit-learn': ['sklearn'],
    'tokenizers': ['tokenizers'],
    'huggingface-hub': ['huggingface_hub'],
    'accelerate': ['accelerate'],

# Vector databases
'chromadb': ['chromadb'],    # HTTP and async
    'aiohttp': ['aiohttp'],
    'requests': ['requests'],
    'websockets': ['websockets'],
    'uvloop': ['uvloop'],

    # Docker
    'docker': ['docker'],

    # Monitoring and utilities
    'prometheus-client': ['prometheus_client'],
    'slowapi': ['slowapi'],
    'psutil': ['psutil'],
    'python-dateutil': ['dateutil'],
    'pytz': ['pytz'],

    # Development and testing
    'pytest': ['pytest'],
    'httpx': ['httpx'],
    'debugpy': ['debugpy'],
    'watchdog': ['watchdog'],

    # Code quality
    'black': ['black'],
    'isort': ['isort'],
    'flake8': ['flake8'],
    'mypy': ['mypy'],
}

OPTIONAL_PACKAGES = {
    'numpy': ['numpy'],
    'aiofiles': ['aiofiles'],
    'email-validator': ['email_validator'],
    'pathspec': ['pathspec'],
    'gunicorn': ['gunicorn'],
    'mkdocs': ['mkdocs'],
    'mkdocs-material': ['mkdocs'],
    'ruff': ['ruff'],
}

def check_package_imports(package_name: str, import_names: List[str]) -> Tuple[bool, str]:
    """Check if a package and its imports are available."""
    for import_name in import_names:
        try:
            importlib.import_module(import_name)
        except ImportError as e:
            return False, f"Failed to import {import_name}: {e}"
        except Exception as e:
            return False, f"Error importing {import_name}: {e}"
    return True, "OK"

def main():
    """Main verification function."""
    print("🔍 Verifying AI Orchestrator Requirements Installation")
    print("=" * 60)

    failed_packages = []
    successful_packages = []

    # Check required packages
    print("\n📦 Checking REQUIRED packages:")
    for package_name, import_names in REQUIRED_PACKAGES.items():
        success, message = check_package_imports(package_name, import_names)
        if success:
            print(f"✅ {package_name}: {message}")
            successful_packages.append(package_name)
        else:
            print(f"❌ {package_name}: {message}")
            failed_packages.append((package_name, message))

    # Check optional packages
    print("\n📦 Checking OPTIONAL packages:")
    for package_name, import_names in OPTIONAL_PACKAGES.items():
        success, message = check_package_imports(package_name, import_names)
        if success:
            print(f"✅ {package_name}: {message}")
        else:
            print(f"⚠️  {package_name}: {message} (optional)")

    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)

    total_required = len(REQUIRED_PACKAGES)
    total_successful = len(successful_packages)
    total_failed = len(failed_packages)

    print(f"Required packages: {total_required}")
    print(f"Successfully installed: {total_successful}")
    print(f"Failed to install: {total_failed}")

    if failed_packages:
        print("\n❌ FAILED PACKAGES:")
        for package, error in failed_packages:
            print(f"   - {package}: {error}")

        print("\n💡 To install missing packages, run:")
        failed_names = [pkg for pkg, _ in failed_packages]
        print(f"   pip install {' '.join(failed_names)}")
        return 1
    else:
        print("\n🎉 All required packages are properly installed!")
        return 0

if __name__ == "__main__":
    sys.exit(main())
