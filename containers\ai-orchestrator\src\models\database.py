"""
Database session and engine management for the AI Orchestrator.
This module provides a centralized way to configure and access the database.
"""

import logging
from typing import Generator, Optional

from sqlalchemy import MetaData, create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session, declarative_base, sessionmaker
from src.core.config import settings

logger = logging.getLogger(__name__)

# =====================================================================================
# Engine and Session Creation
# =====================================================================================

# This engine instance can be None if the DATABASE_URL is not set.
# The application should handle this gracefully during startup.
engine: Optional[Engine] = None
SessionLocal: Optional[sessionmaker] = None

if getattr(settings, "DATABASE_URL", None):
    db_url = str(settings.DATABASE_URL)
    engine_args = {
        "echo": getattr(settings, "DATABASE_ECHO", False),
        "pool_pre_ping": True,
    }
    if "sqlite" not in db_url:
        engine_args.update(
            {
                "pool_size": getattr(settings, "DATABASE_POOL_SIZE", 5),
                "max_overflow": getattr(settings, "DATABASE_POOL_OVERFLOW", 10),
            }
        )

    engine = create_engine(db_url, **engine_args)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
else:
    logger.warning("DATABASE_URL is not configured. Database functionality will be unavailable.")

# =====================================================================================
# SQLAlchemy Base and Naming Convention
# =====================================================================================

naming_convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s",
}

Base = declarative_base(metadata=MetaData(naming_convention=naming_convention))

# =====================================================================================
# FastAPI Dependency
# =====================================================================================


def get_db() -> Generator[Session, None, None]:
    """
    FastAPI dependency that provides a database session.
    Handles session creation and teardown.
    """
    if SessionLocal is None:
        logger.error("Database is not configured, cannot create session.")
        raise RuntimeError("Database session factory is not initialized.")

    db = SessionLocal()
    try:
        yield db
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()


# =====================================================================================
# Database Utility Functions
# =====================================================================================


def get_database_info() -> dict:
    """Get database connection information for health checks."""
    if not engine:
        return {"status": "disconnected", "error": "Database not configured"}

    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version_row = result.fetchone()
            version = version_row[0] if version_row else "Unknown"

        return {
            "status": "connected",
            "dialect": engine.dialect.name,
            "driver": engine.driver,
            "version": version,
        }
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {"status": "error", "error": str(e)}


def create_db_and_tables():
    """Create all database tables defined in SQLAlchemy models."""
    if not engine:
        logger.error("Cannot create tables, database engine is not initialized.")
        return

    logger.info("Creating database tables...")
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully.")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}", exc_info=True)
        raise
