/**
 * useRoles Hook Tests
 */

import { act, renderHook, waitFor } from "@testing-library/react";
import useRoles from "../useRoles";
import { roleApi } from "../../lib/api";
import { LLMProvider } from "../../types/role";
import type { RoleConfiguration } from "../../types/role";

// Mock the API
jest.mock("../../lib/api", () => ({
  roleApi: {
    getRoles: jest.fn(),
    createRole: jest.fn(),
    updateRole: jest.fn(),
    deleteRole: jest.fn(),
  },
}));

const mockRoleApi = roleApi as jest.Mocked<typeof roleApi>;

const mockRoles: Record<string, RoleConfiguration> = {
  architect: {
    provider: LLMProvider.OPENROUTER,
    available_models: ["claude-3-sonnet"],
    selected_model: "claude-3-sonnet",
    api_key: "test-key",
    cost_limit: 100.0,
    max_tokens: 4096,
    temperature: 0.7,
    enabled: true,
    created_at: "2024-01-01T00:00:00.000000",
    updated_at: "2024-01-01T00:00:00.000000",
  },
  backend: {
    provider: LLMProvider.OLLAMA,
    available_models: ["codellama:13b"],
    selected_model: "codellama:13b",
    api_key: null,
    cost_limit: null,
    max_tokens: 4096,
    temperature: 0.3,
    enabled: true,
    created_at: "2024-01-01T00:00:00.000000",
    updated_at: "2024-01-01T00:00:00.000000",
  },
};

describe("useRoles Hook", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("initializes with empty state and loads roles on mount", async () => {
    mockRoleApi.getRoles.mockResolvedValueOnce({
      data: {
        success: true,
        message: "Success",
        roles: mockRoles,
        total_roles: 2,
      },
      status: 200,
    });

    const { result } = renderHook(() => useRoles());

    // Initial state
    expect(result.current.roles).toEqual({});
    expect(result.current.loading.toString()).toBe("loading");
    expect(result.current.error).toBeNull();

    // Wait for roles to load
    await waitFor(() => {
      expect(result.current.loading.toString()).toBe("success");
    });

    expect(result.current.roles).toEqual(mockRoles);
    expect(mockRoleApi.getRoles).toHaveBeenCalledTimes(1);
  });

  it("handles API errors when loading roles", async () => {
    const apiError = {
      message: "Failed to fetch roles",
      details: "Network error",
    };

    mockRoleApi.getRoles.mockResolvedValueOnce({
      error: apiError,
      status: 500,
    });

    const { result } = renderHook(() => useRoles());

    await waitFor(() => {
      expect(result.current.loading.toString()).toBe("error");
    });

    expect(result.current.error).toEqual(apiError);
    expect(result.current.roles).toEqual({});
  });

  it("creates a new role successfully", async () => {
    const newRoleConfig: RoleConfiguration = {
      provider: LLMProvider.OPENAI,
      available_models: ["gpt-4"],
      selected_model: "gpt-4",
      api_key: "sk-test",
      cost_limit: 50.0,
      max_tokens: 2048,
      temperature: 0.5,
      enabled: true,
      created_at: "2024-01-01T00:00:00.000000",
      updated_at: "2024-01-01T00:00:00.000000",
    };

    // Mock initial load
    mockRoleApi.getRoles.mockResolvedValueOnce({
      data: {
        success: true,
        message: "Success",
        roles: mockRoles,
        total_roles: 2,
      },
      status: 200,
    });

    // Mock create
    mockRoleApi.createRole.mockResolvedValueOnce({
      data: {
        success: true,
        message: "Role created",
        role_name: "test_role",
        configuration: newRoleConfig,
      },
      status: 200,
    });

    const { result } = renderHook(() => useRoles());

    // Wait for initial load
    await waitFor(() => {
      expect(result.current.loading.toString()).toBe("success");
    });

    // Create new role
    let createResult: boolean;
    await act(async () => {
      createResult = await result.current.createRole("test_role", {
        provider: LLMProvider.OPENAI,
        available_models: ["gpt-4"],
        selected_model: "gpt-4",
        api_key: "sk-test",
        cost_limit: 50.0,
        max_tokens: 2048,
        temperature: 0.5,
        enabled: true,
      });
    });

    expect(createResult!).toBe(true);
    expect(result.current.roles.test_role).toEqual(newRoleConfig);
    expect(mockRoleApi.createRole).toHaveBeenCalledWith(
      "test_role",
      expect.any(Object),
    );
  });

  it("handles create role API errors", async () => {
    const apiError = { message: "Role already exists" };

    // Mock initial load
    mockRoleApi.getRoles.mockResolvedValueOnce({
      data: {
        success: true,
        message: "Success",
        roles: mockRoles,
        total_roles: 2,
      },
      status: 200,
    });

    // Mock create error
    mockRoleApi.createRole.mockResolvedValueOnce({
      error: apiError,
      status: 400,
    });

    const { result } = renderHook(() => useRoles());

    await waitFor(() => {
      expect(result.current.loading.toString()).toBe("success");
    });

    let createResult: boolean;
    await act(async () => {
      createResult = await result.current.createRole("existing_role", {
        provider: LLMProvider.OPENAI,
        available_models: ["gpt-4"],
        selected_model: "gpt-4",
      });
    });

    expect(createResult!).toBe(false);
    expect(result.current.error).toEqual(apiError);
  });

  it("updates an existing role successfully", async () => {
    const updatedConfig = { ...mockRoles.architect, temperature: 0.9 };

    // Mock initial load
    mockRoleApi.getRoles.mockResolvedValueOnce({
      data: {
        success: true,
        message: "Success",
        roles: mockRoles,
        total_roles: 2,
      },
      status: 200,
    });

    // Mock update
    mockRoleApi.updateRole.mockResolvedValueOnce({
      data: {
        success: true,
        message: "Role updated",
        role_name: "architect",
        configuration: updatedConfig,
      },
      status: 200,
    });

    const { result } = renderHook(() => useRoles());

    await waitFor(() => {
      expect(result.current.loading.toString()).toBe("success");
    });

    let updateResult: boolean;
    await act(async () => {
      updateResult = await result.current.updateRole("architect", {
        temperature: 0.9,
      });
    });

    expect(updateResult!).toBe(true);
    expect(result.current.roles.architect.temperature).toBe(0.9);
  });

  it("deletes a role successfully", async () => {
    // Mock initial load
    mockRoleApi.getRoles.mockResolvedValueOnce({
      data: {
        success: true,
        message: "Success",
        roles: mockRoles,
        total_roles: 2,
      },
      status: 200,
    });

    // Mock delete
    mockRoleApi.deleteRole.mockResolvedValueOnce({
      data: { success: true, message: "Role deleted" },
      status: 200,
    });

    const { result } = renderHook(() => useRoles());

    await waitFor(() => {
      expect(result.current.loading.toString()).toBe("success");
    });

    let deleteResult: boolean;
    await act(async () => {
      deleteResult = await result.current.deleteRole("backend");
    });

    expect(deleteResult!).toBe(true);
    expect(result.current.roles.backend).toBeUndefined();
    expect(Object.keys(result.current.roles)).toHaveLength(1);
  });

  it("provides utility methods", async () => {
    // Mock initial load
    mockRoleApi.getRoles.mockResolvedValueOnce({
      data: {
        success: true,
        message: "Success",
        roles: mockRoles,
        total_roles: 2,
      },
      status: 200,
    });

    const { result } = renderHook(() => useRoles());

    await waitFor(() => {
      expect(result.current.loading.toString()).toBe("success");
    });

    // Test utility methods
    expect(result.current.getRoleNames()).toEqual(["architect", "backend"]);
    expect(result.current.getRoleByName("architect")).toEqual(
      mockRoles.architect,
    );
    expect(result.current.getRoleByName("nonexistent")).toBeNull();
    expect(result.current.isRoleEnabled("architect")).toBe(true);
    expect(result.current.isRoleEnabled("nonexistent")).toBe(true); // Default true
  });

  it("clears errors when requested", async () => {
    const apiError = { message: "Test error" };

    mockRoleApi.getRoles.mockResolvedValueOnce({
      error: apiError,
      status: 500,
    });

    const { result } = renderHook(() => useRoles());

    await waitFor(() => {
      expect(result.current.error).toEqual(apiError);
    });

    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBeNull();
  });

  it("refreshes roles when requested", async () => {
    const updatedRoles = {
      ...mockRoles,
      new_role: {
        ...mockRoles.architect,
        provider: LLMProvider.ANTHROPIC,
      },
    };

    // First load
    mockRoleApi.getRoles.mockResolvedValueOnce({
      data: {
        success: true,
        message: "Success",
        roles: mockRoles,
        total_roles: 2,
      },
      status: 200,
    });

    const { result } = renderHook(() => useRoles());

    await waitFor(() => {
      expect(result.current.loading.toString()).toBe("success");
    });

    expect(Object.keys(result.current.roles)).toHaveLength(2);

    // Mock refresh call
    mockRoleApi.getRoles.mockResolvedValueOnce({
      data: {
        success: true,
        message: "Success",
        roles: updatedRoles,
        total_roles: 3,
      },
      status: 200,
    });

    await act(async () => {
      await result.current.refreshRoles();
    });

    expect(Object.keys(result.current.roles)).toHaveLength(3);
    expect(result.current.roles.new_role).toBeDefined();
  });
});
