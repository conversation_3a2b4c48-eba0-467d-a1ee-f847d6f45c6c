# Docker Compose Configuration Guide

This project uses a structured approach to Docker Compose configurations with different files for different purposes.

## Available Docker Compose Files

### Core Configuration Files

#### 1. `docker-compose.yml` (Main Configuration)
- **Purpose**: Production-ready configuration
- **Usage**: `docker-compose up -d`
- **Contains**: All core services with production settings
- **Networks**: Two-network security model (web + internal)
- **Services**: traefik, ai-orchestrator, user-portal, redis, ollama, monitoring stack

#### 2. `docker-compose.dev.yml` (Development Overrides)
- **Purpose**: Development environment with hot-reload
- **Usage**: `docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch`
- **Features**:
  - Hot-reload for ai-orchestrator and user-portal
  - Development debugging enabled
  - Source code volume mounts
  - Development ports exposed
  - Traefik dashboard enabled

#### 3. `docker-compose.prod.yml` (Production Overrides)
- **Purpose**: Production deployment using pre-built images
- **Usage**: `docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d`
- **Features**:
  - Uses pre-built images from container registry
  - Production environment variables
  - Optimized for deployment

### Utility Configuration Files

#### 4. `docker-compose.debug.yml` (Debug Configuration)
- **Purpose**: Troubleshooting and debugging startup issues
- **Usage**: `docker-compose -f docker-compose.yml -f docker-compose.debug.yml up`
- **Features**:
  - Verbose logging
  - Extended startup timeouts
  - Debug command overrides
  - No restart policy for easier debugging

#### 5. `docker-compose.registry.yml` (Local Registry)
- **Purpose**: Local Docker registry mirror for development
- **Usage**: `docker-compose -f docker-compose.registry.yml up -d`
- **Features**:
  - Caches frequently used images
  - Reduces external Docker Hub pulls
  - Improves build performance

## Common Usage Patterns

### Development Workflow
```bash
# Start development environment with hot-reload
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch

# Start with specific profiles (CPU or GPU)
docker-compose -f docker-compose.yml -f docker-compose.dev.yml --profile cpu up --watch
docker-compose -f docker-compose.yml -f docker-compose.dev.yml --profile gpu up --watch
```

### Production Deployment
```bash
# Deploy using pre-built images
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Deploy with specific image tag
IMAGE_TAG=v1.2.3 docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Debugging Issues
```bash
# Debug startup problems
docker-compose -f docker-compose.yml -f docker-compose.debug.yml up

# Debug specific service
docker-compose -f docker-compose.yml -f docker-compose.debug.yml up ai-orchestrator
```

### Local Registry Setup
```bash
# Start local registry
docker-compose -f docker-compose.registry.yml up -d

# Use with main stack
docker-compose -f docker-compose.yml -f docker-compose.registry.yml -f docker-compose.dev.yml up
```

## Environment Variables

Key environment variables used across configurations:

- `IMAGE_TAG`: Version tag for production images (default: latest)
- `ENVIRONMENT`: Environment name (development/production)
- `OLLAMA_BASE_URL`: Ollama service URL
- `SUPABASE_URL`: Supabase instance URL
- `NEXTAUTH_SECRET`: NextAuth secret for user-portal

## Network Architecture

The project uses a two-network security model:

- **Web Network**: Public-facing services (traefik, ai-orchestrator, user-portal)
- **Internal Network**: Backend services (redis, ollama, docker-proxy)
- **Monitoring Network**: Observability stack (prometheus, grafana, loki)

## Service Profiles

- **CPU Profile**: `--profile cpu` - Uses CPU-only Ollama
- **GPU Profile**: `--profile gpu` - Uses GPU-accelerated Ollama

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Check if ports 80, 443, 8000, 3000 are available
2. **Network Issues**: Ensure external networks are created
3. **Volume Permissions**: Check Docker volume permissions
4. **Service Dependencies**: Wait for health checks to pass

### Debug Commands
```bash
# Check service status
docker-compose ps

# View service logs
docker-compose logs ai-orchestrator

# Check network connectivity
docker-compose exec ai-orchestrator curl http://redis:6379

# Validate configuration
docker-compose config
```

## File Organization

```
├── docker-compose.yml          # Main configuration
├── docker-compose.dev.yml      # Development overrides
├── docker-compose.prod.yml     # Production overrides
├── docker-compose.debug.yml    # Debug configuration
├── docker-compose.registry.yml # Local registry
└── DOCKER_COMPOSE_USAGE.md     # This documentation
```

This structure provides clear separation of concerns while maintaining flexibility for different deployment scenarios.
