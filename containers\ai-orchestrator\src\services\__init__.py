"""
Services Package for AI Orchestrator

This package contains all service classes for the AI Orchestrator,
including LLM services, validation, error recovery, and checkpoint management.

Provides hardened dependency injection with startup validation and robust error handling.
"""

import logging
import os
from functools import lru_cache
from typing import Optional

from fastapi import Depends, HTTPException, status

logger = logging.getLogger(__name__)

# Ensure 'src' package name is available regardless of how this package was
# imported (as top-level 'services' when 'containers/ai-orchestrator/src' is
# on sys.path, or as 'src.services' when repository root is on sys.path).
# This shim registers a lightweight module named 'src' with an appropriate
# __path__ so absolute imports using 'src.*' succeed.
try:
    import sys, types, pathlib
    if 'src' not in sys.modules:
        src_dir = pathlib.Path(__file__).resolve().parents[1]
        src_mod = types.ModuleType('src')
        # __path__ tells the import system where to find subpackages
        src_mod.__path__ = [str(src_dir)]
        sys.modules['src'] = src_mod
except Exception:
    # Best-effort only; do not prevent service package import on failure
    logger.debug('Failed to register src shim; continuing without it')

# Startup validation flags
_startup_validated = False
_validation_errors: list[str] = []


@lru_cache(maxsize=1)
def get_environment_config() -> dict[str, Optional[str]]:
    """
    Get environment configuration with caching for performance.

    Returns:
        Dictionary of environment variables used by services.
    """
    return {
        "supabase_url": os.getenv("SUPABASE_URL"),
        "supabase_key": os.getenv("SUPABASE_ANON_KEY") or os.getenv("SUPABASE_KEY"),
        "jwt_secret": os.getenv("JWT_SECRET"),
        "redis_url": os.getenv("REDIS_URL", "redis://localhost:6379"),
    }


def validate_startup_dependencies() -> None:
    """
    Validate required dependencies and configuration at startup.

    Raises:
        RuntimeError: If critical dependencies are missing or misconfigured.
    """
    global _startup_validated, _validation_errors

    if _startup_validated:
        return

    errors = []

    # Get cached environment configuration
    config = get_environment_config()

    # Validate Supabase configuration
    if not config["supabase_url"]:
        errors.append("SUPABASE_URL environment variable is required")
    if not config["supabase_key"]:
        errors.append("SUPABASE_ANON_KEY or SUPABASE_KEY environment variable is required")
    if not config["jwt_secret"]:
        errors.append("JWT_SECRET environment variable is required")

    # Validate Redis configuration
    if not config["redis_url"]:
        errors.append("REDIS_URL environment variable is required")

    if errors:
        _validation_errors = errors
        error_msg = "Startup validation failed:\n" + "\n".join(f"  - {err}" for err in errors)
        logger.error(error_msg)
        raise RuntimeError(error_msg)

    _startup_validated = True
    logger.info("Startup validation completed successfully")


def _try_import_variants(module_name: str, attr: str = None):
    """Try importing a symbol from a couple of likely absolute module paths.

    Order tried:
    - services.<module> (when ai-orchestrator src is on sys.path and imports are package-local)
    - src.services.<module> (when project root is on sys.path)
    - relative import from current package
    Returns the attribute or None on failure.
    """
    import importlib
    candidates = []
    base = module_name
    if module_name.startswith('services.'):
        # if caller passed full 'services.foo', also try 'src.services.foo'
        candidates = [module_name, 'src.' + module_name]
    else:
        candidates = ['services.' + module_name, 'src.services.' + module_name, f'{__name__}.{module_name}']

    last_exc = None
    for cand in candidates:
        try:
            mod = importlib.import_module(cand)
            if attr:
                return getattr(mod, attr)
            return mod
        except Exception as e:
            last_exc = e
            logger.debug(f"Import candidate failed: {cand} -> {e}")

    logger.error(f"All import attempts failed for {module_name}: {last_exc}")
    return None


# Import all service classes with enhanced error handling using resilient imports
TaskValidator = _try_import_variants('task_validator', 'TaskValidator')
if TaskValidator:
    logger.debug("Successfully imported TaskValidator")

ErrorRecoverySystem = _try_import_variants('error_recovery', 'ErrorRecoverySystem')
if ErrorRecoverySystem:
    logger.debug("Successfully imported ErrorRecoverySystem")

CheckpointManager = _try_import_variants('checkpoint_manager', 'CheckpointManager')
if CheckpointManager:
    logger.debug("Successfully imported CheckpointManager")

EnhancedLLMService = _try_import_variants('enhanced_llm_service', 'EnhancedLLMService')
if EnhancedLLMService:
    logger.debug("Successfully imported EnhancedLLMService")

# LockManager and Dispatcher DI factories with enhanced error handling
try:
    from src.services.lock_manager import LockManager
    from src.services.redis_service import get_redis_client
    from src.services.dispatcher import Dispatcher

    _lock_manager_available = True
    logger.debug("Successfully imported LockManager, Dispatcher, and Redis client")
except Exception as e:
    logger.error(f"Dispatcher/LockManager dependencies unavailable: {e}")
    LockManager = None  # type: ignore
    Dispatcher = None  # type: ignore
    get_redis_client = None  # type: ignore
    _lock_manager_available = False


async def get_lock_manager(redis_client=Depends(get_redis_client)) -> "LockManager":  # type: ignore[name-defined]
    """
    FastAPI dependency to provide a LockManager instance.

    Uses the shared async Redis client for consistent connection handling.

    Raises:
        HTTPException: If LockManager or Redis client is unavailable.
    """
    if not _lock_manager_available or LockManager is None:
        logger.error("LockManager is not available - check Redis configuration and imports")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Lock management service is unavailable"
        )

    if redis_client is None:
        logger.error("Redis client is not available")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Redis service is unavailable"
        )

    # Validate startup dependencies
    try:
        validate_startup_dependencies()
    except RuntimeError as e:
        logger.error(f"Startup validation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service startup validation failed"
        )

    return LockManager(redis_client, tenant_id="default")  # type: ignore[misc]


async def get_dispatcher(lock_manager: "LockManager" = Depends(get_lock_manager)) -> "Dispatcher":  # type: ignore[name-defined]
    """
    FastAPI dependency that constructs and returns a Dispatcher instance.

    Raises:
        HTTPException: If Dispatcher is unavailable.
    """
    if not _lock_manager_available or Dispatcher is None:
        logger.error("Dispatcher is not available - check dependencies")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Dispatcher service is unavailable"
        )

    return Dispatcher(lock_manager)


# Define comprehensive exports for clean imports and proper IDE support
__all__ = [
    # Core service classes
    "TaskValidator",
    "ErrorRecoverySystem",
    "CheckpointManager",
    "EnhancedLLMService",
    # DI factories
    "get_lock_manager",
    "get_dispatcher",
    # Validation
    "validate_startup_dependencies",
]