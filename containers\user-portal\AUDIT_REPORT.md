# Frontend Audit Report

Summary of the comprehensive frontend audit and fixes applied to `containers/user-portal`.

Key changes:

- Added `"use client"` to components that rely on client-only React hooks (session, events).
- Created a typed NextAuth options helper at `src/lib/nextAuth.ts` and migrated auth routes to import typed options.
- Extracted project fetching logic into a reusable `useProjects` hook at `src/hooks/useProjects.ts`.
- Repaired Jest tests by fixing mocks, async handling, and replacing deprecated timer usage.
- Removed unused/obsolete helpers and files where applicable (documented in the repository history).

Notes:

- Some TypeScript lint warnings remain where `any` was used as a pragmatic choice for quick replacement; follow-up PR should add precise types for `Project` and project API responses.
- For server-side NextAuth routes, ensure environment variables `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`, `GITHUB_CLIENT_ID`, and `GITHUB_CLIENT_SECRET` are set in the environment used for deployment.

Next steps:

- Run full CI (Jest + any other tests) and iterate on any failing tests.
- Add stricter types for project models and the `useProjects` hook return values.
- Consider adding integration tests for the embedding flow.
