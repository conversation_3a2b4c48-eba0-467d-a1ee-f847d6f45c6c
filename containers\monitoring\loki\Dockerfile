# syntax=docker/dockerfile:1
# Loki Dockerfile for log aggregation
# Uses official Loki image with security best practices

FROM grafana/loki:2.9.0

# Create loki user explicitly with specific UID/GID for security
USER root
RUN (getent group loki || groupadd -r -g 10001 loki) && \
  (getent passwd loki || useradd -r -g loki -u 10001 -d /etc/loki -s /bin/bash loki)

# Install curl for health checks
RUN apk update && apk add --no-cache \
  curl \
  && rm -rf /var/cache/apk/*

# Create necessary directories with proper permissions
RUN mkdir -p /loki && \
  chown -R loki:loki /loki && \
  chown -R loki:loki /etc/loki

# Switch to non-root user
USER loki

# Expose Loki ports
EXPOSE 3100

# Health check for Loki service
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3100/ready || exit 1

# Set security and project labels
LABEL org.opencontainers.image.title="AI Coding Agent - Loki" \
  org.opencontainers.image.description="Loki log aggregation for AI Coding Agent" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team" \
  security.non-root="true" \
  security.user="loki"

# Start Loki
CMD ["/usr/bin/loki", "-config.file=/etc/loki/local-config.yaml"]
