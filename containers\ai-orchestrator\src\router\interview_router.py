# Project: AI Coding Agent
# Purpose: Interview router for ArchitectAgent interactive functionality

"""
Interview router for the ArchitectAgent interactive interview system.
Provides REST endpoints for managing interview sessions and responses.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional
from pydantic import BaseModel

from src.agents.architect_agent import ArchitectAgent


# Pydantic models for API
class StartInterviewRequest(BaseModel):
    project_id: int
    user_id: str


class UserResponseRequest(BaseModel):
    session_id: str
    question_key: str
    user_response: str


class InterviewResponse(BaseModel):
    type: str
    message: Optional[str] = None
    question_key: Optional[str] = None
    question_text: Optional[str] = None
    is_followup: Optional[bool] = None
    session_id: str
    roadmap: Optional[Dict[str, Any]] = None


# Global ArchitectAgent instance
_architect_agent: Optional[ArchitectAgent] = None


async def get_architect_agent() -> ArchitectAgent:
    """Dependency to get the ArchitectAgent instance."""
    global _architect_agent
    if _architect_agent is None:
        _architect_agent = ArchitectAgent()
        await _architect_agent.initialize()
    return _architect_agent


# Create the interview router
router = APIRouter(prefix="/api/v1/interview", tags=["interview"])


@router.post("/start", response_model=Dict[str, str])
async def start_interview(
    request: StartInterviewRequest,
    agent: ArchitectAgent = Depends(get_architect_agent),
):
    """
    Start a new interview session for a project.

    Args:
        request: Interview start request with project_id and user_id

    Returns:
        Dict containing the session_id
    """
    try:
        session_id = await agent.start_interview(
            project_id=request.project_id,
            user_id=request.user_id
        )
        return {"session_id": session_id, "message": "Interview started successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start interview: {str(e)}")


@router.get("/{session_id}/question", response_model=InterviewResponse)
async def get_current_question(
    session_id: str,
    agent: ArchitectAgent = Depends(get_architect_agent),
):
    """
    Get the current question for an interview session.

    Args:
        session_id: The interview session ID

    Returns:
        InterviewResponse with current question or status
    """
    try:
        question = await agent.get_current_question(session_id)
        if question is None:
            raise HTTPException(status_code=404, detail="Interview session not found")

        return InterviewResponse(**question)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get question: {str(e)}")


@router.post("/response", response_model=Dict[str, Any])
async def submit_response(
    request: UserResponseRequest,
    agent: ArchitectAgent = Depends(get_architect_agent),
):
    """
    Submit a user response to a question.

    Args:
        request: User response with session_id, question_key, and response

    Returns:
        Dict with next action information
    """
    try:
        result = await agent.process_user_response(
            session_id=request.session_id,
            question_key=request.question_key,
            user_response=request.user_response
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to process response: {str(e)}")


@router.get("/{session_id}/progress")
async def get_interview_progress(
    session_id: str,
    agent: ArchitectAgent = Depends(get_architect_agent),
):
    """
    Get the progress of an interview session.

    Args:
        session_id: The interview session ID

    Returns:
        Dict with progress information
    """
    try:
        from src.repository.conversation_repository import ConversationRepository
        from src.models.database import get_db
        async with get_db() as db:
            progress = await ConversationRepository.get_session_progress(db, session_id)
            return progress
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get progress: {str(e)}")


@router.get("/{session_id}/history")
async def get_interview_history(
    session_id: str,
):
    """
    Get the complete conversation history for an interview session.

    Args:
        session_id: The interview session ID

    Returns:
        Dict with conversation history
    """
    try:
        from src.repository.conversation_repository import ConversationRepository
        from src.models.database import get_db
        async with get_db() as db:
            history = await ConversationRepository.get_session_summary(db, session_id)
            return history
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get history: {str(e)}")


@router.post("/{session_id}/complete")
async def complete_interview(
    session_id: str,
):
    """
    Mark an interview session as completed.

    Args:
        session_id: The interview session ID

    Returns:
        Dict with completion status
    """
    try:
        from src.repository.conversation_repository import ConversationRepository
        from src.models.database import get_db
        async with get_db() as db:
            success = await ConversationRepository.complete_interview_session(db, session_id)
            if success:
                return {"message": "Interview completed successfully", "session_id": session_id}
            else:
                raise HTTPException(status_code=404, detail="Interview session not found")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to complete interview: {str(e)}")
