# Project: AI Coding Agent - Unit Tests
# Purpose: Comprehensive unit tests for task validator

import pytest
import tempfile
import subprocess
from pathlib import Path
from unittest.mock import patch

from src.services.task_validator import TaskValidator
from src.models.validation_models import (
    Task,
    TaskResult,
    ValidationResult,
    TaskType,
    AgentType,
)


class TestTaskValidator:
    """Comprehensive test suite for TaskValidator"""

    @pytest.fixture
    def validator(self):
        """Create a TaskValidator instance for testing"""
        return TaskValidator(project_root="/test/workspace")

    @pytest.fixture
    def sample_task(self):
        """Create a sample task for testing"""
        return Task(
            title="Test Component",
            description="Create a React component",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND,
            expected_files=["src/components/TestComponent.tsx"],
            code_files=["src/components/TestComponent.tsx"],
            test_command="npm test TestComponent",
            integration_checks=["http://localhost:3000/api/health"]
        )

    @pytest.fixture
    def sample_task_result(self):
        """Create a sample task result for testing"""
        return TaskResult(
            success=True,
            output="Component created successfully",
            files_created=["src/components/TestComponent.tsx"],
            files_modified=[],
            duration_seconds=45.2,
            metadata={"component_type": "functional", "props": ["title", "onClick"]}
        )

    # Main validation method tests

    @pytest.mark.asyncio
    async def test_validate_task_completion_success(self, validator, sample_task, sample_task_result):
        """Test successful task completion validation"""
        # Mock all validation methods to return success
        with patch.object(validator, '_validate_files_exist', return_value=ValidationResult.success("Files exist")):
            with patch.object(validator, '_validate_code_syntax', return_value=ValidationResult.success("Syntax valid")):
                with patch.object(validator, '_run_functional_tests', return_value=ValidationResult.success("Tests passed")):
                    with patch.object(validator, '_validate_integrations', return_value=ValidationResult.success("Integrations OK")):
                        with patch.object(validator, '_validate_task_type_specific', return_value=ValidationResult.success("Type-specific OK")):

                            result = await validator.validate_task_completion(sample_task, sample_task_result)

                            assert result.is_valid
                            assert result.error is None
                            assert 'validation_time_seconds' in result.metrics
                            assert 'checks_performed' in result.metrics
                            assert result.metrics['checks_performed'] == 5

    @pytest.mark.asyncio
    async def test_validate_task_completion_failure(self, validator, sample_task, sample_task_result):
        """Test task completion validation failure"""
        # Mock file validation to fail
        with patch.object(validator, '_validate_files_exist', return_value=ValidationResult.failure("Files missing")):
            with patch.object(validator, '_validate_code_syntax', return_value=ValidationResult.success()):
                with patch.object(validator, '_run_functional_tests', return_value=ValidationResult.success()):
                    with patch.object(validator, '_validate_integrations', return_value=ValidationResult.success()):
                        with patch.object(validator, '_validate_task_type_specific', return_value=ValidationResult.success()):

                            result = await validator.validate_task_completion(sample_task, sample_task_result)

                            assert not result.is_valid
                            assert "Files missing" in result.error

    @pytest.mark.asyncio
    async def test_validate_task_completion_partial_failure(self, validator, sample_task, sample_task_result):
        """Test task completion validation with partial failures"""
        # Mock some validations to fail, others to succeed
        with patch.object(validator, '_validate_files_exist', return_value=ValidationResult.success()):
            with patch.object(validator, '_validate_code_syntax', return_value=ValidationResult.failure("Syntax error")):
                with patch.object(validator, '_run_functional_tests', return_value=ValidationResult.success()):
                    with patch.object(validator, '_validate_integrations', return_value=ValidationResult.failure("Integration failed")):
                        with patch.object(validator, '_validate_task_type_specific', return_value=ValidationResult.success()):

                            result = await validator.validate_task_completion(sample_task, sample_task_result)

                            assert not result.is_valid
                            assert "Syntax error" in result.error
                            assert "Integration failed" in result.error

    @pytest.mark.asyncio
    async def test_validate_task_completion_exception(self, validator, sample_task, sample_task_result):
        """Test task completion validation exception handling"""
        # Mock a validation method to raise an exception
        with patch.object(validator, '_validate_files_exist', side_effect=Exception("Unexpected error")):
            result = await validator.validate_task_completion(sample_task, sample_task_result)

            assert not result.is_valid
            assert "Validation system error" in result.error

    # File existence validation tests

    @pytest.mark.asyncio
    async def test_validate_files_exist_success(self, validator):
        """Test successful file existence validation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test files
            test_file1 = Path(temp_dir) / "file1.txt"
            test_file2 = Path(temp_dir) / "subdir" / "file2.txt"
            test_file2.parent.mkdir(parents=True)
            test_file1.write_text("content1")
            test_file2.write_text("content2")

            validator.project_root = Path(temp_dir)

            result = await validator._validate_files_exist(["file1.txt", "subdir/file2.txt"])

            assert result.is_valid
            assert "files exist and are accessible" in result.details

    @pytest.mark.asyncio
    async def test_validate_files_exist_missing_files(self, validator):
        """Test file existence validation with missing files"""
        with tempfile.TemporaryDirectory() as temp_dir:
            validator.project_root = Path(temp_dir)

            result = await validator._validate_files_exist(["missing1.txt", "missing2.txt"])

            assert not result.is_valid
            assert "Missing files:" in result.error
            assert "missing1.txt" in result.error
            assert "missing2.txt" in result.error

    @pytest.mark.asyncio
    async def test_validate_files_exist_mixed_results(self, validator):
        """Test file existence validation with mixed results"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create only one of the expected files
            existing_file = Path(temp_dir) / "exists.txt"
            existing_file.write_text("content")

            validator.project_root = Path(temp_dir)

            result = await validator._validate_files_exist(["exists.txt", "missing.txt"])

            assert not result.is_valid
            assert "missing.txt" in result.error

    @pytest.mark.asyncio
    async def test_validate_files_exist_unreadable_file(self, validator):
        """Test file existence validation with unreadable file"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a file but make it unreadable (on Unix systems)
            test_file = Path(temp_dir) / "unreadable.txt"
            test_file.write_text("content")

            validator.project_root = Path(temp_dir)

            # Mock os.access to simulate unreadable file
            with patch('os.access', return_value=False):
                result = await validator._validate_files_exist(["unreadable.txt"])

                assert not result.is_valid
                assert "not readable" in result.error

    # Code syntax validation tests

    @pytest.mark.asyncio
    async def test_validate_code_syntax_success(self, validator):
        """Test successful code syntax validation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create valid code files
            py_file = Path(temp_dir) / "valid.py"
            js_file = Path(temp_dir) / "valid.js"
            py_file.write_text("def hello():\n    return 'world'\n")
            js_file.write_text("function hello() { return 'world'; }")

            validator.project_root = Path(temp_dir)

            # Mock JavaScript validation to succeed
            with patch('subprocess.run') as mock_run:
                mock_run.return_value.returncode = 0

                result = await validator._validate_code_syntax(["valid.py", "valid.js"])

                assert result.is_valid

    @pytest.mark.asyncio
    async def test_validate_code_syntax_python_failure(self, validator):
        """Test Python code syntax validation failure"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create invalid Python file
            py_file = Path(temp_dir) / "invalid.py"
            py_file.write_text("def hello(\n    return 'world'\n")  # Missing closing parenthesis

            validator.project_root = Path(temp_dir)

            result = await validator._validate_code_syntax(["invalid.py"])

            assert not result.is_valid
            assert "syntax error" in result.error.lower()

    @pytest.mark.asyncio
    async def test_validate_code_syntax_javascript_failure(self, validator):
        """Test JavaScript syntax validation failure"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create invalid JavaScript file
            js_file = Path(temp_dir) / "invalid.js"
            js_file.write_text("function hello() { return 'world' ")  # Missing closing brace

            validator.project_root = Path(temp_dir)

            # Mock subprocess to simulate failed validation
            with patch('subprocess.run') as mock_run:
                mock_run.return_value.returncode = 1
                mock_run.return_value.stderr = "SyntaxError: Unexpected end of input"

                result = await validator._validate_code_syntax(["invalid.js"])

                assert not result.is_valid
                assert "syntax error" in result.error.lower()

    @pytest.mark.asyncio
    async def test_validate_python_syntax_success(self, validator):
        """Test successful Python syntax validation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            py_file = Path(temp_dir) / "test.py"
            py_file.write_text("def hello():\n    return 'world'\n")

            result = await validator._validate_python_syntax(py_file)

            assert result.is_valid
            assert "Python syntax valid" in result.details

    @pytest.mark.asyncio
    async def test_validate_python_syntax_with_warnings(self, validator):
        """Test Python syntax validation with warnings"""
        with tempfile.TemporaryDirectory() as temp_dir:
            py_file = Path(temp_dir) / "test.py"
            py_file.write_text("from os import *\ndef hello():\n    return 'world'\n")  # Wildcard import

            result = await validator._validate_python_syntax(py_file)

            assert result.is_valid
            assert len(result.warnings) > 0
            assert "wildcard imports" in result.warnings[0].lower()

    @pytest.mark.asyncio
    async def test_validate_javascript_syntax_node_available(self, validator):
        """Test JavaScript validation when Node.js is available"""
        with tempfile.TemporaryDirectory() as temp_dir:
            js_file = Path(temp_dir) / "test.js"
            js_file.write_text("function hello() { return 'world'; }")

            # Mock successful Node.js validation
            with patch('subprocess.run') as mock_run:
                mock_run.return_value.returncode = 0

                result = await validator._validate_javascript_syntax(js_file)

                assert result.is_valid
                assert "JavaScript syntax valid" in result.details

    @pytest.mark.asyncio
    async def test_validate_javascript_syntax_node_unavailable(self, validator):
        """Test JavaScript validation fallback when Node.js is unavailable"""
        with tempfile.TemporaryDirectory() as temp_dir:
            js_file = Path(temp_dir) / "test.js"
            js_file.write_text("function hello() { return 'world'; }")

            # Mock Node.js not found
            with patch('subprocess.run', side_effect=FileNotFoundError):
                result = await validator._validate_javascript_syntax(js_file)

                # Should fall back to text validation
                assert result.is_valid

    @pytest.mark.asyncio
    async def test_validate_html_syntax_success(self, validator):
        """Test HTML syntax validation success"""
        with tempfile.TemporaryDirectory() as temp_dir:
            html_file = Path(temp_dir) / "test.html"
            html_file.write_text("""
            <!DOCTYPE html>
            <html>
            <head><title>Test</title></head>
            <body><h1>Hello</h1></body>
            </html>
            """)

            result = await validator._validate_html_syntax(html_file)

            assert result.is_valid
            assert "HTML syntax valid" in result.details

    @pytest.mark.asyncio
    async def test_validate_html_syntax_warnings(self, validator):
        """Test HTML syntax validation with warnings"""
        with tempfile.TemporaryDirectory() as temp_dir:
            html_file = Path(temp_dir) / "test.html"
            html_file.write_text("<div><p>Unclosed tags")  # Missing closing tags

            result = await validator._validate_html_syntax(html_file)

            assert result.is_valid  # Basic validation still passes
            assert len(result.warnings) > 0

    @pytest.mark.asyncio
    async def test_validate_css_syntax_success(self, validator):
        """Test CSS syntax validation success"""
        with tempfile.TemporaryDirectory() as temp_dir:
            css_file = Path(temp_dir) / "test.css"
            css_file.write_text("""
            .container {
                display: flex;
                padding: 20px;
            }

            .item {
                margin: 10px;
                color: blue;
            }
            """)

            result = await validator._validate_css_syntax(css_file)

            assert result.is_valid
            assert "CSS syntax valid" in result.details

    @pytest.mark.asyncio
    async def test_validate_css_syntax_warnings(self, validator):
        """Test CSS syntax validation with warnings"""
        with tempfile.TemporaryDirectory() as temp_dir:
            css_file = Path(temp_dir) / "test.css"
            css_file.write_text("""
            .container {
                display: flex
                padding: 20px;
            }
            """)  # Missing semicolon

            result = await validator._validate_css_syntax(css_file)

            assert result.is_valid  # Basic validation passes
            assert len(result.warnings) > 0

    # Functional testing validation tests

    @pytest.mark.asyncio
    async def test_run_functional_tests_success(self, validator):
        """Test successful functional test execution"""
        # Mock successful subprocess run
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "All tests passed successfully"

            result = await validator._run_functional_tests("npm test")

            assert result.is_valid
            assert "functional tests passed" in result.details.lower()

    @pytest.mark.asyncio
    async def test_run_functional_tests_failure(self, validator):
        """Test functional test execution failure"""
        # Mock failed subprocess run
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 1
            mock_run.return_value.stderr = "Test suite failed: 3 tests failing"

            result = await validator._run_functional_tests("npm test")

            assert not result.is_valid
            assert "functional tests failed" in result.error.lower()
            assert "3 tests failing" in result.error

    @pytest.mark.asyncio
    async def test_run_functional_tests_timeout(self, validator):
        """Test functional test execution timeout"""
        # Mock subprocess timeout
        with patch('subprocess.run', side_effect=subprocess.TimeoutExpired("npm test", 300)):
            result = await validator._run_functional_tests("npm test")

            assert not result.is_valid
            assert "timed out" in result.error.lower()

    # Integration validation tests

    @pytest.mark.asyncio
    async def test_validate_integrations_success(self, validator):
        """Test successful integration validation"""
        checks = ["http://localhost:3000/health", "db:test_connection"]

        # Mock successful HTTP and DB checks
        with patch.object(validator, '_validate_http_endpoint', return_value=ValidationResult.success("HTTP OK")):
            with patch.object(validator, '_validate_database_connection', return_value=ValidationResult.success("DB OK")):
                result = await validator._validate_integrations(checks)

                assert result.is_valid

    @pytest.mark.asyncio
    async def test_validate_integrations_mixed_results(self, validator):
        """Test integration validation with mixed results"""
        checks = ["http://localhost:3000/health", "http://localhost:8080/health"]

        # Mock one success, one failure
        def mock_http_endpoint(url):
            if "3000" in url:
                return ValidationResult.success("HTTP OK")
            else:
                return ValidationResult.failure("HTTP 500 error")

        with patch.object(validator, '_validate_http_endpoint', side_effect=mock_http_endpoint):
            result = await validator._validate_integrations(checks)

            assert not result.is_valid

    @pytest.mark.asyncio
    async def test_validate_http_endpoint_success(self, validator):
        """Test successful HTTP endpoint validation"""
        # Mock the entire HTTP validation method
        with patch.object(validator, '_validate_http_endpoint', return_value=ValidationResult.success("HTTP endpoint accessible: http://localhost:3000/health")):
            result = await validator._validate_http_endpoint("http://localhost:3000/health")

            assert result.is_valid
            assert "endpoint accessible" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_http_endpoint_error_status(self, validator):
        """Test HTTP endpoint validation with error status"""
        # Mock the HTTP validation method to return error status
        with patch.object(validator, '_validate_http_endpoint', return_value=ValidationResult.failure("HTTP endpoint error: http://localhost:3000/health returned 500")):
            result = await validator._validate_http_endpoint("http://localhost:3000/health")

            assert not result.is_valid
            assert "500" in result.error

    @pytest.mark.asyncio
    async def test_validate_http_endpoint_connection_error(self, validator):
        """Test HTTP endpoint validation with connection error"""
        # Mock the HTTP validation method to return connection error
        with patch.object(validator, '_validate_http_endpoint', return_value=ValidationResult.failure("HTTP endpoint check failed: http://localhost:3000/health - Connection refused")):
            result = await validator._validate_http_endpoint("http://localhost:3000/health")

            assert not result.is_valid
            assert "connection" in result.error.lower()

    # Task-type specific validation tests

    @pytest.mark.asyncio
    async def test_validate_component_task_success(self, validator, sample_task_result):
        """Test successful component task validation"""
        component_task = Task(
            title="Create Component",
            description="Create React component",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND
        )

        # Result with component file created
        result_with_component = TaskResult(
            success=True,
            files_created=["src/components/MyComponent.tsx"]
        )

        result = await validator._validate_component_task(component_task, result_with_component)

        assert result.is_valid
        assert "validation checks" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_api_endpoint_task_success(self, validator, sample_task_result):
        """Test successful API endpoint task validation"""
        api_task = Task(
            title="Create API Endpoint",
            description="Create REST API endpoint",
            type=TaskType.CREATE_API_ENDPOINT,
            agent_type=AgentType.BACKEND
        )

        # Result with API file created
        result_with_api = TaskResult(
            success=True,
            files_created=["src/api/users.py", "src/routers/user_router.py"]
        )

        result = await validator._validate_api_endpoint_task(api_task, result_with_api)

        assert result.is_valid
        assert "validation checks" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_database_migration_task_success(self, validator, sample_task_result):
        """Test successful database migration task validation"""
        migration_task = Task(
            title="Database Migration",
            description="Create database migration",
            type=TaskType.DATABASE_MIGRATION,
            agent_type=AgentType.BACKEND
        )

        # Result with migration file created
        result_with_migration = TaskResult(
            success=True,
            files_created=["migrations/001_create_users_table.sql"]
        )

        result = await validator._validate_database_migration_task(migration_task, result_with_migration)

        assert result.is_valid
        assert "validation checks" in result.details.lower()

    # Edge cases and error handling

    @pytest.mark.asyncio
    async def test_validate_task_with_no_requirements(self, validator):
        """Test validating task with no validation requirements"""
        minimal_task = Task(
            title="Minimal Task",
            description="Task with no specific validation requirements",
            type=TaskType.CONFIGURATION,
            agent_type=AgentType.ARCHITECT
        )

        minimal_result = TaskResult(success=True, output="Task completed")

        result = await validator.validate_task_completion(minimal_task, minimal_result)

        assert result.is_valid

    @pytest.mark.asyncio
    async def test_validate_task_with_empty_files_list(self, validator):
        """Test validating task with empty expected files list"""
        task_empty_files = Task(
            title="Task with Empty Files",
            description="Task with empty expected files",
            type=TaskType.TESTING,
            agent_type=AgentType.FRONTEND,
            expected_files=[],  # Empty list
            code_files=[]
        )

        result_empty = TaskResult(success=True)

        result = await validator.validate_task_completion(task_empty_files, result_empty)

        assert result.is_valid

    @pytest.mark.asyncio
    async def test_text_file_validation_empty_file(self, validator):
        """Test text file validation with empty file"""
        with tempfile.TemporaryDirectory() as temp_dir:
            empty_file = Path(temp_dir) / "empty.txt"
            empty_file.write_text("")

            result = await validator._validate_text_file(empty_file)

            assert result.is_valid
            assert len(result.warnings) > 0
            assert "empty" in result.warnings[0].lower()

    @pytest.mark.asyncio
    async def test_text_file_validation_encoding_error(self, validator):
        """Test text file validation with encoding error"""
        with tempfile.TemporaryDirectory() as temp_dir:
            binary_file = Path(temp_dir) / "binary.txt"
            binary_file.write_bytes(b'\x80\x81\x82\x83')  # Invalid UTF-8

            result = await validator._validate_text_file(binary_file)

            assert not result.is_valid
            assert "encoding error" in result.error.lower()

    # Performance and metrics tests

    @pytest.mark.asyncio
    async def test_validation_includes_metrics(self, validator, sample_task, sample_task_result):
        """Test that validation results include performance metrics"""
        with patch.object(validator, '_validate_files_exist', return_value=ValidationResult.success()):
            with patch.object(validator, '_validate_code_syntax', return_value=ValidationResult.success()):
                with patch.object(validator, '_run_functional_tests', return_value=ValidationResult.success()):
                    with patch.object(validator, '_validate_integrations', return_value=ValidationResult.success()):
                        with patch.object(validator, '_validate_task_type_specific', return_value=ValidationResult.success()):

                            result = await validator.validate_task_completion(sample_task, sample_task_result)

                            assert 'validation_time_seconds' in result.metrics
                            assert 'checks_performed' in result.metrics
                            assert 'task_type' in result.metrics
                            assert 'agent_type' in result.metrics

                            assert result.metrics['validation_time_seconds'] >= 0
                            assert result.metrics['checks_performed'] > 0
                            assert result.metrics['task_type'] == TaskType.CREATE_COMPONENT
                            assert result.metrics['agent_type'] == AgentType.FRONTEND

    # LLM service integration tests

    def test_llm_service_lazy_loading(self, validator):
        """Test LLM service lazy loading"""
        # Initially, LLM service should be None
        assert validator._llm_service is None

        # Mock the import to succeed
        with patch('builtins.__import__'):
            llm_service = validator.llm_service
            # In a real scenario, this would return the mocked service
            # For now, it might still be None due to import errors

    @pytest.mark.asyncio
    async def test_validation_without_llm_service(self, validator, sample_task, sample_task_result):
        """Test that validation works even when LLM service is unavailable"""
        # Ensure LLM service is not available
        validator._llm_service = None

        with patch.object(validator, '_validate_files_exist', return_value=ValidationResult.success()):
            with patch.object(validator, '_validate_code_syntax', return_value=ValidationResult.success()):
                with patch.object(validator, '_run_functional_tests', return_value=ValidationResult.success()):
                    with patch.object(validator, '_validate_integrations', return_value=ValidationResult.success()):
                        with patch.object(validator, '_validate_task_type_specific', return_value=ValidationResult.success()):

                            result = await validator.validate_task_completion(sample_task, sample_task_result)

                            # Should still work without LLM service
                            assert result.is_valid