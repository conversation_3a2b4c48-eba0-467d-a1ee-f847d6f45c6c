# Docker Hub Rate Limit Avoidance Configuration
# Use alternative container registries to avoid Docker Hub rate limits

# Redis - Use Bitnami instead of official Docker Hub image
REDIS_IMAGE=bitnami/redis:7.2

# PostgreSQL - Use specific version for better caching instead of CrunchyData
POSTGRES_IMAGE=postgres:15.5-alpine

# Nginx - Use specific version instead of latest/alpine
NGINX_IMAGE=nginx:1.25-alpine

# Python base images - Use specific versions for better caching
PYTHON_BASE_IMAGE=python:3.11.7-slim-bullseye

# Monitoring images
GRAFANA_IMAGE=grafana/grafana:9.5.2
PROMTAIL_IMAGE=grafana/promtail:2.9.0
LOKI_IMAGE=grafana/loki:2.9.0

# Alternative registry configuration
REGISTRY_MIRROR_URL=http://localhost:5000
USE_REGISTRY_MIRROR=false

# Docker Hub authentication (set these if you have Docker Hub account)
# DOCKERHUB_USERNAME=your_username
# DOCKERHUB_PASSWORD=your_password_or_token
