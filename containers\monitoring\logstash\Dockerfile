# syntax=docker/dockerfile:1
# Logstash Dockerfile for log processing
# Uses official Logstash image with security best practices

FROM docker.elastic.co/logstash/logstash:8.11.0

# Create logstash user explicitly with specific UID/GID for security
USER root
RUN (getent group logstash || groupadd -r -g 1000 logstash) && \
  (getent passwd logstash || useradd -r -g logstash -u 1000 -d /usr/share/logstash -s /bin/bash logstash)

# Install curl for health checks
RUN apt-get update && apt-get install -y --no-install-recommends \
  curl \
  && rm -rf /var/lib/apt/lists/*

# Create necessary directories with proper permissions
RUN mkdir -p /usr/share/logstash/data && \
  mkdir -p /usr/share/logstash/logs && \
  chown -R logstash:logstash /usr/share/logstash && \
  chown -R logstash:logstash /var/lib/logstash && \
  chown -R logstash:logstash /var/log/logstash

# Copy pipeline configuration if it exists
COPY pipeline/ /usr/share/logstash/pipeline/

# Switch to non-root user
USER logstash

# Expose Logstash ports
EXPOSE 5044 9600

# Health check for Logstash service
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:9600/_node/stats || exit 1

# Set security and project labels
LABEL org.opencontainers.image.title="AI Coding Agent - Logstash" \
  org.opencontainers.image.description="Logstash log processing for AI Coding Agent" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team" \
  security.non-root="true" \
  security.user="logstash"

# Start Logstash
CMD ["logstash"]
