#!/usr/bin/env python3
"""
Standalone Enhancement Validation Script

Validates the four enhancement areas without configuration dependencies:
1. Template extensibility - File structure and content validation
2. Environment isolation - Port management and Docker network configs
3. Resource management - Resource limit configurations and monitoring
4. Rollback capability - Checkpoint and rollback action validation

Usage:
    python scripts/validate-enhancements-standalone.py
"""

import logging
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class StandaloneEnhancementValidator:
    """Validates enhancements without external dependencies."""

    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.validation_results: List[Dict[str, Any]] = []

    def log_result(self, test_name: str, success: bool, message: str, details: Optional[Dict[str, Any]] = None):
        """Log validation result."""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "details": details or {}
        }
        self.validation_results.append(result)

        level = logging.INFO if success else logging.ERROR
        status = " PASS" if success else " FAIL"
        logger.log(level, f"{test_name}: {status} - {message}")

    def validate_template_extensibility(self) -> bool:
        """Validate 1: Template extensibility implementation."""
        logger.info(" Validating Template Extensibility...")

        # Check template registry service exists
        registry_file = self.project_root / "containers" / "ai-orchestrator" / "src" / "services" / "template_registry.py"
        if not registry_file.exists():
            self.log_result("template_registry_service", False, "Template registry service not found")
            return False

        # Validate template registry content
        content = registry_file.read_text()
        required_features = [
            "class TemplateRegistry",
            "TemplateType",
            "TemplateMetadata",
            "get_template_metadata",
            "register_custom_template",
            "get_template_for_project_type"
        ]

        missing_features = [f for f in required_features if f not in content]
        if missing_features:
            self.log_result("template_registry_features", False, f"Missing features: {missing_features}")
            return False

        # Check for multiple template types
        template_types = ["WEBAPP", "REACT_FRONTEND", "MICROSERVICE", "NEXTJS_APP"]
        found_types = [t for t in template_types if t in content]

        if len(found_types) < 3:
            self.log_result("multiple_template_types", False, f"Only found {len(found_types)} template types")
            return False

        self.log_result("template_extensibility", True, f"Template registry with {len(found_types)} types implemented")
        return True

    def validate_environment_isolation(self) -> bool:
        """Validate 2: Environment isolation implementation."""
        logger.info(" Validating Environment Isolation...")

        # Check port manager service exists
        port_manager_file = self.project_root / "containers" / "ai-orchestrator" / "src" / "services" / "port_manager.py"
        if not port_manager_file.exists():
            self.log_result("port_manager_service", False, "Port manager service not found")
            return False

        # Validate port manager content
        content = port_manager_file.read_text()
        required_features = [
            "class PortManager",
            "allocate_port",
            "release_port",
            "get_project_port",
            "_is_port_available",
            "cleanup_inactive_ports"
        ]

        missing_features = [f for f in required_features if f not in content]
        if missing_features:
            self.log_result("port_manager_features", False, f"Missing features: {missing_features}")
            return False

        # Check docker-compose template has network isolation
        compose_template = self.project_root / "templates" / "webapp" / "docker-compose.yml.template"
        if compose_template.exists():
            compose_content = compose_template.read_text()
            if "__PROJECT_NAME__-network" not in compose_content:
                self.log_result("docker_network_isolation", False, "Docker network isolation not configured")
                return False

        self.log_result("environment_isolation", True, "Port management and network isolation implemented")
        return True

    def validate_resource_management(self) -> bool:
        """Validate 3: Resource management implementation."""
        logger.info(" Validating Resource Management...")

        # Check resource manager service exists
        resource_manager_file = self.project_root / "containers" / "ai-orchestrator" / "src" / "services" / "resource_manager.py"
        if not resource_manager_file.exists():
            self.log_result("resource_manager_service", False, "Resource manager service not found")
            return False

        # Validate resource manager content
        content = resource_manager_file.read_text()
        required_features = [
            "class ResourceManager",
            "ResourceLimits",
            "get_default_resource_limits",
            "monitor_project_resources",
            "check_resource_limits",
            "cleanup_abandoned_projects"
        ]

        missing_features = [f for f in required_features if f not in content]
        if missing_features:
            self.log_result("resource_manager_features", False, f"Missing features: {missing_features}")
            return False

        # Check for template-specific resource limits
        if "webapp" not in content or "microservice" not in content:
            self.log_result("template_specific_limits", False, "Template-specific resource limits not found")
            return False

        self.log_result("resource_management", True, "Resource management with template-specific limits implemented")
        return True

    def validate_rollback_capability(self) -> bool:
        """Validate 4: Rollback capability implementation."""
        logger.info(" Validating Rollback Capability...")

        # Check rollback manager service exists
        rollback_manager_file = self.project_root / "containers" / "ai-orchestrator" / "src" / "services" / "rollback_manager.py"
        if not rollback_manager_file.exists():
            self.log_result("rollback_manager_service", False, "Rollback manager service not found")
            return False

        # Validate rollback manager content
        content = rollback_manager_file.read_text()
        required_features = [
            "class RollbackManager",
            "RollbackStage",
            "RollbackCheckpoint",
            "create_checkpoint",
            "rollback_to_checkpoint",
            "_execute_rollback_action"
        ]

        missing_features = [f for f in required_features if f not in content]
        if missing_features:
            self.log_result("rollback_manager_features", False, f"Missing features: {missing_features}")
            return False

        # Check for multiple rollback stages
        rollback_stages = [
            "DIRECTORY_CREATION",
            "TEMPLATE_COPYING",
            "DATABASE_RECORD",
            "DOCKER_COMPOSE_UP",
            "CONTAINER_HEALTH"
        ]

        found_stages = [s for s in rollback_stages if s in content]
        if len(found_stages) < 4:
            self.log_result("rollback_stages", False, f"Only found {len(found_stages)} rollback stages")
            return False

        self.log_result("rollback_capability", True, f"Rollback system with {len(found_stages)} stages implemented")
        return True

    def validate_integration(self) -> bool:
        """Validate 5: Integration with existing system."""
        logger.info(" Validating System Integration...")

        # Check enhanced ProjectRepository
        project_repo_file = self.project_root / "containers" / "ai-orchestrator" / "src" / "repository" / "project_repository.py"
        if not project_repo_file.exists():
            self.log_result("project_repository_exists", False, "ProjectRepository not found")
            return False

        content = project_repo_file.read_text()

        # Check for enhanced create_project method
        integration_features = [
            "template_type",
            "get_template_registry",
            "get_port_manager",
            "get_resource_manager",
            "get_rollback_manager",
            "_provision_project_from_template"
        ]

        missing_features = [f for f in integration_features if f not in content]
        if missing_features:
            self.log_result("integration_features", False, f"Missing integration features: {missing_features}")
            return False

        # Check ArchitectAgent has provisioning logic
        architect_file = self.project_root / "containers" / "ai-orchestrator" / "src" / "agents" / "architect.py"
        if architect_file.exists():
            architect_content = architect_file.read_text()
            if "_execute_provisioning_phase" not in architect_content:
                self.log_result("architect_provisioning", False, "ArchitectAgent provisioning not integrated")
                return False

        self.log_result("system_integration", True, "All services integrated with existing system")
        return True

    def validate_documentation(self) -> bool:
        """Validate 6: Documentation completeness."""
        logger.info(" Validating Documentation...")

        required_docs = [
            "docs/TEMPLATE_BASED_PROVISIONING_GUIDE.md",
            "TEMPLATE_PROVISIONING_INTEGRATION_CHECKLIST.md",
            "TEMPLATE_PROVISIONING_IMPLEMENTATION_COMPLETE.md",
            "TEMPLATE_PROVISIONING_ENHANCEMENTS_COMPLETE.md",
            "ENHANCED_PROVISIONING_DEPLOYMENT_GUIDE.md"
        ]

        missing_docs = []
        for doc_path in required_docs:
            if not (self.project_root / doc_path).exists():
                missing_docs.append(doc_path)

        if missing_docs:
            self.log_result("documentation_complete", False, f"Missing documentation: {missing_docs}")
            return False

        self.log_result("documentation_complete", True, f"All {len(required_docs)} documentation files present")
        return True

    def generate_report(self) -> Dict[str, Any]:
        """Generate validation report."""
        total_tests = len(self.validation_results)
        passed_tests = sum(1 for result in self.validation_results if result["success"])
        failed_tests = total_tests - passed_tests

        return {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%"
            },
            "results": self.validation_results,
            "overall_status": "PASS" if failed_tests == 0 else "FAIL"
        }

    def run_all_validations(self) -> bool:
        """Run all standalone validations."""
        logger.info(" Starting Standalone Enhancement Validation")
        logger.info("=" * 60)

        validations = [
            self.validate_template_extensibility,
            self.validate_environment_isolation,
            self.validate_resource_management,
            self.validate_rollback_capability,
            self.validate_integration,
            self.validate_documentation
        ]

        all_passed = True
        for validation in validations:
            try:
                result = validation()
                if not result:
                    all_passed = False
            except Exception as e:
                logger.error(f"Validation failed with exception: {e}")
                all_passed = False

        # Generate and display report
        report = self.generate_report()

        logger.info("=" * 60)
        logger.info(" STANDALONE ENHANCEMENT VALIDATION REPORT")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {report['summary']['total_tests']}")
        logger.info(f"Passed: {report['summary']['passed']}")
        logger.info(f"Failed: {report['summary']['failed']}")
        logger.info(f"Success Rate: {report['summary']['success_rate']}")
        logger.info(f"Overall Status: {report['overall_status']}")

        if report['summary']['failed'] > 0:
            logger.info("\n FAILED TESTS:")
            for result in report['results']:
                if not result['success']:
                    logger.info(f"  - {result['test']}: {result['message']}")

        return all_passed


def main():
    """Main validation function."""
    validator = StandaloneEnhancementValidator()
    success = validator.run_all_validations()

    if success:
        logger.info("\n All enhancement validations passed!")
        logger.info(" Template extensibility implemented")
        logger.info(" Environment isolation implemented")
        logger.info(" Resource management implemented")
        logger.info(" Rollback capability implemented")
        logger.info(" System integration complete")
        logger.info(" Documentation complete")
        return 0
    else:
        logger.error("\n Some enhancement validations failed.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
