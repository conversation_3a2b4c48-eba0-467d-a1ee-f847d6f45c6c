"use client";

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import toast, { Toaster } from 'react-hot-toast';
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";

interface DeploymentIntegration {
  id: string;
  provider: string;
  team_name: string;
}

const DeploymentsPage = () => {
  const { data: session } = useSession();
  const params = useParams();
  const projectId = params.projectId as string;
  const [integrations, setIntegrations] = useState<DeploymentIntegration[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeploying, setIsDeploying] = useState<string | null>(null);

  useEffect(() => {
    if (session) {
      fetch(`/api/projects/${projectId}/deployments`)
        .then(res => res.json())
        .then(data => {
          setIntegrations(data);
          setIsLoading(false);
        })
        .catch(() => {
          toast.error("Failed to load deployment configurations.");
          setIsLoading(false);
        });
    }
  }, [session]);

  if (!session) {
    redirect('/login');
  }

  const handleDeploy = async (integrationId: string) => {
    setIsDeploying(integrationId);
    const toastId = toast.loading('Starting deployment...');

    try {
      const response = await fetch(`/api/projects/${projectId}/deploy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ deployment_integration_id: integrationId }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`Success! Deployed to ${result.url}`, { id: toastId });
      } else {
        toast.error(`Error: ${result.detail || 'Could not start deployment.'}`, { id: toastId });
      }
    } catch (error) {
      toast.error('Error: Could not start deployment.', { id: toastId });
    } finally {
      setIsDeploying(null);
    }
  };

  return (
    <>
      <Toaster position="top-center" reverseOrder={false} />
      <div className="p-8">
        <h1 className="text-3xl font-bold mb-6">Deployments</h1>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-2xl font-semibold mb-4">Deployment Configurations</h2>
          {isLoading ? (
            <p>Loading configurations...</p>
          ) : integrations.length > 0 ? (
            <ul className="space-y-4">
              {integrations.map((integration) => (
                <li key={integration.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-semibold">{integration.provider}</p>
                    <p className="text-sm text-gray-500">{integration.team_name}</p>
                  </div>
                  <button
                    onClick={() => handleDeploy(integration.id)}
                    disabled={isDeploying === integration.id}
                    className="bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-300"
                  >
                    {isDeploying === integration.id ? 'Deploying...' : 'Deploy Now'}
                  </button>
                </li>
              ))}
            </ul>
          ) : (
            <p>No deployment configurations found.</p>
          )}
        </div>
        <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-2xl font-semibold mb-4">Deployment History</h2>
          <p className="text-gray-600">Deployment history will be shown here in a future update.</p>
        </div>
      </div>
    </>
  );
};

export default DeploymentsPage;
