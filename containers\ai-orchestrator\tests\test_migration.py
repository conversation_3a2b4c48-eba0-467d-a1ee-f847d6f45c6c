#!/usr/bin/env python3
"""
Database Migration Test Script

This script helps test the Alembic migration for the project_exports table.
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header(title: str):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🗄️  {title}")
    print(f"{'='*60}")

def print_step(step: str, description: str):
    """Print a test step."""
    print(f"\n{step} {description}")
    print("-" * 50)

def print_success(message: str):
    """Print success message."""
    print(f"   ✅ {message}")

def print_error(message: str):
    """Print error message."""
    print(f"   ❌ {message}")

def print_info(message: str):
    """Print info message."""
    print(f"   ℹ️  {message}")

def run_command(command: str, description: str = None) -> tuple[bool, str, str]:
    """Run a shell command and return success, stdout, stderr."""
    if description:
        print_info(f"Running: {description}")

    print_info(f"Command: {command}")

    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30
        )

        success = result.returncode == 0
        return success, result.stdout, result.stderr

    except subprocess.TimeoutExpired:
        return False, "", "Command timed out after 30 seconds"
    except Exception as e:
        return False, "", str(e)

def check_alembic_setup():
    """Check if Alembic is properly set up."""
    print_step("1️⃣", "Checking Alembic Setup")

    # Check if alembic.ini exists
    if Path("alembic.ini").exists():
        print_success("alembic.ini found")
    else:
        print_error("alembic.ini not found")
        return False

    # Check if alembic directory exists
    if Path("alembic").exists():
        print_success("alembic directory found")
    else:
        print_error("alembic directory not found")
        return False

    # Check if versions directory exists
    versions_dir = Path("alembic/versions")
    if versions_dir.exists():
        print_success("alembic/versions directory found")

        # List migration files
        migration_files = list(versions_dir.glob("*.py"))
        print_info(f"Found {len(migration_files)} migration files:")
        for file in migration_files:
            print_info(f"  - {file.name}")

        # Check if our export migration exists
        export_migration = versions_dir / "005_project_exports.py"
        if export_migration.exists():
            print_success("Export migration file found: 005_project_exports.py")
        else:
            print_error("Export migration file not found: 005_project_exports.py")
            return False
    else:
        print_error("alembic/versions directory not found")
        return False

    return True

def check_database_connection():
    """Check if we can connect to the database."""
    print_step("2️⃣", "Checking Database Connection")

    # Try to get current migration version
    success, stdout, stderr = run_command(
        "alembic current",
        "Check current migration version"
    )

    if success:
        print_success("Database connection successful")
        if stdout.strip():
            print_info(f"Current migration: {stdout.strip()}")
        else:
            print_info("No migrations applied yet")
        return True
    else:
        print_error("Database connection failed")
        print_error(f"Error: {stderr}")
        print_info("Make sure your database is running and connection settings are correct")
        return False

def check_migration_status():
    """Check the current migration status."""
    print_step("3️⃣", "Checking Migration Status")

    # Check migration history
    success, stdout, stderr = run_command(
        "alembic history",
        "Get migration history"
    )

    if success:
        print_success("Migration history retrieved")
        if stdout.strip():
            print_info("Migration history:")
            for line in stdout.strip().split('\n'):
                print_info(f"  {line}")
        else:
            print_info("No migration history found")
    else:
        print_error("Failed to get migration history")
        print_error(f"Error: {stderr}")
        return False

    # Check what migrations need to be applied
    success, stdout, stderr = run_command(
        "alembic show head",
        "Show head migration"
    )

    if success:
        print_success("Head migration information retrieved")
        if stdout.strip():
            print_info(f"Head migration: {stdout.strip()}")
    else:
        print_info("Could not retrieve head migration info")

    return True

def run_migration():
    """Run the database migration."""
    print_step("4️⃣", "Running Database Migration")

    # First, let's see what would be upgraded
    success, stdout, stderr = run_command(
        "alembic upgrade --sql head",
        "Generate SQL for migration (dry run)"
    )

    if success:
        print_success("Migration SQL generated successfully")
        if stdout.strip():
            print_info("Migration SQL preview:")
            # Show first few lines
            lines = stdout.strip().split('\n')[:10]
            for line in lines:
                print_info(f"  {line}")
            if len(stdout.strip().split('\n')) > 10:
                # Avoid backslash inside f-string expression by precomputing
                total_lines = len(stdout.strip().split('\n'))
                remaining = total_lines - 10
                print_info(f"  ... and {remaining} more lines")
    else:
        print_error("Failed to generate migration SQL")
        print_error(f"Error: {stderr}")
        return False

    # Ask for confirmation
    print_info("Ready to run the actual migration.")
    response = input("Do you want to proceed with the migration? (y/N): ").strip().lower()

    if response != 'y':
        print_info("Migration cancelled by user")
        return False

    # Run the actual migration
    success, stdout, stderr = run_command(
        "alembic upgrade head",
        "Apply database migration"
    )

    if success:
        print_success("Migration completed successfully!")
        if stdout.strip():
            print_info("Migration output:")
            for line in stdout.strip().split('\n'):
                print_info(f"  {line}")
        return True
    else:
        print_error("Migration failed!")
        print_error(f"Error: {stderr}")
        return False

def verify_migration():
    """Verify that the migration was applied correctly."""
    print_step("5️⃣", "Verifying Migration")

    # Check current version
    success, stdout, stderr = run_command(
        "alembic current",
        "Check current migration version"
    )

    if success:
        current_version = stdout.strip()
        if "005_project_exports" in current_version:
            print_success("Export migration is now current!")
            print_info(f"Current version: {current_version}")
        else:
            print_error("Export migration is not current")
            print_info(f"Current version: {current_version}")
            return False
    else:
        print_error("Failed to check current migration version")
        return False

    # Try to connect and check if table exists (if we have database access)
    print_info("Migration verification completed")
    return True

def main():
    """Main test function."""
    print_header("Database Migration Test")

    print_info(f"Working directory: {os.getcwd()}")
    print_info(f"Python version: {sys.version}")

    # Check if we're in the right directory
    if not Path("alembic.ini").exists():
        print_error("Not in the correct directory. Please run from containers/ai-orchestrator/")
        return False

    tests = [
        ("Alembic Setup", check_alembic_setup),
        ("Database Connection", check_database_connection),
        ("Migration Status", check_migration_status),
        ("Run Migration", run_migration),
        ("Verify Migration", verify_migration),
    ]

    for test_name, test_func in tests:
        try:
            result = test_func()
            if not result:
                print_error(f"Test failed: {test_name}")
                print_info("Stopping migration test due to failure")
                return False
        except Exception as e:
            print_error(f"Test crashed: {test_name} - {e}")
            return False

    print_header("Migration Test Complete")
    print_success("All migration tests passed!")
    print_info("The project_exports table should now be available in your database")

    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Migration test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Migration test crashed: {e}")
        sys.exit(1)
