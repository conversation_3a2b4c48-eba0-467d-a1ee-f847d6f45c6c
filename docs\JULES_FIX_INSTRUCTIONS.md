# Jules Repair Plan: Test Suite Fixes

This document catalogs the problems discovered when running the fast test suite and gives step-by-step instructions for <PERSON> to fix them automatically.

Summary
- Repo root: `C:\Users\<USER>\Downloads\codingagenttwo`
- Fast test command: `pytest -q -m "not integration"`
- Venv path: `.venv` (activate before running tests)
- Goal: make the fast test suite stable and reduce failures by at least 70%.

Priority problems and how <PERSON> can fix them

1) ArchitectAgent instantiation (high priority)
- Symptom: many tests fail with: "TypeError: Can't instantiate abstract class ArchitectAgent without an implementation for abstract method '_execute_core'".
- Cause: tests instantiate `ArchitectAgent()` directly; the class is abstract.
- Fix (automated): add a minimal, test-only concrete subclass or fixture that implements `_execute_core` as a no-op.
  - Files to edit: repository `conftest.py` (repo root) or add a new test helper under `tests/`.
  - Example approach: add `TestArchitectAgent` subclass and a `@pytest.fixture def architect_agent(): return TestArchitectAgent()`.
  - Rationale: low-risk, test-only change that unblocks many tests.

2) VectorStorageService signature compatibility (high priority)
- Symptom: tests fail with unexpected keyword arguments and missing methods (e.g., `store_public_knowledge`, `store_document_chunks`, `search_knowledge_tiers`, `get_knowledge_collections`).
- Cause: implementation changed public method names/signatures; tests still call older API.
- Fix (automated): add backward-compatible adapter/wrapper methods to `containers/ai-orchestrator/src/services/vector_service.py` that accept the older signatures and delegate to current implementations.
  - Keep wrappers thin and non-breaking.
  - Files to edit: `containers/ai-orchestrator/src/services/vector_service.py`.

3) Missing auth constants and token expiry representation (high priority)
- Symptom: ImportError for `ALGORITHM`; tests failing when asserting token exp compared to now (format mismatch).
- Cause: tests expect `ALGORITHM` exported and `exp` as an integer timestamp.
- Fix (automated): update `containers/ai-orchestrator/src/utils/auth.py`:
  - Export `ALGORITHM = settings.JWT_ALGORITHM` (alias near `SECRET_KEY`).
  - Ensure `create_access_token` uses `to_encode['exp'] = int(expire.timestamp())` (integer epoch seconds) so tests comparing numeric timestamps pass.

4) conftest shims: ENVIRONMENT normalization and monkeypatch.setenv compatibility (high priority)
- Symptom: Pydantic Settings validation error for `ENVIRONMENT='test'`; MonkeyPatch.setenv called with `raising=False` causing TypeError.
- Cause: tests or environment use `test` alias; some pytest versions' monkeypatch lacks `raising` kw.
- Fix (automated): add an `autouse` fixture to repo `conftest.py`:
  - Normalize `ENVIRONMENT` to `testing` (coerce `test` -> `testing`).
  - Provide a small compatibility helper on `monkeypatch`, e.g. `monkeypatch.setenv_compat(name, value, raising=True)` that delegates to `monkeypatch.setenv` and falls back to `os.environ[name]=value`.
  - Files: `conftest.py` in repo root.

5) role_management atomic replace on Windows (medium priority)
- Symptom: FileExistsError on rename when saving config in tests on Windows.
- Cause: using `os.rename` can fail on Windows if target exists.
- Fix (automated): change atomic rename to `os.replace(temp_path, CONFIG_FILE_PATH)` or call `sync_os.replace` inside `run_in_executor` for the async branch. Add cleanup and retry logic.
  - File: `containers/ai-orchestrator/src/router/role_management.py`.

6) Async/coroutine mismatches (medium priority)
- Symptom: "'coroutine' object is not iterable" and "coroutine was never awaited" in `project_repository` and `enhanced_llm_service` tests.
- Cause: sync/async mixups — code iterates over or indexes a coroutine instead of awaiting it, or mocks return coroutine objects when sync values expected.
- Fix (automated): inspect failing stack traces, and apply minimal fixes:
  - Add missing `await` where callers expect awaited values.
  - If helper should be sync, consider adding `asyncio.run(...)` or a sync wrapper used in tests.
  - Alternatively, update tests to mock with `AsyncMock` where appropriate (less preferred here — prefer minimal code fixes).
  - Files to inspect: `containers/ai-orchestrator/src/repository/project_repository.py`, `containers/ai-orchestrator/src/services/enhanced_llm_service.py`.

7) LLM / external service connectivity (medium priority)
- Symptom: Ollama/OpenRouter connection refused or real network calls cause test failures.
- Cause: unit tests run without services; code attempts network calls.
- Fix (automated): add a runtime guard in LLM initialization (check `ENVIRONMENT == 'testing'`) to use a deterministic no-op provider for embeddings and generation during unit tests; or add mocks around network calls.
  - Files: `containers/ai-orchestrator/src/services/enhanced_llm_service.py`, `containers/ai-orchestrator/src/services/vector_service.py`.

8) Indentation / syntax errors introduced during patching (blocking)
- Symptom: IndentationError in `role_management.py` blocked collection.
- Fix: correct indentation/syntax; ensure code imports without syntax errors before running tests.

9) Test harness adjustments (optional)
- Add a small smoke test verifying `ENVIRONMENT` is `testing` and `ALGORITHM` is available. This provides quick verification after fixes.
- File: `tests/test_smoke_env.py` (optional small test).

Execution plan for Jules (safe, incremental)
1. Create a feature branch `fix/tests-compatibility`.
2. Create a backup commit or PR draft so each change is reviewable.
3. Apply changes in this order: fix syntax/indentation -> conftest shims (ENV & monkeypatch) -> ArchitectAgent stub -> auth exports & token exp -> VectorService adapters -> role_management atomic replace -> async/await fixes -> LLM no-op provider.
4. Run fast tests after each logical change: `pytest -q -m "not integration"` and capture the output.
5. If any failing tests point to deeper API drift, add minimal targeted adapters or create a short test demonstrating the expected API, then fix.
6. Create PR with before/after pytest output, list of files changed and reasoning.

Commands Jules should run

```powershell
Set-Location 'C:\Users\<USER>\Downloads\codingagenttwo'
& '.venv\Scripts\Activate.ps1'
# Fast tests only
pytest -q -m "not integration"
```

Acceptance criteria
- `pytest -q -m "not integration"` runs with zero collection errors (no syntax/indentation errors).
- Failures reduced by >= 70% vs baseline (baseline: 49 failed, 7 errors in earlier run).
- `ENVIRONMENT` normalized to `testing` and `ALGORITHM` importable from `src.utils.auth`.
- PR contains short summary of changes and test outputs (before/after).

Files for Jules to edit
- `conftest.py` (repo root)
- `containers/ai-orchestrator/src/agents/architect_agent.py` or add stub in `conftest.py`
- `containers/ai-orchestrator/src/services/vector_service.py`
- `containers/ai-orchestrator/src/utils/auth.py`
- `containers/ai-orchestrator/src/router/role_management.py`
- `containers/ai-orchestrator/src/repository/project_repository.py` (investigate for missing awaits)
- `containers/ai-orchestrator/src/services/enhanced_llm_service.py` (investigate and guard network calls)

Notes and constraints for Jules
- Prefer minimal, reversible changes grouped into logical commits.
- Avoid enabling integration tests unless services are started explicitly.
- If a change is ambiguous, create a small failing test to reproduce the current behavior, then fix it.

Contact / follow-ups
- If Jules is blocked by missing credentials or by needing to start local services (Ollama/Supabase), it should update the PR with a list of required services and recommended steps.
- Provide full pytest output in the PR check details so reviewers can validate fixes.


---
Generated by assistant to be used as the Jules instruction set for automated repairs. If you want, I can now apply the same fixes here and re-run the fast test suite, or hand this file to Jules directly.
