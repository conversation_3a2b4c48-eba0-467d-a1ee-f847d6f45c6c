from abc import ABC, abstractmethod
from typing import Dict, Type

class Exporter(ABC):
    @abstractmethod
    def export(self, project_id: str, **kwargs) -> str:
        pass

class ExportRegistry:
    def __init__(self):
        self._exporters: Dict[str, Type[Exporter]] = {}

    def register(self, format_name: str, exporter_class: Type[Exporter]):
        self._exporters[format_name] = exporter_class

    def get_exporter(self, format_name: str) -> Type[Exporter]:
        if format_name not in self._exporters:
            raise ValueError(f"No exporter found for format: {format_name}")
        return self._exporters[format_name]

export_registry = ExportRegistry()

def register_exporter(format_name: str):
    def decorator(cls: Type[Exporter]):
        export_registry.register(format_name, cls)
        return cls
    return decorator
