#!/bin/sh
# Enhanced entrypoint script for Next.js container with Windows/Docker Desktop compatibility
# Author: AI Coding Agent Team
# Version: 2.0.0

set -e

# Function to log with timestamp
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] ENTRYPOINT: $1"
}

log "Starting Next.js container initialization..."

# Detect if we're running on Windows/Docker Desktop (bind mount limitations)
WINDOWS_DETECTED=false
if mount | grep -q "type fuse.grpcfuse" || mount | grep -q "osxfs" || mount | grep -q "wslfs"; then
    WINDOWS_DETECTED=true
    log "Windows/Docker Desktop detected - using compatibility mode"
else
    log "Linux environment detected - using standard mode"
fi

# Ensure essential directories exist
log "Creating essential directories..."
mkdir -p /home/<USER>/app/.next /home/<USER>/app/node_modules /home/<USER>/app/public

# Create next-env.d.ts if it doesn't exist or is not writable
log "Ensuring next-env.d.ts exists and is writable..."
if [ ! -f "/home/<USER>/app/next-env.d.ts" ]; then
    log "Creating missing next-env.d.ts file"
    cat > /home/<USER>/app/next-env.d.ts << 'EOF'
// Generated by Docker entrypoint
/// <reference types="next" />
/// <reference types="next/image-types/global" />
EOF
fi

# Test if we can write to next-env.d.ts
if ! echo "// Test write" >> /home/<USER>/app/next-env.d.ts 2>/dev/null; then
    if [ "$WINDOWS_DETECTED" = "true" ]; then
        log "WARNING: Cannot write to next-env.d.ts (Windows bind mount limitation)"
        log "INFO: Next.js may fail to update type definitions during development"
    else
        log "ERROR: Cannot write to next-env.d.ts - unexpected permission issue"
    fi
else
    # Remove the test line
    sed -i '$d' /home/<USER>/app/next-env.d.ts 2>/dev/null || true
    log "next-env.d.ts is writable"
fi

# Try to fix ownership and permissions (will fail silently on Windows)
if [ "$WINDOWS_DETECTED" = "false" ]; then
    log "Attempting to fix ownership and permissions..."
    if chown -R nextjs:nextjs /home/<USER>/app/.next /home/<USER>/app/node_modules 2>/dev/null; then
        log "Successfully fixed directory ownership"
    else
        log "WARNING: Could not fix directory ownership"
    fi

    if chmod -R 755 /home/<USER>/app/.next /home/<USER>/app/node_modules 2>/dev/null; then
        log "Successfully set directory permissions"
    else
        log "WARNING: Could not set directory permissions"
    fi
else
    log "Skipping ownership/permission fixes (Windows compatibility mode)"
fi

# Check if SWC binaries are available for optimal performance
log "Checking SWC binary availability..."
SWC_AVAILABLE=false
if [ -d "/home/<USER>/app/node_modules/@next/swc-linux-x64-gnu" ] || [ -d "/home/<USER>/app/node_modules/@next/swc-linux-x64-musl" ]; then
    SWC_AVAILABLE=true
    log "SWC binaries found - Next.js will use fast compilation"
else
    log "SWC binaries not found - Next.js will use fallback compilation (slower)"
fi

# Try to switch to nextjs user, fallback to root with warning
log "Attempting to switch to nextjs user..."
if command -v gosu >/dev/null 2>&1; then
    if gosu nextjs true 2>/dev/null; then
        log "Successfully switching to nextjs user"
        log "Starting application: $*"
        exec gosu nextjs "$@"
    else
        log "WARNING: Cannot switch to nextjs user (likely Windows/Docker Desktop limitation)"
        log "WARNING: Application will run as root user (security risk in production)"
        log "Starting application as root: $*"
        exec "$@"
    fi
elif command -v su-exec >/dev/null 2>&1; then
    if su-exec nextjs true 2>/dev/null; then
        log "Successfully switching to nextjs user (using su-exec)"
        log "Starting application: $*"
        exec su-exec nextjs "$@"
    else
        log "WARNING: Cannot switch to nextjs user (likely Windows/Docker Desktop limitation)"
        log "WARNING: Application will run as root user (security risk in production)"
        log "Starting application as root: $*"
        exec "$@"
    fi
else
    # No user switching utility available, try basic su
    if su nextjs -s /bin/sh -c 'true' 2>/dev/null; then
        log "Successfully switching to nextjs user (using su)"
        log "Starting application: $*"
        exec su nextjs -s /bin/sh -c "exec $*"
    else
        log "WARNING: No user switching utility available or user switch failed"
        log "WARNING: Application will run as root user (security risk in production)"
        log "Starting application as root: $*"
        exec "$@"
    fi
fi
