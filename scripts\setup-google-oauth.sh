#!/bin/bash
# Supabase Google OAuth Setup Script

echo "=== AI Coding Agent - Supabase Google OAuth Setup ==="
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "1. Checking prerequisites..."
if ! command_exists docker; then
    echo "[FAIL] Docker is not installed or not in PATH"
    exit 1
fi

if ! command_exists docker-compose; then
    echo "[FAIL] Docker Compose is not installed or not in PATH"
    exit 1
fi

echo "[OK] Docker and Docker Compose are available"
echo ""

# Check if environment file exists
echo "2. Checking environment configuration..."
if [ ! -f "containers/user-portal/.env.local" ]; then
    echo "[WARN] .env.local file not found"
    echo "       Creating from template..."

    if [ -f "containers/user-portal/.env.example" ]; then
        cp "containers/user-portal/.env.example" "containers/user-portal/.env.local"
        echo "[OK] Environment template copied to .env.local"
        echo "[ACTION REQUIRED] Please edit containers/user-portal/.env.local and add your credentials"
    else
        echo "[FAIL] .env.example template not found"
        exit 1
    fi
else
    echo "[OK] Environment file exists"
fi
echo ""

# Rebuild containers with new dependencies
echo "3. Rebuilding containers with new dependencies..."
echo "   This may take a few minutes..."

docker-compose -f docker-compose.yml -f docker-compose.dev.yml down --remove-orphans
if [ $? -ne 0 ]; then
    echo "[FAIL] Failed to stop containers"
    exit 1
fi

# Build with no cache to ensure dependencies are installed
docker-compose -f docker-compose.yml -f docker-compose.dev.yml build --no-cache user-portal
if [ $? -ne 0 ]; then
    echo "[FAIL] Failed to build user-portal container"
    exit 1
fi

echo "[OK] Container rebuild completed"
echo ""

# Start containers
echo "4. Starting containers..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
if [ $? -ne 0 ]; then
    echo "[FAIL] Failed to start containers"
    exit 1
fi

echo "[OK] Containers started"
echo ""

# Wait for services to be ready
echo "5. Waiting for services to be ready..."
sleep 10

# Check container health
echo "6. Checking container status..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml ps

echo ""
echo "=== Setup Complete! ==="
echo ""
echo "Next Steps:"
echo "1. Configure Supabase and Google OAuth (see docs/SUPABASE_GOOGLE_OAUTH_SETUP.md)"
echo "2. Edit containers/user-portal/.env.local with your credentials"
echo "3. Restart containers: docker-compose down && docker-compose up -d"
echo "4. Test login at: http://portal.localhost/login"
echo ""
echo "For detailed implementation guide, see: docs/SUPABASE_GOOGLE_OAUTH_IMPLEMENTATION.md"
