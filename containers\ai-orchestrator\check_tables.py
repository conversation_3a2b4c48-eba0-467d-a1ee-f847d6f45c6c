#!/usr/bin/env python3
"""
Check if deployment_integrations and ingestion_errors tables exist
"""
from sqlalchemy import create_engine, inspect
import os

def main():
    url = os.environ.get('DATABASE_URL')
    if not url:
        print("DATABASE_URL not found")
        return

    engine = create_engine(url)
    inspector = inspect(engine)

    print('All tables in database:')
    try:
        tables = inspector.get_table_names()
        for table in sorted(tables):
            print(f'  {table}')
    except Exception as e:
        print(f'Error getting tables: {e}')

    print('\nChecking for specific tables:')
    tables_to_check = ['deployment_integrations', 'ingestion_errors', 'user_projects']

    for table in tables_to_check:
        if table in tables:
            print(f'✅ {table} exists')
            try:
                columns = inspector.get_columns(table)
                print(f'  Columns: {[col["name"] for col in columns]}')
            except Exception as e:
                print(f'  Error getting columns: {e}')
        else:
            print(f'❌ {table} does not exist')

if __name__ == '__main__':
    main()
