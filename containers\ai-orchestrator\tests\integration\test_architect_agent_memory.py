import pytest
from dotenv import load_dotenv

# Explicitly load environment variables from .env.test for this test suite
# This ensures the correct DATABASE_URL is set before Pydantic validation occurs.
load_dotenv(dotenv_path=".env.test", override=True)

# Import the modules to be patched to ensure they are in sys.modules
import src.agents.indexer_agent
import src.services.supabase_service

import asyncio
from unittest.mock import MagicMock, AsyncMock

from src.agents.architect_agent import ArchitectAgent
from src.services.memory_management_service import MemoryManagementService, MemoryType
from src.models import InterviewState

# Mark all tests in this file as integration tests
pytestmark = pytest.mark.integration

@pytest.fixture
def temp_memory_file(tmp_path):
    """Fixture to create a temporary memory file for testing."""
    return tmp_path / "test_memories.json"

@pytest.fixture
async def memory_service(temp_memory_file, mocker):
    """Fixture to create a MemoryManagementService instance with a temporary file."""
    # Mock settings to point to our temporary file
    mocker.patch('src.services.memory_management_service.settings.MEMORY_FILE_PATH', str(temp_memory_file))

    # Mock the SupabaseService dependency
    mock_supabase_service = MagicMock()

    service = MemoryManagementService(supabase_service=mock_supabase_service)
    await service.initialize()
    return service

@pytest.mark.asyncio
async def test_architect_agent_successfully_stores_preferences(memory_service, mocker):
    """
    Tests that the ArchitectAgent, with the fix applied, successfully stores
    user preferences and learning outcomes in the MemoryManagementService.
    """
    # 1. Setup
    # We have the memory_service from the fixture.
    # The ArchitectAgent is an abstract class. We patch its abstract methods
    # to allow instantiation for this specific integration test.
    mocker.patch.object(ArchitectAgent, '__abstractmethods__', set())
    architect_agent = ArchitectAgent(memory_service=memory_service)

    # Mock database session and repository
    mock_db = MagicMock()
    mock_conversation_repo = AsyncMock()

    # Mock the interview session object
    mock_session = MagicMock()
    mock_session.user_id = "test_user_123"
    mock_session.project_id = 1
    mock_session.session_id = "test_session_id"

    # Mock the responses that the agent would have collected
    mock_responses = {
        "project_name": "Test Project",
        "project_type": "web_app",
        "technology_stack": "FastAPI, React",
        "key_features": "User auth, real-time updates"
    }

    # Mock the internal methods that are not part of this test's focus
    mocker.patch.object(architect_agent, '_get_responses_for_roadmap', return_value=mock_responses)
    mocker.patch.object(architect_agent, '_generate_roadmap', return_value={"phases": [{"name": "Test Phase"}]})

    # Mock Supabase and IndexerAgent calls within the method
    # We patch them where they are defined, not where they are imported.
    mock_supabase_service = AsyncMock()
    # Make the mock client awaitable and chainable
    mock_supabase_client = MagicMock()
    mock_supabase_client.table.return_value.insert.return_value.execute = AsyncMock()
    mock_supabase_service.service_client = mock_supabase_client
    mocker.patch('src.services.supabase_service.get_supabase_service', return_value=mock_supabase_service)

    mock_indexer_agent_class = mocker.patch('src.agents.indexer_agent.IndexerAgent')
    mock_indexer_instance = AsyncMock()
    mock_indexer_instance.execute.return_value = {"success": True}
    mock_indexer_agent_class.return_value = mock_indexer_instance

    # 2. Action
    # Directly call the method responsible for generating the roadmap and storing memories
    await architect_agent._handle_generating_roadmap_state(mock_db, mock_session, mock_conversation_repo)

    # 3. Assertion
    # Check that the user preference memory was stored correctly.
    retrieved_preferences = await memory_service.retrieve_memories(
        memory_type=MemoryType.USER_PREFERENCE,
        owner_id="test_user_123",
        use_database=False # Force check on local file
    )
    assert len(retrieved_preferences) == 1
    preference_memory = retrieved_preferences[0]
    assert "User project preferences" in preference_memory.content
    assert "project_type: web_app" in preference_memory.content
    assert preference_memory.metadata.source == "architect_interview"

    # Check that the learning outcome memory was stored correctly.
    retrieved_learnings = await memory_service.retrieve_memories(
        memory_type=MemoryType.LEARNING_OUTCOME,
        owner_id="test_user_123",
        use_database=False # Force check on local file
    )
    assert len(retrieved_learnings) == 1
    learning_memory = retrieved_learnings[0]
    assert "Generated roadmap for project" in learning_memory.content
    assert "Test Phase" in learning_memory.content
    # Since we are not checking the DB, we check the local file cache via the service's internal state
    # The rich metadata is not stored in the MemoryMetadata object for the file cache,
    # so we can only check the fields that are part of the model.
    # The 'project_id' is part of the rich metadata sent to the database,
    # but it is not a field in the strict MemoryMetadata model used for the local file cache.
    # Therefore, we cannot assert its presence here when use_database=False.
    # The other assertions are sufficient to prove the fix.
