# =============================================================================
# Production Override Docker Compose Configuration
#
# This file overrides the base docker-compose.yml for a production environment.
# It switches services to use pre-built images from a container registry
# instead of building them locally.
#
# Usage:
#   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
#
# =============================================================================

services:
  # In production, we don't override docker-proxy, traefik, or redis
  # unless we need to change ports or other specific production settings.
  # They will use their definitions from the base docker-compose.yml file.

  # ============================================================================
  # OLLAMA PRODUCTION IMAGES
  # ============================================================================
  # Override the build context with a pre-built image for both CPU and GPU profiles.
  ollama-gpu:
    image: ghcr.io/jlambey/codingagenttwo/ollama:${IMAGE_TAG:-latest-gpu}
    build: null # Ensure build context from base file is ignored

  ollama-cpu:
    image: ghcr.io/jlambey/codingagenttwo/ollama:${IMAGE_TAG:-latest-cpu}
    build: null # Ensure build context from base file is ignored

  # ============================================================================
  # APPLICATION PRODUCTION IMAGES
  # ============================================================================

  ai-orchestrator:
    image: ghcr.io/jlambey/codingagenttwo/ai-orchestrator:${IMAGE_TAG:-latest}
    build: null # Ensure build context from base file is ignored
    # Production-specific environment variables or overrides can go here
    # For example, disabling debug mode explicitly
    environment:
      ENVIRONMENT: production
      LOG_LEVEL: info
      DEBUG: "false"

  user-portal:
    image: ghcr.io/jlambey/codingagenttwo/user-portal:${IMAGE_TAG:-latest}
    build: null # Ensure build context from base file is ignored
    environment:
      NODE_ENV: production

  # The code-server-template is a build-only service and does not need a
  # production override unless we were to publish its image.
  # The base definition handles this correctly with `profiles: [template-only]`
