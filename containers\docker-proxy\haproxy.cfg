# HAProxy configuration for Docker Socket Proxy
# Based on tecnativa/docker-socket-proxy with fixes for warnings

global
    log stdout format raw local0 info
    maxconn 4096
    # Run as haproxy user for security (non-root)
    user haproxy
    group haproxy
    daemon

    # SSL/TLS settings
    ssl-default-bind-ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256
    ssl-default-bind-options no-sslv3 no-tlsv10 no-tlsv11

defaults
    log global
    mode http
    option httplog
    option dontlognull
    option http-server-close
    option forwardfor
    timeout connect 10s
    timeout client 30s
    timeout server 30s
    timeout http-keep-alive 10s
    timeout http-request 30s
    timeout queue 30s
    timeout tunnel 3600s
    maxconn 2000

# Main Docker API proxy
frontend docker_api
    bind *:2375
    default_backend docker_backend

backend docker_backend
    mode http
    balance roundrobin
    server docker_socket unix@/var/run/docker.sock

# Health check endpoint
frontend health_check
    bind *:2377
    mode http
    monitor-uri /health
