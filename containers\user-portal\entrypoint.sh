#!/bin/sh
# Exit immediately if a command exits with a non-zero status.
set -e

echo "Entrypoint: Ensuring .next directory exists and has correct permissions"
# Create the .next directory if it doesn't exist.
mkdir -p /home/<USER>/app/.next

# Try to change ownership, but don't fail if it doesn't work (Windows/bind mount issues)
echo "Entrypoint: Attempting to fix directory ownership..."
if chown -R nextjs:nodejs /home/<USER>/app/.next 2>/dev/null; then
    echo "Successfully changed ownership of .next directory"
else
    echo "Could not change ownership of .next directory (likely Windows/bind mount limitation)"
fi

# Also try to set permissions on the main app directory for Next.js files
if chmod -R 755 /home/<USER>/app 2>/dev/null; then
    echo "Successfully set permissions on app directory"
else
    echo "Could not set permissions on app directory (likely Windows/bind mount limitation)"
fi

# Check and install missing SWC binaries if needed
echo "Entrypoint: Checking for SWC binaries..."
if [ ! -d "/home/<USER>/app/node_modules/@next/swc-linux-x64-gnu" ] && [ ! -d "/home/<USER>/app/node_modules/@next/swc-linux-x64-musl" ]; then
    echo "SWC binaries missing, attempting to install..."
    cd /home/<USER>/app
    npm install @next/swc-linux-x64-gnu --save-optional --legacy-peer-deps --no-audit --no-fund 2>/dev/null || \
    npm install @next/swc-linux-x64-musl --save-optional --legacy-peer-deps --no-audit --no-fund 2>/dev/null || \
    echo "Warning: Could not install SWC binaries, Next.js will use fallback compilation"
fi

echo "Entrypoint: Handing off to user 'nextjs' to run the application..."
# Try to use 'gosu' to drop privileges and execute as the 'nextjs' user
# If that fails (Windows/Docker Desktop limitation), run as root
if gosu nextjs true 2>/dev/null; then
    echo "Successfully switching to nextjs user"
    exec gosu nextjs "$@"
else
    echo "Cannot switch to nextjs user (Windows/Docker Desktop limitation), running as root..."
    echo "Warning: Application will run as root user"
    exec "$@"
fi
