@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles for User Portal */
:root {
  --bg: #f9fafb;
  --fg: #111827;
  --muted: #6b7280;
  --accent: #2563eb;
}

html, body, #__next {
  height: 100%;
}

body {
  background-color: var(--bg);
  color: var(--fg);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: var(--accent);
}

/* Utility tweaks for the app shell */
.main-container {
  min-height: 100vh;
}

/* Small form / card helpers */
.card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px rgba(0,0,0,0.04);
  padding: 1rem;
}

/* Accessibility: reduce motion for prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}
