"""
Memory Management Service for AI Coding Agent.

This module provides comprehensive memory management functionality for the AI coding agent,
integrating with MCP memory server for persistent knowledge storage and retrieval.
Implements learning loops, pattern recognition, and context-aware code generation.

Author: AI Coding Agent
Version: 1.0.0
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import hashlib
from pathlib import Path

import aiofiles
from fastapi import Depends, HTTPException
from pydantic import BaseModel, Field, validator

from src.core.config import settings
from src.schemas.memory_schemas import (CodePattern, LearningOutcome,
                                        MemoryEntry, MemoryMetadata,
                                        MemoryPriority, MemoryType)
from src.services.supabase_service import SupabaseService

# Configure logging
logger = logging.getLogger(__name__)


class MemoryManagementService:
    """
    Comprehensive memory management service for AI coding agent.

    Provides persistent storage, retrieval, and learning capabilities
    integrated with MCP memory server.
    """

    def __init__(self, supabase_service: SupabaseService):
        self.supabase = supabase_service
        self.memory_file = Path(settings.MEMORY_FILE_PATH)
        self.memory_file.parent.mkdir(parents=True, exist_ok=True)
        self.backup_file = Path(settings.MEMORY_BACKUP_PATH)
        self.backup_file.parent.mkdir(parents=True, exist_ok=True)
        self.memories: Dict[str, MemoryEntry] = {}
        self.code_patterns: Dict[str, CodePattern] = {}
        self.learning_outcomes: Dict[str, LearningOutcome] = {}
        self._initialized = False

        # Configuration values
        self.max_memory_entries = settings.MAX_MEMORY_ENTRIES
        self.memory_search_similarity_threshold = settings.MEMORY_SEARCH_SIMILARITY_THRESHOLD
        self.memory_max_search_results = settings.MEMORY_MAX_SEARCH_RESULTS
        self.memory_search_timeout = settings.MEMORY_SEARCH_TIMEOUT
        self.enable_learning_loops = settings.ENABLE_LEARNING_LOOPS
        self.learning_confidence_threshold = settings.LEARNING_CONFIDENCE_THRESHOLD
        self.learning_pattern_min_occurrences = settings.LEARNING_PATTERN_MIN_OCCURRENCES
        self.code_pattern_similarity_threshold = settings.CODE_PATTERN_SIMILARITY_THRESHOLD
        self.code_pattern_max_patterns = settings.CODE_PATTERN_MAX_PATTERNS
        self.memory_auto_save = settings.MEMORY_AUTO_SAVE
        self.memory_save_interval_seconds = settings.MEMORY_SAVE_INTERVAL_SECONDS

    async def initialize(self):
        """Initialize the memory service."""
        if self._initialized:
            return

        try:
            await self._load_memories()
            await self._load_code_patterns()
            await self._load_learning_outcomes()
            self._initialized = True
            logger.info("Memory management service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize memory service: {e}")
            raise

    # =====================================================================================
    # DATABASE INTEGRATION METHODS
    # =====================================================================================

    async def store_memory_db(
        self,
        content: str,
        memory_type: MemoryType,
        owner_id: str,
        metadata: Optional[Dict[str, Any]] = None,
        embedding: Optional[List[float]] = None
    ) -> str:
        """
        Store a memory entry in the database.

        Args:
            content: The memory content
            memory_type: Type of memory
            owner_id: Owner's Supabase user ID
            metadata: Optional metadata
            embedding: Optional vector embedding

        Returns:
            The memory ID
        """
        try:
            memory_data = {
                "owner_id": owner_id,
                "memory_type": memory_type.value,
                "content": content,
                "metadata": metadata or {},
                "embedding": embedding
            }

            result = await self.supabase.table("memories").insert(memory_data).execute()

            if result.data:
                memory_id = result.data[0]["id"]
                logger.info(f"Stored memory in database: {memory_id}")
                return str(memory_id)
            else:
                raise Exception("Failed to insert memory")

        except Exception as e:
            logger.error(f"Failed to store memory in database: {e}")
            raise

    async def retrieve_memories_db(
        self,
        owner_id: str,
        memory_type: Optional[MemoryType] = None,
        limit: int = 10,
        query: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve memories from the database.

        Args:
            owner_id: Owner's Supabase user ID
            memory_type: Optional filter by memory type
            limit: Maximum number of results
            query: Optional text search query

        Returns:
            List of memory records
        """
        try:
            query_builder = self.supabase.table("memories").select("*").eq("owner_id", owner_id)

            if memory_type:
                query_builder = query_builder.eq("memory_type", memory_type.value)

            if query:
                # Use text search if available, otherwise filter by content containing query
                query_builder = query_builder.ilike("content", f"%{query}%")

            result = await query_builder.limit(limit).execute()

            memories = []
            for record in result.data:
                # Convert database record to MemoryEntry format
                memory = {
                    "id": record["id"],
                    "content": record["content"],
                    "memory_type": record["memory_type"],
                    "metadata": record["metadata"],
                    "created_at": record["created_at"],
                    "embedding": record["embedding"]
                }
                memories.append(memory)

            logger.info(f"Retrieved {len(memories)} memories from database")
            return memories

        except Exception as e:
            logger.error(f"Failed to retrieve memories from database: {e}")
            raise

    async def search_memories_semantic_db(
        self,
        owner_id: str,
        query_embedding: List[float],
        similarity_threshold: float = 0.7,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Search memories by semantic similarity using database.

        Args:
            owner_id: Owner's Supabase user ID
            query_embedding: Query vector embedding
            similarity_threshold: Minimum similarity score
            limit: Maximum number of results

        Returns:
            List of similar memories with scores
        """
        try:
            # Use the custom database function for semantic search
            result = await self.supabase.rpc(
                "search_memories_semantic",
                {
                    "query_embedding": query_embedding,
                    "user_id": owner_id,
                    "similarity_threshold": similarity_threshold,
                    "max_results": limit
                }
            ).execute()

            memories = []
            for record in result.data:
                memory = {
                    "id": record["id"],
                    "content": record["content"],
                    "memory_type": record["memory_type"],
                    "metadata": record["metadata"],
                    "similarity": record["similarity"]
                }
                memories.append(memory)

            logger.info(f"Found {len(memories)} semantically similar memories")
            return memories

        except Exception as e:
            logger.error(f"Failed to search memories semantically: {e}")
            raise

    async def store_memory_pattern_db(
        self,
        owner_id: str,
        pattern_description: str,
        association_strength: float
    ) -> str:
        """
        Store a memory pattern in the database.

        Args:
            owner_id: Owner's Supabase user ID
            pattern_description: Description of the pattern
            association_strength: Strength of the association (0.0-1.0)

        Returns:
            The pattern ID
        """
        try:
            pattern_data = {
                "owner_id": owner_id,
                "pattern_description": pattern_description,
                "association_strength": association_strength
            }

            result = await self.supabase.table("memory_patterns").insert(pattern_data).execute()

            if result.data:
                pattern_id = result.data[0]["id"]
                logger.info(f"Stored memory pattern in database: {pattern_id}")
                return str(pattern_id)
            else:
                raise Exception("Failed to insert memory pattern")

        except Exception as e:
            logger.error(f"Failed to store memory pattern in database: {e}")
            raise

    async def get_memory_stats_db(self, owner_id: str) -> Dict[str, Any]:
        """
        Get memory statistics from the database.

        Args:
            owner_id: Owner's Supabase user ID

        Returns:
            Dictionary with memory statistics
        """
        try:
            result = await self.supabase.rpc(
                "get_user_memory_stats",
                {"user_id": owner_id}
            ).execute()

            if result.data:
                stats = result.data[0]
                logger.info(f"Retrieved memory stats for user {owner_id}")
                return dict(stats)
            else:
                return {
                    "total_memories": 0,
                    "memories_by_type": {},
                    "total_patterns": 0,
                    "avg_association_strength": 0.0
                }

        except Exception as e:
            logger.error(f"Failed to get memory stats: {e}")
            raise

    async def _load_memories(self):
        """Load memories from persistent storage."""
        if self.memory_file.exists():
            try:
                async with aiofiles.open(self.memory_file, 'r') as f:
                    data = json.loads(await f.read())
                    for item in data.get('memories', []):
                        memory = MemoryEntry(**item)
                        self.memories[memory.id] = memory
                logger.info(f"Loaded {len(self.memories)} memories from storage")
            except Exception as e:
                logger.warning(f"Failed to load memories: {e}")

    async def _save_memories(self):
        """Save memories to persistent storage."""
        try:
            data = {
                'memories': [memory.dict() for memory in self.memories.values()],
                'last_updated': datetime.now().isoformat()
            }
            async with aiofiles.open(self.memory_file, 'w') as f:
                await f.write(json.dumps(data, indent=2, default=str))
        except Exception as e:
            logger.error(f"Failed to save memories: {e}")

    async def _load_code_patterns(self):
        """Load code patterns from storage."""
        patterns_file = self.memory_file.parent / "code_patterns.json"
        if patterns_file.exists():
            try:
                async with aiofiles.open(patterns_file, 'r') as f:
                    data = json.loads(await f.read())
                    for item in data.get('patterns', []):
                        pattern = CodePattern(**item)
                        self.code_patterns[pattern.name] = pattern
                logger.info(f"Loaded {len(self.code_patterns)} code patterns")
            except Exception as e:
                logger.warning(f"Failed to load code patterns: {e}")

    async def _load_learning_outcomes(self):
        """Load learning outcomes from storage."""
        outcomes_file = self.memory_file.parent / "learning_outcomes.json"
        if outcomes_file.exists():
            try:
                async with aiofiles.open(outcomes_file, 'r') as f:
                    data = json.loads(await f.read())
                    for item in data.get('outcomes', []):
                        outcome = LearningOutcome(**item)
                        self.learning_outcomes[outcome.topic] = outcome
                logger.info(f"Loaded {len(self.learning_outcomes)} learning outcomes")
            except Exception as e:
                logger.warning(f"Failed to load learning outcomes: {e}")

    async def store_memory(
        self,
        content: str,
        memory_type: MemoryType,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None,
        owner_id: Optional[str] = None,
        use_database: bool = True
    ) -> MemoryEntry:
        """
        Store a new memory entry.

        Args:
            content: The memory content.
            memory_type: Type of memory.
            metadata: A dictionary of metadata. Known keys will be used to
                      populate the MemoryMetadata model for the local cache,
                      while the full dictionary is stored in the database.
            tags: Optional tags to add to the metadata.
            owner_id: Owner's Supabase user ID (required for database storage).
            use_database: Whether to use database storage (default: True).

        Returns:
            The created memory entry.
        """
        raw_metadata = metadata or {}

        # Extract fields known to MemoryMetadata for the Pydantic model.
        known_fields = {k for k in MemoryMetadata.model_fields.keys()}
        metadata_for_model_dict = {k: v for k, v in raw_metadata.items() if k in known_fields}

        # If 'tags' are passed as a separate argument, merge them.
        if tags:
            current_tags = metadata_for_model_dict.get('tags', [])
            current_tags.extend(tag for tag in tags if tag not in current_tags)
            metadata_for_model_dict['tags'] = current_tags

        # Create the strict Pydantic model for the file cache entry.
        pydantic_metadata = MemoryMetadata(**metadata_for_model_dict)

        # Create the local cache entry using the strict metadata model.
        memory = MemoryEntry(
            content=content,
            memory_type=memory_type,
            metadata=pydantic_metadata
        )

        # Store in database if enabled, passing the FULL, rich metadata dictionary.
        if use_database and owner_id and self.supabase:
            try:
                # TODO: Implement embedding generation for semantic search
                # embedding = await self._generate_embedding(content)
                embedding = None # Placeholder

                db_memory_id = await self.store_memory_db(
                    content=content,
                    memory_type=memory_type,
                    owner_id=owner_id,
                    metadata=raw_metadata,  # Pass the original rich metadata dict to DB
                    embedding=embedding
                )
                # Update memory with database ID
                memory.id = db_memory_id
                logger.info(f"Stored memory in database: {db_memory_id}")
            except Exception as db_error:
                logger.warning(f"Database storage failed, using local storage: {db_error}")

        # Always store locally for immediate access.
        self.memories[memory.id] = memory
        await self._save_memories()

        logger.info(f"Stored memory: {memory.id} ({memory_type.value})")
        return memory

    async def retrieve_memories(
        self,
        query: Optional[str] = None,
        memory_type: Optional[MemoryType] = None,
        tags: Optional[List[str]] = None,
        limit: int = 10,
        owner_id: Optional[str] = None,
        use_database: bool = True
    ) -> List[MemoryEntry]:
        """
        Retrieve memories based on criteria.

        Args:
            query: Text to search for in content
            memory_type: Filter by memory type
            tags: Filter by tags
            limit: Maximum number of results
            owner_id: Owner's Supabase user ID (required for database retrieval)
            use_database: Whether to use database retrieval (default: True)

        Returns:
            List of matching memories
        """
        # Try database retrieval first if enabled and owner_id provided
        if use_database and owner_id and self.supabase:
            try:
                db_memories = await self.retrieve_memories_db(
                    owner_id=owner_id,
                    memory_type=memory_type,
                    limit=limit,
                    query=query
                )

                # Convert database records to MemoryEntry objects
                memories = []
                for record in db_memories:
                    metadata = MemoryMetadata(**record.get("metadata", {}))
                    if tags:
                        # Filter by tags if specified
                        if not any(tag in metadata.tags for tag in tags):
                            continue

                    memory = MemoryEntry(
                        id=record["id"],
                        content=record["content"],
                        memory_type=MemoryType(record["memory_type"]),
                        metadata=metadata,
                        embedding=record.get("embedding")
                    )
                    memories.append(memory)

                # Update local cache with retrieved memories
                for memory in memories:
                    self.memories[memory.id] = memory

                logger.info(f"Retrieved {len(memories)} memories from database")
                return memories

            except Exception as db_error:
                logger.warning(f"Database retrieval failed, falling back to local: {db_error}")

        # Fallback to local retrieval
        candidates = list(self.memories.values())

        # Filter by type
        if memory_type:
            candidates = [m for m in candidates if m.memory_type == memory_type]

        # Filter by tags
        if tags:
            candidates = [
                m for m in candidates
                if any(tag in m.metadata.tags for tag in tags)
            ]

        # Filter by query
        if query:
            query_lower = query.lower()
            candidates = [
                m for m in candidates
                if query_lower in m.content.lower()
            ]

        # Sort by relevance (access count, recency)
        candidates.sort(
            key=lambda m: (m.access_count, m.last_accessed or m.created_at),
            reverse=True
        )

        # Update access tracking
        for memory in candidates[:limit]:
            memory.update_access()

        await self._save_memories()

        return candidates[:limit]

    async def search_memories(
        self,
        query: str,
        owner_id: Optional[str] = None,
        memory_type: Optional[MemoryType] = None,
        limit: int = 10,
        use_semantic: bool = True,
        use_database: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Search memories using text or semantic similarity.

        Args:
            query: Search query
            owner_id: Owner's Supabase user ID (required for database search)
            memory_type: Optional filter by memory type
            limit: Maximum number of results
            use_semantic: Whether to use semantic search (requires embeddings)
            use_database: Whether to use database search

        Returns:
            List of matching memories with relevance scores
        """
        # Try semantic search with database if enabled
        if use_semantic and use_database and owner_id and self.supabase:
            try:
                # Generate embedding for the query
                query_embedding = await self._generate_embedding(query)

                semantic_results = await self.search_memories_semantic_db(
                    owner_id=owner_id,
                    query_embedding=query_embedding,
                    similarity_threshold=self.memory_search_similarity_threshold,
                    limit=limit
                )

                if semantic_results:
                    logger.info(f"Found {len(semantic_results)} semantic matches")
                    return semantic_results

            except Exception as semantic_error:
                logger.warning(f"Semantic search failed, falling back to text search: {semantic_error}")

        # Fallback to text-based search
        try:
            memories = await self.retrieve_memories(
                query=query,
                memory_type=memory_type,
                limit=limit,
                owner_id=owner_id,
                use_database=use_database
            )

            # Convert to search result format with scores
            results = []
            for memory in memories:
                # Calculate simple relevance score based on content match
                content_lower = memory.content.lower()
                query_lower = query.lower()

                if query_lower in content_lower:
                    # Boost score for exact matches and position
                    score = 1.0 if query_lower == content_lower else 0.8
                    # Boost for matches at the beginning
                    if content_lower.startswith(query_lower):
                        score += 0.2
                else:
                    # Partial match score
                    score = 0.5

                results.append({
                    "id": memory.id,
                    "content": memory.content,
                    "memory_type": memory.memory_type.value,
                    "metadata": memory.metadata.model_dump(),
                    "relevance_score": score,
                    "match_type": "text"
                })

            # Sort by relevance score
            results.sort(key=lambda x: x["relevance_score"], reverse=True)

            logger.info(f"Found {len(results)} text-based matches")
            return results[:limit]

        except Exception as text_error:
            logger.error(f"Text search failed: {text_error}")
            return []

    async def get_memory_stats(
        self,
        owner_id: Optional[str] = None,
        use_database: bool = True
    ) -> Dict[str, Any]:
        """
        Get comprehensive memory statistics.

        Args:
            owner_id: Owner's Supabase user ID (required for database stats)
            use_database: Whether to use database statistics

        Returns:
            Dictionary with memory statistics
        """
        # Try database stats first if enabled
        if use_database and owner_id and self.supabase:
            try:
                db_stats = await self.get_memory_stats_db(owner_id)
                logger.info(f"Retrieved database memory stats for user {owner_id}")
                return db_stats
            except Exception as db_error:
                logger.warning(f"Database stats failed, using local stats: {db_error}")

        # Fallback to local statistics
        total_memories = len(self.memories)
        memories_by_type = {}
        total_access_count = 0
        recent_memories = 0
        oldest_memory = None
        newest_memory = None

        now = datetime.now()
        week_ago = now - timedelta(days=7)

        for memory in self.memories.values():
            # Count by type
            mem_type = memory.memory_type.value
            memories_by_type[mem_type] = memories_by_type.get(mem_type, 0) + 1

            # Track access patterns
            total_access_count += memory.access_count

            # Track recency
            if memory.created_at > week_ago:
                recent_memories += 1

            # Track age range
            if oldest_memory is None or memory.created_at < oldest_memory:
                oldest_memory = memory.created_at
            if newest_memory is None or memory.created_at > newest_memory:
                newest_memory = memory.created_at

        # Calculate averages
        avg_access_count = total_access_count / total_memories if total_memories > 0 else 0

        # Code patterns stats
        total_patterns = len(self.code_patterns)
        pattern_usage = sum(p.usage_count for p in self.code_patterns.values())
        avg_pattern_usage = pattern_usage / total_patterns if total_patterns > 0 else 0

        # Learning outcomes stats
        total_learning_outcomes = len(self.learning_outcomes)
        avg_confidence = sum(lo.confidence for lo in self.learning_outcomes.values()) / total_learning_outcomes if total_learning_outcomes > 0 else 0

        stats = {
            "total_memories": total_memories,
            "memories_by_type": memories_by_type,
            "total_code_patterns": total_patterns,
            "total_learning_outcomes": total_learning_outcomes,
            "average_access_count": round(avg_access_count, 2),
            "recent_memories_count": recent_memories,
            "average_pattern_usage": round(avg_pattern_usage, 2),
            "average_learning_confidence": round(avg_confidence, 2),
            "oldest_memory_date": oldest_memory.isoformat() if oldest_memory else None,
            "newest_memory_date": newest_memory.isoformat() if newest_memory else None,
            "data_source": "local_cache"
        }

        logger.info(f"Generated local memory stats: {total_memories} memories, {total_patterns} patterns")
        return stats

    async def store_code_pattern(self, pattern: CodePattern) -> CodePattern:
        """
        Store a reusable code pattern.

        Args:
            pattern: The code pattern to store

        Returns:
            The stored pattern
        """
        self.code_patterns[pattern.name] = pattern

        patterns_file = self.memory_file.parent / "code_patterns.json"
        try:
            data = {
                'patterns': [p.dict() for p in self.code_patterns.values()],
                'last_updated': datetime.now().isoformat()
            }
            async with aiofiles.open(patterns_file, 'w') as f:
                await f.write(json.dumps(data, indent=2, default=str))
        except Exception as e:
            logger.error(f"Failed to save code patterns: {e}")

        logger.info(f"Stored code pattern: {pattern.name}")
        return pattern

    async def get_code_patterns(
        self,
        language: Optional[str] = None,
        framework: Optional[str] = None,
        pattern_type: Optional[str] = None
    ) -> List[CodePattern]:
        """
        Retrieve code patterns based on criteria.

        Args:
            language: Filter by programming language
            framework: Filter by framework
            pattern_type: Filter by pattern type

        Returns:
            List of matching patterns
        """
        candidates = list(self.code_patterns.values())

        if language:
            candidates = [p for p in candidates if p.language.lower() == language.lower()]

        if framework:
            candidates = [p for p in candidates if p.framework and p.framework.lower() == framework.lower()]

        if pattern_type:
            candidates = [p for p in candidates if p.pattern_type.lower() == pattern_type.lower()]

        return candidates

    async def learn_from_interaction(
        self,
        interaction_type: str,
        content: str,
        outcome: str,
        context: Dict[str, Any]
    ) -> LearningOutcome:
        """
        Learn from an interaction and store the outcome.

        Args:
            interaction_type: Type of interaction (e.g., "code_generation", "error_resolution")
            content: The interaction content
            outcome: The outcome or lesson learned
            context: Additional context

        Returns:
            The created learning outcome
        """
        topic = f"{interaction_type}_{hashlib.md5(content.encode()).hexdigest()[:8]}"

        learning = LearningOutcome(
            topic=topic,
            lesson=outcome,
            context=json.dumps(context),
            confidence=settings.LEARNING_CONFIDENCE_THRESHOLD  # Initial confidence from config
        )

        self.learning_outcomes[topic] = learning

        # Store as memory as well
        await self.store_memory(
            content=f"Learning: {outcome}\nContext: {json.dumps(context)}",
            memory_type=MemoryType.LEARNING_OUTCOME,
            tags=["learning", interaction_type]
        )

        outcomes_file = self.memory_file.parent / "learning_outcomes.json"
        try:
            data = {
                'outcomes': [o.dict() for o in self.learning_outcomes.values()],
                'last_updated': datetime.now().isoformat()
            }
            async with aiofiles.open(outcomes_file, 'w') as f:
                await f.write(json.dumps(data, indent=2, default=str))
        except Exception as e:
            logger.error(f"Failed to save learning outcomes: {e}")

        logger.info(f"Learned from interaction: {topic}")
        return learning

    async def get_relevant_context(
        self,
        task: str,
        language: Optional[str] = None,
        framework: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get relevant context for a coding task.

        Args:
            task: Description of the coding task
            language: Programming language
            framework: Framework being used

        Returns:
            Dictionary with relevant memories, patterns, and learnings
        """
        # Get relevant memories
        memories = await self.retrieve_memories(
            query=task,
            tags=[language, framework] if language and framework else [language or framework] if language or framework else None,
            limit=5
        )

        # Get relevant patterns
        patterns = await self.get_code_patterns(
            language=language,
            framework=framework
        )

        # Get relevant learnings
        learnings = [
            outcome for outcome in self.learning_outcomes.values()
            if any(keyword in outcome.lesson.lower() for keyword in task.lower().split())
        ][:3]

        return {
            "memories": [m.dict() for m in memories],
            "patterns": [p.dict() for p in patterns],
            "learnings": [l.dict() for l in learnings],
            "context_summary": f"Found {len(memories)} relevant memories, {len(patterns)} patterns, {len(learnings)} learnings"
        }

    async def cleanup_expired_memories(self):
        """Clean up expired memories."""
        now = datetime.now()
        expired_ids = [
            mid for mid, memory in self.memories.items()
            if memory.metadata.expires_at and memory.metadata.expires_at < now
        ]

        for mid in expired_ids:
            del self.memories[mid]

        if expired_ids:
            await self._save_memories()
            logger.info(f"Cleaned up {len(expired_ids)} expired memories")

    async def get_statistics(self) -> Dict[str, Any]:
        """Get memory system statistics."""
        total_memories = len(self.memories)
        memory_types = {}
        all_tags = set()

        for memory in self.memories.values():
            memory_types[memory.memory_type.value] = memory_types.get(memory.memory_type.value, 0) + 1
            all_tags.update(memory.metadata.tags)

        return {
            "total_memories": total_memories,
            "memory_types": memory_types,
            "unique_tags": len(all_tags),
            "code_patterns": len(self.code_patterns),
            "learning_outcomes": len(self.learning_outcomes),
            "storage_path": str(self.memory_file)
        }


from fastapi import Request
# Dependency injection
async def get_memory_service(request: Request) -> MemoryManagementService:
    """
    Retrieves the singleton instance of the MemoryManagementService from the
    application state. This ensures that the same service instance is used
    across all requests, preventing re-initialization.
    """
    memory_service = getattr(request.app.state, "memory_service", None)
    if not memory_service:
        logger.error("MemoryManagementService not initialized or not found in app state.")
        raise HTTPException(
            status_code=503,
            detail="The Memory Management Service is currently unavailable. Please try again later.",
        )
    return memory_service
