"""
CUDA Accelerated Service

GPU-accelerated service for machine learning workloads using CUDA toolkit.
Provides optimized performance for embeddings, vector operations, and ML inference.

Author: AI Coding Agent Team
Version: 1.0.0
"""

import os
import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

# CUDA/ML Libraries
try:
    import torch
    import cupy
    import faiss
    from sentence_transformers import SentenceTransformer

    # All heavy dependencies are available
    CUDA_AVAILABLE = torch.cuda.is_available()
except ImportError:
    # If any of the imports fail, the feature is not available
    CUDA_AVAILABLE = False

logger = logging.getLogger(__name__)


class CUDAAcceleratedService:
    """
    CUDA-accelerated service for ML workloads.

    Features:
    - GPU-accelerated embeddings generation
    - CUDA-optimized vector operations
    - FAISS GPU indexing for similarity search
    - Memory-efficient batch processing
    - Automatic CPU fallback for non-GPU systems
    """

    def __init__(self):
        self.device = None
        self.embedding_model = None
        self.faiss_index = None
        self.initialized = False

        # Check CUDA availability
        self.cuda_available = self._check_cuda_availability()
        if self.cuda_available:
            self.device = torch.device("cuda")
            logger.info("CUDA acceleration enabled")
        else:
            self.device = torch.device("cpu")
            logger.info("CUDA not available, using CPU fallback")

    def _check_cuda_availability(self) -> bool:
        """Check if CUDA is available and properly configured."""
        if not TORCH_AVAILABLE:
            logger.warning("PyTorch not available, CUDA acceleration disabled")
            return False

        try:
            if torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                current_device = torch.cuda.current_device()
                device_name = torch.cuda.get_device_name(current_device)
                logger.info(f"CUDA available: {device_count} device(s), using {device_name}")
                return True
            else:
                logger.warning("CUDA not available on this system")
                return False
        except Exception as e:
            logger.error(f"Error checking CUDA availability: {e}")
            return False

    async def initialize(self):
        """Initialize CUDA-accelerated components."""
        if self.initialized:
            return

        try:
            # Initialize embedding model
            if SENTENCE_TRANSFORMERS_AVAILABLE and SentenceTransformer is not None:
                model_name = "all-MiniLM-L6-v2"  # Fast and efficient model
                self.embedding_model = SentenceTransformer(model_name, device=self.device)
                logger.info(f"Loaded embedding model: {model_name}")
            else:
                logger.warning("SentenceTransformers not available, embeddings disabled")

            # Initialize FAISS GPU index if available
            if FAISS_AVAILABLE and self.cuda_available:
                try:
                    # Create GPU FAISS index
                    self.faiss_index = faiss.IndexFlatIP(384)  # 384-dim for MiniLM
                    if hasattr(faiss, 'GpuIndexFlatIP'):
                        res = faiss.StandardGpuResources()
                        self.faiss_index = faiss.GpuIndexFlatIP(res, 384)
                        logger.info("FAISS GPU index initialized")
                    else:
                        logger.warning("FAISS GPU support not available, using CPU index")
                except Exception as e:
                    logger.warning(f"FAISS GPU initialization failed: {e}, using CPU")
                    self.faiss_index = faiss.IndexFlatIP(384)
            elif FAISS_AVAILABLE:
                self.faiss_index = faiss.IndexFlatIP(384)
                logger.info("FAISS CPU index initialized")
            else:
                logger.warning("FAISS not available, vector search disabled")

            self.initialized = True
            logger.info("CUDA Accelerated Service initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize CUDA service: {e}")
            raise

    async def generate_embeddings(self, texts: List[str], batch_size: int = 32) -> List[List[float]]:
        """
        Generate embeddings for a list of texts using GPU acceleration.

        Args:
            texts: List of text strings to embed
            batch_size: Batch size for processing

        Returns:
            List of embedding vectors
        """
        if not self.initialized:
            await self.initialize()

        if not self.embedding_model:
            raise RuntimeError("Embedding model not available")

        try:
            start_time = datetime.now()

            # Process in batches for memory efficiency
            all_embeddings = []

            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]

                # Generate embeddings
                if self.cuda_available:
                    with torch.no_grad():
                        embeddings = self.embedding_model.encode(
                            batch_texts,
                            convert_to_tensor=True,
                            device=self.device
                        )
                        # Move to CPU and convert to list
                        embeddings = embeddings.cpu().numpy().tolist()
                else:
                    embeddings = self.embedding_model.encode(
                        batch_texts,
                        convert_to_tensor=False
                    )

                all_embeddings.extend(embeddings)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info(f"Generated {len(texts)} embeddings in {duration:.2f}s using {'GPU' if self.cuda_available else 'CPU'}")

            return all_embeddings

        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            raise

    async def similarity_search(
        self,
        query_embedding: List[float],
        index_embeddings: List[List[float]],
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Perform similarity search using CUDA-accelerated FAISS.

        Args:
            query_embedding: Query embedding vector
            index_embeddings: List of embedding vectors to search
            top_k: Number of top results to return

        Returns:
            List of search results with scores and indices
        """
        if not self.initialized:
            await self.initialize()

        if not self.faiss_index:
            raise RuntimeError("FAISS index not available")

        try:
            import numpy as np

            # Convert to numpy arrays
            query_array = np.array([query_embedding], dtype=np.float32)
            index_array = np.array(index_embeddings, dtype=np.float32)

            # Add vectors to index if not already done
            if self.faiss_index.ntotal == 0:
                self.faiss_index.add(index_array)

            # Perform search
            scores, indices = self.faiss_index.search(query_array, min(top_k, len(index_embeddings)))

            # Format results
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx != -1:  # Valid result
                    results.append({
                        "index": int(idx),
                        "score": float(score),
                        "rank": i + 1
                    })

            return results

        except Exception as e:
            logger.error(f"Similarity search failed: {e}")
            raise

    async def batch_process_embeddings(
        self,
        texts: List[str],
        operation: str = "encode",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Batch process embeddings with CUDA optimization.

        Args:
            texts: List of texts to process
            operation: Operation to perform ("encode", "cluster", etc.)
            **kwargs: Additional parameters

        Returns:
            Processing results
        """
        if not self.initialized:
            await self.initialize()

        try:
            if operation == "encode":
                embeddings = await self.generate_embeddings(texts, **kwargs)
                return {
                    "operation": "encode",
                    "embeddings": embeddings,
                    "count": len(embeddings),
                    "device": str(self.device)
                }

            elif operation == "cluster":
                # Implement clustering if needed
                embeddings = await self.generate_embeddings(texts, **kwargs)
                return {
                    "operation": "cluster",
                    "embeddings": embeddings,
                    "clusters": [],  # Placeholder for clustering results
                    "device": str(self.device)
                }

            else:
                raise ValueError(f"Unsupported operation: {operation}")

        except Exception as e:
            logger.error(f"Batch processing failed: {e}")
            raise

    async def optimize_memory(self):
        """Optimize GPU memory usage."""
        if self.cuda_available and TORCH_AVAILABLE:
            try:
                torch.cuda.empty_cache()
                logger.info("GPU memory cache cleared")
            except Exception as e:
                logger.warning(f"Memory optimization failed: {e}")

    def get_device_info(self) -> Dict[str, Any]:
        """Get information about the current device."""
        info = {
            "device": str(self.device),
            "cuda_available": self.cuda_available,
            "torch_available": TORCH_AVAILABLE,
            "cupy_available": CUPY_AVAILABLE,
            "faiss_available": FAISS_AVAILABLE,
            "sentence_transformers_available": SENTENCE_TRANSFORMERS_AVAILABLE
        }

        if self.cuda_available:
            info.update({
                "cuda_device_count": torch.cuda.device_count(),
                "cuda_current_device": torch.cuda.current_device(),
                "cuda_device_name": torch.cuda.get_device_name(),
                "cuda_memory_allocated": torch.cuda.memory_allocated() / 1024**2,  # MB
                "cuda_memory_reserved": torch.cuda.memory_reserved() / 1024**2    # MB
            })

        return info

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on CUDA components."""
        health_status = {
            "service": "cuda_accelerated",
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {}
        }

        try:
            # Check embedding model
            if self.embedding_model:
                health_status["components"]["embedding_model"] = "healthy"
            else:
                health_status["components"]["embedding_model"] = "unavailable"

            # Check FAISS index
            if self.faiss_index:
                health_status["components"]["faiss_index"] = "healthy"
            else:
                health_status["components"]["faiss_index"] = "unavailable"

            # Check CUDA
            if self.cuda_available:
                health_status["components"]["cuda"] = "healthy"
            else:
                health_status["components"]["cuda"] = "unavailable"

        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)

        return health_status


# Global CUDA service instance
_cuda_service_instance = None

async def get_cuda_accelerated_service() -> CUDAAcceleratedService:
    """Get or create the global CUDA accelerated service instance."""
    global _cuda_service_instance

    if _cuda_service_instance is None:
        _cuda_service_instance = CUDAAcceleratedService()
        await _cuda_service_instance.initialize()

    return _cuda_service_instance
