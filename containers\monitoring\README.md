# Monitoring Stack Container

Comprehensive observability platform for AI Coding Agent with metrics, logging, and alerting.

## 🎯 Overview

This container provides a complete monitoring and observability stack including Prometheus for metrics collection, Grafana for visualization, Loki for log aggregation, and Alertmanager for notifications, all optimized for the AI Coding Agent's microservices architecture.

## 🏗️ Architecture

### Components

- **Prometheus**: Metrics collection and time-series database
- **Grafana**: Dashboard and visualization platform
- **Loki**: Log aggregation and querying system
- **Promtail**: Log shipping agent for Docker containers
- **Alertmanager**: Alert routing and notification management
- **Node Exporter**: System metrics collection
- **cAdvisor**: Container metrics and performance monitoring

### Monitoring Targets

- **AI Orchestrator**: API response times, error rates, model inference metrics
- **User Portal**: Frontend performance, user interactions, error tracking
- **Database**: Query performance, connection pools, vector search metrics
- **Docker Services**: Container health, resource usage, network metrics
- **External APIs**: Third-party service response times and reliability

## 🚀 Configuration

### Environment Variables

```bash
# Prometheus Configuration
PROMETHEUS_RETENTION_TIME=30d
PROMETHEUS_RETENTION_SIZE=50GB
PROMETHEUS_WEB_LISTEN_ADDRESS=:9090

# Grafana Configuration
GF_SECURITY_ADMIN_PASSWORD=secure_password
GF_USERS_ALLOW_SIGN_UP=false
GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel

# Loki Configuration
LOKI_RETENTION_PERIOD=30d
LOKI_CHUNK_RETENTION_PERIOD=1h
LOKI_MAX_CHUNK_AGE=2h

# Alertmanager Configuration
ALERTMANAGER_CLUSTER_LISTEN_ADDRESS=:9093
ALERTMANAGER_WEB_LISTEN_ADDRESS=:9093
```

### Volume Mounts

```yaml
volumes:
  - ./volumes/prometheus/data:/prometheus
  - ./volumes/grafana/data:/var/lib/grafana
  - ./volumes/loki/data:/loki
  - ./volumes/promtail/positions:/var/lib/promtail
  - ./configs/prometheus.yml:/etc/prometheus/prometheus.yml
  - ./configs/grafana/provisioning:/etc/grafana/provisioning
  - ./configs/loki-config.yaml:/etc/loki/local-config.yaml
  - ./configs/promtail-config.yml:/etc/promtail/config.yml
```

## 🔧 Usage

### Building the Container

```bash
docker build -t ai-coding-agent-monitoring .
```

### Running with Docker Compose

```bash
docker-compose up monitoring
```

### Accessing Interfaces

```bash
# Grafana Dashboard
open http://localhost:3000

# Prometheus Metrics
open http://localhost:9090

# Alertmanager
open http://localhost:9093

# Loki Logs
open http://localhost:3100
```

## 📊 Metrics Collection

### Application Metrics

```python
# FastAPI metrics middleware
from prometheus_client import Counter, Histogram, Gauge

REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
REQUEST_LATENCY = Histogram('http_request_duration_seconds', 'HTTP request latency', ['method', 'endpoint'])
ACTIVE_CONNECTIONS = Gauge('active_connections', 'Number of active connections')

@app.middleware("http")
async def metrics_middleware(request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time

    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()

    REQUEST_LATENCY.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(process_time)

    return response
```

### Vector Search Metrics

```python
# Vector operation metrics
VECTOR_SEARCH_COUNT = Counter('vector_searches_total', 'Total vector searches', ['user_id', 'model'])
VECTOR_SEARCH_LATENCY = Histogram('vector_search_duration_seconds', 'Vector search latency', ['similarity_threshold'])
EMBEDDING_GENERATION_TIME = Histogram('embedding_generation_duration_seconds', 'Embedding generation time', ['provider', 'model'])

async def track_vector_search(query, user_id, similarity_threshold):
    start_time = time.time()
    results = await vector_service.search_documents(query, user_id, similarity_threshold)
    duration = time.time() - start_time

    VECTOR_SEARCH_COUNT.labels(user_id=user_id, model="bge-large").inc()
    VECTOR_SEARCH_LATENCY.labels(similarity_threshold=str(similarity_threshold)).observe(duration)

    return results
```

### Database Metrics

```sql
-- Query performance metrics
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Custom metrics view
CREATE VIEW system_metrics AS
SELECT
    schemaname,
    tablename,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    n_live_tup,
    n_dead_tup,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables;

-- Vector search performance
CREATE VIEW vector_search_metrics AS
SELECT
    query,
    user_id,
    similarity_threshold,
    result_count,
    execution_time,
    created_at
FROM vector_search_logs
WHERE created_at > NOW() - INTERVAL '24 hours';
```

## 📈 Dashboards & Visualization

### Grafana Dashboards

#### System Overview Dashboard
- CPU, memory, and disk usage across all services
- Network I/O and connection counts
- Docker container health and resource usage
- Database connection pools and query performance

#### AI Orchestrator Dashboard
- API response times and error rates
- Model inference latency and throughput
- Vector search performance metrics
- User authentication and authorization events

#### Application Performance Dashboard
- Frontend loading times and user interactions
- Database query performance and slow queries
- External API response times and failure rates
- Cache hit rates and memory usage

### Custom Panels

```json
// Vector Search Performance Panel
{
  "type": "graph",
  "title": "Vector Search Latency",
  "targets": [
    {
      "expr": "histogram_quantile(0.95, rate(vector_search_duration_seconds_bucket[5m]))",
      "legendFormat": "95th percentile"
    },
    {
      "expr": "histogram_quantile(0.50, rate(vector_search_duration_seconds_bucket[5m]))",
      "legendFormat": "50th percentile"
    }
  ]
}
```

## 📝 Log Aggregation

### Promtail Configuration

```yaml
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: docker
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
    relabel_configs:
      - source_labels: ['__meta_docker_container_name']
        regex: '/(.*)'
        target_label: 'container'
      - source_labels: ['__meta_docker_container_log_stream']
        target_label: 'stream'

  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: varlogs
          __path__: /var/log/*log
```

### Log Queries

```logql
# Error rate by service
rate({container="ai-orchestrator"} |= "ERROR" [5m])

# API response times
{container="ai-orchestrator"} | json | latency > 1000

# User authentication events
{container="ai-orchestrator"} |= "LOGIN" or "LOGOUT"

# Vector search performance
{container="ai-orchestrator"} | json | operation="vector_search" | latency > 500
```

## 🚨 Alerting & Notifications

### Alert Rules

```yaml
groups:
  - name: ai_coding_agent
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }}% for {{ $labels.service }}"

      - alert: SlowVectorSearch
        expr: histogram_quantile(0.95, rate(vector_search_duration_seconds_bucket[5m])) > 2
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Slow vector search detected"
          description: "95th percentile vector search latency is {{ $value }}s"

      - alert: DatabaseConnectionPoolExhausted
        expr: pg_stat_activity_count{state="active"} / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Database connection pool nearly exhausted"
```

### Notification Channels

```yaml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'email'

receivers:
  - name: 'email'
    email_configs:
      - to: '<EMAIL>'
        send_resolved: true
```

## 🔒 Security & Access Control

### Grafana Authentication

```yaml
# OAuth configuration
[auth.generic_oauth]
enabled = true
name = "AI Coding Agent"
client_id = "grafana"
client_secret = "secret"
scopes = "openid profile email"
auth_url = "http://ai-orchestrator:8000/auth/authorize"
token_url = "http://ai-orchestrator:8000/auth/token"
api_url = "http://ai-orchestrator:8000/auth/userinfo"
```

### Network Security

```yaml
# Internal network only
networks:
  - internal

# No external ports exposed
ports: []

# Service mesh integration
labels:
  - "traefik.enable=false"
```

## 📚 Additional Resources

- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [Loki Documentation](https://grafana.com/docs/loki/)
- [Alertmanager Documentation](https://prometheus.io/docs/alerting/latest/alertmanager/)

## 🤝 Contributing

When modifying this container:

1. Update dashboard configurations for new metrics
2. Test alerting rules with realistic scenarios
3. Validate log parsing and aggregation
4. Update monitoring targets for new services
5. Ensure security configurations remain intact
