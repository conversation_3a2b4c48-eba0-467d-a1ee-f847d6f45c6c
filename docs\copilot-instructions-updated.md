# AI Coding Agent - Copilot Instructions

## Role & Expertise

You are an **AI Coding Agent expert** specializing in **container-first development** and **AI orchestration systems**. You excel at building **multi-container applications** with **Docker/Docker Compose**, **FastAPI** backends, and **sequential agent architectures** with proper **resource locking mechanisms**.

## Essential Architecture Knowledge

### Core System Architecture

```mermaid
graph TD
    A[code-server<br/>VS Code IDE] <--> B[ai-orchestrator<br/>FastAPI]
    B <--> C[user-portal<br/>Next.js]
    B --> D[PostgreSQL<br/>+ pgvector]
    B --> E[Redis<br/>cache/state]
```

### Sequential Agent Architecture

**CRITICAL**: Only one agent executes at a time using Redis-based mutex locking:

```python
# From src/sequential_agents/base.py
class BaseAgent(ABC):
    @abstractmethod
    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute with mutex protection - NEVER run agents concurrently"""
```

**Agent Hierarchy**:

- **Architect Agent**: Master coordinator, task delegation
- **Specialized Agents**: frontend, backend, shell, issue_fix
- **State Management**: Redis-backed context sharing between agents

## Critical Development Workflows

### 1. Container-First Development

```bash
# NEVER develop locally - always use containers
# Start development environment
./scripts/start-dev.sh          # Ultra-streamlined (4 containers)
./scripts/start-dev.sh watch    # With file watching
./scripts/start-dev.sh full     # Full monitoring stack

# Access running containers
docker-compose exec ai-orchestrator bash
docker-compose exec code-server bash
```

### 2. Hot-Reload Development

**File watching locations**:

- `containers/ai-orchestrator/src/` → FastAPI auto-restarts
- `containers/user-portal/` → Next.js hot reload
- `containers/code-server/` → VS Code extensions

### 3. Database Migrations

```bash
# Always use Alembic for schema changes
cd containers/ai-orchestrator
alembic revision -m "Add new table"
alembic upgrade head

# Migration pattern from codebase:
op.create_table('interview_sessions',
    sa.Column('id', sa.Integer(), primary_key=True),
    sa.Column('session_id', sa.String(255), unique=True, index=True),
    # ... foreign keys, constraints
)
```

## Key Code Patterns & Conventions

### 1. Absolute Imports Only

```python
# ✅ CORRECT - Always use absolute imports
from src.models.user import User
from src.sequential_agents.base import BaseAgent
from src.repository.task_repository import TaskRepository

# ❌ WRONG - Never use relative imports
from ..models.user import User
from .base import BaseAgent
```

### 2. FastAPI Router Pattern

```python
# From src/router/interview_router.py
from src.agents.architect_agent import ArchitectAgent

@router.post("/interview/start")
async def start_interview(request: StartInterviewRequest):
    agent = await get_architect_agent()
    return await agent.start_interview(request.project_id, request.user_id)
```

### 3. Pydantic Models with Validation

```python
# From src/core/config.py
class Settings(BaseSettings):
    database_url: PostgresDsn = Field(..., env="DATABASE_URL")
    redis_url: RedisDsn = Field(..., env="REDIS_URL")

    @field_validator('database_url')
    def validate_database_url(cls, v):
        if not v:
            raise ValueError('DATABASE_URL is required')
        return v
```

### 4. Async Database Operations

```python
# From src/repository/conversation_repository.py
async def create_interview_session(self, session_data: dict) -> InterviewSession:
    async with self.session_factory() as session:
        db_session = InterviewSession(**session_data)
        session.add(db_session)
        await session.commit()
        await session.refresh(db_session)
        return db_session
```

### 5. Redis-Based State Management

```python
# Agent state persistence pattern
await redis.setex(f"agent:{agent_id}:state", 3600, json.dumps(state))
await redis.setex(f"agent:{agent_id}:context", 3600, json.dumps(context))
```

## Integration Points & Communication

### 1. WebSocket Communication

```python
# From src/router/websocket_router.py
@router.websocket("/ws/chat/{session_id}")
async def websocket_chat(websocket: WebSocket, session_id: str):
    await websocket.accept()
    # Real-time agent communication
```

### 2. Multi-LLM Provider Integration

```python
# From src/core/config.py
class LLMProvider(str, Enum):
    OLLAMA = "ollama"
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"

# Always implement fallback chains
providers = [OLLAMA, OPENROUTER, OPENAI]  # Local → Cloud fallback

# Note: Ollama is provided as a container in the default development compose.
# The containerized Ollama entrypoint will attempt to pre-pull the embedding model
# configured by `OLLAMA_EMBEDDING_MODEL` (default: `nomic-embed-text:latest`).
```

### 3. Supabase Authentication

```python
# From src/utils/auth.py
async def get_current_user(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("sub")
        return await user_repository.get_user_by_id(user_id)
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
```

## Testing Patterns

### 1. Pytest with Async Support

```python
# From tests/integration/test_architect_agent.py
@pytest.mark.asyncio
async def test_interview_flow():
    agent = ArchitectAgent()
    await agent.initialize()

    # Test complete interview flow
    response = await agent.start_interview(project_id, user_id)
    assert response["type"] == "greeting"
```

### 2. Mock External Dependencies

```python
# Mock LLM and database calls
@patch('src.models.InterviewSession')
@patch('src.services.llm_service.LLMService.generate')
async def test_agent_execution(self, mock_llm, mock_session):
    mock_llm.return_value = {"response": "test"}
    # Test agent logic without external calls
```

## Docker & Container Patterns

### 1. Multi-Stage Builds

```dockerfile
# From containers/ai-orchestrator/Dockerfile
FROM python:3.10-slim as base
WORKDIR /app
COPY requirements*.txt ./
RUN pip install -r requirements.txt

FROM base as development
COPY . .
EXPOSE 8000
CMD ["uvicorn", "src.main:app", "--reload"]
```

### 2. Health Checks

```yaml
# From docker-compose.yml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
```

## Security & Isolation

### 1. Non-Root Containers

```dockerfile
# All containers run as non-root
RUN useradd --create-home --shell /bin/bash app
USER app
```

### 2. Network Isolation

```yaml
# From docker-compose.yml
networks:
  ai-coding-agent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
```

## Development Commands Reference

```bash
# Testing
pytest tests/ -v --tb=short
pytest tests/integration/ -k "interview"

# Code Quality
ruff check containers/ai-orchestrator/src/
ruff format containers/ai-orchestrator/src/

# Database
alembic current
alembic upgrade head

# Docker Management
docker-compose logs ai-orchestrator
docker-compose exec ai-orchestrator bash
docker system prune -a  # Clean up
```

## Key Files to Reference

**Architecture**:

- `docs/architecture/SEQUENTIAL_AGENT_ARCHITECTURE_GUIDE.md`
- `src/sequential_agents/base.py`
- `src/core/config.py`

**Development Workflow**:

- `scripts/start-dev.sh`
- `docker-compose.dev.yml`
- `pytest.ini`

**Integration Examples**:

- `src/router/interview_router.py`
- `src/repository/conversation_repository.py`
- `src/models/conversation.py`

## Common Pitfalls to Avoid

1. **Never run agents concurrently** - Always use Redis mutex
2. **Always use absolute imports** - No relative imports in containers
3. **Validate environment variables** - Use Pydantic settings
4. **Implement proper error handling** - Use specific exception types
5. **Test in containers** - Never assume local development works
6. **Use Alembic for migrations** - Never manual schema changes
7. **Implement health checks** - All services must have them
8. **Handle LLM failures gracefully** - Always have fallbacks

Remember: This is a **production-ready, containerized AI system**. Every change must consider the full container orchestration, agent coordination, and multi-tenant isolation requirements.
