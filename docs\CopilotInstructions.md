# Copilot Instructions for AI Coding Agent Project

1. **Absolute Imports Only:** All Python import statements in the ai-orchestrator service must be absolute imports starting from the src root. Relative imports (using leading dots like . or ..) are strictly forbidden. Use absolute imports like `from src.models.user import User` instead of `from ..models.user import User` or `from .base import BaseAgent`.
2. **Follow the Roadmap:** Always refer to the latest roadmap in `docs/Projectroadmap.md` before making changes. Complete tasks in the order specified unless directed otherwise.
3. **Directory Organization:**
   - Place backend code in `/backend`
   - Place frontend/UI code in `/frontend`
   - Place infrastructure (Docker, configs) in `/infra`
   - Place documentation in `/docs`
   - Place tests in `/tests`
   - Place scripts/utilities in `/scripts`
4. **Naming Conventions:** Use clear, descriptive names for files, folders, and variables. Follow Python and React best practices.
5. **Documentation:** Update documentation in `/docs` for any new features, changes, or setup steps.
6. **Security:** Apply container and code security best practices. Do not expose secrets or sensitive data.
7. **Authentication:** Integrate Supabase Auth for all user-facing and API endpoints.
8. **Testing:** Add or update tests for new code in `/tests`. Ensure all tests pass before merging changes.
9. **CI/CD:** Use the CI/CD pipeline for builds, tests, and deployments. Do not bypass automated checks.
10. **Monitoring:** Ensure monitoring/logging is enabled for all containers and services.
11. **Feedback:** After each milestone, review progress and update the roadmap if needed.
12. **Backups:** Ensure database backup scripts/configs are present and documented.
13. **Resource Usage:** Monitor and optimize resource usage for containers and services.
14. **Communication:** Clearly comment code and communicate changes in commit messages and documentation.
