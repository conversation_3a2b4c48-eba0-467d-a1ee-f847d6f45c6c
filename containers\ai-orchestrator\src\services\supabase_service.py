"""
Supabase Service Layer for AI Coding Agent.

This module provides a comprehensive service layer for interacting with Supabase,
including database operations, authentication, and vector storage with proper
async support and connection pooling.

Author: AI Coding Agent
Version: 1.0.0
"""

import os
import asyncio
from typing import Optional, Dict, Any, List, Union
from contextlib import asynccontextmanager
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta

# Import secrets utility for secure configuration
from src.utils.secrets import get_supabase_config, get_database_url

# Configure logging
logger = logging.getLogger(__name__)

# External imports
try:
    from supabase import create_client, Client
except ImportError:
    raise ImportError(
        "Supabase client not installed. "
        "Install with: pip install supabase"
    )

try:
    import asyncpg
    from pgvector.asyncpg import register_vector
    ASYNCPG_AVAILABLE = True
except ImportError:
    asyncpg = None
    register_vector = None
    ASYNCPG_AVAILABLE = False
    logger.warning(
        "AsyncPG or pgvector not installed. "
        "Some database features may not be available. "
        "Install with: pip install asyncpg pgvector"
    )


@dataclass
class SupabaseConfig:
    """Configuration class for Supabase service."""

    url: str
    anon_key: str
    service_key: str
    database_url: str
    jwt_secret: str
    max_connections: int = 20
    min_connections: int = 5
    connection_timeout: float = 30.0
    command_timeout: float = 60.0
    retry_attempts: int = 3
    retry_delay: float = 1.0
    enable_ssl: bool = False

    @classmethod
    def from_env(cls) -> 'SupabaseConfig':
        """Create configuration from environment variables and Docker Secrets.

        Tries structured settings (src.core.config.settings) first; falls back to os.getenv
        and Docker secrets helpers.
        """
        from typing import Dict, Any

        config_data: Dict[str, Any] = {}

        # Helper to read optional settings safely
        def read_optional() -> Dict[str, Any]:
            # Try importing settings, fall back to env vars
            try:
                from src.core.config import settings  # type: ignore
                return {
                    'max_connections': int(getattr(settings, 'SUPABASE_MAX_CONNECTIONS', 20)),
                    'min_connections': int(getattr(settings, 'SUPABASE_MIN_CONNECTIONS', 5)),
                    'connection_timeout': float(getattr(settings, 'SUPABASE_CONNECTION_TIMEOUT', 30.0)),
                    'command_timeout': float(getattr(settings, 'SUPABASE_COMMAND_TIMEOUT', 60.0)),
                    'retry_attempts': int(getattr(settings, 'SUPABASE_RETRY_ATTEMPTS', 3)),
                    'retry_delay': float(getattr(settings, 'SUPABASE_RETRY_DELAY', 1.0)),
                    'enable_ssl': bool(getattr(settings, 'SUPABASE_ENABLE_SSL', False)),
                }
            except Exception:
                return {
                    'max_connections': int(os.getenv('SUPABASE_MAX_CONNECTIONS', '20')),
                    'min_connections': int(os.getenv('SUPABASE_MIN_CONNECTIONS', '5')),
                    'connection_timeout': float(os.getenv('SUPABASE_CONNECTION_TIMEOUT', '30')),
                    'command_timeout': float(os.getenv('SUPABASE_COMMAND_TIMEOUT', '60')),
                    'retry_attempts': int(os.getenv('SUPABASE_RETRY_ATTEMPTS', '3')),
                    'retry_delay': float(os.getenv('SUPABASE_RETRY_DELAY', '1')),
                    'enable_ssl': os.getenv('SUPABASE_ENABLE_SSL', 'false').lower() == 'true',
                }

        # Preferred path: Docker secrets
        try:
            supabase_config = get_supabase_config()
            database_url = get_database_url()
            config_data = {
                'url': supabase_config['url'],
                'anon_key': supabase_config['anon_key'],
                'service_key': supabase_config['service_key'],
                'database_url': database_url,
                'jwt_secret': supabase_config['jwt_secret'],
                **read_optional(),
            }
        except ValueError as e:
            logger.error(f"Failed to load configuration from secrets: {e}")
            # Fallback: environment variables
            required_vars = {
                'SUPABASE_URL': 'url',
                'SUPABASE_ANON_KEY': 'anon_key',
                'SUPABASE_SERVICE_KEY': 'service_key',
                'SUPABASE_DB_URL': 'database_url',
                'JWT_SECRET': 'jwt_secret',
            }
            missing_vars: List[str] = []
            for env_var, config_key in required_vars.items():
                value = os.getenv(env_var)
                if not value:
                    missing_vars.append(env_var)
                else:
                    config_data[config_key] = value
            if missing_vars:
                raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
            config_data.update(read_optional())

        return cls(**config_data)


@dataclass
class DatabaseStats:
    """Database statistics and health information."""

    total_connections: int
    active_connections: int
    idle_connections: int
    pool_size: int
    avg_query_time: float
    total_queries: int
    last_health_check: datetime = field(default_factory=datetime.utcnow)


class SupabaseServiceError(Exception):
    """Base exception for Supabase service errors."""
    pass


class ConnectionError(SupabaseServiceError):
    """Database connection related errors."""
    pass


class AuthenticationError(SupabaseServiceError):
    """Authentication related errors."""
    pass


class PermissionError(SupabaseServiceError):
    """Permission and authorization related errors."""
    pass


class SupabaseService:
    """
    Main Supabase service class providing async database operations,
    authentication, and vector storage capabilities.

    This service handles:
    - Database connection pooling
    - Authentication and JWT management
    - Vector operations with pgvector
    - Row Level Security (RLS) enforcement
    - Error handling and retry logic
    """

    def __init__(self, config: Optional[SupabaseConfig] = None):
        """
        Initialize Supabase service.

        Args:
            config: Supabase configuration. If None, loads from environment.
        """
        self.config = config or SupabaseConfig.from_env()
        self._client: Optional[Client] = None
        self._service_client: Optional[Client] = None
        self._db_pool: Optional[Any] = None
        self._initialized = False
        self._stats = DatabaseStats(0, 0, 0, 0, 0.0, 0)

        logger.info("Supabase service initialized with configuration")

    @property
    def is_initialized(self) -> bool:
        """Check if service is properly initialized."""
        return self._initialized

    @property
    def stats(self) -> DatabaseStats:
        """Get current database statistics."""
        return self._stats

    async def initialize(self) -> None:
        """
        Initialize Supabase clients and database connection pool.

        Raises:
            ConnectionError: If unable to establish database connections.
            AuthenticationError: If authentication setup fails.
        """
        try:
            logger.info("Initializing Supabase service...")

            # Initialize Supabase clients
            await self._initialize_clients()

            # Initialize database connection pool
            await self._initialize_db_pool()

            # Register vector types
            await self._register_vector_types()

            # Verify connections
            await self._verify_connections()

            self._initialized = True
            logger.info("Supabase service initialization completed successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Supabase service: {str(e)}")
            await self.cleanup()
            raise ConnectionError(f"Supabase initialization failed: {str(e)}") from e

    async def _initialize_clients(self) -> None:
        """Initialize Supabase API clients."""
        try:
            # Anonymous/public client
            self._client = create_client(
                self.config.url,
                self.config.anon_key
            )

            # Service role client (admin privileges)
            self._service_client = create_client(
                self.config.url,
                self.config.service_key
            )

            logger.debug("Supabase API clients initialized")

        except Exception as e:
            logger.error(f"Failed to initialize Supabase clients: {str(e)}")
            raise AuthenticationError(f"Client initialization failed: {str(e)}") from e

    async def _initialize_db_pool(self) -> None:
        """Initialize database connection pool."""
        if not ASYNCPG_AVAILABLE:
            logger.warning("AsyncPG not available, skipping database pool initialization")
            return

        try:
            # Parse database URL for SSL settings
            db_url = self.config.database_url
            if self.config.enable_ssl and '?sslmode=' not in db_url:
                db_url += '?sslmode=require'

            self._db_pool = await asyncpg.create_pool(
                db_url,
                min_size=self.config.min_connections,
                max_size=self.config.max_connections,
                timeout=self.config.connection_timeout,
                command_timeout=self.config.command_timeout,
                server_settings={
                    'jit': 'off',  # Disable JIT for better connection performance
                    'application_name': 'ai_coding_agent_supabase'
                }
            )

            logger.debug(
                f"Database connection pool initialized "
                f"(min: {self.config.min_connections}, max: {self.config.max_connections})"
            )

        except Exception as e:
            logger.error(f"Failed to initialize database pool: {str(e)}")
            raise ConnectionError(f"Database pool initialization failed: {str(e)}") from e

    async def _register_vector_types(self) -> None:
        """Register pgvector types with asyncpg."""
        if not ASYNCPG_AVAILABLE or not self._db_pool or register_vector is None:
            logger.warning("AsyncPG not available or pool not initialized, skipping vector type registration")
            return

        try:
            async with self._db_pool.acquire() as conn:
                await register_vector(conn)

            logger.debug("Vector types registered with asyncpg")

        except Exception as e:
            logger.error(f"Failed to register vector types: {str(e)}")
            raise ConnectionError(f"Vector type registration failed: {str(e)}") from e

    async def _verify_connections(self) -> None:
        """Verify that all connections are working properly."""
        try:
            # Test database connection
            async with self._db_pool.acquire() as conn:
                result = await conn.fetchval('SELECT 1')
                if result != 1:
                    raise ConnectionError("Database connection verification failed")

            # Test Supabase API connections
            try:
                # Test with service client (should always work)
                response = self._service_client.table('user_profiles').select('id').limit(1).execute()
                if response.data is not None:
                    logger.debug("Supabase API connection verified successfully")
                else:
                    logger.debug("Supabase API connection verified (empty result)")
            except Exception as e:
                logger.warning(f"Supabase API verification warning: {str(e)}")
                # Non-fatal for initial setup

            logger.debug("All connections verified successfully")

        except Exception as e:
            logger.error(f"Connection verification failed: {str(e)}")
            raise ConnectionError(f"Connection verification failed: {str(e)}") from e

    def get_client(self, user_jwt: Optional[str] = None) -> Client:
        """
        Get Supabase client with optional user JWT for RLS.

        Args:
            user_jwt: Optional JWT token for user-specific operations.

        Returns:
            Configured Supabase client.

        Raises:
            SupabaseServiceError: If service not initialized.
        """
        if not self._initialized:
            raise SupabaseServiceError("Service not initialized. Call initialize() first.")

        if not self._client:
            raise SupabaseServiceError("Client not available.")

        if user_jwt:
            # Create client for user-specific operations
            # Note: JWT handling would need to be implemented based on specific Supabase SDK version
            logger.warning("JWT-based client creation not fully implemented. Using default client.")
            return self._client

        return self._client

    @property
    def service_client(self) -> Client:
        """
        Get service role client for admin operations.

        Returns:
            Service role Supabase client.

        Raises:
            SupabaseServiceError: If service not initialized.
        """
        if not self._initialized:
            raise SupabaseServiceError("Service not initialized. Call initialize() first.")

        if not self._service_client:
            raise SupabaseServiceError("Service client not available.")

        return self._service_client

    @asynccontextmanager
    async def get_db_connection(self):
        """
        Get database connection from pool.

        Yields:
            Database connection.

        Raises:
            ConnectionError: If unable to acquire connection.
        """
        if not self._db_pool:
            raise ConnectionError("Database pool not initialized")

        connection = None
        try:
            connection = await self._db_pool.acquire()
            yield connection
        except Exception as e:
            logger.error(f"Database operation failed: {str(e)}")
            raise
        finally:
            if connection:
                await self._db_pool.release(connection)

    async def execute_query(
        self,
        query: str,
        *args,
        fetch_type: str = 'all'
    ) -> Union[List[Dict[str, Any]], Dict[str, Any], Any]:
        """
        Execute database query with proper error handling.

        Args:
            query: SQL query to execute.
            *args: Query parameters.
            fetch_type: Type of fetch ('all', 'one', 'val').

        Returns:
            Query results based on fetch_type.

        Raises:
            ConnectionError: If database operation fails.
        """
        start_time = datetime.utcnow()

        try:
            async with self.get_db_connection() as conn:
                if fetch_type == 'all':
                    result = await conn.fetch(query, *args)
                    return [dict(row) for row in result]
                elif fetch_type == 'one':
                    result = await conn.fetchrow(query, *args)
                    return dict(result) if result else None
                elif fetch_type == 'val':
                    return await conn.fetchval(query, *args)
                else:
                    raise ValueError(f"Invalid fetch_type: {fetch_type}")

        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}")
            raise ConnectionError(f"Query execution failed: {str(e)}") from e

        finally:
            # Update statistics
            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            self._update_stats(execution_time)

    def _update_stats(self, execution_time_ms: float) -> None:
        """Update internal statistics."""
        self._stats.total_queries += 1
        if self._stats.total_queries == 1:
            self._stats.avg_query_time = execution_time_ms
        else:
            # Moving average
            self._stats.avg_query_time = (
                (self._stats.avg_query_time * (self._stats.total_queries - 1) + execution_time_ms)
                / self._stats.total_queries
            )

        # Update pool stats if available
        if self._db_pool:
            self._stats.pool_size = self._db_pool.get_size()
            self._stats.active_connections = len(self._db_pool._holders)
            self._stats.idle_connections = len(self._db_pool._queue._queue)

        self._stats.last_health_check = datetime.utcnow()

    async def health_check(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check.

        Returns:
            Health status information.
        """
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'initialized': self._initialized,
            'database': {'status': 'unknown'},
            'api': {'status': 'unknown'},
            'stats': {
                'total_queries': self._stats.total_queries,
                'avg_query_time_ms': round(self._stats.avg_query_time, 2),
                'pool_size': self._stats.pool_size,
                'active_connections': self._stats.active_connections,
                'idle_connections': self._stats.idle_connections
            }
        }

        # Test database connection
        try:
            await self.execute_query('SELECT 1', fetch_type='val')
            health_status['database']['status'] = 'healthy'
        except Exception as e:
            health_status['database']['status'] = 'unhealthy'
            health_status['database']['error'] = str(e)
            health_status['status'] = 'degraded'

        # Test API connection
        try:
            self._service_client.table('user_profiles').select('id').limit(1).execute()
            health_status['api']['status'] = 'healthy'
        except Exception as e:
            health_status['api']['status'] = 'unhealthy'
            health_status['api']['error'] = str(e)
            health_status['status'] = 'degraded'

        return health_status

    async def get_session_expiry(self, user_id: str, session_duration_hours: int = 24) -> datetime:
        """
        Calculate session expiry time using timedelta.

        Args:
            user_id: User ID for logging purposes.
            session_duration_hours: Session duration in hours.

        Returns:
            Session expiry datetime.
        """
        current_time = datetime.utcnow()
        session_expiry = current_time + timedelta(hours=session_duration_hours)

        logger.debug(f"Session for user {user_id} expires at {session_expiry.isoformat()}")
        return session_expiry

    async def cleanup_expired_sessions(self, retention_days: int = 30) -> int:
        """
        Clean up expired sessions using timedelta for date calculations.

        Args:
            retention_days: Number of days to retain sessions.

        Returns:
            Number of sessions cleaned up.
        """
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)

        try:
            query = """
                DELETE FROM user_sessions
                WHERE created_at < $1 AND expired = true
                RETURNING id
            """

            result = await self.execute_query(
                query,
                cutoff_date,
                fetch_type='all'
            )

            cleaned_count = len(result) if result else 0
            logger.info(f"Cleaned up {cleaned_count} expired sessions older than {retention_days} days")
            return cleaned_count

        except Exception as e:
            logger.error(f"Failed to cleanup expired sessions: {str(e)}")
            return 0

    async def cleanup(self) -> None:
        """Clean up resources and close connections."""
        logger.info("Cleaning up Supabase service...")

        if self._db_pool:
            await self._db_pool.close()
            self._db_pool = None

        # Supabase clients don't need explicit cleanup
        self._client = None
        self._service_client = None
        self._initialized = False

        logger.info("Supabase service cleanup completed")


# Global service instance
_supabase_service: Optional[SupabaseService] = None


async def get_supabase_service() -> SupabaseService:
    """
    Get global Supabase service instance.

    Returns:
        Initialized Supabase service.

    Raises:
        SupabaseServiceError: If service initialization fails.
    """
    global _supabase_service

    if _supabase_service is None:
        _supabase_service = SupabaseService()
        await _supabase_service.initialize()

    return _supabase_service


async def cleanup_supabase_service() -> None:
    """Clean up global Supabase service instance."""
    global _supabase_service

    if _supabase_service:
        await _supabase_service.cleanup()
        _supabase_service = None


# Dependency function for FastAPI
async def get_supabase_dependency() -> SupabaseService:
    """FastAPI dependency for Supabase service."""
    return await get_supabase_service()


if __name__ == "__main__":
    # Example usage and testing
    async def test_service():
        """Test the Supabase service."""
        try:
            service = SupabaseService()
            await service.initialize()

            # Test health check
            health = await service.health_check()
            print(f"Health check: {health}")

            # Test basic query
            result = await service.execute_query(
                "SELECT COUNT(*) as user_count FROM auth.users",
                fetch_type='one'
            )
            print(f"User count: {result}")

        except Exception as e:
            print(f"Service test failed: {str(e)}")
        finally:
            await service.cleanup()

    # Run test
    asyncio.run(test_service())