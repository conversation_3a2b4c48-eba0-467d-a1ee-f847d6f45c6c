"""add_missing_tables

Revision ID: 878495c9450c
Revises: 3e3b476e8c1a
Create Date: 2025-09-05 16:54:28.640037

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '878495c9450c'
down_revision: Union[str, None] = '3e3b476e8c1a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create deployment_integrations table
    op.create_table('deployment_integrations',
        sa.Column('id', sa.UUID(), nullable=False, default=sa.text('uuid_generate_v4()')),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('provider', sa.Enum('VERCEL', name='deploymentprovider'), nullable=False),
        sa.Column('encrypted_api_key', sa.Text(), nullable=False),
        sa.Column('team_id', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['user_id'], ['user_profiles.supabase_user_id'], name='fk_deployment_integrations_user_id_users', ondelete='CASCADE')
    )
    op.create_index('ix_deployment_integrations_id', 'deployment_integrations', ['id'], unique=False)
    op.create_index('ix_deployment_integrations_user_id', 'deployment_integrations', ['user_id'], unique=False)
    op.create_index('ix_deployment_integrations_provider', 'deployment_integrations', ['provider'], unique=False)

    # Create ingestion_errors table
    op.create_table('ingestion_errors',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('file_path', sa.String(length=500), nullable=False),
        sa.Column('error_type', sa.String(length=100), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=False),
        sa.Column('stack_trace', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name='fk_ingestion_errors_project_id_projects', ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['user_profiles.supabase_user_id'], name='fk_ingestion_errors_user_id_users', ondelete='CASCADE')
    )
    op.create_index('ix_ingestion_errors_id', 'ingestion_errors', ['id'], unique=False)
    op.create_index('ix_ingestion_errors_project_id', 'ingestion_errors', ['project_id'], unique=False)
    op.create_index('ix_ingestion_errors_user_id', 'ingestion_errors', ['user_id'], unique=False)
    op.create_index('ix_ingestion_errors_error_type', 'ingestion_errors', ['error_type'], unique=False)
    op.create_index('ix_ingestion_errors_created_at', 'ingestion_errors', ['created_at'], unique=False)

    # Add foreign key constraints for user references
    op.create_foreign_key('fk_deployment_integrations_user_id_user_profiles', 'deployment_integrations', 'user_profiles', ['user_id'], ['supabase_user_id'], ondelete='CASCADE')
    op.create_foreign_key('fk_ingestion_errors_user_id_user_profiles', 'ingestion_errors', 'user_profiles', ['user_id'], ['supabase_user_id'], ondelete='CASCADE')


def downgrade() -> None:
    # Drop foreign key constraints
    op.drop_constraint('fk_ingestion_errors_user_id_user_profiles', 'ingestion_errors', type_='foreignkey')
    op.drop_constraint('fk_deployment_integrations_user_id_user_profiles', 'deployment_integrations', type_='foreignkey')

    # Drop ingestion_errors table
    op.drop_index('ix_ingestion_errors_created_at', table_name='ingestion_errors')
    op.drop_index('ix_ingestion_errors_error_type', table_name='ingestion_errors')
    op.drop_index('ix_ingestion_errors_user_id', table_name='ingestion_errors')
    op.drop_index('ix_ingestion_errors_project_id', table_name='ingestion_errors')
    op.drop_index('ix_ingestion_errors_id', table_name='ingestion_errors')
    op.drop_table('ingestion_errors')

    # Drop deployment_integrations table
    op.drop_index('ix_deployment_integrations_provider', table_name='deployment_integrations')
    op.drop_index('ix_deployment_integrations_user_id', table_name='deployment_integrations')
    op.drop_index('ix_deployment_integrations_id', table_name='deployment_integrations')
    op.drop_table('deployment_integrations')
