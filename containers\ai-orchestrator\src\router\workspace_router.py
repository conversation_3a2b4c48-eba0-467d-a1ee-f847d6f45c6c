"""
Workspace Router for Multi-Tenant AI Coding Agent.

This module provides FastAPI endpoints for managing user workspaces,
including starting, stopping, and monitoring workspace containers.

Author: AI Coding Agent
Version: 1.0.0
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from pydantic import BaseModel
from docker.errors import DockerException, NotFound

from src.models import Workspace, get_db
from src.models.workspace import WorkspaceStatus
from src.services.docker_service import DockerWorkspaceService
from src.services.auth_service import get_current_user, UserProfile

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/workspaces", tags=["workspaces"])


# Docker service dependency
def get_docker_service() -> DockerWorkspaceService:
    """Get Docker workspace service dependency."""
    return DockerWorkspaceService()


# Pydantic models for request/response
class WorkspaceResponse(BaseModel):
    """Workspace response model."""
    id: int
    user_id: str
    container_id: Optional[str]
    container_name: str
    subdomain: str
    status: str
    workspace_url: str
    error_message: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class WorkspaceStatusResponse(BaseModel):
    """Workspace status response model."""
    exists: bool
    workspace: Optional[WorkspaceResponse]
    container_status: Optional[Dict[str, Any]]


class StartWorkspaceRequest(BaseModel):
    """Start workspace request model."""
    workspace_config: Optional[Dict[str, Any]] = None


class WorkspaceActionResponse(BaseModel):
    """Workspace action response model."""
    success: bool
    message: str
    workspace: Optional[WorkspaceResponse]


async def start_workspace_container(
    workspace: Workspace,
    docker_service: DockerWorkspaceService,
    db: Session
) -> None:
    """
    Background task to start workspace container.

    Args:
        workspace: Workspace database model
        docker_service: Docker service instance
        db: Database session
    """
    try:
        # Update status to creating
        workspace.update_status(WorkspaceStatus.CREATING)
        db.commit()

        # Get user_id safely
        user_id = getattr(workspace, 'user_id', '')
        if not user_id:
            raise ValueError("Invalid user_id in workspace")

        # Create container
        container = await docker_service.create_workspace(user_id, workspace)

        # Update workspace with container info
        setattr(workspace, 'container_id', container.id)
        workspace.update_status(WorkspaceStatus.RUNNING)
        db.commit()

        logger.info(f"Successfully started workspace {workspace.id} for user {user_id}")

    except SQLAlchemyError as e:
        logger.error(f"Database error while starting workspace {workspace.id}: {str(e)}")
        db.rollback()
        try:
            workspace.update_status(WorkspaceStatus.ERROR, f"Database error: {str(e)}")
            db.commit()
        except Exception:
            logger.error(f"Failed to update workspace status after database error for workspace {workspace.id}")
    except DockerException as e:
        logger.error(f"Docker error while starting workspace {workspace.id}: {str(e)}")
        try:
            workspace.update_status(WorkspaceStatus.ERROR, f"Docker error: {str(e)}")
            db.commit()
        except Exception:
            logger.error(f"Failed to update workspace status after Docker error for workspace {workspace.id}")
    except ValueError as e:
        logger.error(f"Configuration error while starting workspace {workspace.id}: {str(e)}")
        try:
            workspace.update_status(WorkspaceStatus.ERROR, f"Configuration error: {str(e)}")
            db.commit()
        except Exception:
            logger.error(f"Failed to update workspace status after configuration error for workspace {workspace.id}")
    except Exception as e:
        logger.error(f"Unexpected error while starting workspace {workspace.id}: {str(e)}")
        try:
            workspace.update_status(WorkspaceStatus.ERROR, f"Unexpected error: {str(e)}")
            db.commit()
        except Exception:
            logger.error(f"Failed to update workspace status after unexpected error for workspace {workspace.id}")


async def stop_workspace_container(
    workspace: Workspace,
    docker_service: DockerWorkspaceService,
    db: Session
) -> None:
    """
    Background task to stop workspace container.

    Args:
        workspace: Workspace database model
        docker_service: Docker service instance
        db: Database session
    """
    try:
        # Update status to stopping
        workspace.update_status(WorkspaceStatus.STOPPING)
        db.commit()

        # Stop container if it exists
        container_id = getattr(workspace, 'container_id', None)
        if container_id:
            success = await docker_service.stop_workspace(container_id)
            if not success:
                logger.warning(f"Failed to stop container {container_id}")

        # Update workspace status
        workspace.update_status(WorkspaceStatus.STOPPED)
        db.commit()

        user_id = getattr(workspace, 'user_id', 'unknown')
        logger.info(f"Successfully stopped workspace {workspace.id} for user {user_id}")

    except SQLAlchemyError as e:
        logger.error(f"Database error while stopping workspace {workspace.id}: {str(e)}")
        db.rollback()
        try:
            workspace.update_status(WorkspaceStatus.ERROR, f"Database error: {str(e)}")
            db.commit()
        except Exception:
            logger.error(f"Failed to update workspace status after database error for workspace {workspace.id}")
    except DockerException as e:
        logger.error(f"Docker error while stopping workspace {workspace.id}: {str(e)}")
        try:
            workspace.update_status(WorkspaceStatus.ERROR, f"Docker error: {str(e)}")
            db.commit()
        except Exception:
            logger.error(f"Failed to update workspace status after Docker error for workspace {workspace.id}")
    except Exception as e:
        logger.error(f"Unexpected error while stopping workspace {workspace.id}: {str(e)}")
        try:
            workspace.update_status(WorkspaceStatus.ERROR, f"Unexpected error: {str(e)}")
            db.commit()
        except Exception:
            logger.error(f"Failed to update workspace status after unexpected error for workspace {workspace.id}")


@router.get("/me", response_model=WorkspaceStatusResponse)
async def get_my_workspace(
    current_user: UserProfile = Depends(get_current_user),
    db: Session = Depends(get_db),
    docker_service: DockerWorkspaceService = Depends(get_docker_service)
):
    """
    Get the current user's workspace status and information.

    Returns workspace details including container status if running.
    """
    user_id = current_user.id
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid user token")

    try:
        # Get user's workspace from database
        workspace = db.query(Workspace).filter(
            Workspace.user_id == user_id
        ).first()

        if not workspace:
            return WorkspaceStatusResponse(exists=False, workspace=None, container_status=None)

        # Get container status if container exists
        container_status = None
        container_id = getattr(workspace, 'container_id', None)
        if container_id:
            container_status = await docker_service.get_container_status(container_id)

            # Update workspace status based on container state
            if container_status:
                current_status = getattr(workspace, 'status', WorkspaceStatus.STOPPED)
                if container_status["status"] == "running" and current_status != WorkspaceStatus.RUNNING:
                    workspace.update_status(WorkspaceStatus.RUNNING)
                    db.commit()
                elif container_status["status"] in ["exited", "stopped"] and current_status == WorkspaceStatus.RUNNING:
                    workspace.update_status(WorkspaceStatus.STOPPED)
                    db.commit()

        return WorkspaceStatusResponse(
            exists=True,
            workspace=WorkspaceResponse.model_validate(workspace),
            container_status=container_status
        )

    except SQLAlchemyError as e:
        logger.error(f"Database error while getting workspace for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except DockerException as e:
        logger.error(f"Docker error while getting workspace status for user {user_id}: {str(e)}")
        raise HTTPException(status_code=503, detail="Docker service unavailable")
    except Exception as e:
        logger.error(f"Unexpected error while getting workspace for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve workspace information")


@router.post("/start", response_model=WorkspaceActionResponse)
async def start_workspace(
    request: StartWorkspaceRequest,
    background_tasks: BackgroundTasks,
    current_user: UserProfile = Depends(get_current_user),
    db: Session = Depends(get_db),
    docker_service: DockerWorkspaceService = Depends(get_docker_service)
):
    """
    Start a workspace for the current user.

    Creates a new workspace if none exists, or starts an existing stopped workspace.
    """
    user_id = current_user.id
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid user token")

    try:
        # Check if user already has a workspace
        existing_workspace = db.query(Workspace).filter(
            Workspace.user_id == user_id
        ).filter(
            Workspace.status.in_([WorkspaceStatus.RUNNING.value, WorkspaceStatus.CREATING.value])  # type: ignore
        ).first()

        if existing_workspace:
            return WorkspaceActionResponse(
                success=False,
                message="Workspace is already running or starting",
                workspace=WorkspaceResponse.model_validate(existing_workspace)
            )

        # Get or create workspace
        workspace = db.query(Workspace).filter(Workspace.user_id == user_id).first()

        if not workspace:
            # Create new workspace
            config = docker_service.generate_workspace_config(user_id)
            workspace = Workspace(
                user_id=user_id,
                container_name=config["container_name"],
                subdomain=config["subdomain"],
                status=WorkspaceStatus.CREATING,
                workspace_config=json.dumps(request.workspace_config) if request.workspace_config else None
            )
            db.add(workspace)
            db.commit()
            db.refresh(workspace)

        # Start workspace in background
        background_tasks.add_task(
            start_workspace_container,
            workspace,
            docker_service,
            db
        )

        return WorkspaceActionResponse(
            success=True,
            message="Workspace is starting...",
            workspace=WorkspaceResponse.model_validate(workspace)
        )

    except SQLAlchemyError as e:
        logger.error(f"Database error while starting workspace for user {user_id}: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Database error occurred")
    except DockerException as e:
        logger.error(f"Docker error while starting workspace for user {user_id}: {str(e)}")
        raise HTTPException(status_code=503, detail="Docker service unavailable")
    except ValueError as e:
        logger.error(f"Invalid configuration for workspace start for user {user_id}: {str(e)}")
        raise HTTPException(status_code=400, detail="Invalid workspace configuration")
    except Exception as e:
        logger.error(f"Unexpected error while starting workspace for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to start workspace")


@router.post("/stop", response_model=WorkspaceActionResponse)
async def stop_workspace(
    background_tasks: BackgroundTasks,
    current_user: UserProfile = Depends(get_current_user),
    db: Session = Depends(get_db),
    docker_service: DockerWorkspaceService = Depends(get_docker_service)
):
    """
    Stop the current user's workspace.

    Stops the running container but keeps the workspace record for future use.
    """
    user_id = current_user.id
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid user token")

    try:
        # Get user's workspace
        workspace = db.query(Workspace).filter(Workspace.user_id == user_id).first()

        if not workspace:
            raise HTTPException(status_code=404, detail="No workspace found")

        current_status = getattr(workspace, 'status', WorkspaceStatus.STOPPED)
        if current_status in [WorkspaceStatus.STOPPED, WorkspaceStatus.STOPPING]:
            return WorkspaceActionResponse(
                success=False,
                message="Workspace is already stopped or stopping",
                workspace=WorkspaceResponse.model_validate(workspace)
            )

        # Stop workspace in background
        background_tasks.add_task(
            stop_workspace_container,
            workspace,
            docker_service,
            db
        )

        return WorkspaceActionResponse(
            success=True,
            message="Workspace is stopping...",
            workspace=WorkspaceResponse.model_validate(workspace)
        )

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        logger.error(f"Database error while stopping workspace for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Database error occurred")
    except DockerException as e:
        logger.error(f"Docker error while stopping workspace for user {user_id}: {str(e)}")
        raise HTTPException(status_code=503, detail="Docker service unavailable")
    except Exception as e:
        logger.error(f"Unexpected error while stopping workspace for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to stop workspace")


@router.delete("/delete", response_model=WorkspaceActionResponse)
async def delete_workspace(
    remove_data: bool = False,
    current_user: UserProfile = Depends(get_current_user),
    db: Session = Depends(get_db),
    docker_service: DockerWorkspaceService = Depends(get_docker_service)
):
    """
    Delete the current user's workspace.

    Removes the container and optionally the associated data volumes.
    """
    user_id = current_user.id
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid user token")

    try:
        # Get user's workspace
        workspace = db.query(Workspace).filter(Workspace.user_id == user_id).first()

        if not workspace:
            raise HTTPException(status_code=404, detail="No workspace found")

        # Remove container if it exists
        container_id = getattr(workspace, 'container_id', None)
        if container_id:
            await docker_service.remove_workspace(container_id, remove_volumes=remove_data)

        # Remove workspace record
        db.delete(workspace)
        db.commit()

        return WorkspaceActionResponse(
            success=True,
            message=f"Workspace deleted{' with data' if remove_data else ''}",
            workspace=None
        )

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        logger.error(f"Database error while deleting workspace for user {user_id}: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail="Database error occurred")
    except NotFound as e:
        logger.warning(f"Container not found while deleting workspace for user {user_id}: {str(e)}")
        # Continue with database cleanup even if container doesn't exist
        try:
            db.delete(workspace)
            db.commit()
            return WorkspaceActionResponse(
                success=True,
                message=f"Workspace deleted{' with data' if remove_data else ''} (container was already removed)",
                workspace=None
            )
        except SQLAlchemyError as db_error:
            logger.error(f"Database error during cleanup for user {user_id}: {str(db_error)}")
            db.rollback()
            raise HTTPException(status_code=500, detail="Database error occurred during cleanup")
    except DockerException as e:
        logger.error(f"Docker error while deleting workspace for user {user_id}: {str(e)}")
        raise HTTPException(status_code=503, detail="Docker service unavailable")
    except Exception as e:
        logger.error(f"Unexpected error while deleting workspace for user {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete workspace")


@router.get("/health")
async def workspace_health_check(
    docker_service: DockerWorkspaceService = Depends(get_docker_service)
):
    """
    Health check endpoint for workspace management service.
    """
    try:
        # Perform basic docker service health check
        try:
            docker_service.client.ping()
            docker_info = docker_service.client.info()
            health = {
                "status": "healthy",
                "docker_version": docker_info.get("ServerVersion"),
                "containers_running": docker_info.get("ContainersRunning", 0),
                "containers_total": docker_info.get("Containers", 0),
                "images": docker_info.get("Images", 0)
            }
        except DockerException as docker_error:
            logger.error(f"Docker service health check failed: {str(docker_error)}")
            health = {
                "status": "unhealthy",
                "error": str(docker_error)
            }
        except Exception as docker_error:
            logger.error(f"Unexpected error during Docker health check: {str(docker_error)}")
            health = {
                "status": "unhealthy",
                "error": str(docker_error)
            }

        return {
            "status": "healthy" if health["status"] == "healthy" else "unhealthy",
            "docker_service": health,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f"Workspace health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }