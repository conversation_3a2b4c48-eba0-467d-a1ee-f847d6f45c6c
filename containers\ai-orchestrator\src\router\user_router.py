# Project: AI Coding Agent
# Purpose: User management API router using UserRepository
# Author: AI Coding Agent Team

"""
User API Router for AI Coding Agent.

This module provides REST API endpoints for user management operations,
leveraging the UserRepository for data access and Supabase Auth for authentication.

Key Features:
- Profile management endpoints (/users/me)
- Admin user listing with pagination
- Comprehensive error handling
- Supabase Auth integration
- Type-safe request/response validation
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime
import jwt

# FastAPI imports
from fastapi import APIRouter, Depends, HTTPException, status, Query, Header
from sqlalchemy.orm import Session

# Internal imports
from src.core.config import settings
from src.models.database import get_db
from src.repository.user_repository import (
    UserRepository,
    get_user_repository,
    UserRepositoryError,
    UserAlreadyExistsError
)
from src.schemas.user_schemas import (
    UserResponse,
    UserListResponse,
    UserProfileUpdateSchema,
    SupabaseUser
)
# Import production-ready authentication dependencies

# Configure logging
logger = logging.getLogger(__name__)

# Create router with enhanced configuration
router = APIRouter(
    prefix="/users",
    tags=["users"],
    responses={
        400: {
            "description": "Bad request - Invalid input data",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Invalid request data",
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Authentication required",
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        403: {
            "description": "Forbidden - Insufficient privileges",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Admin access required",
                        "error_code": "FORBIDDEN"
                    }
                }
            }
        },
        404: {
            "description": "Not found - User does not exist",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "User not found",
                        "error_code": "USER_NOT_FOUND"
                    }
                }
            }
        },
        422: {
            "description": "Unprocessable Entity - Data validation error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Validation error",
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        },
        429: {
            "description": "Too Many Requests - Rate limit exceeded",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Rate limit exceeded. Please try again later.",
                        "error_code": "RATE_LIMIT_EXCEEDED"
                    }
                }
            }
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Internal server error",
                        "error_code": "INTERNAL_ERROR"
                    }
                }
            }
        }
    }
)


# ==================================================================================
# INPUT SANITIZATION AND SECURITY UTILITIES
# ==================================================================================

def sanitize_search_input(search: Optional[str]) -> Optional[str]:
    """
    Sanitize search input to prevent injection attacks.

    Args:
        search: Raw search input from user

    Returns:
        Optional[str]: Sanitized search string or None
    """
    if not search:
        return None

    # Remove potentially dangerous characters
    import re
    sanitized = re.sub(r'[<>"\';\\]', '', search.strip())

    # Limit length to prevent DoS
    max_length = 100
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]

    return sanitized if sanitized else None


def log_user_operation(
    operation: str,
    user_email: str,
    target_user_id: Optional[int] = None,
    additional_data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log user operations for audit trail.

    Args:
        operation: Type of operation (e.g., 'profile_update', 'admin_list_users')
        user_email: Email of user performing the operation
        target_user_id: ID of user being operated on (if applicable)
        additional_data: Additional context data
    """
    audit_data = {
        "timestamp": datetime.utcnow().isoformat(),
        "operation": operation,
        "user_email": user_email,
        "target_user_id": target_user_id,
        "additional_data": additional_data or {}
    }

    logger.info(f"AUDIT: {operation} by {user_email}", extra=audit_data)


# ==================================================================================
# AUTHENTICATION DEPENDENCIES
# ==================================================================================

async def get_current_user_from_supabase(
    authorization: Optional[str] = Header(None)
) -> SupabaseUser:
    """
    Dependency to get current authenticated user from Supabase.

    Validates JWT token from Authorization header and extracts user data.

    Args:
        authorization: Authorization header containing Bearer token

    Returns:
        SupabaseUser: Authenticated user data

    Raises:
        HTTPException: If token is invalid or missing
    """
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header is required",
            headers={"WWW-Authenticate": "Bearer"}
        )

    try:
        # Extract Bearer token
        scheme, token = authorization.split(" ", 1)
        if scheme.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication scheme. Use Bearer token",
                headers={"WWW-Authenticate": "Bearer"}
            )
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authorization header format",
            headers={"WWW-Authenticate": "Bearer"}
        )

    # Get Supabase JWT secret from environment
    jwt_secret = settings.SUPABASE_JWT_SECRET
    if not jwt_secret:
        logger.error("SUPABASE_JWT_SECRET not configured")
        # For development, use placeholder user
        if settings.ENVIRONMENT == "development":
            logger.warning("Using development placeholder user - NOT FOR PRODUCTION")
            from uuid import uuid4
            return SupabaseUser(
                id=uuid4(),
                email="<EMAIL>",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                user_metadata={"full_name": "Development User"},
                app_metadata={}
            )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service configuration error"
        )

    try:
        # Decode and validate JWT token
        payload = jwt.decode(
            token,
            jwt_secret,
            algorithms=["HS256"],
            options={"verify_exp": True, "verify_aud": False}
        )

        # Extract user data from JWT payload
        user_id = payload.get("sub")
        email = payload.get("email")
        user_metadata = payload.get("user_metadata", {})
        app_metadata = payload.get("app_metadata", {})

        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload"
            )

        # Create SupabaseUser instance from JWT data
        return SupabaseUser(
            id=user_id,
            email=email,
            created_at=datetime.fromtimestamp(payload.get("iat", 0)),
            updated_at=datetime.utcnow(),
            user_metadata=user_metadata,
            app_metadata=app_metadata,
            email_confirmed_at=datetime.utcnow() if payload.get("email_confirmed_at") else None
        )

    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"}
        )
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        logger.error(f"Unexpected error during token validation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error"
        )


async def require_admin_user(
    current_user: SupabaseUser = Depends(get_current_user_from_supabase),
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
) -> SupabaseUser:
    """
    Dependency to ensure current user has admin privileges.

    Validates admin status from both Supabase metadata and local database.
    Implements comprehensive admin role checking with audit logging.

    Args:
        current_user: Current authenticated user from Supabase
        db: Database session for role verification
        user_repo: User repository for local user data

    Returns:
        SupabaseUser: Verified admin user

    Raises:
        HTTPException: If user is not an admin or verification fails
    """
    try:
        # Check admin status from Supabase app_metadata first
        supabase_admin = current_user.app_metadata.get("role") == "admin"
        supabase_is_superuser = current_user.app_metadata.get("is_superuser", False)

        # Get local user record for additional verification
        try:
            result = user_repo.get_or_create_user_from_supabase(db, current_user)
            local_user_data = result.user

            # Check if user has local admin privileges
            local_admin = getattr(local_user_data, 'is_superuser', False)

        except UserRepositoryError as e:
            logger.error(f"Failed to verify local user admin status: {str(e)}")
            # If local verification fails, rely on Supabase metadata
            local_admin = False

        # User is admin if they have admin status in either Supabase or local DB
        is_admin = supabase_admin or supabase_is_superuser or local_admin

        if not is_admin:
            # Log security event for audit trail
            logger.warning(
                f"Unauthorized admin access attempt: "
                f"user={current_user.email}, "
                f"supabase_role={current_user.app_metadata.get('role')}, "
                f"supabase_superuser={supabase_is_superuser}, "
                f"local_admin={local_admin}"
            )

            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required. Contact system administrator if you believe this is an error."
            )

        # Log successful admin verification for audit
        logger.info(
            f"Admin access granted: "
            f"user={current_user.email}, "
            f"supabase_admin={supabase_admin}, "
            f"local_admin={local_admin}"
        )

        return current_user

    except HTTPException:
        # Re-raise HTTP exceptions (like auth failures)
        raise
    except Exception as e:
        logger.error(f"Unexpected error during admin verification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Admin verification service error"
        )


# ==================================================================================
# USER PROFILE ENDPOINTS
# ==================================================================================

@router.get(
    "/me",
    response_model=UserResponse,
    summary="Get current user profile",
    description="Retrieve the current authenticated user's profile information"
)
async def get_current_user_profile(
    current_user: SupabaseUser = Depends(get_current_user_from_supabase),
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
) -> UserResponse:
    """
    Get current user's profile.

    This endpoint:
    1. Uses Supabase authentication to identify the current user
    2. Gets or creates the user profile in our local database
    3. Returns the complete user profile data

    The get_or_create_user_from_supabase method ensures that:
    - New users are automatically created on first API access
    - Existing users have their last_login timestamp updated
    - Profile data stays synchronized with Supabase
    """
    try:
        logger.info(f"Getting profile for user: {current_user.email}")

        # Use repository to get or create user from Supabase data
        result = user_repo.get_or_create_user_from_supabase(db, current_user)

        logger.info(
            f"Profile retrieved for {result.user.username} "
            f"(created: {result.created_from_supabase})"
        )

        return result.user

    except UserRepositoryError as e:
        logger.error(f"Repository error getting user profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user profile"
        )
    except Exception as e:
        logger.error(f"Unexpected error getting user profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put(
    "/me",
    response_model=UserResponse,
    summary="Update current user profile",
    description="Update the current authenticated user's profile information"
)
async def update_current_user_profile(
    profile_update: UserProfileUpdateSchema,
    current_user: SupabaseUser = Depends(get_current_user_from_supabase),
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
) -> UserResponse:
    """
    Update current user's profile.

    This endpoint:
    1. Authenticates the user via Supabase
    2. Gets the local user profile (creates if doesn't exist)
    3. Updates the profile with provided data
    4. Returns the updated profile

    Only the fields provided in the request will be updated.
    All validation is handled by the UserProfileUpdateSchema.
    """
    try:
        # Log profile update attempt for audit trail
        log_user_operation(
            operation="profile_update",
            user_email=current_user.email,
            additional_data={
                "fields_updated": [field for field in profile_update.model_fields_set] if hasattr(profile_update, 'model_fields_set') else "unknown"
            }
        )

        logger.info(f"Updating profile for user: {current_user.email}")

        # First, ensure user exists in local database
        result = user_repo.get_or_create_user_from_supabase(db, current_user)
        local_user = result.user

        # Convert UserResponse back to User model for repository method
        # In practice, you might want to modify the repository to work with UserResponse
        # or add a method to get the actual User model by ID
        user_model = user_repo.get_by_id(db, local_user.id)

        if not user_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found in local database"
            )

        # Update profile using repository
        updated_user = user_repo.update_profile(db, user_model, profile_update)

        logger.info(f"Profile updated for user: {updated_user.username}")

        # Return updated profile as UserResponse
        return UserResponse.from_user_model(updated_user)

    except UserAlreadyExistsError as e:
        logger.warning(f"Profile update conflict: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except UserRepositoryError as e:
        logger.error(f"Repository error updating profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile"
        )
    except Exception as e:
        logger.error(f"Unexpected error updating profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# ==================================================================================
# ADMIN ENDPOINTS
# ==================================================================================

@router.get(
    "/",
    response_model=UserListResponse,
    summary="List users (Admin Only)",
    description="Get paginated list of users with optional search filtering"
)
async def list_users(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    search: Optional[str] = Query(None, description="Search term for username/email/name"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    admin_user: SupabaseUser = Depends(require_admin_user),
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
) -> UserListResponse:
    """
    List users with pagination and filtering (Admin only).

    This endpoint:
    1. Verifies admin authorization
    2. Accepts pagination and filtering parameters
    3. Uses the repository's pagination method
    4. Returns structured paginated results

    Query Parameters:
    - page: Page number (1-based, default: 1)
    - page_size: Items per page (1-100, default: 20)
    - search: Search username, email, or full name (optional)
    - is_active: Filter by active status (optional)
    """
    try:
        # Sanitize search input to prevent injection attacks
        sanitized_search = sanitize_search_input(search)

        # Log admin operation for audit trail
        log_user_operation(
            operation="admin_list_users",
            user_email=admin_user.email,
            additional_data={
                "page": page,
                "page_size": page_size,
                "search_provided": search is not None,
                "search_sanitized": sanitized_search != search if search else False,
                "is_active_filter": is_active
            }
        )

        logger.info(
            f"Admin {admin_user.email} listing users "
            f"(page {page}, size {page_size}, search: '{sanitized_search}', active: {is_active})"
        )

        # Use repository to get paginated users with sanitized input
        result = user_repo.get_users_paginated(
            db=db,
            page=page,
            page_size=page_size,
            search=sanitized_search,
            is_active=is_active
        )

        logger.info(
            f"Retrieved {len(result.users)} users "
            f"(total: {result.total}, page {result.page}/{result.total_pages})"
        )

        return result

    except UserRepositoryError as e:
        logger.error(f"Repository error listing users: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve users"
        )
    except Exception as e:
        logger.error(f"Unexpected error listing users: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# ==================================================================================
# ADDITIONAL USER ENDPOINTS
# ==================================================================================

@router.get(
    "/{user_id}",
    response_model=UserResponse,
    summary="Get user by ID (Admin Only)",
    description="Retrieve specific user profile by ID"
)
async def get_user_by_id(
    user_id: int,
    admin_user: SupabaseUser = Depends(require_admin_user),
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
) -> UserResponse:
    """
    Get specific user by ID (Admin only).

    Args:
        user_id: Internal user ID to retrieve
        admin_user: Authenticated admin user
        db: Database session
        user_repo: User repository

    Returns:
        UserResponse: User profile data
    """
    try:
        # Log admin operation for audit trail
        log_user_operation(
            operation="admin_get_user_by_id",
            user_email=admin_user.email,
            target_user_id=user_id
        )

        logger.info(f"Admin {admin_user.email} requesting user {user_id}")

        user = user_repo.get_by_id(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User with ID {user_id} not found"
            )

        logger.info(f"Retrieved user {user.username} for admin {admin_user.email}")
        return UserResponse.from_user_model(user)

    except HTTPException:
        raise
    except UserRepositoryError as e:
        logger.error(f"Repository error getting user by ID: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user"
        )
    except Exception as e:
        logger.error(f"Unexpected error getting user by ID: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# ==================================================================================
# ERROR HANDLERS
# ==================================================================================

# Note: Exception handlers should be registered with the main FastAPI app,
# not with individual routers. The UserRepositoryError handling is done
# within each endpoint's try-catch blocks above.


# ==================================================================================
# HEALTH CHECK
# ==================================================================================

@router.get(
    "/health",
    summary="Health check for user service",
    description="Check if user service is operational"
)
async def user_service_health(
    db: Session = Depends(get_db),
    user_repo: UserRepository = Depends(get_user_repository)
):
    """
    Health check endpoint for user service.

    Verifies:
    - Database connection is working
    - Repository is properly initialized
    - Basic functionality is operational
    """
    try:
        # Test database connection by attempting a simple query
        result = user_repo.get_users_paginated(db, page=1, page_size=1)

        return {
            "status": "healthy",
            "service": "user_service",
            "database": "connected",
            "repository": "operational",
            "total_users": result.total
        }

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "service": "user_service",
            "error": str(e)
        }