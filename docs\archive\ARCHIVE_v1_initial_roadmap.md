# ARCHIVED: Original Project Roadmap

This file is preserved as a historical record of the initial architectural decisions, technical specifications, and project scope for the MVP phase. It includes:

- Original intent and rationale for core components
- <PERSON><PERSON> docker-compose.yml
- Initial file structure
- Environment variable list
- Database schema for admin dashboard
- Timeline and priorities

This document is not the current source of truth, but is retained for reference and project retrospectives.

---

Revised Roadmap: AI Coding Agent - Container-First Development
Simplified, essential-container approach with code-server UI

**INTEGRATION DOCUMENTS REFERENCE:**
- `RoadmapEnforcement&ValidationSystem.md` → Phases 2-3 (Backend/Agent Framework)
- `Code-ServerIDEIntegrationwithAICodingagent.md` → Phase 5 (Code-Server Integration)

Overview
The AI Coding Agent will use a container-first development approach, integrating essential services into a minimal number of containers for efficiency. The UI will be powered by code-server, which provides a VS Code-like experience in the browser. The core services include code-server (UI + Development Environment), ai-orchestrator (FastAPI backend + AI agents), postgresql (Database with pgvector), redis (Cache + sessions), and ollama (Local LLM models).

...existing content from Projectroadmap.md...
