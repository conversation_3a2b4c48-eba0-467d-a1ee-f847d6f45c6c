#!/usr/bin/env python3
"""
ChromaDB + Ollama Embedding Librarian Example

This script demonstrates how ChromaDB can serve as the "library" for your
embedding "librarian" (Ollama). It shows the complete workflow:

1. <PERSON><PERSON><PERSON> (Ollama) generates embeddings
2. Library (ChromaDB) stores and organizes them
3. <PERSON><PERSON><PERSON> can search and retrieve from the library

Usage:
    python chromadb_librarian_example.py

This runs against the ChromaDB instance in your Ollama container.
"""

import asyncio
import chromadb
import requests
from typing import List, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmbeddingLibrarian:
    """The Librarian: Manages embeddings using Ollama + ChromaDB"""

    def __init__(self, ollama_url: str = "http://localhost:11434", chroma_url: str = "http://localhost:8000"):
        self.ollama_url = ollama_url
        self.chroma_client = chromadb.HttpClient(
            host=chroma_url.replace("http://", "").split(":")[0],
            port=int(chroma_url.split(":")[-1])
        )
        self.collection = None

    async def initialize_library(self, collection_name: str = "embedding_library"):
        """Initialize the library (ChromaDB collection)"""
        try:
            # Try to get existing collection
            self.collection = self.chroma_client.get_collection(name=collection_name)
            logger.info(f"📚 Found existing library: {collection_name}")
        except:
            # Create new collection if it doesn't exist
            self.collection = self.chroma_client.create_collection(name=collection_name)
            logger.info(f"🏗️ Created new library: {collection_name}")

    async def generate_embedding(self, text: str, model: str = "bge-large-en-v1.5") -> List[float]:
        """Librarian generates an embedding for a document"""
        try:
            response = requests.post(
                f"{self.ollama_url}/api/embeddings",
                json={
                    "model": model,
                    "input": text
                },
                timeout=30
            )
            response.raise_for_status()
            return response.json()["embedding"]
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise

    async def add_document_to_library(self, document_id: str, content: str, metadata: Dict[str, Any] = None):
        """Add a document to the library"""
        logger.info(f"📖 Adding document '{document_id}' to library")

        # Generate embedding for the document
        embedding = await self.generate_embedding(content)

        # Store in ChromaDB
        self.collection.add(
            ids=[document_id],
            embeddings=[embedding],
            documents=[content],
            metadatas=[metadata or {}]
        )

        logger.info(f"✅ Document '{document_id}' stored in library")

    async def search_library(self, query: str, n_results: int = 5) -> Dict[str, Any]:
        """Search the library for relevant documents"""
        logger.info(f"🔍 Searching library for: '{query}'")

        # Generate embedding for the query
        query_embedding = await self.generate_embedding(query)

        # Search ChromaDB
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=n_results,
            include=['documents', 'metadatas', 'distances']
        )

        return results

    async def get_library_stats(self) -> Dict[str, Any]:
        """Get statistics about the library"""
        count = self.collection.count()
        return {
            "total_documents": count,
            "collection_name": self.collection.name,
            "library_status": "open" if count > 0 else "empty"
        }


async def demonstrate_librarian_workflow():
    """Demonstrate the complete librarian/library workflow"""

    print("🎭 Embedding Librarian & Library Demo")
    print("=" * 50)

    # Initialize the librarian
    librarian = EmbeddingLibrarian()

    try:
        # Step 1: Initialize the library
        print("\n📚 Step 1: Opening the Library")
        await librarian.initialize_library("demo_library")

        # Step 2: Add some documents to the library
        print("\n📖 Step 2: Adding Books to the Library")

        documents = [
            {
                "id": "python_basics",
                "content": "Python is a high-level programming language known for its simplicity and readability. It supports multiple programming paradigms including procedural, object-oriented, and functional programming.",
                "metadata": {"category": "programming", "language": "python", "difficulty": "beginner"}
            },
            {
                "id": "fastapi_guide",
                "content": "FastAPI is a modern, fast web framework for building APIs with Python 3.7+ based on standard Python type hints. It automatically generates OpenAPI and JSON Schema documentation.",
                "metadata": {"category": "web_framework", "language": "python", "difficulty": "intermediate"}
            },
            {
                "id": "docker_concepts",
                "content": "Docker is a platform for developing, shipping, and running applications inside containers. Containers are lightweight, portable, and self-sufficient units that can run anywhere.",
                "metadata": {"category": "devops", "tool": "docker", "difficulty": "intermediate"}
            },
            {
                "id": "machine_learning_intro",
                "content": "Machine learning is a subset of artificial intelligence that enables computers to learn from data without being explicitly programmed. It uses algorithms to identify patterns in data.",
                "metadata": {"category": "ai_ml", "topic": "machine_learning", "difficulty": "advanced"}
            }
        ]

        for doc in documents:
            await librarian.add_document_to_library(
                doc["id"],
                doc["content"],
                doc["metadata"]
            )

        # Step 3: Search the library
        print("\n🔍 Step 3: Searching the Library")

        search_queries = [
            "How do I create web APIs with Python?",
            "What is containerization?",
            "Tell me about AI and learning algorithms"
        ]

        for query in search_queries:
            print(f"\nQuery: '{query}'")
            results = await librarian.search_library(query, n_results=2)

            for i, (doc_id, content, metadata, distance) in enumerate(zip(
                results['ids'][0],
                results['documents'][0],
                results['metadatas'][0],
                results['distances'][0]
            )):
                print(f"  {i+1}. {doc_id} (similarity: {1-distance:.3f})")
                print(f"     Category: {metadata.get('category', 'unknown')}")
                print(f"     Content: {content[:100]}...")

        # Step 4: Library statistics
        print("\n📊 Step 4: Library Statistics")
        stats = await librarian.get_library_stats()
        print(f"Library Status: {stats}")

        print("\n🎉 Librarian Demo Complete!")
        print("Your embedding librarian can now:")
        print("  • 📖 Store documents with embeddings in ChromaDB")
        print("  • 🔍 Find relevant documents using semantic search")
        print("  • 📊 Track library statistics and organization")
        print("  • 🏗️ Scale to handle large document collections")

    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"\n❌ Demo failed: {e}")
        print("Make sure both Ollama and ChromaDB are running:")
        print("  - Ollama: http://localhost:11434")
        print("  - ChromaDB: http://localhost:8000")


if __name__ == "__main__":
    asyncio.run(demonstrate_librarian_workflow())
