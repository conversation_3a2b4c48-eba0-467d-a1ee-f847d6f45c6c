templates:
  webapp:
    name: "Full-Stack Web Application"
    type: "webapp"
    version: "1.0.0"
    description: "Complete web application with frontend and backend"
    required_placeholders:
      - "PROJECT_NAME"
      - "DATABASE_URL"
      - "API_PORT"
    optional_placeholders:
      - "REDIS_URL"
      - "JWT_SECRET"
      - "CORS_ORIGINS"
    default_port: 3000
    health_endpoint: "/health"
    startup_timeout: 60
    dependencies:
      - "react"
      - "fastapi"
      - "postgresql"
    tags:
      - "fullstack"
      - "web"
      - "production-ready"

  react-frontend:
    name: "React Frontend Application"
    type: "react-frontend"
    version: "1.2.0"
    description: "Modern React frontend with TypeScript"
    required_placeholders:
      - "PROJECT_NAME"
      - "API_BASE_URL"
    optional_placeholders:
      - "PUBLIC_URL"
      - "REACT_APP_ENV"
    default_port: 3000
    health_endpoint: "/"
    startup_timeout: 30
    dependencies:
      - "react"
      - "typescript"
      - "tailwindcss"
    tags:
      - "frontend"
      - "react"
      - "typescript"

  microservice:
    name: "Python Microservice"
    type: "microservice"
    version: "1.1.0"
    description: "FastAPI-based microservice with Docker support"
    required_placeholders:
      - "SERVICE_NAME"
      - "DATABASE_URL"
      - "PORT"
    optional_placeholders:
      - "REDIS_URL"
      - "LOG_LEVEL"
    default_port: 8000
    health_endpoint: "/health"
    startup_timeout: 45
    dependencies:
      - "fastapi"
      - "uvicorn"
      - "postgresql"
      - "redis"
    tags:
      - "microservice"
      - "api"
      - "scalable"

  nextjs-app:
    name: "Next.js Application"
    type: "nextjs-app"
    version: "2.0.0"
    description: "Full-stack Next.js application with App Router"
    required_placeholders:
      - "PROJECT_NAME"
      - "DATABASE_URL"
    optional_placeholders:
      - "NEXTAUTH_SECRET"
      - "NEXTAUTH_URL"
      - "NODE_ENV"
    default_port: 3000
    health_endpoint: "/api/health"
    startup_timeout: 40
    dependencies:
      - "next"
      - "react"
      - "prisma"
      - "nextauth"
    tags:
      - "nextjs"
      - "fullstack"
      - "ssr"

  django-api:
    name: "Django REST API"
    type: "django-api"
    version: "1.0.0"
    description: "Django REST Framework API with authentication"
    required_placeholders:
      - "PROJECT_NAME"
      - "DATABASE_URL"
      - "SECRET_KEY"
    optional_placeholders:
      - "REDIS_URL"
      - "EMAIL_HOST"
      - "MEDIA_ROOT"
    default_port: 8000
    health_endpoint: "/health/"
    startup_timeout: 50
    dependencies:
      - "django"
      - "djangorestframework"
      - "postgresql"
      - "celery"
    tags:
      - "django"
      - "api"
      - "rest"

  mobile-app:
    name: "React Native Mobile App"
    type: "mobile-app"
    version: "1.0.0"
    description: "Cross-platform mobile application with React Native"
    required_placeholders:
      - "APP_NAME"
      - "BUNDLE_ID"
      - "API_BASE_URL"
    optional_placeholders:
      - "ENVIRONMENT"
      - "SENTRY_DSN"
    default_port: 8081
    health_endpoint: "/health"
    startup_timeout: 60
    dependencies:
      - "react-native"
      - "expo"
      - "react-navigation"
    tags:
      - "mobile"
      - "react-native"
      - "cross-platform"
