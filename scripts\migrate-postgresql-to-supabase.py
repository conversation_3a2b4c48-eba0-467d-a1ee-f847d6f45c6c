#!/usr/bin/env python3
"""
PostgreSQL to Supabase Data Migration Script.

This script migrates existing PostgreSQL data to Supabase while maintaining
data integrity, establishing proper user relationships, and setting up
Row Level Security permissions.

Features:
- Safe migration with rollback capability
- Data validation and integrity checks
- User mapping and permission setup
- Progress tracking and logging
- Dry-run mode for testing

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import asyncpg
import json
import os
import sys
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
import argparse
import uuid

# Add the source directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    from supabase import create_client, Client
    from services.supabase_service import SupabaseService, SupabaseConfig
except ImportError:
    print("Error: Supabase dependencies not found. Please install with: pip install supabase")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class MigrationConfig:
    """Configuration for migration process."""
    source_db_url: str
    target_db_url: str
    supabase_url: str
    supabase_service_key: str
    dry_run: bool = False
    batch_size: int = 100
    enable_rollback: bool = True
    validate_data: bool = True
    default_user_email: str = "<EMAIL>"
    default_user_password: str = "admin_password_123"


@dataclass
class MigrationStats:
    """Migration statistics."""
    tables_processed: int = 0
    records_migrated: int = 0
    errors_encountered: int = 0
    warnings_issued: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

    def duration_seconds(self) -> float:
        """Calculate migration duration in seconds."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0


@dataclass
class TableMapping:
    """Mapping configuration for table migration."""
    source_table: str
    target_table: str
    column_mappings: Dict[str, str] = field(default_factory=dict)
    required_columns: List[str] = field(default_factory=list)
    default_values: Dict[str, Any] = field(default_factory=dict)
    owner_id_column: Optional[str] = None  # For RLS
    transform_functions: Dict[str, str] = field(default_factory=dict)


class MigrationError(Exception):
    """Migration-specific errors."""
    pass


class DataMigrator:
    """
    Main migration class handling PostgreSQL to Supabase data migration.
    """

    def __init__(self, config: MigrationConfig):
        """Initialize migrator with configuration."""
        self.config = config
        self.stats = MigrationStats()
        self.source_pool: Optional[asyncpg.Pool] = None
        self.target_pool: Optional[asyncpg.Pool] = None
        self.supabase_client: Optional[Client] = None
        self.default_user_id: Optional[str] = None

        # Define table mappings
        self.table_mappings = self._define_table_mappings()

        logger.info(f"Migration initialized - Dry run: {config.dry_run}")

    def _define_table_mappings(self) -> List[TableMapping]:
        """Define how tables should be migrated."""
        return [
            # User data migration (if exists in legacy system)
            TableMapping(
                source_table="users",
                target_table="user_profiles",
                column_mappings={
                    "id": "id",
                    "email": "email",  # Will be used to create auth.users
                    "username": "username",
                    "full_name": "full_name",
                    "created_at": "created_at",
                    "updated_at": "updated_at"
                },
                required_columns=["id", "email"],
                default_values={
                    "role": "user",
                    "preferences": {}
                }
            ),

            # Projects migration
            TableMapping(
                source_table="projects",
                target_table="projects",
                column_mappings={
                    "id": "id",
                    "name": "name",
                    "description": "description",
                    "owner_id": "owner_id",
                    "created_at": "created_at",
                    "updated_at": "updated_at"
                },
                required_columns=["id", "name"],
                default_values={
                    "status": "active",
                    "settings": {}
                },
                owner_id_column="owner_id"
            ),

            # Documents migration
            TableMapping(
                source_table="documents",
                target_table="documents",
                column_mappings={
                    "id": "id",
                    "project_id": "project_id",
                    "name": "name",
                    "content": "content",
                    "file_path": "file_path",
                    "file_type": "file_type",
                    "file_size": "file_size",
                    "owner_id": "owner_id",
                    "created_at": "created_at",
                    "updated_at": "updated_at"
                },
                required_columns=["id", "project_id", "name"],
                default_values={
                    "metadata": {}
                },
                owner_id_column="owner_id"
            ),

            # Agent executions migration (if exists)
            TableMapping(
                source_table="agent_executions",
                target_table="agent_executions",
                column_mappings={
                    "id": "id",
                    "user_id": "user_id",
                    "project_id": "project_id",
                    "agent_name": "agent_name",
                    "task_description": "task_description",
                    "status": "status",
                    "input_data": "input_data",
                    "output_data": "output_data",
                    "error_message": "error_message",
                    "execution_time_ms": "execution_time_ms",
                    "created_at": "created_at",
                    "updated_at": "updated_at"
                },
                required_columns=["id", "user_id", "agent_name", "task_description"],
                default_values={
                    "status": "completed",
                    "input_data": {},
                    "output_data": {}
                },
                owner_id_column="user_id"
            )
        ]

    async def initialize_connections(self) -> None:
        """Initialize database connections."""
        try:
            logger.info("Initializing database connections...")

            # Source database connection
            self.source_pool = await asyncpg.create_pool(
                self.config.source_db_url,
                min_size=2,
                max_size=10,
                timeout=30
            )

            # Target database connection
            self.target_pool = await asyncpg.create_pool(
                self.config.target_db_url,
                min_size=2,
                max_size=10,
                timeout=30
            )

            # Supabase client for auth operations
            self.supabase_client = create_client(
                self.config.supabase_url,
                self.config.supabase_service_key
            )

            logger.info("Database connections established")

        except Exception as e:
            logger.error(f"Failed to initialize connections: {str(e)}")
            raise MigrationError(f"Connection initialization failed: {str(e)}")

    async def run_migration(self) -> MigrationStats:
        """Run the complete migration process."""
        self.stats.start_time = datetime.utcnow()

        try:
            await self.initialize_connections()

            # Step 1: Create default admin user if needed
            await self._ensure_default_user()

            # Step 2: Validate source data
            if self.config.validate_data:
                await self._validate_source_data()

            # Step 3: Migrate tables in order
            for table_mapping in self.table_mappings:
                await self._migrate_table(table_mapping)
                self.stats.tables_processed += 1

            # Step 4: Validate migrated data
            if self.config.validate_data:
                await self._validate_migrated_data()

            # Step 5: Set up permissions and RLS
            await self._setup_permissions()

            logger.info("Migration completed successfully")

        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            self.stats.errors_encountered += 1

            if self.config.enable_rollback and not self.config.dry_run:
                await self._rollback_migration()

            raise

        finally:
            self.stats.end_time = datetime.utcnow()
            await self._cleanup_connections()
            await self._generate_migration_report()

        return self.stats

    async def _ensure_default_user(self) -> None:
        """Ensure default admin user exists."""
        try:
            # Check if we need to create a default user
            async with self.source_pool.acquire() as conn:
                user_count = await conn.fetchval("SELECT COUNT(*) FROM users") if await self._table_exists(conn, "users") else 0

            if user_count == 0:
                logger.info("No users found in source database, creating default admin user")

                # Create auth user in Supabase
                try:
                    auth_response = self.supabase_client.auth.admin.create_user({
                        "email": self.config.default_user_email,
                        "password": self.config.default_user_password,
                        "email_confirm": True,
                        "user_metadata": {
                            "role": "admin",
                            "created_by": "migration_script"
                        }
                    })

                    if auth_response.user:
                        self.default_user_id = auth_response.user.id
                        logger.info(f"Default admin user created with ID: {self.default_user_id}")

                except Exception as e:
                    logger.warning(f"Failed to create default user in auth: {str(e)}")
                    # Generate a UUID for default user
                    self.default_user_id = str(uuid.uuid4())
                    logger.info(f"Using generated UUID for default user: {self.default_user_id}")

        except Exception as e:
            logger.error(f"Failed to ensure default user: {str(e)}")
            raise MigrationError(f"Default user creation failed: {str(e)}")

    async def _validate_source_data(self) -> None:
        """Validate source database data."""
        logger.info("Validating source data...")

        try:
            async with self.source_pool.acquire() as conn:
                for table_mapping in self.table_mappings:
                    if await self._table_exists(conn, table_mapping.source_table):
                        # Check required columns exist
                        columns = await self._get_table_columns(conn, table_mapping.source_table)
                        missing_columns = set(table_mapping.required_columns) - set(columns)

                        if missing_columns:
                            raise MigrationError(f"Missing required columns in {table_mapping.source_table}: {missing_columns}")

                        # Count records
                        record_count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_mapping.source_table}")
                        logger.info(f"Table {table_mapping.source_table}: {record_count} records")
                    else:
                        logger.warning(f"Source table {table_mapping.source_table} does not exist")

            logger.info("Source data validation completed")

        except Exception as e:
            logger.error(f"Source data validation failed: {str(e)}")
            raise MigrationError(f"Source validation failed: {str(e)}")

    async def _migrate_table(self, table_mapping: TableMapping) -> None:
        """Migrate a single table."""
        logger.info(f"Migrating table: {table_mapping.source_table} -> {table_mapping.target_table}")

        try:
            # Check if source table exists
            async with self.source_pool.acquire() as source_conn:
                if not await self._table_exists(source_conn, table_mapping.source_table):
                    logger.warning(f"Source table {table_mapping.source_table} does not exist, skipping")
                    return

                # Get total record count
                total_records = await source_conn.fetchval(f"SELECT COUNT(*) FROM {table_mapping.source_table}")
                logger.info(f"Found {total_records} records to migrate")

                if total_records == 0:
                    logger.info("No records to migrate")
                    return

                # Migrate in batches
                offset = 0
                migrated_count = 0

                while offset < total_records:
                    # Fetch batch from source
                    query = f"SELECT * FROM {table_mapping.source_table} ORDER BY id LIMIT {self.config.batch_size} OFFSET {offset}"
                    source_records = await source_conn.fetch(query)

                    if not source_records:
                        break

                    # Transform and insert into target
                    transformed_records = []
                    for record in source_records:
                        transformed_record = await self._transform_record(dict(record), table_mapping)
                        if transformed_record:
                            transformed_records.append(transformed_record)

                    if transformed_records and not self.config.dry_run:
                        await self._insert_batch(table_mapping.target_table, transformed_records)

                    migrated_count += len(transformed_records)
                    offset += self.config.batch_size

                    logger.info(f"Migrated {migrated_count}/{total_records} records from {table_mapping.source_table}")

                self.stats.records_migrated += migrated_count
                logger.info(f"Completed migration of {table_mapping.source_table}: {migrated_count} records")

        except Exception as e:
            logger.error(f"Failed to migrate table {table_mapping.source_table}: {str(e)}")
            self.stats.errors_encountered += 1
            raise MigrationError(f"Table migration failed: {str(e)}")

    async def _transform_record(self, record: Dict[str, Any], table_mapping: TableMapping) -> Optional[Dict[str, Any]]:
        """Transform a single record according to mapping rules."""
        try:
            transformed = {}

            # Apply column mappings
            for source_col, target_col in table_mapping.column_mappings.items():
                if source_col in record:
                    value = record[source_col]

                    # Apply transformation functions if defined
                    if source_col in table_mapping.transform_functions:
                        transform_func = table_mapping.transform_functions[source_col]
                        # This would be implemented based on specific transformation needs
                        pass

                    transformed[target_col] = value

            # Add default values
            for col, default_value in table_mapping.default_values.items():
                if col not in transformed:
                    transformed[col] = default_value

            # Handle owner_id mapping for RLS
            if table_mapping.owner_id_column and table_mapping.owner_id_column not in transformed:
                # If no owner_id specified, use default user
                if self.default_user_id:
                    transformed[table_mapping.owner_id_column] = self.default_user_id
                else:
                    logger.warning(f"No owner_id available for record in {table_mapping.target_table}")
                    return None

            # Ensure required columns are present
            for required_col in table_mapping.required_columns:
                target_col = table_mapping.column_mappings.get(required_col, required_col)
                if target_col not in transformed or transformed[target_col] is None:
                    logger.warning(f"Missing required column {target_col} in record")
                    return None

            return transformed

        except Exception as e:
            logger.error(f"Failed to transform record: {str(e)}")
            self.stats.errors_encountered += 1
            return None

    async def _insert_batch(self, table_name: str, records: List[Dict[str, Any]]) -> None:
        """Insert a batch of records into target table."""
        if not records:
            return

        try:
            async with self.target_pool.acquire() as conn:
                # Build insert query
                columns = list(records[0].keys())
                placeholders = ', '.join([f'${i+1}' for i in range(len(columns))])
                column_names = ', '.join(columns)

                query = f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"

                # Execute batch insert
                for record in records:
                    values = [record[col] for col in columns]
                    await conn.execute(query, *values)

                logger.debug(f"Inserted {len(records)} records into {table_name}")

        except Exception as e:
            logger.error(f"Failed to insert batch into {table_name}: {str(e)}")
            raise MigrationError(f"Batch insert failed: {str(e)}")

    async def _validate_migrated_data(self) -> None:
        """Validate migrated data integrity."""
        logger.info("Validating migrated data...")

        try:
            async with self.source_pool.acquire() as source_conn, \
                     self.target_pool.acquire() as target_conn:

                for table_mapping in self.table_mappings:
                    if await self._table_exists(source_conn, table_mapping.source_table) and \
                       await self._table_exists(target_conn, table_mapping.target_table):

                        # Compare record counts
                        source_count = await source_conn.fetchval(f"SELECT COUNT(*) FROM {table_mapping.source_table}")
                        target_count = await target_conn.fetchval(f"SELECT COUNT(*) FROM {table_mapping.target_table}")

                        if source_count != target_count:
                            logger.warning(f"Record count mismatch in {table_mapping.target_table}: source={source_count}, target={target_count}")
                            self.stats.warnings_issued += 1
                        else:
                            logger.info(f"Data validation passed for {table_mapping.target_table}: {target_count} records")

            logger.info("Data validation completed")

        except Exception as e:
            logger.error(f"Data validation failed: {str(e)}")
            self.stats.errors_encountered += 1

    async def _setup_permissions(self) -> None:
        """Set up Row Level Security permissions."""
        logger.info("Setting up RLS permissions...")

        if self.config.dry_run:
            logger.info("Dry run mode: skipping RLS setup")
            return

        try:
            async with self.target_pool.acquire() as conn:
                # Enable RLS on tables (should already be enabled from schema migration)
                rls_tables = ['user_profiles', 'projects', 'documents', 'document_sections', 'agent_executions']

                for table in rls_tables:
                    if await self._table_exists(conn, table):
                        await conn.execute(f"ALTER TABLE {table} ENABLE ROW LEVEL SECURITY")
                        logger.debug(f"RLS enabled for {table}")

            logger.info("RLS permissions setup completed")

        except Exception as e:
            logger.error(f"Failed to setup permissions: {str(e)}")
            self.stats.errors_encountered += 1

    async def _rollback_migration(self) -> None:
        """Rollback migration by truncating target tables."""
        logger.warning("Starting migration rollback...")

        try:
            async with self.target_pool.acquire() as conn:
                # Truncate tables in reverse order to handle foreign key constraints
                for table_mapping in reversed(self.table_mappings):
                    if await self._table_exists(conn, table_mapping.target_table):
                        await conn.execute(f"TRUNCATE TABLE {table_mapping.target_table} CASCADE")
                        logger.info(f"Truncated table: {table_mapping.target_table}")

            logger.info("Migration rollback completed")

        except Exception as e:
            logger.error(f"Rollback failed: {str(e)}")

    async def _table_exists(self, conn: asyncpg.Connection, table_name: str) -> bool:
        """Check if table exists."""
        result = await conn.fetchval(
            "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
            table_name
        )
        return result

    async def _get_table_columns(self, conn: asyncpg.Connection, table_name: str) -> List[str]:
        """Get list of columns for a table."""
        rows = await conn.fetch(
            "SELECT column_name FROM information_schema.columns WHERE table_name = $1",
            table_name
        )
        return [row['column_name'] for row in rows]

    async def _cleanup_connections(self) -> None:
        """Clean up database connections."""
        if self.source_pool:
            await self.source_pool.close()
        if self.target_pool:
            await self.target_pool.close()

        logger.info("Database connections closed")

    async def _generate_migration_report(self) -> None:
        """Generate migration report."""
        report = {
            "migration_summary": {
                "start_time": self.stats.start_time.isoformat() if self.stats.start_time else None,
                "end_time": self.stats.end_time.isoformat() if self.stats.end_time else None,
                "duration_seconds": self.stats.duration_seconds(),
                "tables_processed": self.stats.tables_processed,
                "records_migrated": self.stats.records_migrated,
                "errors_encountered": self.stats.errors_encountered,
                "warnings_issued": self.stats.warnings_issued,
                "dry_run": self.config.dry_run
            },
            "configuration": {
                "batch_size": self.config.batch_size,
                "validation_enabled": self.config.validate_data,
                "rollback_enabled": self.config.enable_rollback
            }
        }

        report_file = f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        logger.info(f"Migration report saved to: {report_file}")
        logger.info(f"Migration completed in {self.stats.duration_seconds():.2f} seconds")
        logger.info(f"Total records migrated: {self.stats.records_migrated}")


async def main():
    """Main migration entry point."""
    parser = argparse.ArgumentParser(description="PostgreSQL to Supabase Migration Tool")
    parser.add_argument("--source-db", required=True, help="Source PostgreSQL database URL")
    parser.add_argument("--target-db", required=True, help="Target Supabase database URL")
    parser.add_argument("--supabase-url", required=True, help="Supabase project URL")
    parser.add_argument("--supabase-key", required=True, help="Supabase service role key")
    parser.add_argument("--dry-run", action="store_true", help="Run in dry-run mode (no actual migration)")
    parser.add_argument("--batch-size", type=int, default=100, help="Batch size for migration")
    parser.add_argument("--no-validation", action="store_true", help="Skip data validation")
    parser.add_argument("--no-rollback", action="store_true", help="Disable rollback on error")
    parser.add_argument("--default-user-email", default="<EMAIL>", help="Default admin user email")
    parser.add_argument("--default-user-password", default="admin_password_123", help="Default admin user password")

    args = parser.parse_args()

    # Create migration configuration
    config = MigrationConfig(
        source_db_url=args.source_db,
        target_db_url=args.target_db,
        supabase_url=args.supabase_url,
        supabase_service_key=args.supabase_key,
        dry_run=args.dry_run,
        batch_size=args.batch_size,
        validate_data=not args.no_validation,
        enable_rollback=not args.no_rollback,
        default_user_email=args.default_user_email,
        default_user_password=args.default_user_password
    )

    # Run migration
    migrator = DataMigrator(config)

    try:
        stats = await migrator.run_migration()

        if stats.errors_encountered == 0:
            logger.info(" Migration completed successfully!")
            return 0
        else:
            logger.error(f" Migration completed with {stats.errors_encountered} errors")
            return 1

    except Exception as e:
        logger.error(f" Migration failed: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())