import pytest
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>

from src.router.project_router import approve_changes
from src.schemas.project_schemas import ApproveChangesRequest
from src.approval.approval_models import ApprovalRequest, ApprovalStatus, ApprovalType

@pytest.mark.asyncio
async def test_approve_changes_success():
    # Arrange
    project_id = uuid4()
    token = "test_token"
    user_id = "test_user"
    commit_hash = "test_commit"

    request = ApproveChangesRequest(approval_token=token)

    current_user = MagicMock()
    current_user.id = user_id

    approval_manager = AsyncMock()
    approval_manager.get_approval_request.return_value = ApprovalRequest(
        id=token,
        user_id=user_id,
        approval_type=ApprovalType.DEPLOYMENT,
        title="Approve Preview Deployment",
        description="A new preview for project test_project is ready for your review.",
        item_type="project",
        item_id=str(project_id),
        preview_data={"commit_hash": commit_hash},
    )
    approval_manager.respond_to_approval.return_value = True

    project_repo = AsyncMock()
    project_repo.get_project_by_id.return_value = MagicMock(project_hostname="test.com")

    deployment_agent = AsyncMock()
    deployment_agent.deploy_main_branch.return_value = {"success": True, "deployment_id": "dep_123"}

    # Act
    response = await approve_changes(
        project_id=project_id,
        request=request,
        current_user=current_user,
        approval_manager=approval_manager,
        project_repo=project_repo,
        deployment_agent=deployment_agent,
    )

    # Assert
    assert response["status"] == "success"
    assert response["message"] == "Approval received. Final deployment started."
    approval_manager.get_approval_request.assert_called_once_with(token)
    approval_manager.respond_to_approval.assert_called_once()
    deployment_agent.deploy_main_branch.assert_called_once()

@pytest.mark.asyncio
async def test_approve_changes_invalid_token():
    # Arrange
    project_id = uuid4()
    token = "invalid_token"
    request = ApproveChangesRequest(approval_token=token)

    current_user = MagicMock()
    current_user.id = "test_user"

    approval_manager = AsyncMock()
    approval_manager.get_approval_request.return_value = None

    project_repo = AsyncMock()
    project_repo.get_project_by_id.return_value = MagicMock()

    deployment_agent = AsyncMock()

    # Act & Assert
    with pytest.raises(HTTPException) as exc_info:
        await approve_changes(
            project_id=project_id,
            request=request,
            current_user=current_user,
            approval_manager=approval_manager,
            project_repo=project_repo,
            deployment_agent=deployment_agent,
        )
    assert exc_info.value.status_code == 400
    assert "Invalid or expired approval token" in exc_info.value.detail
