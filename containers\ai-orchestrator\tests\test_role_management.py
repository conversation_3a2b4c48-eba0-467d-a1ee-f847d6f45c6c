# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: Unit tests for role management API endpoints

import pytest
import tempfile
import os
from unittest.mock import patch
from fastapi.testclient import TestClient

from src.main import app
from src.models.role_config import (
    RoleConfiguration,
    RoleConfigurationList,
    LLMProvider
)
from src.router.role_management import RoleConfigurationManager


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def sample_role_config():
    """Create sample role configuration."""
    return RoleConfiguration(
        provider=LLMProvider.OPENROUTER,
        available_models=["claude-3-sonnet", "gpt-4"],
        selected_model="claude-3-sonnet",
        api_key="test_api_key",
        cost_limit=100.0,
        max_tokens=4096,
        temperature=0.7,
        enabled=True
    )


@pytest.fixture
def sample_config_data():
    """Create sample configuration data."""
    return {
        "roles": {
            "test_role": {
                "provider": "openrouter",
                "available_models": ["claude-3-sonnet", "gpt-4"],
                "selected_model": "claude-3-sonnet",
                "api_key": "test_key",
                "cost_limit": 100.0,
                "max_tokens": 4096,
                "temperature": 0.7,
                "enabled": True,
                "created_at": "2024-01-01T00:00:00.000000",
                "updated_at": "2024-01-01T00:00:00.000000"
            }
        },
        "version": "1.0",
        "updated_at": "2024-01-01T00:00:00.000000"
    }


class TestRoleConfigurationManager:
    """Test the RoleConfigurationManager class."""

    @pytest.mark.asyncio
    async def test_save_and_load_configuration(self, sample_config_data):
        """Test saving and loading configuration."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as tmp:
            config_path = tmp.name

        try:
            # Patch the CONFIG_FILE_PATH
            with patch('src.router.role_management.CONFIG_FILE_PATH', config_path):
                config = RoleConfigurationList(**sample_config_data)

                # Test save
                await RoleConfigurationManager.save_configuration(config)

                # Test load
                loaded_config = await RoleConfigurationManager.load_configuration()

                assert loaded_config.roles["test_role"].provider == LLMProvider.OPENROUTER
                assert loaded_config.roles["test_role"].selected_model == "claude-3-sonnet"
                assert len(loaded_config.roles) == 1

        finally:
            # Cleanup
            if os.path.exists(config_path):
                os.unlink(config_path)

    @pytest.mark.asyncio
    async def test_load_missing_config_creates_default(self):
        """Test that loading missing config creates default configuration."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            config_path = os.path.join(tmp_dir, 'missing_config.json')

            with patch('src.router.role_management.CONFIG_FILE_PATH', config_path):
                config = await RoleConfigurationManager.load_configuration()

                # Should create default config with expected roles
                assert "architect" in config.roles
                assert "backend" in config.roles
                assert "frontend" in config.roles
                assert "issue_fix" in config.roles
                assert "shell" in config.roles
                assert len(config.roles) == 5


class TestRoleManagementEndpoints:
    """Test the role management API endpoints."""

    def test_get_all_roles_unauthorized(self, client):
        """Test getting all roles without authentication."""
        response = client.get("/api/roles")
        # Should require authentication
        assert response.status_code in [401, 403]

    @patch('src.router.role_management.get_current_user')
    @patch('src.router.role_management.RoleConfigurationManager.load_configuration')
    def test_get_all_roles_success(self, mock_load, mock_auth, client, sample_config_data):
        """Test successful retrieval of all roles."""
        # Mock authentication
        mock_auth.return_value = {"user_id": "test_user"}

        # Mock configuration loading
        mock_load.return_value = RoleConfigurationList(**sample_config_data)

        response = client.get("/api/roles")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["total_roles"] == 1
        assert "test_role" in data["roles"]

    @patch('src.router.role_management.get_current_user')
    @patch('src.router.role_management.RoleConfigurationManager.load_configuration')
    def test_get_specific_role_success(self, mock_load, mock_auth, client, sample_config_data):
        """Test successful retrieval of specific role."""
        # Mock authentication
        mock_auth.return_value = {"user_id": "test_user"}

        # Mock configuration loading
        mock_load.return_value = RoleConfigurationList(**sample_config_data)

        response = client.get("/api/roles/test_role")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["role_name"] == "test_role"
        assert data["configuration"]["provider"] == "openrouter"

    @patch('src.router.role_management.get_current_user')
    @patch('src.router.role_management.RoleConfigurationManager.load_configuration')
    def test_get_nonexistent_role(self, mock_load, mock_auth, client, sample_config_data):
        """Test retrieval of non-existent role."""
        # Mock authentication
        mock_auth.return_value = {"user_id": "test_user"}

        # Mock configuration loading
        mock_load.return_value = RoleConfigurationList(**sample_config_data)

        response = client.get("/api/roles/nonexistent_role")

        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()

    @patch('src.router.role_management.get_current_user')
    @patch('src.router.role_management.RoleConfigurationManager.load_configuration')
    @patch('src.router.role_management.RoleConfigurationManager.save_configuration')
    def test_create_role_success(self, mock_save, mock_load, mock_auth, client, sample_config_data):
        """Test successful role creation."""
        # Mock authentication
        mock_auth.return_value = {"user_id": "test_user"}

        # Mock configuration loading (empty config)
        empty_config = RoleConfigurationList(roles={}, version="1.0")
        mock_load.return_value = empty_config

        # Mock save operation
        mock_save.return_value = None

        new_role_data = {
            "provider": "openai",
            "available_models": ["gpt-4", "gpt-3.5-turbo"],
            "selected_model": "gpt-4",
            "api_key": "new_test_key",
            "cost_limit": 50.0,
            "max_tokens": 2048,
            "temperature": 0.5,
            "enabled": True
        }

        response = client.post("/api/roles/new_role", json=new_role_data)

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["role_name"] == "new_role"

    @patch('src.router.role_management.get_current_user')
    def test_create_role_invalid_name(self, mock_auth, client):
        """Test role creation with invalid name."""
        # Mock authentication
        mock_auth.return_value = {"user_id": "test_user"}

        new_role_data = {
            "provider": "openai",
            "available_models": ["gpt-4"],
            "selected_model": "gpt-4",
            "api_key": "test_key"
        }

        response = client.post("/api/roles/invalid-role-name!", json=new_role_data)

        assert response.status_code == 400
        data = response.json()
        assert "alphanumeric" in data["detail"].lower()

    @patch('src.router.role_management.get_current_user')
    def test_get_provider_models(self, mock_auth, client):
        """Test getting available models by provider."""
        # Mock authentication
        mock_auth.return_value = {"user_id": "test_user"}

        response = client.get("/api/roles/providers/models")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0

        # Check that each provider has expected structure
        for provider_info in data:
            assert "provider" in provider_info
            assert "models" in provider_info
            assert "status" in provider_info
            assert isinstance(provider_info["models"], list)


class TestRoleConfigurationModel:
    """Test the RoleConfiguration Pydantic model."""

    def test_valid_role_configuration(self):
        """Test creating valid role configuration."""
        config = RoleConfiguration(
            provider=LLMProvider.OPENAI,
            available_models=["gpt-4", "gpt-3.5-turbo"],
            selected_model="gpt-4",
            api_key="test_key",
            cost_limit=100.0
        )

        assert config.provider == LLMProvider.OPENAI
        assert config.selected_model == "gpt-4"
        assert config.cost_limit == 100.0

    def test_selected_model_validation(self):
        """Test that selected_model must be in available_models."""
        with pytest.raises(ValueError, match="must be in available_models"):
            RoleConfiguration(
                provider=LLMProvider.OPENAI,
                available_models=["gpt-4", "gpt-3.5-turbo"],
                selected_model="claude-3-sonnet",  # Not in available_models
                api_key="test_key"
            )

    def test_cloud_provider_requires_api_key(self):
        """Test that cloud providers require API keys."""
        with pytest.raises(ValueError, match="api_key is required"):
            RoleConfiguration(
                provider=LLMProvider.OPENAI,
                available_models=["gpt-4"],
                selected_model="gpt-4",
                api_key=None  # Missing API key for cloud provider
            )

    def test_ollama_provider_no_api_key_required(self):
        """Test that Ollama provider doesn't require API key."""
        config = RoleConfiguration(
            provider=LLMProvider.OLLAMA,
            available_models=["llama2:7b"],
            selected_model="llama2:7b",
            api_key=None  # No API key needed for Ollama
        )

        assert config.provider == LLMProvider.OLLAMA
        assert config.api_key is None

    def test_cost_limit_validation(self):
        """Test cost limit validation."""
        # Valid cost limit
        config = RoleConfiguration(
            provider=LLMProvider.OLLAMA,
            available_models=["llama2:7b"],
            selected_model="llama2:7b",
            cost_limit=50.0
        )
        assert config.cost_limit == 50.0

        # Invalid cost limit (negative)
        with pytest.raises(ValueError):
            RoleConfiguration(
                provider=LLMProvider.OLLAMA,
                available_models=["llama2:7b"],
                selected_model="llama2:7b",
                cost_limit=-10.0
            )

    def test_temperature_validation(self):
        """Test temperature validation."""
        # Valid temperature
        config = RoleConfiguration(
            provider=LLMProvider.OLLAMA,
            available_models=["llama2:7b"],
            selected_model="llama2:7b",
            temperature=0.7
        )
        assert config.temperature == 0.7

        # Invalid temperature (too high)
        with pytest.raises(ValueError):
            RoleConfiguration(
                provider=LLMProvider.OLLAMA,
                available_models=["llama2:7b"],
                selected_model="llama2:7b",
                temperature=3.0  # Above maximum
            )