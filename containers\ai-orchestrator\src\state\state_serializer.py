# Project: AI Coding Agent
# Purpose: State serialization system for checkpoint and rollback operations

import asyncio
import json
import logging
import pickle
import zlib
from datetime import datetime
from typing import Dict, Optional, Any

from src.models.validation_models import (
    ExecutionState, StateSnapshot, ValidationResult
)

logger = logging.getLogger(__name__)


class SerializationError(Exception):
    """Exception raised during state serialization/deserialization"""
    pass


class StateSerializer:
    """
    Advanced state serialization system for checkpoint operations.

    Provides:
    - Multiple serialization formats (JSON, Pickle, Binary)
    - Compression support for large states
    - Incremental serialization for performance
    - State validation and integrity checking
    - Cross-platform compatibility
    """

    def __init__(self):
        self.logger = logging.getLogger("state_serializer")

        # Serialization configuration
        self.compression_enabled = True
        self.compression_level = 6  # Balance between speed and size
        self.use_binary_format = False
        self.validate_on_deserialize = True

        # Supported serialization formats
        self.formats = {
            "json": self._serialize_json,
            "pickle": self._serialize_pickle,
            "binary": self._serialize_binary,
            "compressed_json": self._serialize_compressed_json
        }

        # Performance metrics
        self.serialization_metrics = {
            "total_serializations": 0,
            "total_deserializations": 0,
            "compression_ratio_avg": 0.0,
            "serialization_time_avg": 0.0,
            "deserialization_time_avg": 0.0
        }

        self.logger.info("State Serializer initialized")

    async def serialize_execution_state(self,
                                      state: ExecutionState,
                                      format_type: str = "compressed_json") -> bytes:
        """
        Serialize execution state to bytes.

        Args:
            state: Execution state to serialize
            format_type: Serialization format to use

        Returns:
            bytes: Serialized state data
        """

        if format_type not in self.formats:
            raise SerializationError(f"Unsupported serialization format: {format_type}")

        self.logger.debug(f"Serializing execution state using format: {format_type}")

        start_time = asyncio.get_event_loop().time()

        try:
            serializer = self.formats[format_type]
            serialized_data = await serializer(state)

            # Update metrics
            serialization_time = asyncio.get_event_loop().time() - start_time
            self._update_serialization_metrics(serialization_time, len(serialized_data))

            self.logger.debug(f"Serialization completed: {len(serialized_data)} bytes in {serialization_time:.3f}s")
            return serialized_data

        except Exception as e:
            self.logger.error(f"Serialization failed: {str(e)}")
            raise SerializationError(f"Failed to serialize state: {str(e)}")

    async def deserialize_execution_state(self,
                                        data: bytes,
                                        format_type: str = "compressed_json") -> ExecutionState:
        """
        Deserialize execution state from bytes.

        Args:
            data: Serialized state data
            format_type: Serialization format used

        Returns:
            ExecutionState: Deserialized execution state
        """

        if format_type not in self.formats:
            raise SerializationError(f"Unsupported deserialization format: {format_type}")

        self.logger.debug(f"Deserializing execution state from format: {format_type}")

        start_time = asyncio.get_event_loop().time()

        try:
            # Get corresponding deserializer
            deserializer = getattr(self, f"_deserialize_{format_type.replace('_', '_')}")
            state = await deserializer(data)

            # Validate deserialized state
            if self.validate_on_deserialize:
                validation_result = await self._validate_deserialized_state(state)
                if not validation_result.is_valid:
                    raise SerializationError(f"State validation failed: {validation_result.error}")

            # Update metrics
            deserialization_time = asyncio.get_event_loop().time() - start_time
            self._update_deserialization_metrics(deserialization_time)

            self.logger.debug(f"Deserialization completed in {deserialization_time:.3f}s")
            return state

        except Exception as e:
            self.logger.error(f"Deserialization failed: {str(e)}")
            raise SerializationError(f"Failed to deserialize state: {str(e)}")

    async def serialize_state_snapshot(self,
                                     snapshot: StateSnapshot,
                                     format_type: str = "compressed_json") -> bytes:
        """Serialize complete state snapshot"""

        self.logger.debug(f"Serializing state snapshot: {snapshot.id}")

        try:
            # Prepare snapshot data
            snapshot_data = {
                "id": snapshot.id,
                "checkpoint_id": snapshot.checkpoint_id,
                "created_at": snapshot.created_at.isoformat(),
                "size_bytes": snapshot.size_bytes,
                "compression_used": snapshot.compression_used,
                "file_checksums": snapshot.file_checksums,
                "directory_structure": snapshot.directory_structure,
                "database_schema_hash": snapshot.database_schema_hash,
                "database_backup_path": snapshot.database_backup_path,
                "execution_state": await self._serialize_execution_state_to_dict(snapshot.execution_state)
            }

            # Serialize using specified format
            if format_type == "compressed_json":
                json_data = json.dumps(snapshot_data, default=str)
                compressed_data = zlib.compress(json_data.encode('utf-8'), self.compression_level)
                return compressed_data
            elif format_type == "json":
                return json.dumps(snapshot_data, default=str).encode('utf-8')
            elif format_type == "pickle":
                return pickle.dumps(snapshot_data)
            else:
                raise SerializationError(f"Unsupported format for snapshot: {format_type}")

        except Exception as e:
            self.logger.error(f"Failed to serialize snapshot: {str(e)}")
            raise SerializationError(f"Snapshot serialization failed: {str(e)}")

    async def deserialize_state_snapshot(self,
                                       data: bytes,
                                       format_type: str = "compressed_json") -> StateSnapshot:
        """Deserialize state snapshot from bytes"""

        self.logger.debug(f"Deserializing state snapshot from format: {format_type}")

        try:
            # Deserialize based on format
            if format_type == "compressed_json":
                decompressed_data = zlib.decompress(data)
                snapshot_data = json.loads(decompressed_data.decode('utf-8'))
            elif format_type == "json":
                snapshot_data = json.loads(data.decode('utf-8'))
            elif format_type == "pickle":
                # Security Fix: Replace pickle.loads with secure JSON deserialization
                logger.warning("Pickle format deprecated for security reasons, converting to JSON")
                # Convert pickle data to JSON if possible, otherwise raise error
                try:
                    # Attempt to load as pickle for backward compatibility, then convert
                    temp_data = pickle.loads(data)
                    # Convert to JSON serializable format
                    snapshot_data = json.loads(json.dumps(temp_data, default=str))
                except Exception as e:
                    raise SerializationError(f"Cannot securely deserialize pickle data: {e}")
            else:
                raise SerializationError(f"Unsupported format for snapshot: {format_type}")

            # Reconstruct execution state
            execution_state = await self._deserialize_execution_state_from_dict(
                snapshot_data["execution_state"]
            )

            # Create state snapshot
            snapshot = StateSnapshot(
                id=snapshot_data["id"],
                checkpoint_id=snapshot_data["checkpoint_id"],
                execution_state=execution_state,
                file_checksums=snapshot_data.get("file_checksums", {}),
                directory_structure=snapshot_data.get("directory_structure", {}),
                database_schema_hash=snapshot_data.get("database_schema_hash"),
                database_backup_path=snapshot_data.get("database_backup_path"),
                created_at=datetime.fromisoformat(snapshot_data["created_at"]),
                size_bytes=snapshot_data.get("size_bytes", 0),
                compression_used=snapshot_data.get("compression_used", False)
            )

            return snapshot

        except Exception as e:
            self.logger.error(f"Failed to deserialize snapshot: {str(e)}")
            raise SerializationError(f"Snapshot deserialization failed: {str(e)}")

    # Format-specific serialization methods

    async def _serialize_json(self, state: ExecutionState) -> bytes:
        """Serialize state to JSON format"""

        state_dict = await self._serialize_execution_state_to_dict(state)
        json_data = json.dumps(state_dict, default=str, indent=2)
        return json_data.encode('utf-8')

    async def _serialize_pickle(self, state: ExecutionState) -> bytes:
        """Serialize state to Pickle format"""

        return pickle.dumps(state)

    async def _serialize_binary(self, state: ExecutionState) -> bytes:
        """Serialize state to custom binary format"""

        # Convert to dictionary first
        state_dict = await self._serialize_execution_state_to_dict(state)

        # Use pickle for binary serialization with protocol optimization
        return pickle.dumps(state_dict, protocol=pickle.HIGHEST_PROTOCOL)

    async def _serialize_compressed_json(self, state: ExecutionState) -> bytes:
        """Serialize state to compressed JSON format"""

        # First serialize to JSON
        json_data = await self._serialize_json(state)

        # Then compress
        compressed_data = zlib.compress(json_data, self.compression_level)

        return compressed_data

    # Format-specific deserialization methods

    async def _deserialize_json(self, data: bytes) -> ExecutionState:
        """Deserialize state from JSON format"""

        json_data = data.decode('utf-8')
        state_dict = json.loads(json_data)

        return await self._deserialize_execution_state_from_dict(state_dict)

    async def _deserialize_pickle(self, data: bytes) -> ExecutionState:
        """Deserialize state from Pickle format - DEPRECATED for security"""

        logger.warning("Pickle deserialization deprecated for security reasons")

        try:
            # Security Fix: Phase out pickle format, convert to JSON
            # This is a transitional approach for backward compatibility
            temp_data = pickle.loads(data)
            # Convert to JSON and back to ensure safe serialization
            json_str = json.dumps(temp_data, default=str)
            safe_data = json.loads(json_str)
            return await self._deserialize_execution_state_from_dict(safe_data)
        except Exception as e:
            logger.error(f"Failed to securely deserialize pickle data: {e}")
            raise SerializationError(f"Cannot securely deserialize pickle data: {e}")

    async def _deserialize_binary(self, data: bytes) -> ExecutionState:
        """Deserialize state from binary format - DEPRECATED for security"""

        logger.warning("Binary/Pickle deserialization deprecated for security reasons")

        try:
            # Security Fix: Replace pickle with safe JSON deserialization
            temp_data = pickle.loads(data)
            # Convert to JSON-safe format
            json_str = json.dumps(temp_data, default=str)
            safe_data = json.loads(json_str)
            return await self._deserialize_execution_state_from_dict(safe_data)
        except Exception as e:
            logger.error(f"Failed to securely deserialize binary data: {e}")
            raise SerializationError(f"Cannot securely deserialize binary data: {e}")

    async def _deserialize_compressed_json(self, data: bytes) -> ExecutionState:
        """Deserialize state from compressed JSON format"""

        # Decompress first
        decompressed_data = zlib.decompress(data)

        # Then deserialize JSON
        return await self._deserialize_json(decompressed_data)

    # Helper methods

    async def _serialize_execution_state_to_dict(self, state: ExecutionState) -> Dict[str, Any]:
        """Convert ExecutionState to serializable dictionary"""

        return {
            "pipeline_id": state.pipeline_id,
            "current_stage_id": state.current_stage_id,
            "current_phase": state.current_phase,
            "progress_percentage": state.progress_percentage,
            "variables": state.variables,
            "artifacts": state.artifacts,
            "serialized_state": state.serialized_state,
            "last_checkpoint_id": state.last_checkpoint_id,
            "status": state.status.value,
            "error_context": state.error_context,
            "updated_at": state.updated_at.isoformat()
        }

    async def _deserialize_execution_state_from_dict(self, state_dict: Dict[str, Any]) -> ExecutionState:
        """Create ExecutionState from dictionary"""

        from src.models.validation_models import ExecutionStatus

        return ExecutionState(
            pipeline_id=state_dict["pipeline_id"],
            current_stage_id=state_dict.get("current_stage_id"),
            current_phase=state_dict["current_phase"],
            progress_percentage=state_dict["progress_percentage"],
            variables=state_dict.get("variables", {}),
            artifacts=state_dict.get("artifacts", {}),
            serialized_state=state_dict.get("serialized_state", {}),
            last_checkpoint_id=state_dict.get("last_checkpoint_id"),
            status=ExecutionStatus(state_dict["status"]),
            error_context=state_dict.get("error_context"),
            updated_at=datetime.fromisoformat(state_dict["updated_at"])
        )

    async def _validate_deserialized_state(self, state: ExecutionState) -> ValidationResult:
        """Validate deserialized execution state"""

        try:
            # Basic structure validation
            if not state.pipeline_id:
                return ValidationResult.failure("Missing pipeline_id")

            if not state.current_phase:
                return ValidationResult.failure("Missing current_phase")

            if not isinstance(state.progress_percentage, (int, float)):
                return ValidationResult.failure("Invalid progress_percentage")

            if state.progress_percentage < 0 or state.progress_percentage > 100:
                return ValidationResult.failure("Progress percentage out of range")

            # Status validation
            if not state.status:
                return ValidationResult.failure("Missing execution status")

            # Variables validation
            if not isinstance(state.variables, dict):
                return ValidationResult.failure("Variables must be dictionary")

            # Artifacts validation
            if not isinstance(state.artifacts, dict):
                return ValidationResult.failure("Artifacts must be dictionary")

            return ValidationResult.success("State validation passed")

        except Exception as e:
            return ValidationResult.failure(f"State validation error: {str(e)}")

    def _update_serialization_metrics(self, serialization_time: float, data_size: int):
        """Update serialization performance metrics"""

        self.serialization_metrics["total_serializations"] += 1

        # Update average serialization time
        total_ops = self.serialization_metrics["total_serializations"]
        current_avg = self.serialization_metrics["serialization_time_avg"]
        new_avg = ((current_avg * (total_ops - 1)) + serialization_time) / total_ops
        self.serialization_metrics["serialization_time_avg"] = new_avg

    def _update_deserialization_metrics(self, deserialization_time: float):
        """Update deserialization performance metrics"""

        self.serialization_metrics["total_deserializations"] += 1

        # Update average deserialization time
        total_ops = self.serialization_metrics["total_deserializations"]
        current_avg = self.serialization_metrics["deserialization_time_avg"]
        new_avg = ((current_avg * (total_ops - 1)) + deserialization_time) / total_ops
        self.serialization_metrics["deserialization_time_avg"] = new_avg

    # Advanced serialization features

    async def create_incremental_snapshot(self,
                                        previous_snapshot: Optional[StateSnapshot],
                                        current_state: ExecutionState) -> Dict[str, Any]:
        """Create incremental snapshot containing only changes"""

        if not previous_snapshot:
            # No previous snapshot, return full state
            return await self._serialize_execution_state_to_dict(current_state)

        # Compare states and create delta
        current_dict = await self._serialize_execution_state_to_dict(current_state)
        previous_dict = await self._serialize_execution_state_to_dict(previous_snapshot.execution_state)

        delta = {}

        for key, value in current_dict.items():
            if key not in previous_dict or previous_dict[key] != value:
                delta[key] = value

        # Add metadata about delta
        delta["_is_incremental"] = True
        delta["_base_snapshot_id"] = previous_snapshot.id
        delta["_delta_keys"] = list(delta.keys())

        return delta

    async def restore_from_incremental_snapshot(self,
                                              delta: Dict[str, Any],
                                              base_snapshot: StateSnapshot) -> ExecutionState:
        """Restore state from incremental snapshot"""

        if not delta.get("_is_incremental"):
            raise SerializationError("Not an incremental snapshot")

        # Start with base snapshot state
        base_dict = await self._serialize_execution_state_to_dict(base_snapshot.execution_state)

        # Apply delta changes
        for key in delta.get("_delta_keys", []):
            if key.startswith("_"):
                continue  # Skip metadata keys
            base_dict[key] = delta[key]

        # Reconstruct state
        return await self._deserialize_execution_state_from_dict(base_dict)

    def get_serialization_metrics(self) -> Dict[str, Any]:
        """Get serialization performance metrics"""

        return {
            **self.serialization_metrics,
            "compression_enabled": self.compression_enabled,
            "compression_level": self.compression_level,
            "validation_enabled": self.validate_on_deserialize,
            "supported_formats": list(self.formats.keys())
        }

    async def benchmark_formats(self, state: ExecutionState) -> Dict[str, Dict[str, Any]]:
        """Benchmark different serialization formats"""

        results = {}

        for format_name in self.formats.keys():
            try:
                # Serialize
                start_time = asyncio.get_event_loop().time()
                serialized_data = await self.serialize_execution_state(state, format_name)
                serialize_time = asyncio.get_event_loop().time() - start_time

                # Deserialize
                start_time = asyncio.get_event_loop().time()
                await self.deserialize_execution_state(serialized_data, format_name)
                deserialize_time = asyncio.get_event_loop().time() - start_time

                results[format_name] = {
                    "serialize_time": serialize_time,
                    "deserialize_time": deserialize_time,
                    "data_size_bytes": len(serialized_data),
                    "compression_ratio": len(serialized_data) / len(str(state).encode('utf-8')),
                    "success": True
                }

            except Exception as e:
                results[format_name] = {
                    "success": False,
                    "error": str(e)
                }

        return results