/**
 * ApiKeyInput Component Tests
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ApiKeyInput from '../ApiKeyInput';
import { LLMProvider } from '@/types/role';

const defaultProps = {
  value: '',
  onChange: jest.fn(),
  provider: LLMProvider.OPENAI,
  required: true,
};

describe('ApiKeyInput', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with proper label', () => {
    render(<ApiKeyInput {...defaultProps} />);

    expect(screen.getByLabelText(/API Key/)).toBeInTheDocument();
    expect(screen.getByText('API Key')).toBeInTheDocument();
    expect(screen.getByText('*')).toBeInTheDocument(); // Required indicator
  });

  it('shows special UI for Ollama provider', () => {
    render(<ApiKeyInput {...defaultProps} provider={LLMProvider.OLLAMA} />);

    expect(screen.getByText('No API Key Required')).toBeInTheDocument();
    expect(screen.getByText('Ollama runs locally and does not require an API key.')).toBeInTheDocument();
    expect(screen.queryByRole('textbox')).not.toBeInTheDocument();
  });

  it('renders password input by default', () => {
    render(<ApiKeyInput {...defaultProps} />);

    const input = screen.getByRole('textbox', { hidden: true }); // password inputs are hidden from role
    expect(input).toHaveAttribute('type', 'password');
  });

  it('toggles input visibility when show/hide button is clicked', async () => {
    const user = userEvent.setup();

    render(<ApiKeyInput {...defaultProps} value="test-key" />);

    const input = screen.getByDisplayValue('test-key');
    const toggleButton = screen.getByLabelText('Show API key');

    expect(input).toHaveAttribute('type', 'password');

    await user.click(toggleButton);

    expect(input).toHaveAttribute('type', 'text');
    expect(screen.getByLabelText('Hide API key')).toBeInTheDocument();
  });

  it('calls onChange when input value changes', async () => {
    const user = userEvent.setup();
    const mockOnChange = jest.fn();

    render(<ApiKeyInput {...defaultProps} onChange={mockOnChange} />);

    const input = screen.getByRole('textbox', { hidden: true });
    await user.type(input, 'sk-test');

    expect(mockOnChange).toHaveBeenCalledWith('sk-test');
  });

  it('displays error message when provided', () => {
    const errorMessage = 'API key is required';
    render(<ApiKeyInput {...defaultProps} error={errorMessage} />);

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('is disabled when disabled prop is true', () => {
    render(<ApiKeyInput {...defaultProps} disabled />);

    const input = screen.getByRole('textbox', { hidden: true });
    expect(input).toBeDisabled();
  });

  it('shows validation hint for invalid format', () => {
    render(<ApiKeyInput {...defaultProps} value="invalid-key" />);

    const input = screen.getByDisplayValue('invalid-key');
    fireEvent.focus(input);

    expect(screen.getByText(/Invalid API key format/)).toBeInTheDocument();
  });

  it('shows provider-specific placeholder and hint', () => {
    render(<ApiKeyInput {...defaultProps} provider={LLMProvider.OPENAI} />);

    const input = screen.getByPlaceholderText('sk-1234567890abcdef...');
    expect(input).toBeInTheDocument();

    expect(screen.getByText(/OpenAI API keys start with "sk-"/)).toBeInTheDocument();
  });

  it('shows security notice for valid API key', () => {
    // Use a valid-format API key for OpenAI
    const validKey = 'sk-' + 'a'.repeat(48);
    render(<ApiKeyInput {...defaultProps} value={validKey} provider={LLMProvider.OPENAI} />);

    expect(screen.getByText(/Your API key is encrypted and stored securely/)).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    const errorMessage = 'Test error';
    render(<ApiKeyInput {...defaultProps} error={errorMessage} />);

    const input = screen.getByRole('textbox', { hidden: true });
    expect(input).toHaveAttribute('aria-describedby', 'api-key-error');
    expect(input).toHaveAttribute('aria-invalid', 'true');
  });

  it('disables toggle button when no value', () => {
    render(<ApiKeyInput {...defaultProps} value="" />);

    const toggleButton = screen.getByLabelText('Show API key');
    expect(toggleButton).toBeDisabled();
  });

  it('validates different provider key formats', () => {
    const { rerender } = render(
      <ApiKeyInput {...defaultProps} provider={LLMProvider.ANTHROPIC} />
    );

    expect(screen.getByText(/Anthropic API keys start with "sk-ant-api03-"/)).toBeInTheDocument();

    rerender(
      <ApiKeyInput {...defaultProps} provider={LLMProvider.OPENROUTER} />
    );

    expect(screen.getByText(/OpenRouter API keys start with "sk-or-v1-"/)).toBeInTheDocument();
  });

  it('handles focus and blur events for validation', async () => {
    const user = userEvent.setup();

    render(<ApiKeyInput {...defaultProps} value="invalid" />);

    const input = screen.getByDisplayValue('invalid');

    // Focus should show validation hint
    await user.click(input);
    expect(screen.getByText(/Invalid API key format/)).toBeInTheDocument();

    // Blur should hide validation hint
    await user.tab();
    expect(screen.queryByText(/Invalid API key format/)).not.toBeInTheDocument();
  });
});