import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { logger } from "@/lib/logger";

const API_BASE_URL = process.env.API_BASE_URL ||
  process.env.NEXT_PUBLIC_API_BASE_URL ||
  "http://ai-orchestrator:8000";

interface ProjectFromAPI {
  id: string;
  name: string;
  description?: string;
  status?: string;
  progress_percentage?: number;
  total_files_scanned?: number;
  total_files_indexed?: number;
  created_at: string;
}

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const accessToken = (session as unknown as Record<string, unknown>)
      ?.accessToken as string | undefined;
    const userId = session?.user?.id ?? "user-123";

    // Forward request to ai-orchestrator
    const response = await axios.get(
      `${API_BASE_URL}/projects/list`,
      {
        headers: {
          ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
          "X-User-ID": userId,
        },
      },
    );

    // Transform the response to include embedding status
    const projects: ProjectFromAPI[] = response.data.projects || [];
    const projectsWithStatus = projects.map((project) => ({
      id: project.id,
      name: project.name,
      description: project.description,
      status: project.status || "UNINITIALIZED",
      progress_percentage: project.progress_percentage || 0,
      total_files_scanned: project.total_files_scanned,
      total_files_indexed: project.total_files_indexed,
      created_at: project.created_at,
    }));

    return NextResponse.json({
      projects: projectsWithStatus,
    });
  } catch (error) {
    logger.error("Failed to fetch projects:", error);
    const errorMessage = error instanceof Error
      ? error.message
      : "Unknown error";
    return NextResponse.json({
      message: "Failed to fetch projects",
      error: errorMessage,
    }, { status: 500 });
  }
}
