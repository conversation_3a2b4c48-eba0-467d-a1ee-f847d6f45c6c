# Local Registry Mirror Configuration
# Use this to set up a local registry cache to reduce external Docker Hub pulls

services:
  # Local Docker Registry Mirror
  # Provides caching of frequently used images to reduce external pulls
  registry:
    image: registry:2
    container_name: local-registry
    ports:
      - "5000:5000"
    volumes:
      - registry_data:/var/lib/registry
    networks:
      - internal
    restart: unless-stopped
    environment:
      REGISTRY_STORAGE_FILESYSTEM_ROOTDIRECTORY: /var/lib/registry
      REGISTRY_HTTP_ADDR: 0.0.0.0:5000
      REGISTRY_HTTP_SECRET: ${REGISTRY_HTTP_SECRET:-change-me-in-production}
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
      labels:
        - "com.docker.compose.project=codingagenttwo"
        - "com.docker.compose.service=registry"
        - "security.access-level=internal-only"
        - "security.privilege-level=registry"
        - "security.isolation=network-isolated"
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:5000/v2/" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  registry_data:
    driver: local

networks:
  internal:
    external: true
    name: ai-coding-agent_internal_network

# Usage:
# 1. Start the registry: docker-compose -f docker-compose.registry.yml up -d
# 2. Configure Docker daemon to use the mirror (see docs/docker/DOCKER_HUB_RATE_LIMIT_AVOIDANCE.md)
# 3. Restart Docker daemon: sudo systemctl restart docker
# 4. Use normally - images will be cached locally
