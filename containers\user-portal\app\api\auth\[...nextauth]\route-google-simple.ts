import NextAuth, { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import Credentials from "next-auth/providers/credentials";
import axios from "axios";
import { logger } from "@/lib/logger";

// Backend base URL (container-to-container or public). Prefer internal first.
const API_BASE_URL = process.env.API_BASE_URL ||
  process.env.NEXT_PUBLIC_API_BASE_URL || "http://ai-orchestrator:8000";

export const authOptions: NextAuthOptions = {
  session: {
    strategy: "jwt",
    maxAge: 60 * 60, // 1 hour
  },
  pages: {
    signIn: "/login",
  },
  providers: [
    // Google OAuth Provider - Works without adapter, stores in JWT
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET
      ? [
        GoogleProvider({
          clientId: process.env.GOOGLE_CLIENT_ID,
          clientSecret: process.env.GOOGLE_CLIENT_SECRET,
          authorization: {
            params: {
              prompt: "consent",
              access_type: "offline",
              response_type: "code",
            },
          },
        }),
      ]
      : []),
    // Existing Credentials Provider
    Credentials({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials.password) return null;
        try {
          const response = await axios.post(`${API_BASE_URL}/token`, {
            username: credentials.email,
            password: credentials.password,
          }, { timeout: 8000 });

          // Expecting backend to return { access_token, token_type, user?: {...} }
          const data = response.data;
          if (!data?.access_token) return null;

          return {
            id: data.user?.id || credentials.email,
            email: credentials.email,
            name: data.user?.name || credentials.email.split("@")[0],
            accessToken: data.access_token,
            tokenType: data.token_type || "bearer",
            raw: data.user || null,
          };
        } catch (err) {
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      // For Google OAuth
      if (account?.provider === "google" && account.access_token) {
        try {
          // Store Google OAuth tokens
          token.accessToken = account.access_token;
          token.refreshToken = account.refresh_token;
          token.provider = "google";
          token.googleUser = user;

          // Forward to your backend for user creation/validation
          try {
            const backendResponse = await axios.post(
              `${API_BASE_URL}/auth/google`,
              {
                access_token: account.access_token,
                user: user,
                google_account: account,
              },
              {
                timeout: 8000,
                headers: {
                  "Authorization": `Bearer ${account.access_token}`,
                },
              },
            );

            if (backendResponse.data?.success) {
              token.backendUser = backendResponse.data.user;
              token.backendToken = backendResponse.data.access_token;
            }
          } catch (backendError) {
            // Continue without backend sync - can be handled later
            // Backend sync failed, continuing with Google auth
          }
        } catch (error) {
          logger.error("Google OAuth processing failed:", error);
        }
      }

      // For Credentials provider, use the existing flow
      if (user && account?.provider === "credentials") {
        const u = user as unknown as Record<string, unknown>;
        token.accessToken = u.accessToken as string | undefined;
        token.tokenType = u.tokenType as string | undefined;
        token.rawUser = u.raw as unknown;
        token.provider = "credentials";
      }

      return token;
    },
    async session({ session, token }) {
      // Add provider information to session (use unknown-based casts)
      const s = session as unknown as Record<string, unknown>;
      const t = token as unknown as Record<string, unknown>;

      s.provider = t.provider;

      // For Google OAuth
      if (t.provider === "google") {
        s.accessToken = (t.backendToken as string) || (t.accessToken as string);
        s.refreshToken = t.refreshToken;
        s.tokenType = "bearer";
        s.user = (t.backendUser ?? t.googleUser) ?? session.user;
      }

      // For Credentials
      if (t.provider === "credentials") {
        s.accessToken = t.accessToken;
        s.tokenType = t.tokenType;
        s.user = t.rawUser ?? session.user;
      }

      return session;
    },
    async signIn(_params: { user?: unknown; account?: unknown }) {
      // Allow all sign-ins - let the JWT callback handle the logic
      return true;
    },
  },
  cookies: {},
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };
