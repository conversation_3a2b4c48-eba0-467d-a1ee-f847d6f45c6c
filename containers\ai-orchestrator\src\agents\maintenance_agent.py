"""
MaintenanceAgent: Proactive maintenance and dependency management agent.

This agent handles scheduled maintenance tasks including dependency updates,
project cleanup, and other proactive maintenance operations.
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, Optional

from src.agents.base_agent import BaseAgent, ValidationResult
from src.agents.shell_agent import ShellAgent
from src.agents.deployment_agent import DeploymentAgent

logger = logging.getLogger(__name__)


class MaintenanceAgent(BaseAgent):
    """
    Agent responsible for proactive maintenance tasks including dependency updates,
    project cleanup, and scheduled maintenance operations.
    """

    def __init__(
        self,
        shell_agent: ShellAgent,
        deployment_agent: Optional[DeploymentAgent] = None,
        max_concurrent_tasks: int = 1,
        enable_validation: bool = True,
        enable_error_recovery: bool = True,
        enable_task_history: bool = True,
        max_retries: int = 2,
    ) -> None:
        """
        Initialize the MaintenanceAgent.

        Args:
            shell_agent: ShellAgent instance for executing shell commands
            deployment_agent: Optional DeploymentAgent for creating preview deployments
            max_concurrent_tasks: Maximum concurrent tasks (inherited from BaseAgent)
            enable_validation: Enable validation features (inherited from BaseAgent)
            enable_error_recovery: Enable error recovery features (inherited from BaseAgent)
            enable_task_history: Enable task history tracking (inherited from BaseAgent)
            max_retries: Maximum retry attempts (inherited from BaseAgent)
        """
        super().__init__(
            max_concurrent_tasks=max_concurrent_tasks,
            enable_validation=enable_validation,
            enable_error_recovery=enable_error_recovery,
            enable_task_history=enable_task_history,
            max_retries=max_retries,
        )
        self.shell_agent = shell_agent
        self.deployment_agent = deployment_agent

    async def execute(self, task_type: str, **kwargs) -> Dict[str, Any]:
        """
        Public entry point for maintenance tasks.

        Args:
            task_type: Type of maintenance task to execute
            **kwargs: Additional task-specific parameters

        Returns:
            Dictionary containing task execution result

        Raises:
            ValueError: If task_type is not supported
        """
        logger.info(f"Executing maintenance task: {task_type}")

        if task_type == "dependency_update":
            project_path = kwargs.get("project_path")
            project_id = kwargs.get("project_id")
            if not project_path or not project_id:
                raise ValueError("project_path and project_id are required for dependency_update")
            return await self._perform_dependency_update(project_path, project_id)
        else:
            raise ValueError(f"Unsupported task type: {task_type}")

    async def _perform_dependency_update(
        self, project_path: str, project_id: str
    ) -> Dict[str, Any]:
        """
        Perform dependency update workflow.

        Steps:
        1. Create a new git branch
        2. Run dependency update (e.g., npm update)
        3. Run tests (e.g., npm test)
        4. If tests pass: commit, push, and create preview deployment
        5. If tests fail: rollback changes

        Args:
            project_path: Path to the project directory
            project_id: ID of the project being updated

        Returns:
            Dictionary containing update result with status and details
        """
        logger.info(f"Starting dependency update for project {project_id} at {project_path}")

        # Create timestamped branch name
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        branch_name = f"maintenance/dependency-updates-{timestamp}"

        try:
            # Step 1: Create and checkout new branch
            logger.info(f"Creating branch: {branch_name}")
            git_checkout_result = await self.shell_agent.execute(
                {"command": ["git", "checkout", "-b", branch_name], "cwd": project_path}
            )

            if git_checkout_result.get("returncode") != 0:
                error_msg = git_checkout_result.get("stderr", "Unknown git checkout error")
                logger.error(f"Failed to create branch: {error_msg}")
                return {"status": "failed", "reason": f"Branch creation failed: {error_msg}"}

            # Step 2: Run dependency update
            logger.info("Running dependency update")
            update_result = await self.shell_agent.execute(
                {"command": ["npm", "update"], "cwd": project_path}
            )

            if update_result.get("returncode") != 0:
                error_msg = update_result.get("stderr", "Unknown npm update error")
                logger.error(f"Dependency update failed: {error_msg}")
                await self._rollback_changes(project_path, branch_name)
                return {"status": "failed", "reason": f"Dependency update failed: {error_msg}"}

            # Step 3: Run tests
            logger.info("Running tests after dependency update")
            test_result = await self.shell_agent.execute(
                {"command": ["npm", "test"], "cwd": project_path}
            )

            if test_result.get("returncode") != 0:
                error_msg = test_result.get("stderr", "Tests failed")
                logger.error(f"Tests failed after dependency update: {error_msg}")
                await self._rollback_changes(project_path, branch_name)
                return {"status": "failed", "reason": f"Tests failed after update: {error_msg}"}

            # Step 4: Commit and push changes
            logger.info("Tests passed, committing changes")
            commit_result = await self.shell_agent.execute(
                {"command": ["git", "add", "."], "cwd": project_path}
            )

            if commit_result.get("returncode") != 0:
                error_msg = commit_result.get("stderr", "Git add failed")
                logger.error(f"Git add failed: {error_msg}")
                await self._rollback_changes(project_path, branch_name)
                return {"status": "failed", "reason": f"Git add failed: {error_msg}"}

            commit_message = f"chore(deps): Automated dependency update {timestamp}"
            commit_result = await self.shell_agent.execute(
                {"command": ["git", "commit", "-m", commit_message], "cwd": project_path}
            )

            if commit_result.get("returncode") != 0:
                error_msg = commit_result.get("stderr", "Git commit failed")
                logger.error(f"Git commit failed: {error_msg}")
                await self._rollback_changes(project_path, branch_name)
                return {"status": "failed", "reason": f"Git commit failed: {error_msg}"}

            push_result = await self.shell_agent.execute(
                {"command": ["git", "push", "origin", branch_name], "cwd": project_path}
            )

            if push_result.get("returncode") != 0:
                error_msg = push_result.get("stderr", "Git push failed")
                logger.error(f"Git push failed: {error_msg}")
                await self._rollback_changes(project_path, branch_name)
                return {"status": "failed", "reason": f"Git push failed: {error_msg}"}

            # Step 5: Create preview deployment if deployment_agent is available
            if self.deployment_agent:
                logger.info("Creating preview deployment")
                try:
                    deployment_result = await self.deployment_agent.execute(
                        {
                            "action": "create_preview_deployment",
                            "project_id": project_id,
                            "project_hostname": f"project-{project_id}",
                            "commit_hash": branch_name,
                            "project_workspace_path": project_path,
                        }
                    )

                    if deployment_result.get("success", False):
                        preview_url = deployment_result.get("data", {}).get("preview_url")
                        logger.info(f"Preview deployment created successfully: {preview_url}")
                        return {
                            "status": "preview_created",
                            "url": preview_url,
                            "branch": branch_name,
                            "message": "Dependency update completed with preview deployment",
                        }
                    else:
                        error_msg = deployment_result.get("error", "Preview deployment failed")
                        logger.error(f"Preview deployment failed: {error_msg}")
                        return {
                            "status": "partial_success",
                            "branch": branch_name,
                            "message": f"Dependency update completed but preview failed: {error_msg}",
                        }
                except Exception as e:
                    logger.error(f"Preview deployment error: {str(e)}")
                    return {
                        "status": "partial_success",
                        "branch": branch_name,
                        "message": f"Dependency update completed but preview failed: {str(e)}",
                    }
            else:
                logger.info("No deployment agent available, skipping preview creation")
                return {
                    "status": "success",
                    "branch": branch_name,
                    "message": "Dependency update completed successfully (no preview deployment)",
                }

        except Exception as e:
            logger.error(f"Unexpected error during dependency update: {str(e)}")
            await self._rollback_changes(project_path, branch_name)
            return {"status": "failed", "reason": f"Unexpected error: {str(e)}"}

    async def _rollback_changes(self, project_path: str, branch_name: str) -> None:
        """
        Rollback changes by resetting git state and deleting the branch.

        Args:
            project_path: Path to the project directory
            branch_name: Name of the branch to delete
        """
        logger.info(f"Rolling back changes for branch: {branch_name}")

        try:
            # Reset any changes
            await self.shell_agent.execute(
                {"command": ["git", "reset", "--hard"], "cwd": project_path}
            )

            # Checkout main branch
            await self.shell_agent.execute(
                {"command": ["git", "checkout", "main"], "cwd": project_path}
            )

            # Delete the branch
            await self.shell_agent.execute(
                {"command": ["git", "branch", "-D", branch_name], "cwd": project_path}
            )

            logger.info(f"Successfully rolled back changes and deleted branch: {branch_name}")
        except Exception as e:
            logger.error(f"Error during rollback: {str(e)}")

    # BaseAgent abstract method implementations
    async def _execute_core(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Core task execution logic for MaintenanceAgent.

        Args:
            task_input: Task input dictionary containing task_type and parameters

        Returns:
            Dictionary containing execution result
        """
        task_type = task_input.get("task_type")
        if not task_type:
            return {"success": False, "error": "task_type is required"}

        try:
            result = await self.execute(task_type, **task_input)
            return {"success": True, "data": result}
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _validate_agent_specific_prerequisites(
        self, task_input: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate maintenance task prerequisites.

        Args:
            task_input: Task input dictionary

        Returns:
            ValidationResult indicating validation status
        """
        task_type = task_input.get("task_type")
        if not task_type:
            return ValidationResult.failure("task_type is required")

        if task_type == "dependency_update":
            if not task_input.get("project_path"):
                return ValidationResult.failure("project_path is required for dependency_update")
            if not task_input.get("project_id"):
                return ValidationResult.failure("project_id is required for dependency_update")

        return ValidationResult.success("Prerequisites validation passed")

    async def _validate_agent_specific_completion(
        self, task_input: Dict[str, Any], result: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate maintenance task completion.

        Args:
            task_input: Task input dictionary
            result: Task execution result

        Returns:
            ValidationResult indicating validation status
        """
        if not isinstance(result, dict):
            return ValidationResult.failure("Result must be a dictionary")

        if "success" not in result:
            return ValidationResult.failure("Result must contain 'success' field")

        return ValidationResult.success("Completion validation passed")
