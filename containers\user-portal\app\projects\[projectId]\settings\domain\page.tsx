"use client";

import { useState } from 'react';
import { useParams } from 'next/navigation';
import toast, { Toaster } from 'react-hot-toast';
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";

const DomainSettingsPage = () => {
  const { data: session } = useSession();
  const params = useParams();
  const projectId = params.projectId as string;
  const [domain, setDomain] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  if (!session) {
    redirect('/login');
  }

  const validateDomain = (domain: string): boolean => {
    if (!domain) return false;
    // A simple regex to check for a valid domain format.
    const domainRegex = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/;
    return domainRegex.test(domain);
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateDomain(domain)) {
      toast.error('Please enter a valid domain name.');
      return;
    }

    setIsLoading(true);
    const toastId = toast.loading('Saving...');

    try {
      const response = await fetch(`/api/projects/${projectId}/custom-domain`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ custom_domain: domain }),
      });

      if (response.ok) {
        toast.success('Success! Your domain has been added.', { id: toastId });
        setDomain('');
      } else {
        const errorData = await response.json();
        toast.error(`Error: ${errorData.message || 'Could not add domain.'}`, { id: toastId });
      }
    } catch (error) {
      toast.error('Error: Could not add domain.', { id: toastId });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Toaster position="top-center" reverseOrder={false} />
      <div className="p-8">
        <h1 className="text-3xl font-bold mb-6">Custom Domain</h1>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Configure your Domain Section */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-semibold mb-4">Configure your Domain</h2>
            <p className="text-gray-600 mb-4">
              To connect your domain, log in to your domain provider (e.g., GoDaddy, Namecheap) and create an A record pointing to our server's IP address.
            </p>
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium text-gray-700">Type</span>
                <span className="font-mono bg-gray-200 px-2 py-1 rounded">A</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-medium text-gray-700">Name/Host</span>
                <span className="font-mono bg-gray-200 px-2 py-1 rounded">@</span>
              </div>
              <div className="flex justify-between py-2">
                <span className="font-medium text-gray-700">Value</span>
                {/* This should come from process.env.NEXT_PUBLIC_SERVER_IP */}
                <span className="font-mono bg-gray-200 px-2 py-1 rounded">************</span>
              </div>
            </div>
          </div>

          {/* Add Domain Section */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-semibold mb-4">Add Domain</h2>
            <form onSubmit={handleSave}>
              <div className="mb-4">
                <label htmlFor="domain" className="block text-sm font-medium text-gray-700 mb-1">
                  Domain Name
                </label>
                <input
                  type="text"
                  id="domain"
                  value={domain}
                  onChange={(e) => setDomain(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="www.my-awesome-site.com"
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-300"
              >
                {isLoading ? 'Saving...' : 'Add Domain'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default DomainSettingsPage;
