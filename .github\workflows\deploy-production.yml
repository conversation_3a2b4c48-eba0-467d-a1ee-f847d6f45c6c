name: Deploy to Production

on:
  workflow_run:
    workflows: ["Publish Images to GHCR"]
    types:
      - completed
    branches:
      - main

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production
    if: ${{ github.event.workflow_run.conclusion == 'success' }}

    steps:
      - name: Deploy to production server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -e

            echo "Navigating to application directory..."
            cd /opt/codingagenttwo

            echo "Logging in to GitHub Container Registry..."
            echo ${{ secrets.CR_PAT }} | docker login ${{ env.REGISTRY }} -u ${{ github.actor }} --password-stdin

            echo "Pulling latest docker-compose.prod.yml..."
            git pull origin main

            echo "Setting image tag from triggering commit..."
            export IMAGE_TAG=${{ github.event.workflow_run.head_sha }}

            echo "Pulling updated images..."
            docker compose -f docker-compose.yml -f docker-compose.prod.yml pull

            echo "Deploying updated services..."
            docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d --remove-orphans

            echo "Cleaning up old images..."
            docker image prune -af

            echo "Deployment completed successfully!"
