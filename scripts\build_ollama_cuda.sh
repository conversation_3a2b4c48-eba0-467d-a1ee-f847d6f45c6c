#!/bin/bash
# Build script for Ollama with CUDA baked in
# This prevents CUDA runtime installation on every container restart

set -e

echo "Building Ollama with CUDA baked in..."
echo "This will create a custom image that includes CUDA + Ollama"
echo "No more runtime CUDA installation on container restarts!"
echo ""

# Build the image
docker build -t ollama-cuda ./containers/ollama

echo ""
echo " Build complete!"
echo "Image: ollama-cuda"
echo ""
echo "To use this image, update your docker-compose.yml:"
echo "  ollama:"
echo "    image: ollama-cuda"
echo "    # ... rest of your config"
echo ""
echo "Or simply run: docker compose up --build ollama"
