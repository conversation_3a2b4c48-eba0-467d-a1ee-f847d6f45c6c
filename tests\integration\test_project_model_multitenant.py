"""
Tests for Multi-Tenant Project Model Updates.

This module tests the updated Project model with multi-tenant database
schema fields (db_schema_name and db_connection_url).

Tests cover:
- Model field validation
- Schema name uniqueness
- Connection URL storage
- Integration with existing functionality

Author: AI Coding Agent
Version: 1.0.0
"""

import pytest
from sqlalchemy import create_engine, Column, String, Table, text
from sqlalchemy.orm import sessionmaker

from src.models.project import Project, Base
from src.models.user import UserProfile as User


@pytest.fixture
def test_engine():
    """Create an in-memory SQLite engine for testing."""
    return create_engine("sqlite:///:memory:")


@pytest.fixture(scope="function")
def test_session(test_engine):
    """Create a test database session with proper table setup."""
    # For testing, we need to mock the Supabase auth.users table
    # Since we're using SQLite for tests, we'll create a simplified version

    # Create auth schema and users table for Supabase compatibility
    if 'auth_users' not in Base.metadata.tables:
        # Note: In production this would be auth.users, but for SQLite testing we use auth_users
        auth_users = Table('auth_users', Base.metadata,
            Column('id', String(36), primary_key=True),
            Column('email', String(255))
        )

    # Create all tables
    Base.metadata.create_all(test_engine)

    Session = sessionmaker(bind=test_engine)
    session = Session()
    yield session
    session.close()
class TestProjectModelMultiTenant:
    """Test suite for multi-tenant Project model functionality."""

    def test_project_model_has_schema_fields(self):
        """Test that Project model includes the new multi-tenant fields."""
        # Check that the model has the required fields
        assert hasattr(Project, 'db_schema_name')
        assert hasattr(Project, 'db_connection_url')

        # Check field properties
        schema_field = Project.__table__.columns['db_schema_name']
        url_field = Project.__table__.columns['db_connection_url']

        # Verify field types
        assert str(schema_field.type) == 'VARCHAR(255)'
        assert str(url_field.type) == 'VARCHAR(500)'

        # Verify constraints
        assert schema_field.unique is True  # Schema names must be unique
        assert schema_field.nullable is True  # Allow null for backward compatibility
        assert url_field.nullable is True  # Allow null for backward compatibility

    def test_create_project_with_schema_info(self, test_session):
        """Test creating a project with schema information."""
        # Create a mock auth user first
        test_session.execute(text("INSERT INTO auth_users (id, email) VALUES ('test-user-id', '<EMAIL>')"))
        test_session.commit()

        # Create a mock user
        user = User(
            supabase_user_id="test-user-id",
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        test_session.add(user)
        test_session.commit()

        # Create a project with schema details
        project = Project(
            name="Test Project",
            description="A test project for multi-tenant testing",
            db_schema_name="project_123",
            db_connection_url="postgresql://user:pass@localhost:5432/db?options=-csearch_path%3Dproject_123"
        )

        test_session.add(project)
        test_session.commit()
        test_session.refresh(project)

        # Verify the project was created with correct schema info
        assert project.id is not None
        assert project.db_schema_name == "project_123"
        assert project.db_connection_url == "postgresql://user:pass@localhost:5432/db?options=-csearch_path%3Dproject_123"
        assert project.name == "Test Project"
        assert project.description == "A test project for multi-tenant testing"

    def test_schema_name_uniqueness(self, test_session):
        """Test that schema names must be unique across projects."""
        # Create a mock user first
        user = User(
            supabase_user_id="test-user-id-2",
            username="testuser2",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        test_session.add(user)
        test_session.commit()

        # Create first project
        project1 = Project(
            name="Project 1",
            db_schema_name="project_456",
            db_connection_url="postgresql://user:pass@localhost:5432/db?options=-csearch_path%3Dproject_456"
        )
        test_session.add(project1)
        test_session.commit()

        # Try to create second project with same schema name
        project2 = Project(
            name="Project 2",
            db_schema_name="project_456",  # Same schema name
            db_connection_url="postgresql://user:pass@localhost:5432/db?options=-csearch_path%3Dproject_456"
        )
        test_session.add(project2)

        # This should raise an IntegrityError due to unique constraint
        with pytest.raises(Exception):  # Could be IntegrityError or similar
            test_session.commit()

    def test_project_without_schema_info(self, test_session):
        """Test creating a project without schema information (backward compatibility)."""
        # Create a mock user first
        user = User(
            supabase_user_id="test-user-id-3",
            username="testuser3",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        test_session.add(user)
        test_session.commit()

        # Create a project without schema details (should be allowed)
        project = Project(
            name="Legacy Project",
            description="A project created without schema info"
        )

        test_session.add(project)
        test_session.commit()
        test_session.refresh(project)

        # Verify the project was created successfully
        assert project.id is not None
        assert project.db_schema_name is None
        assert project.db_connection_url is None
        assert project.name == "Legacy Project"

    def test_update_project_schema_info(self, test_session):
        """Test updating a project's schema information."""
        # Create a mock user first
        user = User(
            supabase_user_id="test-user-id-4",
            username="testuser4",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        test_session.add(user)
        test_session.commit()

        # Create a project without schema info
        project = Project(name="Update Test Project")
        test_session.add(project)
        test_session.commit()
        test_session.refresh(project)

        # Update with schema information
        project.db_schema_name = "project_789"
        project.db_connection_url = "postgresql://user:pass@localhost:5432/db?options=-csearch_path%3Dproject_789"
        test_session.commit()
        test_session.refresh(project)

        # Verify the update was successful
        assert project.db_schema_name == "project_789"
        assert project.db_connection_url == "postgresql://user:pass@localhost:5432/db?options=-csearch_path%3Dproject_789"

    def test_connection_url_length(self, test_session):
        """Test that connection URLs can handle long URLs."""
        # Create a mock user first
        user = User(
            supabase_user_id="test-user-id-5",
            username="testuser5",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        test_session.add(user)
        test_session.commit()

        long_url = "***************************************************************************************************************************************************************************************/very_long_database_name_that_exceeds_normal_limits?options=-csearch_path%3Dproject_123&sslmode=require&connect_timeout=30&application_name=my_app&keepalives=1&keepalives_idle=30&keepalives_interval=10&keepalives_count=5"

        project = Project(
            name="Long URL Test",
            db_schema_name="project_123",
            db_connection_url=long_url
        )

        test_session.add(project)
        test_session.commit()
        test_session.refresh(project)

        # Verify the long URL was stored correctly
        assert project.db_connection_url == long_url
        assert len(project.db_connection_url) > 255  # Should handle URLs longer than typical VARCHAR limits

    def test_schema_name_format(self, test_session):
        """Test that schema names follow the expected format."""
        # Create a mock user first
        user = User(
            supabase_user_id="test-user-id-6",
            username="testuser6",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        test_session.add(user)
        test_session.commit()

        test_cases = [
            "project_1",
            "project_123",
            "project_999999",
            "project_0"
        ]

        for i, schema_name in enumerate(test_cases):
            project = Project(
                name=f"Test {schema_name}",
                db_schema_name=schema_name,
                db_connection_url=f"postgresql://test?options=-csearch_path%3D{schema_name}"
            )
            test_session.add(project)
            test_session.commit()
            test_session.refresh(project)

            assert project.db_schema_name == schema_name
            assert schema_name.startswith("project_")
            assert schema_name[len("project_"):].isdigit()  # Should be followed by digits

    def test_backend_agent_schema_aware_input(self, test_session):
        """Test that BackendAgent can handle schema-aware task input data."""
        # Create a mock user first
        user = User(
            supabase_user_id="test-user-id-7",
            username="testuser7",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        test_session.add(user)
        test_session.commit()

        # Create a project with schema info
        project = Project(
            name="Backend Agent Test Project",
            db_schema_name="project_456",
            db_connection_url="postgresql://test_db?options=-csearch_path%3Dproject_456"
        )
        test_session.add(project)
        test_session.commit()
        test_session.refresh(project)

        # Test the input data structure that ArchitectAgent would create
        task_input = {
            "feature": "Create a blog post management system",
            "source": "architect_plan",
            "goal": "Build a complete blog system",
            "integration_points": ["user_auth", "content_management"],
            "db_connection_url": project.db_connection_url,
            "db_schema_name": project.db_schema_name,
            "project_id": project.id
        }

        # Verify all required fields are present
        assert task_input["db_connection_url"] == project.db_connection_url
        assert task_input["db_schema_name"] == project.db_schema_name
        assert task_input["project_id"] == project.id
        assert "feature" in task_input
        assert "integration_points" in task_input

        # Verify the connection URL contains the schema search path
        assert "project_456" in task_input["db_connection_url"]
        assert "search_path" in task_input["db_connection_url"]
