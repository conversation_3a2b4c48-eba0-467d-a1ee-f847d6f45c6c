# syntax=docker/dockerfile:1
# Grafana Dockerfile for monitoring stack
# Uses official Grafana image with security best practices

FROM grafana/grafana:10.2.0

# Create grafana user explicitly with specific UID/GID for security
USER root
RUN (getent group grafana || addgroup -g 472 -S grafana) && \
  (getent passwd grafana || adduser -u 472 -S grafana -G grafana -h /usr/share/grafana -s /bin/sh)

# Install curl for health checks
RUN apk update && apk add --no-cache \
  curl \
  && rm -rf /var/cache/apk/*

# Create necessary directories with proper permissions
RUN mkdir -p /var/lib/grafana && \
  chown -R grafana:grafana /var/lib/grafana && \
  chown -R grafana:grafana /usr/share/grafana && \
  chown -R grafana:grafana /etc/grafana

# Switch to non-root user
USER grafana

# Expose Grafana port
EXPOSE 3000

# Health check for Grafana service
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Set security and project labels
LABEL org.opencontainers.image.title="AI Coding Agent - Grafana" \
  org.opencontainers.image.description="Grafana monitoring dashboard for AI Coding Agent" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team" \
  security.non-root="true" \
  security.user="grafana"

# Start Grafana
CMD ["/run.sh"]
