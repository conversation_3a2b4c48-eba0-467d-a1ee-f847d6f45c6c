#!/usr/bin/env python3
"""
Simplified Export Feature Test

This script tests the export components without requiring full configuration.
"""

import asyncio
import sys
import os
import uuid
import tempfile
from pathlib import Path
from datetime import datetime

# Add src to path for imports
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))
original_cwd = os.getcwd()
os.chdir(src_dir)

def print_header(title: str):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_step(step: str, description: str):
    """Print a test step."""
    print(f"\n{step} {description}")
    print("-" * 50)

def print_success(message: str):
    """Print success message."""
    print(f"   ✅ {message}")

def print_error(message: str):
    """Print error message."""
    print(f"   ❌ {message}")

def print_info(message: str):
    """Print info message."""
    print(f"   ℹ️  {message}")

async def test_schema_imports():
    """Test schema imports only."""
    print_step("1️⃣", "Testing Schema Imports")

    try:
        from schemas.project_schemas import (
            ProjectExportRequest,
            ExportFormat
        )
        print_success("Schema imports successful")

        # Test basic schema validation
        request = ProjectExportRequest(
            include_database=True,
            include_files=True,
            export_format=ExportFormat.ZIP
        )
        print_success(f"Export request created: {request.export_format}")

        return True

    except Exception as e:
        print_error(f"Schema import failed: {e}")
        return False

async def test_shell_agent_validation():
    """Test ShellAgent command validation without full imports."""
    print_step("2️⃣", "Testing ShellAgent Command Validation")

    try:
        from src.agents.shell import ShellAgent

        shell = ShellAgent()
        print_success("ShellAgent instance created")

        # Test command validation
        test_commands = [
            (["pg_dump", "--help"], True, "pg_dump command"),
            (["rsync", "-av", "src/", "dest/"], True, "rsync command"),
            (["zip", "-r", "archive.zip", "files/"], True, "zip command"),
            (["rm", "-rf", "/tmp/exports/test-123"], True, "rm export directory"),
            (["pg_dump", "; rm -rf /"], False, "command injection"),
            (["unknown_command"], False, "unknown command"),
        ]

        for command, should_pass, description in test_commands:
            is_valid = shell._validate_export_command(command)
            if is_valid == should_pass:
                status = "✅" if should_pass else "🛡️"
                print_success(f"{status} {description}: {'allowed' if should_pass else 'blocked'}")
            else:
                print_error(f"Validation failed for {description}")
                return False

        return True

    except Exception as e:
        print_error(f"ShellAgent test failed: {e}")
        return False

async def test_export_metadata():
    """Test export metadata creation."""
    print_step("3️⃣", "Testing Export Metadata")

    try:
        from schemas.project_schemas import ProjectExportMetadata, ExportFormat

        metadata = ProjectExportMetadata(
            export_id=str(uuid.uuid4()),
            project_id="test-project-456",
            user_id="test-user-123",
            project_name="test-project",
            export_format=ExportFormat.ZIP,
            include_database=True,
            include_files=True
        )

        print_success(f"Export metadata created: {metadata.export_id}")
        print_success(f"Format: {metadata.export_format}")
        print_success(f"Include DB: {metadata.include_database}")
        print_success(f"Include Files: {metadata.include_files}")

        return True

    except Exception as e:
        print_error(f"Export metadata test failed: {e}")
        return False

async def test_file_operations():
    """Test file system operations."""
    print_step("4️⃣", "Testing File System Operations")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            print_success(f"Created temporary directory: {temp_path}")

            # Create mock export structure
            export_id = str(uuid.uuid4())
            export_dir = temp_path / "exports" / export_id
            export_dir.mkdir(parents=True)
            print_success(f"Created export directory: {export_dir}")

            # Create mock database export
            sql_file = export_dir / "project_database.sql"
            sql_content = f"""-- Project Export Metadata
-- Export ID: {export_id}
-- Project Name: test-project
-- Export Date: {datetime.now().isoformat()}

INSERT INTO projects VALUES ('test-id', 'test-project', 'Test project');
"""
            sql_file.write_text(sql_content)
            print_success(f"Created SQL file: {sql_file.name} ({sql_file.stat().st_size} bytes)")

            # Create mock project files
            project_files = export_dir / "project_files"
            project_files.mkdir()

            (project_files / "main.py").write_text("print('Hello, World!')")
            (project_files / "README.md").write_text("# Test Project\n\nThis is a test project.")
            (project_files / "requirements.txt").write_text("fastapi==0.68.0\nuvicorn==0.15.0")

            src_dir = project_files / "src"
            src_dir.mkdir()
            (src_dir / "app.py").write_text("from fastapi import FastAPI\napp = FastAPI()")

            print_success("Created project files structure")

            # List all created files
            all_files = list(export_dir.rglob("*"))
            file_count = len([f for f in all_files if f.is_file()])
            print_success(f"Total files created: {file_count}")

            # Calculate total size
            total_size = sum(f.stat().st_size for f in all_files if f.is_file())
            print_success(f"Total size: {total_size} bytes")

        return True

    except Exception as e:
        print_error(f"File operations test failed: {e}")
        return False

async def test_export_workflow_simulation():
    """Simulate the export workflow without full dependencies."""
    print_step("5️⃣", "Testing Export Workflow Simulation")

    try:
        from schemas.project_schemas import ProjectExportRequest, ExportFormat

        # Step 1: Create export request
        export_request = ProjectExportRequest(
            include_database=True,
            include_files=True,
            export_format=ExportFormat.ZIP
        )
        print_success("Step 1: Export request created")

        # Step 2: Generate export ID
        export_id = str(uuid.uuid4())
        print_success(f"Step 2: Export ID generated: {export_id}")

        # Step 3: Simulate export plan
        export_plan = {
            "export_type": "project_export",
            "project_name": "test-project",
            "export_format": export_request.export_format.value,
            "include_database": export_request.include_database,
            "include_files": export_request.include_files,
            "steps": [
                {"step": "database_export", "description": "Export database data"},
                {"step": "filesystem_export", "description": "Copy project files"},
                {"step": "package_export", "description": "Create archive"},
                {"step": "cleanup_export", "description": "Clean up temporary files"}
            ],
            "total_steps": 4,
            "created_at": datetime.now().isoformat()
        }
        print_success(f"Step 3: Export plan created with {export_plan['total_steps']} steps")

        # Step 4: Simulate command validation
        from src.agents.shell import ShellAgent
        shell = ShellAgent()

        commands_to_validate = [
            ["pg_dump", "--host=postgresql", "--port=5432", "--username=postgres"],
            ["rsync", "-av", "/workspace/users/test-user/test-project/", "/tmp/exports/test/project_files/"],
            ["zip", "-r", "/downloads/exports/test-project_export.zip", "project_files/", "project_database.sql"]
        ]

        for cmd in commands_to_validate:
            if shell._validate_export_command(cmd):
                print_success(f"Step 4: Command validated: {cmd[0]}")
            else:
                print_error(f"Step 4: Command validation failed: {cmd[0]}")
                return False

        print_success("Step 5: Export workflow simulation completed successfully")

        return True

    except Exception as e:
        print_error(f"Export workflow simulation failed: {e}")
        return False

async def run_simplified_tests():
    """Run simplified tests."""
    print_header("Simplified Export Feature Tests")

    tests = [
        ("Schema Imports", test_schema_imports),
        ("ShellAgent Validation", test_shell_agent_validation),
        ("Export Metadata", test_export_metadata),
        ("File Operations", test_file_operations),
        ("Export Workflow Simulation", test_export_workflow_simulation),
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print_error(f"Test {test_name} crashed: {e}")
            results[test_name] = False

    # Print summary
    print_header("Test Results Summary")

    passed = 0
    total = len(results)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1

    print(f"\n📊 Overall Result: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All simplified tests passed! Core export functionality is working.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

def cleanup():
    """Restore original working directory."""
    try:
        os.chdir(original_cwd)
    except:
        pass

if __name__ == "__main__":
    print("🚀 Starting Simplified Export Feature Tests...")
    print(f"📁 Working directory: {os.getcwd()}")
    print(f"🐍 Python version: {sys.version}")

    try:
        success = asyncio.run(run_simplified_tests())
        cleanup()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        cleanup()
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        import traceback
        traceback.print_exc()
        cleanup()
        sys.exit(1)
