# Traefik Reverse Proxy Container

Modern, cloud-native reverse proxy and load balancer for AI Coding Agent with automatic service discovery.

## 🎯 Overview

This container provides Traefik as a dynamic reverse proxy that automatically discovers and configures routes for AI Coding Agent microservices, with built-in support for Docker, Kubernetes, and cloud platforms.

## 🏗️ Architecture

### Features

- **Automatic Service Discovery**: Zero-configuration routing for containers
- **Dynamic Configuration**: Hot-reload configuration without restarts
- **SSL/TLS**: Automatic HTTPS with Let's Encrypt integration
- **Load Balancing**: Advanced load balancing algorithms
- **Circuit Breaker**: Fault tolerance and resilience
- **Rate Limiting**: Traffic shaping and abuse prevention
- **Metrics & Tracing**: Built-in monitoring and observability
- **Middleware**: Request/response transformation pipeline

### Supported Providers

- **Docker**: Automatic container discovery
- **Docker Swarm**: Cluster-aware service discovery
- **Kubernetes**: Native ingress controller
- **Consul**: Service registry integration
- **Etcd**: Distributed key-value store
- **File**: Static configuration files

## 🚀 Configuration

### Environment Variables

```bash
# Traefik Configuration
TRAEFIK_API=true
TRAEFIK_API_DASHBOARD=true
TRAEFIK_API_INSECURE=true

# Providers
TRAEFIK_PROVIDERS_DOCKER=true
TRAEFIK_PROVIDERS_DOCKER_EXPOSEDBYDEFAULT=false
TRAEFIK_PROVIDERS_DOCKER_NETWORK=ai-coding-agent

# Entry Points
TRAEFIK_ENTRYPOINTS_WEB_ADDRESS=:80
TRAEFIK_ENTRYPOINTS_WEBSECURE_ADDRESS=:443

# SSL/TLS
TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_EMAIL=<EMAIL>
TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_TLSCHALLENGE=true
```

### Static Configuration

```yaml
# traefik.yml
api:
  dashboard: true
  insecure: true

providers:
  docker:
    exposedByDefault: false
    network: ai-coding-agent

entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entryPoint:
          to: websecure
          scheme: https

  websecure:
    address: ":443"
    http:
      tls:
        certResolver: letsencrypt

certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      tlsChallenge: {}
```

## 🔧 Usage

### Building the Container

```bash
docker build -t ai-coding-agent-traefik .
```

### Running with Docker Compose

```bash
docker-compose up traefik
```

### Accessing Dashboard

```bash
# Web UI
open http://localhost:8080

# API
curl http://localhost:8080/api/http/routers
```

## 🏷️ Service Labels

### Docker Container Labels

```yaml
services:
  ai-orchestrator:
    image: ai-orchestrator:latest
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ai-orchestrator.rule=Host(`api.example.com`)"
      - "traefik.http.routers.ai-orchestrator.entrypoints=websecure"
      - "traefik.http.routers.ai-orchestrator.tls.certresolver=letsencrypt"
      - "traefik.http.services.ai-orchestrator.loadbalancer.server.port=8000"
      - "traefik.http.middlewares.ai-orchestrator-rate-limit.ratelimit.burst=100"
      - "traefik.http.middlewares.ai-orchestrator-rate-limit.ratelimit.average=50"
      - "traefik.http.routers.ai-orchestrator.middlewares=ai-orchestrator-rate-limit"
```

### WebSocket Support

```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.websocket.rule=Host(`ws.example.com`)"
  - "traefik.http.routers.websocket.entrypoints=websecure"
  - "traefik.http.services.websocket.loadbalancer.server.port=8080"
  - "traefik.http.middlewares.websocket.headers.customrequestheaders.X-Forwarded-Proto=https"
  - "traefik.http.middlewares.websocket.headers.customrequestheaders.Upgrade=websocket"
  - "traefik.http.middlewares.websocket.headers.customrequestheaders.Connection=upgrade"
```

## 🔒 Security Features

### Authentication

```yaml
# Basic Auth Middleware
labels:
  - "traefik.http.middlewares.auth.basicAuth.users=test:$2y$10$..."
  - "traefik.http.routers.secure.middlewares=auth"

# JWT Authentication
labels:
  - "traefik.http.middlewares.jwt.plugin=jwt"
  - "traefik.http.middlewares.jwt.plugin.secret=your-secret-key"
```

### Security Headers

```yaml
labels:
  - "traefik.http.middlewares.security.headers.customrequestheaders.X-Forwarded-Proto=https"
  - "traefik.http.middlewares.security.headers.customresponseheaders.X-Frame-Options=SAMEORIGIN"
  - "traefik.http.middlewares.security.headers.customresponseheaders.X-Content-Type-Options=nosniff"
  - "traefik.http.middlewares.security.headers.customresponseheaders.Referrer-Policy=strict-origin-when-cross-origin"
```

### Rate Limiting

```yaml
labels:
  - "traefik.http.middlewares.ratelimit.ratelimit.burst=100"
  - "traefik.http.middlewares.ratelimit.ratelimit.average=50"
  - "traefik.http.routers.api.middlewares=ratelimit"
```

## ⚖️ Load Balancing

### Algorithms

```yaml
# Round Robin (default)
labels:
  - "traefik.http.services.api.loadbalancer.server.port=8000"

# Weighted Round Robin
labels:
  - "traefik.http.services.api.loadbalancer.server.port=8000"
  - "traefik.http.services.api.loadbalancer.server.weight=3"

# IP Hash (session affinity)
labels:
  - "traefik.http.services.api.loadbalancer.sticky=true"
  - "traefik.http.services.api.loadbalancer.sticky.cookie.name=session"
```

### Health Checks

```yaml
labels:
  - "traefik.http.services.api.loadbalancer.healthcheck.path=/health"
  - "traefik.http.services.api.loadbalancer.healthcheck.interval=30s"
  - "traefik.http.services.api.loadbalancer.healthcheck.timeout=5s"
```

## 📊 Monitoring & Observability

### Metrics

```yaml
# Prometheus metrics
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.metrics.rule=Host(`metrics.example.com`)"
  - "traefik.http.routers.metrics.service=api@internal"
  - "traefik.http.routers.metrics.middlewares=auth"
```

### Tracing

```yaml
# Jaeger tracing
labels:
  - "traefik.http.middlewares.tracing.plugin=jaeger"
  - "traefik.http.middlewares.tracing.plugin.serviceName=traefik"
  - "traefik.http.middlewares.tracing.plugin.agentHost=jaeger"
  - "traefik.http.middlewares.tracing.plugin.agentPort=14268"
```

### Logging

```yaml
# Structured logging
labels:
  - "traefik.http.middlewares.logging.plugin=accesslog"
  - "traefik.http.middlewares.logging.plugin.filename=/var/log/traefik/access.log"
  - "traefik.http.middlewares.logging.plugin.format=json"
```

## 🔌 Plugins & Middleware

### Official Plugins

- **Rate Limit**: Traffic shaping and abuse prevention
- **Circuit Breaker**: Fault tolerance and resilience
- **Retry**: Automatic retry on failures
- **Strip Prefix**: Path prefix removal
- **Redirect**: HTTP redirects and rewrites

### Custom Middleware

```yaml
# CORS middleware
labels:
  - "traefik.http.middlewares.cors.headers.accesscontrolalloworiginlist=*"
  - "traefik.http.middlewares.cors.headers.accesscontrolallowmethods=GET,POST,PUT,DELETE,OPTIONS"
  - "traefik.http.middlewares.cors.headers.accesscontrolallowheaders=Content-Type,Authorization"
```

## 🐛 Troubleshooting

### Common Issues

1. **Services Not Discovered**
   - Check Docker network configuration
   - Verify container labels are correct
   - Ensure Traefik can access Docker socket

2. **SSL Certificate Issues**
   - Verify DNS configuration
   - Check Let's Encrypt rate limits
   - Validate firewall settings for port 80/443

3. **Load Balancing Problems**
   - Check health check endpoints
   - Verify service ports are correct
   - Monitor container logs for errors

### Debug Commands

```bash
# Check Traefik configuration
docker exec traefik traefik healthcheck

# View discovered services
curl http://localhost:8080/api/http/services

# Check logs
docker logs traefik
```

## 📚 Additional Resources

- [Traefik Documentation](https://doc.traefik.io/traefik/)
- [Traefik Plugins](https://plugins.traefik.io/)
- [Docker Labels](https://docs.docker.com/config/labels-custom-metadata/)
- [Let's Encrypt](https://letsencrypt.org/)

## 🤝 Contributing

When modifying this container:

1. Test configuration changes with multiple providers
2. Validate SSL/TLS certificate generation
3. Test load balancing with multiple backend instances
4. Update middleware configurations for new requirements
5. Monitor performance impact of configuration changes
