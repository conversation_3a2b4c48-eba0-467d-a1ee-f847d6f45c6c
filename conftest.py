# conftest.py - Pytest configuration for proper module imports
"""
Pytest configuration file that sets up the Python path correctly
for all tests to import from src.models and other modules.

This file is automatically loaded by pytest and ensures that all
tests can import from the ai-orchestrator source code.
"""

# Add the project's source directory to the Python path before pytest collects tests.
# This ensures imports like `from src.models import ...` work during test collection.
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Load .env file before anything else
load_dotenv()

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), 'containers/ai-orchestrator/src'))
if src_path not in sys.path:
    sys.path.insert(0, src_path)

def pytest_configure(config):
    """Configure pytest with proper Python paths."""

    # Get the project root directory
    project_root = Path(__file__).parent

    # Define paths
    ai_orchestrator_path = project_root / "containers" / "ai-orchestrator"
    src_path = ai_orchestrator_path / "src"

    # Add paths to sys.path for imports
    paths_to_add = [
        str(src_path),              # Allows 'from src.models import ...'
        str(ai_orchestrator_path),  # Allows container-specific imports
        str(project_root),          # Allows test utilities
    ]

    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)

    # Set default environment variables for testing.
    # These can be overridden by a local .env file.
    os.environ.setdefault('ENVIRONMENT', 'testing')
    os.environ.setdefault('USE_SUPABASE', 'false')
    os.environ.setdefault('ENABLE_ROLE_CONFIG_INIT', 'false')
    os.environ.setdefault('ENABLE_WEBSOCKET_CHAT', 'false')
    os.environ.setdefault('DATABASE_URL', 'sqlite:///./test_jules.db') # Use in-memory sqlite for tests
    os.environ.setdefault('REDIS_URL', 'redis://localhost:6379')
    os.environ.setdefault('CORS_ORIGINS', '["http://localhost:3000", "http://localhost:8080"]')
    os.environ.setdefault('OLLAMA_BASE_URL', 'http://localhost:11434')
    os.environ.setdefault('POSTGRES_PASSWORD', 'test_password')
    os.environ.setdefault('JWT_SECRET', 'test_jwt_secret_key_for_testing_only_32_chars_minimum')
    os.environ.setdefault('SUPABASE_URL', 'https://test.supabase.co')
    os.environ.setdefault('SUPABASE_KEY', 'test_supabase_key')
    os.environ.setdefault('SUPABASE_ANON_KEY', 'test_supabase_anon_key')
    os.environ.setdefault('SUPABASE_SERVICE_KEY', 'test_supabase_service_key')
    os.environ.setdefault('OPENROUTER_API_KEY', 'mock_openrouter_key_for_tests')
    os.environ.setdefault('OPENAI_API_KEY', 'mock_openai_key_for_tests')
    os.environ.setdefault('ANTHROPIC_API_KEY', 'mock_anthropic_key_for_tests')
    os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
    os.environ.setdefault('PYTHONLEGACYWINDOWSSTDIO', '1')

# Pytest fixtures can be added here if needed
import pytest

@pytest.fixture(scope="session")
def test_environment():
    """Fixture that ensures test environment is properly set up."""
    # Verify that critical paths exist
    project_root = Path(__file__).parent
    ai_orchestrator_path = project_root / "containers" / "ai-orchestrator"
    src_path = ai_orchestrator_path / "src"

    assert ai_orchestrator_path.exists(), f"AI Orchestrator path not found: {ai_orchestrator_path}"
    assert src_path.exists(), f"Source path not found: {src_path}"

    # Verify that we can import critical modules
    try:
        import src.models  # noqa: F401
        import src.agents  # noqa: F401
    except ImportError as e:
        pytest.fail(f"Failed to import critical modules: {e}")

    return {
        "project_root": project_root,
        "ai_orchestrator_path": ai_orchestrator_path,
        "src_path": src_path
    }


# -----------------------------
# Test shims and compatibility
# -----------------------------

@pytest.fixture(autouse=True)
def env_and_monkeypatch_shims(monkeypatch):
    """Session-wide fixture to normalize environment and provide monkeypatch helper.

    - Ensure ENVIRONMENT is set to `testing` (accept 'test' alias)
    - Monkeypatch `monkeypatch.setenv` to accept a `raising` kwarg for compatibility.
    """
    import os

    # Normalize ENVIRONMENT aliases
    env = os.environ.get('ENVIRONMENT') or os.environ.get('ENV') or 'testing'
    if env.lower() == 'test':
        env = 'testing'
    os.environ['ENVIRONMENT'] = env
    monkeypatch.setenv('ENVIRONMENT', env)

    # Patch monkeypatch.setenv to be compatible with old tests that use `raising`
    original_setenv = monkeypatch.setenv

    def setenv_compat(name, value, raising=True):
        """A wrapper for monkeypatch.setenv that ignores the `raising` kwarg."""
        original_setenv(name, str(value))

    monkeypatch.setenv = setenv_compat

    return monkeypatch


@pytest.fixture
def architect_agent():
    """Provides a test-only instance of the ArchitectAgent."""
    from tests.mocks.agent_mock import TestArchitectAgent
    return TestArchitectAgent()
