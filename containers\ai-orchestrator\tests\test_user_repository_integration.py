# Project: AI Coding Agent
# Purpose: Integration tests for UserRepository and user API endpoints
# Author: AI Coding Agent Team

"""
Integration tests for UserRepository and User API endpoints.

This module provides comprehensive tests for the complete user management system,
including the repository pattern, API endpoints, and authentication integration.
"""

import pytest
from datetime import datetime, timezone
from uuid import uuid4
from typing import Generator
import json

# FastAPI testing
from fastapi.testclient import TestClient

# SQLAlchemy testing
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Internal imports
from src.models.database import Base, get_db
from src.repository.user_repository import UserRepository
from src.schemas.user_schemas import (
    SupabaseUser,
    UserProfileUpdateSchema
)
from src.router.user_router import router as user_router

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"

# Create test engine
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Test fixtures
@pytest.fixture
def db_session() -> Generator:
    """Create test database session."""
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)


@pytest.fixture
def user_repository() -> UserRepository:
    """Create UserRepository instance for testing."""
    return UserRepository()


@pytest.fixture
def sample_supabase_user() -> SupabaseUser:
    """Create sample Supabase user data for testing."""
    return SupabaseUser(
        id=uuid4(),
        email="<EMAIL>",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        email_confirmed_at=datetime.now(timezone.utc),
        user_metadata={
            "full_name": "Test User",
            "username": "testuser"
        },
        app_metadata={}
    )


@pytest.fixture
def sample_admin_user() -> SupabaseUser:
    """Create sample admin user for testing."""
    return SupabaseUser(
        id=uuid4(),
        email="<EMAIL>",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        email_confirmed_at=datetime.now(timezone.utc),
        user_metadata={
            "full_name": "Admin User",
            "username": "adminuser"
        },
        app_metadata={"role": "admin"}
    )


# ==================================================================================
# REPOSITORY TESTS
# ==================================================================================

class TestUserRepository:
    """Test suite for UserRepository functionality."""

    def test_get_or_create_user_from_supabase_new_user(
        self,
        db_session,
        user_repository: UserRepository,
        sample_supabase_user: SupabaseUser
    ):
        """Test creating new user from Supabase data."""
        result = user_repository.get_or_create_user_from_supabase(
            db_session,
            sample_supabase_user
        )

        # Verify user was created
        assert result.created_from_supabase is True
        assert result.sync_status == "created_new"
        assert result.user.email == sample_supabase_user.email
        assert result.user.username == sample_supabase_user.username

        # Verify user exists in database
        db_user = user_repository.get_by_email(db_session, sample_supabase_user.email)
        assert db_user is not None
        assert str(db_user.supabase_user_id) == str(sample_supabase_user.id)

    def test_get_or_create_user_from_supabase_existing_user(
        self,
        db_session,
        user_repository: UserRepository,
        sample_supabase_user: SupabaseUser
    ):
        """Test getting existing user from Supabase data."""
        # First call creates user
        result1 = user_repository.get_or_create_user_from_supabase(
            db_session,
            sample_supabase_user
        )

        # Second call should find existing user
        result2 = user_repository.get_or_create_user_from_supabase(
            db_session,
            sample_supabase_user
        )

        # Verify existing user was found
        assert result2.created_from_supabase is False
        assert result2.sync_status == "found_existing"
        assert result2.user.id == result1.user.id
        assert result2.user.email == sample_supabase_user.email

    def test_update_profile(
        self,
        db_session,
        user_repository: UserRepository,
        sample_supabase_user: SupabaseUser
    ):
        """Test updating user profile."""
        # Create user first
        result = user_repository.get_or_create_user_from_supabase(
            db_session,
            sample_supabase_user
        )

        # Get the actual User model
        user_model = user_repository.get_by_id(db_session, result.user.id)

        # Update profile
        profile_update = UserProfileUpdateSchema(
            full_name="Updated Name",
            preferences={"theme": "dark", "language": "en"}
        )

        updated_user = user_repository.update_profile(
            db_session,
            user_model,
            profile_update
        )

        # Verify updates
        assert updated_user.full_name == "Updated Name"

        # Verify profile data contains preferences
        profile_data = json.loads(updated_user.profile_data)
        assert profile_data["preferences"]["theme"] == "dark"
        assert profile_data["preferences"]["language"] == "en"

    def test_get_users_paginated(
        self,
        db_session,
        user_repository: UserRepository
    ):
        """Test paginated user listing."""
        # Create multiple test users
        users_data = [
            SupabaseUser(
                id=uuid4(),
                email=f"user{i}@example.com",
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                user_metadata={"full_name": f"User {i}", "username": f"user{i}"},
                app_metadata={}
            )
            for i in range(5)
        ]

        # Create users in repository
        for user_data in users_data:
            user_repository.get_or_create_user_from_supabase(db_session, user_data)

        # Test pagination
        result = user_repository.get_users_paginated(
            db_session,
            page=1,
            page_size=3
        )

        # Verify pagination
        assert len(result.users) == 3
        assert result.total == 5
        assert result.page == 1
        assert result.total_pages == 2
        assert result.has_next is True
        assert result.has_previous is False

        # Test search functionality
        search_result = user_repository.get_users_paginated(
            db_session,
            page=1,
            page_size=10,
            search="user1"
        )

        assert len(search_result.users) == 1
        assert search_result.users[0].email == "<EMAIL>"


# ==================================================================================
# API ENDPOINT TESTS
# ==================================================================================

@pytest.fixture
def test_app():
    """Create test FastAPI application."""
    from fastapi import FastAPI

    app = FastAPI()
    app.include_router(user_router, prefix="/api/v2")

    # Override database dependency
    def override_get_db():
        db = TestingSessionLocal()
        try:
            yield db
        finally:
            db.close()

    app.dependency_overrides[get_db] = override_get_db

    # Set up test database
    Base.metadata.create_all(bind=engine)

    yield app

    # Clean up
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client(test_app):
    """Create test client."""
    return TestClient(test_app)


class TestUserAPI:
    """Test suite for User API endpoints."""

    def test_get_current_user_profile_success(self, client, sample_supabase_user):
        """Test getting current user profile."""
        # Override auth dependency to return test user
        from src.router.user_router import get_current_user

        async def mock_get_current_user():
            return sample_supabase_user

        client.app.dependency_overrides[get_current_user] = mock_get_current_user

        response = client.get("/api/v2/users/me")

        assert response.status_code == 200
        data = response.json()
        assert data["email"] == sample_supabase_user.email
        assert data["username"] == sample_supabase_user.username

    def test_update_current_user_profile_success(self, client, sample_supabase_user):
        """Test updating current user profile."""
        # Override auth dependency
        from src.router.user_router import get_current_user

        async def mock_get_current_user():
            return sample_supabase_user

        client.app.dependency_overrides[get_current_user] = mock_get_current_user

        # First get profile to create user
        client.get("/api/v2/users/me")

        # Then update profile
        update_data = {
            "full_name": "Updated Test User",
            "preferences": {"theme": "dark"}
        }

        response = client.put("/api/v2/users/me", json=update_data)

        assert response.status_code == 200
        data = response.json()
        assert data["full_name"] == "Updated Test User"

    def test_list_users_admin_success(self, client, sample_admin_user):
        """Test listing users as admin."""
        from src.router.user_router import require_admin

        async def mock_require_admin():
            return sample_admin_user

        client.app.dependency_overrides[require_admin] = mock_require_admin

        response = client.get("/api/v2/users/")

        assert response.status_code == 200
        data = response.json()
        assert "users" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data

    def test_list_users_pagination(self, client, sample_admin_user):
        """Test user listing with pagination parameters."""
        from src.router.user_router import require_admin

        async def mock_require_admin():
            return sample_admin_user

        client.app.dependency_overrides[require_admin] = mock_require_admin

        response = client.get("/api/v2/users/?page=1&page_size=5&search=test")

        assert response.status_code == 200
        data = response.json()
        assert data["page"] == 1
        assert data["page_size"] == 5

    def test_health_check(self, client):
        """Test user service health check."""
        response = client.get("/api/v2/users/health")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] in ["healthy", "unhealthy"]
        assert "service" in data


# ==================================================================================
# INTEGRATION TESTS
# ==================================================================================

class TestUserManagementIntegration:
    """Integration tests for complete user management flow."""

    def test_complete_user_lifecycle(
        self,
        db_session,
        user_repository: UserRepository,
        sample_supabase_user: SupabaseUser
    ):
        """Test complete user lifecycle: create, retrieve, update, paginate."""

        # 1. Create user from Supabase
        create_result = user_repository.get_or_create_user_from_supabase(
            db_session,
            sample_supabase_user
        )

        assert create_result.created_from_supabase is True
        created_user = create_result.user

        # 2. Retrieve user by various methods
        user_by_id = user_repository.get_by_id(db_session, created_user.id)
        user_by_email = user_repository.get_by_email(db_session, created_user.email)
        user_by_supabase_id = user_repository.get_by_supabase_id(
            db_session,
            sample_supabase_user.id
        )

        assert user_by_id.id == created_user.id
        assert user_by_email.email == created_user.email
        assert str(user_by_supabase_id.supabase_user_id) == str(sample_supabase_user.id)

        # 3. Update user profile
        profile_update = UserProfileUpdateSchema(
            full_name="Updated Full Name",
            preferences={"theme": "light", "notifications": True}
        )

        updated_user = user_repository.update_profile(
            db_session,
            user_by_id,
            profile_update
        )

        assert updated_user.full_name == "Updated Full Name"

        # 4. Verify in paginated results
        paginated_result = user_repository.get_users_paginated(
            db_session,
            page=1,
            page_size=10
        )

        assert paginated_result.total >= 1
        assert any(user.id == created_user.id for user in paginated_result.users)

        # 5. Test idempotency - second call should find existing user
        second_result = user_repository.get_or_create_user_from_supabase(
            db_session,
            sample_supabase_user
        )

        assert second_result.created_from_supabase is False
        assert second_result.user.id == created_user.id


# ==================================================================================
# PERFORMANCE TESTS
# ==================================================================================

class TestUserRepositoryPerformance:
    """Performance tests for UserRepository operations."""

    def test_bulk_user_creation_performance(
        self,
        db_session,
        user_repository: UserRepository
    ):
        """Test performance of creating multiple users."""
        import time

        # Generate test data
        users_data = [
            SupabaseUser(
                id=uuid4(),
                email=f"perftest{i}@example.com",
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                user_metadata={"full_name": f"Perf Test User {i}"},
                app_metadata={}
            )
            for i in range(100)
        ]

        # Measure creation time
        start_time = time.time()

        for user_data in users_data:
            user_repository.get_or_create_user_from_supabase(db_session, user_data)

        end_time = time.time()
        creation_time = end_time - start_time

        # Verify performance (should create 100 users in under 5 seconds)
        assert creation_time < 5.0
        print(f"Created 100 users in {creation_time:.2f} seconds")

        # Measure pagination performance
        start_time = time.time()

        result = user_repository.get_users_paginated(
            db_session,
            page=1,
            page_size=50
        )

        end_time = time.time()
        pagination_time = end_time - start_time

        # Verify pagination performance (should be under 1 second)
        assert pagination_time < 1.0
        assert result.total >= 100
        print(f"Paginated 100+ users in {pagination_time:.3f} seconds")


# ==================================================================================
# ERROR HANDLING TESTS
# ==================================================================================

class TestUserRepositoryErrorHandling:
    """Test error handling in UserRepository."""

    def test_duplicate_username_handling(
        self,
        db_session,
        user_repository: UserRepository
    ):
        """Test handling of duplicate usernames."""
        # Create first user
        user1_data = SupabaseUser(
            id=uuid4(),
            email="<EMAIL>",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            user_metadata={"username": "testuser", "full_name": "User 1"},
            app_metadata={}
        )

        # Create second user with same username
        user2_data = SupabaseUser(
            id=uuid4(),
            email="<EMAIL>",
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            user_metadata={"username": "testuser", "full_name": "User 2"},
            app_metadata={}
        )

        # Create first user
        result1 = user_repository.get_or_create_user_from_supabase(db_session, user1_data)

        # Create second user - should get unique username
        result2 = user_repository.get_or_create_user_from_supabase(db_session, user2_data)

        # Verify usernames are different
        assert result1.user.username != result2.user.username
        assert result1.user.username == "testuser"
        assert result2.user.username.startswith("testuser")
        assert result2.user.username != "testuser"


# Run tests
if __name__ == "__main__":
    pytest.main([__file__, "-v"])