# Project: AI Coding Agent - Unit Tests
# Purpose: Comprehensive unit tests for validation rules engine

import pytest
from unittest.mock import AsyncMock, patch

from src.validation.validation_rules import ValidationRuleEngine
from src.models.validation_models import (
    Task,
    TaskType,
    AgentType,
    ValidationResult,
    Step,
    Phase,
)


class TestValidationRuleEngine:
    """Comprehensive test suite for ValidationRuleEngine"""

    @pytest.fixture
    def engine(self):
        """Create a ValidationRuleEngine instance for testing"""
        return ValidationRuleEngine()

    @pytest.fixture
    def sample_task(self):
        """Create a sample task for testing"""
        return Task(
            title="Test Task",
            description="A test task",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND,
            expected_files=["src/component.tsx"],
            code_files=["src/component.tsx"],
            test_command="npm test",
            integration_checks=["http://localhost:3000/health"]
        )

    @pytest.fixture
    def sample_step(self):
        """Create a sample step for testing"""

        task = Task(
            title="Test Task",
            description="A test task",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND,
            code_files=["src/component.tsx"]
        )

        return Step(
            title="Test Step",
            description="A test step",
            tasks=[task]
        )

    @pytest.fixture
    def sample_phase(self):
        """Create a sample phase for testing"""

        task = Task(
            title="Test Task",
            description="A test task",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND,
            code_files=["src/component.tsx"]
        )

        step = Step(
            title="Test Step",
            description="A test step",
            tasks=[task]
        )

        return Phase(
            title="Test Phase",
            description="A test phase",
            steps=[step]
        )

    # Basic validation rule tests

    @pytest.mark.asyncio
    async def test_validate_task_success(self, engine, sample_task):
        """Test successful task validation"""
        # Mock rule creation and validation
        mock_rule = AsyncMock()
        mock_rule.is_applicable.return_value = True
        mock_rule.validate = AsyncMock(return_value=ValidationResult.success("Mock validation passed"))

        with patch.object(engine, '_create_rule_instance', return_value=mock_rule):
            result = await engine.validate_task(sample_task)

            assert result.is_valid
            assert result.error is None
            # Verify that rules were created and validated
            mock_rule.validate.assert_called()

    @pytest.mark.asyncio
    async def test_validate_task_failure(self, engine, sample_task):
        """Test task validation with failures"""
        # Mock rule creation and validation with failure
        mock_rule = AsyncMock()
        mock_rule.is_applicable.return_value = True
        mock_rule.validate = AsyncMock(return_value=ValidationResult.failure("Mock validation failed"))

        with patch.object(engine, '_create_rule_instance', return_value=mock_rule):
            result = await engine.validate_task(sample_task)

            assert not result.is_valid
            assert result.error is not None
            assert "Mock validation failed" in result.error

    @pytest.mark.asyncio
    async def test_validate_task_empty_requirements(self, engine):
        """Test validation of task with no requirements"""
        empty_task = Task(
            title="Empty Task",
            description="Task with no validation requirements",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND
        )

        result = await engine.validate_task(empty_task)

        assert result.is_valid
        assert "validation checks" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_step_success(self, engine, sample_step):
        """Test successful step validation"""
        # Mock rule creation and validation
        mock_rule = AsyncMock()
        mock_rule.is_applicable.return_value = True
        mock_rule.validate = AsyncMock(return_value=ValidationResult.success("Mock validation passed"))

        with patch.object(engine, '_create_rule_instance', return_value=mock_rule):
            result = await engine.validate_step(sample_step)

            assert result.is_valid
            assert result.error is None

    @pytest.mark.asyncio
    async def test_validate_step_failure(self, engine, sample_step):
        """Test step validation with failures"""
        # Mock rule creation and validation with failure
        mock_rule = AsyncMock()
        mock_rule.is_applicable.return_value = True
        mock_rule.validate = AsyncMock(return_value=ValidationResult.failure("Mock validation failed"))

        with patch.object(engine, '_create_rule_instance', return_value=mock_rule):
            result = await engine.validate_step(sample_step)

            assert not result.is_valid
            assert result.error is not None

    @pytest.mark.asyncio
    async def test_validate_empty_step(self, engine):
        """Test validating step with no tasks"""

        empty_step = Step(
            title="Empty Step",
            description="Step with no tasks",
            tasks=[]
        )

        result = await engine.validate_step(empty_step)

        assert result.is_valid
        assert "validation checks" in result.details.lower()

    @pytest.mark.asyncio
    async def test_validate_phase_success(self, engine, sample_phase):
        """Test successful phase validation"""
        # Mock rule creation and validation
        mock_rule = AsyncMock()
        mock_rule.is_applicable.return_value = True
        mock_rule.validate = AsyncMock(return_value=ValidationResult.success("Mock validation passed"))

        with patch.object(engine, '_create_rule_instance', return_value=mock_rule):
            result = await engine.validate_phase(sample_phase)

            assert result.is_valid
            assert result.error is None

    @pytest.mark.asyncio
    async def test_validate_phase_failure(self, engine, sample_phase):
        """Test phase validation with failures"""
        # Mock rule creation and validation with failure
        mock_rule = AsyncMock()
        mock_rule.is_applicable.return_value = True
        mock_rule.validate = AsyncMock(return_value=ValidationResult.failure("Mock validation failed"))

        with patch.object(engine, '_create_rule_instance', return_value=mock_rule):
            result = await engine.validate_phase(sample_phase)

            assert not result.is_valid
            assert result.error is not None

    @pytest.mark.asyncio
    async def test_validate_empty_phase(self, engine):
        """Test validating phase with no steps"""

        empty_phase = Phase(
            title="Empty Phase",
            description="Phase with no steps",
            steps=[]
        )

        result = await engine.validate_phase(empty_phase)

        assert result.is_valid
        assert "validation checks" in result.details.lower()
