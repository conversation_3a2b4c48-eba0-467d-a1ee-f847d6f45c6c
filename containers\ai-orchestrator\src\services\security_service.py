"""
Security Monitoring Service for Multi-Tenant Container Platform.

This module provides security monitoring, container behavior validation,
and anomaly detection for the AI Coding Agent platform.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import json
import logging
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum

import docker
from docker.models.containers import Container
from sqlalchemy.orm import Session

from src.models import Workspace, get_db
from src.models.workspace import WorkspaceStatus
from src.services.redis_service import get_redis_client

# Configure logging
logger = logging.getLogger(__name__)


class SecurityEventType(Enum):
    """Security event types."""
    RESOURCE_LIMIT_EXCEEDED = "resource_limit_exceeded"
    SUSPICIOUS_NETWORK_ACTIVITY = "suspicious_network_activity"
    CONTAINER_ESCAPE_ATTEMPT = "container_escape_attempt"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    MALICIOUS_PROCESS = "malicious_process"
    EXCESSIVE_RESOURCE_USAGE = "excessive_resource_usage"
    CONTAINER_CREATION_SPIKE = "container_creation_spike"
    UNUSUAL_API_BEHAVIOR = "unusual_api_behavior"


class SecurityLevel(Enum):
    """Security event severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityEvent:
    """Security event data structure."""
    event_type: SecurityEventType
    severity: SecurityLevel
    timestamp: datetime
    user_id: Optional[str]
    container_id: Optional[str]
    description: str
    details: Dict[str, Any]
    action_taken: Optional[str] = None


@dataclass
class ContainerMetrics:
    """Container resource metrics."""
    container_id: str
    cpu_usage: float
    memory_usage: int
    memory_limit: int
    network_rx: int
    network_tx: int
    disk_io_read: int
    disk_io_write: int
    process_count: int
    timestamp: datetime


class SecurityMonitoringService:
    """
    Service for monitoring container security and behavior.

    Monitors container resource usage, network activity, and processes
    to detect potential security threats and policy violations.
    """

    def __init__(self):
        """Initialize security monitoring service."""
        self.docker_client = docker.from_env()
        self.monitoring_enabled = os.getenv("SECURITY_MONITORING_ENABLED", "true").lower() == "true"
        self.alert_threshold_cpu = float(os.getenv("ALERT_THRESHOLD_CPU", "80.0"))
        self.alert_threshold_memory = float(os.getenv("ALERT_THRESHOLD_MEMORY", "90.0"))
        self.max_containers_per_user = int(os.getenv("MAX_USER_CONTAINERS", "10"))
        self.monitoring_interval = int(os.getenv("MONITORING_INTERVAL_SECONDS", "30"))

        # Security patterns
        self.suspicious_processes = [
            "nc", "netcat", "nmap", "wget", "curl", "ssh", "scp", "rsync",
            "python -m http.server", "php -S", "ruby -run", "perl -MHTTP::Server::Brick"
        ]

        self.dangerous_commands = [
            "docker", "kubectl", "sudo", "su", "chmod +s", "setuid",
            "/proc/sys/kernel", "/dev/kmem", "/dev/mem"
        ]

        # Event storage
        self.security_events: List[SecurityEvent] = []
        self.max_events = 1000

    async def start_monitoring(self) -> None:
        """Start continuous security monitoring."""
        if not self.monitoring_enabled:
            logger.info("Security monitoring is disabled")
            return

        logger.info("Starting security monitoring service")

        while True:
            try:
                await self.monitor_containers()
                await self.monitor_user_activity()
                await self.check_system_health()
                await asyncio.sleep(self.monitoring_interval)

            except Exception as e:
                logger.error(f"Security monitoring error: {e}")
                await asyncio.sleep(60)  # Longer sleep on error

    async def monitor_containers(self) -> None:
        """Monitor all user containers for security violations."""
        try:
            # Get all workspace containers
            containers = self.docker_client.containers.list(
                filters={"label": "workspace.type=code-server"}
            )

            for container in containers:
                await self.analyze_container_security(container)

        except Exception as e:
            logger.error(f"Container monitoring error: {e}")

    async def analyze_container_security(self, container: Container) -> None:
        """
        Analyze individual container for security violations.

        Args:
            container: Docker container to analyze
        """
        try:
            # Get container metrics
            metrics = await self.get_container_metrics(container)

            # Check resource usage
            await self.check_resource_violations(container, metrics)

            # Check running processes
            await self.check_suspicious_processes(container)

            # Check network activity
            await self.check_network_activity(container, metrics)

            # Check filesystem access patterns
            await self.check_filesystem_access(container)

        except Exception as e:
            logger.error(f"Container security analysis error for {container.id}: {e}")

    async def get_container_metrics(self, container: Container) -> ContainerMetrics:
        """
        Get comprehensive metrics for a container.

        Args:
            container: Docker container

        Returns:
            ContainerMetrics object
        """
        try:
            stats = container.stats(stream=False)

            # Calculate CPU usage percentage
            cpu_delta = stats["cpu_stats"]["cpu_usage"]["total_usage"] - \
                       stats["precpu_stats"]["cpu_usage"]["total_usage"]
            system_delta = stats["cpu_stats"]["system_cpu_usage"] - \
                          stats["precpu_stats"]["system_cpu_usage"]

            cpu_usage = 0.0
            if system_delta > 0:
                cpu_usage = (cpu_delta / system_delta) * 100.0

            # Memory usage
            memory_usage = stats["memory_stats"]["usage"]
            memory_limit = stats["memory_stats"]["limit"]

            # Network I/O
            network_rx = sum(net["rx_bytes"] for net in stats["networks"].values())
            network_tx = sum(net["tx_bytes"] for net in stats["networks"].values())

            # Disk I/O
            disk_stats = stats.get("blkio_stats", {}).get("io_service_bytes_recursive", [])
            disk_io_read = sum(item["value"] for item in disk_stats if item["op"] == "Read")
            disk_io_write = sum(item["value"] for item in disk_stats if item["op"] == "Write")

            # Process count (approximate)
            process_count = stats.get("pids_stats", {}).get("current", 0)

            return ContainerMetrics(
                container_id=container.id or "unknown",
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                memory_limit=memory_limit,
                network_rx=network_rx,
                network_tx=network_tx,
                disk_io_read=disk_io_read,
                disk_io_write=disk_io_write,
                process_count=process_count,
                timestamp=datetime.now(timezone.utc)
            )

        except Exception as e:
            logger.error(f"Failed to get metrics for container {container.id}: {e}")
            raise

    async def check_resource_violations(
        self,
        container: Container,
        metrics: ContainerMetrics
    ) -> None:
        """
        Check for resource usage violations.

        Args:
            container: Docker container
            metrics: Container metrics
        """
        user_id = container.labels.get("workspace.user_id", "unknown")

        # CPU usage check
        if metrics.cpu_usage > self.alert_threshold_cpu:
            await self.create_security_event(
                SecurityEvent(
                    event_type=SecurityEventType.EXCESSIVE_RESOURCE_USAGE,
                    severity=SecurityLevel.MEDIUM if metrics.cpu_usage < 95 else SecurityLevel.HIGH,
                    timestamp=metrics.timestamp,
                    user_id=user_id,
                    container_id=container.id,
                    description=f"High CPU usage detected: {metrics.cpu_usage:.1f}%",
                    details={
                        "cpu_usage": metrics.cpu_usage,
                        "threshold": self.alert_threshold_cpu,
                        "container_name": container.name
                    }
                )
            )

        # Memory usage check
        memory_percent = (metrics.memory_usage / metrics.memory_limit) * 100
        if memory_percent > self.alert_threshold_memory:
            await self.create_security_event(
                SecurityEvent(
                    event_type=SecurityEventType.EXCESSIVE_RESOURCE_USAGE,
                    severity=SecurityLevel.MEDIUM if memory_percent < 95 else SecurityLevel.HIGH,
                    timestamp=metrics.timestamp,
                    user_id=user_id,
                    container_id=container.id,
                    description=f"High memory usage detected: {memory_percent:.1f}%",
                    details={
                        "memory_usage": metrics.memory_usage,
                        "memory_limit": metrics.memory_limit,
                        "memory_percent": memory_percent,
                        "threshold": self.alert_threshold_memory,
                        "container_name": container.name
                    }
                )
            )

    async def check_suspicious_processes(self, container: Container) -> None:
        """
        Check for suspicious processes running in container.

        Args:
            container: Docker container
        """
        try:
            # Get running processes
            processes = container.top()

            if not processes or not isinstance(processes, dict) or "Processes" not in processes:
                return

            user_id = container.labels.get("workspace.user_id", "unknown")
            process_list = processes.get("Processes", [])

            if not isinstance(process_list, list):
                return

            for process in process_list:
                if len(process) < 8:  # Ensure we have enough fields
                    continue

                command = " ".join(process[7:])  # Command is typically in fields 7+

                # Check for suspicious processes
                for suspicious_proc in self.suspicious_processes:
                    if suspicious_proc in command.lower():
                        await self.create_security_event(
                            SecurityEvent(
                                event_type=SecurityEventType.MALICIOUS_PROCESS,
                                severity=SecurityLevel.HIGH,
                                timestamp=datetime.now(timezone.utc),
                                user_id=user_id,
                                container_id=container.id,
                                description=f"Suspicious process detected: {suspicious_proc}",
                                details={
                                    "process_command": command,
                                    "suspicious_pattern": suspicious_proc,
                                    "container_name": container.name,
                                    "action": "monitoring"
                                },
                                action_taken="Process flagged for review"
                            )
                        )

                # Check for dangerous commands
                for dangerous_cmd in self.dangerous_commands:
                    if dangerous_cmd in command.lower():
                        await self.create_security_event(
                            SecurityEvent(
                                event_type=SecurityEventType.PRIVILEGE_ESCALATION,
                                severity=SecurityLevel.CRITICAL,
                                timestamp=datetime.now(timezone.utc),
                                user_id=user_id,
                                container_id=container.id,
                                description=f"Potentially dangerous command detected: {dangerous_cmd}",
                                details={
                                    "process_command": command,
                                    "dangerous_pattern": dangerous_cmd,
                                    "container_name": container.name,
                                    "action": "immediate_review_required"
                                },
                                action_taken="Security team notified"
                            )
                        )

        except Exception as e:
            logger.error(f"Process monitoring error for container {container.id}: {e}")

    async def check_network_activity(
        self,
        container: Container,
        metrics: ContainerMetrics
    ) -> None:
        """
        Check for suspicious network activity.

        Args:
            container: Docker container
            metrics: Container metrics
        """
        # Check for excessive network usage (basic check)
        total_network = metrics.network_rx + metrics.network_tx
        network_threshold = 100 * 1024 * 1024  # 100MB threshold

        if total_network > network_threshold:
            user_id = container.labels.get("workspace.user_id", "unknown")

            await self.create_security_event(
                SecurityEvent(
                    event_type=SecurityEventType.SUSPICIOUS_NETWORK_ACTIVITY,
                    severity=SecurityLevel.MEDIUM,
                    timestamp=metrics.timestamp,
                    user_id=user_id,
                    container_id=container.id,
                    description=f"High network usage detected: {total_network / (1024*1024):.1f} MB",
                    details={
                        "network_rx": metrics.network_rx,
                        "network_tx": metrics.network_tx,
                        "total_bytes": total_network,
                        "threshold": network_threshold,
                        "container_name": container.name
                    }
                )
            )

    async def check_filesystem_access(self, container: Container) -> None:
        """
        Check for suspicious filesystem access patterns.

        Args:
            container: Docker container
        """
        # This is a placeholder for more advanced filesystem monitoring
        # In a production environment, you might use:
        # - auditd logs
        # - inotify watches
        # - Security modules like AppArmor/SELinux
        pass

    async def monitor_user_activity(self) -> None:
        """Monitor user activity patterns for anomalies."""
        try:
            db = next(get_db())

            # Check for container creation spikes
            await self.check_container_creation_patterns(db)

            # Check user resource limits
            await self.check_user_container_limits(db)

        except Exception as e:
            logger.error(f"User activity monitoring error: {e}")

    async def check_container_creation_patterns(self, db: Session) -> None:
        """
        Check for unusual container creation patterns.

        Args:
            db: Database session
        """
        # Get containers created in the last hour
        one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)

        recent_workspaces = db.query(Workspace).filter(
            Workspace.created_at >= one_hour_ago
        ).all()

        # Group by user
        user_creation_counts = {}
        for workspace in recent_workspaces:
            user_id = workspace.user_id
            user_creation_counts[user_id] = user_creation_counts.get(user_id, 0) + 1

        # Alert on excessive container creation
        for user_id, count in user_creation_counts.items():
            if count > 5:  # More than 5 containers in 1 hour
                await self.create_security_event(
                    SecurityEvent(
                        event_type=SecurityEventType.CONTAINER_CREATION_SPIKE,
                        severity=SecurityLevel.MEDIUM,
                        timestamp=datetime.now(timezone.utc),
                        user_id=user_id,
                        container_id=None,
                        description=f"Excessive container creation: {count} containers in 1 hour",
                        details={
                            "container_count": count,
                            "time_window": "1 hour",
                            "threshold": 5
                        }
                    )
                )

    async def check_user_container_limits(self, db: Session) -> None:
        """
        Check if users are exceeding container limits.

        Args:
            db: Database session
        """
        # Get container counts per user
        from sqlalchemy import func

        # Define status values for query
        running_statuses = [WorkspaceStatus.RUNNING.value, WorkspaceStatus.CREATING.value]

        # Query user container counts - type: ignore to work around SQLAlchemy type checker issues
        user_counts = db.query(
            Workspace.user_id,
            func.count(Workspace.id).label("container_count")
        ).filter(
            Workspace.status.in_(running_statuses)  # type: ignore[attr-defined]
        ).group_by(Workspace.user_id).all()

        for user_id, count in user_counts:
            if count > self.max_containers_per_user:
                await self.create_security_event(
                    SecurityEvent(
                        event_type=SecurityEventType.RESOURCE_LIMIT_EXCEEDED,
                        severity=SecurityLevel.HIGH,
                        timestamp=datetime.now(timezone.utc),
                        user_id=user_id,
                        container_id=None,
                        description=f"User exceeded container limit: {count}/{self.max_containers_per_user}",
                        details={
                            "container_count": count,
                            "limit": self.max_containers_per_user,
                            "action_required": "Review user access"
                        }
                    )
                )

    async def check_system_health(self) -> None:
        """Check overall system health and security posture."""
        try:
            # Check Docker daemon health
            docker_info = self.docker_client.info()

            # Alert on high container density
            total_containers = docker_info.get("Containers", 0)
            running_containers = docker_info.get("ContainersRunning", 0)

            if total_containers > 100:  # Arbitrary threshold
                await self.create_security_event(
                    SecurityEvent(
                        event_type=SecurityEventType.EXCESSIVE_RESOURCE_USAGE,
                        severity=SecurityLevel.MEDIUM,
                        timestamp=datetime.now(timezone.utc),
                        user_id=None,
                        container_id=None,
                        description=f"High container density: {total_containers} total containers",
                        details={
                            "total_containers": total_containers,
                            "running_containers": running_containers,
                            "docker_version": docker_info.get("ServerVersion")
                        }
                    )
                )

        except Exception as e:
            logger.error(f"System health check error: {e}")

    async def create_security_event(self, event: SecurityEvent) -> None:
        """
        Create and store a security event.

        Args:
            event: Security event to store
        """
        try:
            # Add to local storage
            self.security_events.append(event)

            # Trim old events
            if len(self.security_events) > self.max_events:
                self.security_events = self.security_events[-self.max_events:]

            # Store in Redis for real-time access
            redis_client = await get_redis_client()
            event_data = {
                "event_type": event.event_type.value,
                "severity": event.severity.value,
                "timestamp": event.timestamp.isoformat(),
                "user_id": event.user_id,
                "container_id": event.container_id,
                "description": event.description,
                "details": event.details,
                "action_taken": event.action_taken
            }

            # Store with expiration (7 days)
            await redis_client.lpush(
                "security_events",
                json.dumps(event_data)
            )
            await redis_client.expire("security_events", 7 * 24 * 3600)

            # Log based on severity
            if event.severity in [SecurityLevel.HIGH, SecurityLevel.CRITICAL]:
                logger.warning(f"SECURITY ALERT [{event.severity.value.upper()}]: {event.description}")
            else:
                logger.info(f"Security event [{event.severity.value}]: {event.description}")

        except Exception as e:
            logger.error(f"Failed to create security event: {e}")

    async def get_recent_events(
        self,
        hours: int = 24,
        severity: Optional[SecurityLevel] = None
    ) -> List[Dict[str, Any]]:
        """
        Get recent security events.

        Args:
            hours: Number of hours to look back
            severity: Filter by severity level

        Returns:
            List of security events
        """
        try:
            redis_client = await get_redis_client()
            events_data = await redis_client.lrange("security_events", 0, -1)

            events = []
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)

            for event_json in events_data:
                try:
                    event_data = json.loads(event_json)
                    event_time = datetime.fromisoformat(event_data["timestamp"])

                    if event_time >= cutoff_time:
                        if severity is None or event_data["severity"] == severity.value:
                            events.append(event_data)

                except Exception as e:
                    logger.error(f"Failed to parse security event: {e}")
                    continue

            return sorted(events, key=lambda x: x["timestamp"], reverse=True)

        except Exception as e:
            logger.error(f"Failed to get recent events: {e}")
            return []

    async def get_security_metrics(self) -> Dict[str, Any]:
        """
        Get security monitoring metrics.

        Returns:
            Security metrics dictionary
        """
        try:
            recent_events = await self.get_recent_events(hours=24)

            # Count events by type and severity
            event_counts = {}
            severity_counts = {}

            for event in recent_events:
                event_type = event["event_type"]
                severity = event["severity"]

                event_counts[event_type] = event_counts.get(event_type, 0) + 1
                severity_counts[severity] = severity_counts.get(severity, 0) + 1

            return {
                "monitoring_enabled": self.monitoring_enabled,
                "monitoring_interval": self.monitoring_interval,
                "events_24h": len(recent_events),
                "event_types": event_counts,
                "severity_breakdown": severity_counts,
                "alert_thresholds": {
                    "cpu_percent": self.alert_threshold_cpu,
                    "memory_percent": self.alert_threshold_memory,
                    "max_containers_per_user": self.max_containers_per_user
                },
                "last_check": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get security metrics: {e}")
            return {"error": str(e)}


# Global security monitoring service instance
_security_service: Optional[SecurityMonitoringService] = None


def get_security_monitoring_service() -> SecurityMonitoringService:
    """
    Get security monitoring service singleton.

    Returns:
        SecurityMonitoringService instance
    """
    global _security_service
    if _security_service is None:
        _security_service = SecurityMonitoringService()
    return _security_service