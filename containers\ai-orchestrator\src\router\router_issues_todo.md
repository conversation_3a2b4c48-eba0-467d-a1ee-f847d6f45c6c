# Router Issues TODO List

Below are the issues identified in each router module, with recommended fixes. No changes have been applied.

## approval_router.py
- Issue: No authentication/authorization on any endpoint.
  - Fix: Add Depends(get_current_user) and role/ownership checks where appropriate.
- Issue: Global ApprovalManager with ad-hoc `initialized` attribute; not DI-friendly.
  - Fix: Provide a proper dependency factory that constructs/initializes per-app lifecycle (startup event) or uses a singleton with an explicit `initialize()` guard.
- Issue: `BackgroundTasks` injected but unused in multiple endpoints.
  - Fix: Remove the parameter or use it for async notifications/audit logging.
- Issue: Mixed sync/async usage on manager methods (e.g., `get_pending_approvals` called without await, while others are awaited).
  - Fix: Make repository/service methods consistently async or sync and call accordingly.
- Issue: Returning internal domain models directly as response models may expose fields unintentionally.
  - Fix: Use dedicated Pydantic response schemas tailored for API exposure.

## auth_router.py
- Issue: `HTTPBearer` imported but unused.
  - Fix: Remove unused import or integrate it for route protection if intended.
- Issue: No rate limiting on auth endpoints.
  - Fix: Add lightweight rate limiting (e.g., per-IP) to /login and /register.
- Issue: Error handling returns raw exception messages to clients.
  - Fix: Normalize error responses, avoid leaking internal details.
- Issue: No email verification / weak session handling shown.
  - Fix: Enforce email verification path and communicate status accordingly.

## deployment_router.py
- Issue: Async endpoints calling synchronous SQLAlchemy ORM directly.
  - Fix: Use FastAPI dependency that yields a sync Session inside a threadpool (run_in_threadpool) or migrate to SQLAlchemy async engine/session.
- Issue: Returns ORM instances directly (though response_model with from_attributes mitigates); still risks tight coupling.
  - Fix: Map to explicit Pydantic response models everywhere.
- Issue: No fine-grained permission model beyond authentication.
  - Fix: Validate user permissions/roles for create/delete/test where applicable.

## dispatcher_router.py
- Issue: Async endpoint with sync DB session + repository doing `await` (mixed contract).
  - Fix: Ensure TaskRepository uses async DB access or wrap sync operations in threadpool.
- Issue: Inconsistent error details (generic "Failed to create task").
  - Fix: Include structured error codes/messages; keep sensitive info out.

## github_auth_router.py
- Issue: Likely incorrect Supabase OAuth call (`auth.get_url_for_provider` may not exist in Python client).
  - Fix: Use the supported method (e.g., `auth.sign_in_with_oauth` or appropriate SDK method) and extract URL accordingly.
- Issue: Embedding JWT in query (`/?token=...`) for code-server redirect is insecure (leaks via logs/referrers).
  - Fix: Use a short-lived one-time code, cookie, or fragment (`/#token=...`) with client-side handling.
- Issue: Error messages expose underlying exceptions to redirect param.
  - Fix: Map to generic error codes; log details server-side only.

## health_router.py
- Issue: References to `get_redis_client()` and `get_docker_service()` without imports; will raise NameError.
  - Fix: Import these dependencies from the correct services modules or inject via Depends.
- Issue: Uses `next(get_db())` inside async functions.
  - Fix: Use proper FastAPI dependency injection for DB session or context manager compatible with async.
- Issue: Potential blocking calls to Docker/Redis in async context if underlying client is sync.
  - Fix: Ensure async clients or run sync calls in threadpool.
- Issue: DB URL partially logged; may leak host details.
  - Fix: Redact sensitive parts and avoid logging connection info.

## interview_router.py
- Issue: Uses `async with get_db()`; `get_db` likely returns a sync session not usable as async context manager.
  - Fix: Switch to FastAPI Depends(get_db) or implement proper async session manager.
- Issue: Global singleton of `ArchitectAgent` managed manually.
  - Fix: Initialize on app startup and inject via dependency.
- Issue: Limited auth/permission checks.
  - Fix: Require authenticated user and validate access to project/session.

## llm_router.py
- Issue: Public endpoints like `/providers`, `/models`, `/statistics`, `/validate-keys` not consistently protected.
  - Fix: Require authentication and restrict sensitive operations to admins.
- Issue: Background logging helpers `_log_generation_success`, `_log_model_pull` referenced (ensure implemented and imported).
  - Fix: Implement or import these functions; otherwise remove background tasks.
- Issue: Error messages may leak provider details.
  - Fix: Normalize error payloads behind codes; log details internally.

## project_router.py
- Issue: Redefines `logger` twice; redundant.
  - Fix: Remove duplicate logger initialization.
- Issue: `httpx` imported but unused (in shown segment).
  - Fix: Remove unused imports.
- Issue: Mix of async endpoints and sync DB operations.
  - Fix: Use async SQLAlchemy or threadpool for blocking operations.
- Issue: Background task `scan_project_directory` referenced; ensure defined/imported.
  - Fix: Import from the correct module or implement function.
- Issue: Security: ensure per-user project access checks in repository methods.
  - Fix: Verify repository functions enforce ownership and RLS alignment.

## redis_router.py
- Issue: Uses KEYS-like patterns via `eval` and `keys()` which are O(N) and unsafe in production.
  - Fix: Track counts via metadata, use SCAN with cursors, or maintain separate indices.
- Issue: Broad cache invalidation with `*` can be expensive.
  - Fix: Namespace keys and invalidate by prefix using SCAN; consider time-bounded TTLs.
- Issue: Error responses leak exception strings.
  - Fix: Normalize error messages; log details only.

## roadmap_router.py
- Issue: Uses `db.func.now()`; `db` is a Session, not the `sqlalchemy.func` namespace.
  - Fix: Import `from sqlalchemy import func` and use `func.now()`.
- Issue: Async endpoints calling repository methods that may be sync.
  - Fix: Ensure repository layer is async or wrap sync access in threadpool.
- Issue: Router prefix not versioned (`/roadmap`).
  - Fix: Use versioned prefix (e.g., `/api/v1/roadmap`) to align with others.

## role_management.py
- Issue: Inconsistent configuration schema usage (`roles` vs `configurations`); code logs `len(config.roles)` but fallback model defines `configurations`.
  - Fix: Standardize the RoleConfigurationList schema (field names and structure) and update all accesses accordingly.
- Issue: `aiofiles.os.path.exists` used; `aiofiles.os` may not expose `path.exists` API.
  - Fix: Use `os.path.exists` via threadpool or `asyncio.to_thread`; alternatively, use `aiofiles.os.stat` with try/except.
- Issue: Potentially storing API keys in plain JSON on disk.
  - Fix: Encrypt sensitive fields at rest or store only references; rely on env/secret storage.
- Issue: Rate limiting via SlowAPI optional, but decorator may not register globally.
  - Fix: If used, integrate limiter middleware and exception handlers at app startup.
- Issue: Multiple fallbacks and broad excepts can hide real errors.
  - Fix: Tighten exception handling and validate config on load/save with explicit errors.

## routers.py
- Issue: Simple aggregator without health or conditional inclusion checks besides import time.
  - Fix: Consider conditional inclusion based on feature flags and consistent API versioning.

## security_router.py
- Issue: Authorization relies on `current_user.get("role") == "admin"`; may not match real RBAC.
  - Fix: Centralize RBAC/roles and use a dedicated dependency (e.g., require_admin).
- Issue: Error responses are generic; good, but ensure sensitive details not leaked anywhere else.
  - Fix: Keep logging internal, expose codes.

## supabase_router.py
- Issue: Large surface area; ensure all endpoints require auth where appropriate.
  - Fix: Verify RLS is enforced (use user client) for read operations; use service client only where necessary and safe.
- Issue: Input validation for file uploads and content types.
  - Fix: Enforce size/type limits; scan or sanitize content as needed.
- Issue: Potential sync calls in async context (Supabase client is sync-like under the hood).
  - Fix: Run blocking calls in threadpool.

## user_router.py
- Issue: Duplicated auth dependencies: imports from `src.services.auth_dependencies` while re-defining local `get_current_user_from_supabase` and `require_admin_user`.
  - Fix: Use a single standardized auth dependency module; remove duplicates.
- Issue: Uses `os.getenv("SUPABASE_JWT_SECRET")` directly in router.
  - Fix: Centralize config in settings; avoid reading envs in hot paths.
- Issue: Potential mixing of async endpoints with sync DB operations.
  - Fix: Migrate to async DB or threadpool wrappers.
- Issue: `sanitize_search_input` basic; ensure consistent usage where needed.
  - Fix: Apply sanitization consistently on query params that feed search.

## websocket_router.py
- Issue: Global `architect_agent` lifecycle managed from within router.
  - Fix: Initialize on app startup and inject via dependency; provide shutdown cleanup.
- Issue: Error messages sent to client are generic (good) but code sends dict via `send_text(str(...))` in error path.
  - Fix: Send proper JSON via `send_json` for consistency.
- Issue: Optional `token` not validated; TODO in code.
  - Fix: Implement JWT validation dependency for WebSocket or gate via HTTP auth before upgrading.

## __init__.py (router package)
- Issue: Tries to import/export `initialize_llm_service` from llm_router which may not exist.
  - Fix: Ensure llm_router actually exposes this symbol or remove from exports.
- Issue: Fallback imports set exported names to None which can cause attribute errors downstream.
  - Fix: Guard uses where imported objects may be None or raise at startup with clear error.
