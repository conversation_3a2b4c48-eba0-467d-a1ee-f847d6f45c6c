#!/usr/bin/env python3
"""
Vector Storage Consolidation Validation Tests

This test suite validates that the ChromaDB to Supabase pgvector
consolidation was successful and all vector operations work correctly.

Author: AI Coding Agent
Version: 1.0.0
"""

import pytest
import pytest_asyncio
import uuid
import hashlib
import os
import sys

from src.services.vector_service import VectorStorageService as VectorService, KnowledgeTier, DocumentChunk


class TestVectorStorageConsolidation:
    """Test suite for vector storage consolidation validation."""

    @pytest_asyncio.fixture
    async def vector_service(self):
        """Create vector service instance for testing."""
        # Initialize vector service
        service = VectorService()
        # Mock the initialize method to avoid embedding setup issues in tests
        original_initialize = service.initialize
        async def mock_initialize():
            # Skip actual embedding initialization for tests
            service._initialization_failed = False
            return
        service.initialize = mock_initialize

        await service.initialize()
        return service

    @pytest.fixture
    def sample_documents(self):
        """Sample documents for testing."""
        return [
            {
                'content': 'This is a public documentation about FastAPI basics.',
                'metadata': {'source': 'docs', 'category': 'tutorial'},
                'knowledge_tier': KnowledgeTier.PUBLIC
            },
            {
                'content': 'Private user code: async def my_function(): pass',
                'metadata': {'user_id': 'test-user-123', 'project_id': 'test-project-456'},
                'knowledge_tier': KnowledgeTier.PRIVATE
            },
            {
                'content': 'Another public guide about container deployment.',
                'metadata': {'source': 'guides', 'category': 'deployment'},
                'knowledge_tier': KnowledgeTier.PUBLIC
            }
        ]

    @pytest.mark.asyncio
    async def test_no_chromadb_imports(self):
        """Test that ChromaDB imports are completely removed."""
        import importlib.util

        # Try to import chromadb - should fail
        spec = importlib.util.find_spec("chromadb")
        assert spec is None, "ChromaDB should not be available after removal"

    @pytest.mark.asyncio
    async def test_vector_service_initialization(self, vector_service):
        """Test vector service initializes without ChromaDB."""
        assert vector_service is not None
        assert hasattr(vector_service, 'supabase_service')

        # Verify no ChromaDB attributes exist
        assert not hasattr(vector_service, 'chromadb_client')
        assert not hasattr(vector_service, 'chroma_collections')

    @pytest.mark.asyncio
    async def test_store_public_knowledge(self, vector_service, sample_documents):
        """Test storing public knowledge documents."""
        public_doc = sample_documents[0]

        # Store public knowledge
        document_id = f"test-public-{uuid.uuid4()}"
        chunks = await vector_service.store_public_knowledge(
            document_id=document_id,
            content=public_doc['content'],
            metadata=public_doc['metadata']
        )

        assert len(chunks) > 0
        assert all(chunk.knowledge_tier == KnowledgeTier.PUBLIC for chunk in chunks)
        assert all(chunk.user_id is None for chunk in chunks)
        assert all(chunk.project_id is None for chunk in chunks)

    @pytest.mark.asyncio
    async def test_store_private_knowledge(self, vector_service, sample_documents):
        """Test storing private knowledge documents."""
        private_doc = sample_documents[1]

        # Store private knowledge
        document_id = f"test-private-{uuid.uuid4()}"
        chunks = await vector_service.store_document_chunks(
            chunks=[DocumentChunk(
                document_id=document_id,
                content=private_doc['content'],
                embedding=[0.1] * 1536,  # Mock embedding
                chunk_index=0,
                knowledge_tier=KnowledgeTier.PRIVATE,
                metadata=private_doc['metadata'],
                user_id=private_doc['metadata']['user_id'],
                project_id=private_doc['metadata']['project_id']
            )]
        )

        assert len(chunks) > 0
        assert all(chunk.knowledge_tier == KnowledgeTier.PRIVATE for chunk in chunks)
        assert all(chunk.user_id == private_doc['metadata']['user_id'] for chunk in chunks)

    @pytest.mark.asyncio
    async def test_search_knowledge_tiers(self, vector_service):
        """Test searching across knowledge tiers."""
        # Search public knowledge only
        public_results = await vector_service.search_knowledge_tiers(
            query="FastAPI documentation",
            knowledge_tiers=[KnowledgeTier.PUBLIC],
            limit=5
        )

        assert isinstance(public_results, list)

        # Search both tiers (with user context)
        all_results = await vector_service.search_knowledge_tiers(
            query="function code",
            knowledge_tiers=[KnowledgeTier.PUBLIC, KnowledgeTier.PRIVATE],
            user_id="test-user-123",
            limit=5
        )

        assert isinstance(all_results, list)

    @pytest.mark.asyncio
    async def test_get_knowledge_collections(self, vector_service):
        """Test getting knowledge collections."""
        # Get public collections
        public_collections = await vector_service.get_knowledge_collections(
            knowledge_tier=KnowledgeTier.PUBLIC
        )

        assert isinstance(public_collections, list)

        # Get private collections for user
        private_collections = await vector_service.get_knowledge_collections(
            knowledge_tier=KnowledgeTier.PRIVATE,
            user_id="test-user-123"
        )

        assert isinstance(private_collections, list)

    @pytest.mark.asyncio
    async def test_knowledge_tier_isolation(self, vector_service):
        """Test that knowledge tiers are properly isolated."""
        # Search private knowledge without user context should return empty
        try:
            private_results = await vector_service.search_knowledge_tiers(
                query="private code",
                knowledge_tiers=[KnowledgeTier.PRIVATE],
                user_id=None,  # No user context
                limit=5
            )
            # Should return empty results due to RLS policies
            assert len(private_results) == 0
        except Exception:
            # RLS policies should prevent access
            pass

    @pytest.mark.asyncio
    async def test_content_deduplication(self, vector_service):
        """Test content deduplication works correctly."""
        content = "This is duplicate content for testing"
        metadata = {'source': 'test'}

        # Store same content twice
        doc_id_1 = f"test-dup-1-{uuid.uuid4()}"
        doc_id_2 = f"test-dup-2-{uuid.uuid4()}"

        chunks_1 = await vector_service.store_public_knowledge(
            document_id=doc_id_1,
            content=content,
            metadata=metadata
        )

        chunks_2 = await vector_service.store_public_knowledge(
            document_id=doc_id_2,
            content=content,
            metadata=metadata
        )

        # Both should succeed but content hash should be the same
        assert len(chunks_1) > 0
        assert len(chunks_2) > 0

        # Content hashes should match
        content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
        assert all(chunk.content_hash == content_hash for chunk in chunks_1)
        assert all(chunk.content_hash == content_hash for chunk in chunks_2)

    @pytest.mark.asyncio
    async def test_database_schema_exists(self, vector_service):
        """Test that the database schema exists and is accessible."""
        # Test basic query to ensure schema exists
        try:
            query = """
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = 'document_embeddings'
                ORDER BY ordinal_position
            """

            result = await vector_service.supabase_service.execute_query(
                query,
                fetch_type='all'
            )

            # Should have the expected columns
            expected_columns = [
                'id', 'document_id', 'content', 'embedding', 'chunk_index',
                'knowledge_tier', 'metadata', 'content_hash', 'user_id',
                'project_id', 'created_at', 'updated_at'
            ]

            actual_columns = [row['column_name'] for row in result]

            for col in expected_columns:
                assert col in actual_columns, f"Column {col} missing from document_embeddings table"

        except Exception as e:
            pytest.fail(f"Database schema validation failed: {str(e)}")

    @pytest.mark.asyncio
    async def test_rls_policies_exist(self, vector_service):
        """Test that RLS policies exist and are enabled."""
        try:
            # Check if RLS is enabled on document_embeddings
            rls_query = """
                SELECT tablename, rowsecurity
                FROM pg_tables
                WHERE schemaname = 'public'
                AND tablename = 'document_embeddings'
            """

            result = await vector_service.supabase_service.execute_query(
                rls_query,
                fetch_type='one'
            )

            assert result is not None, "document_embeddings table not found"
            assert result['rowsecurity'] is True, "RLS not enabled on document_embeddings"

            # Check that policies exist
            policies_query = """
                SELECT policyname, permissive, roles, cmd
                FROM pg_policies
                WHERE tablename = 'document_embeddings'
            """

            policies = await vector_service.supabase_service.execute_query(
                policies_query,
                fetch_type='all'
            )

            assert len(policies) > 0, "No RLS policies found for document_embeddings"

            # Should have policies for public and private knowledge
            policy_names = [p['policyname'] for p in policies]
            assert any('public' in name.lower() for name in policy_names), "No public knowledge policy found"
            assert any('private' in name.lower() for name in policy_names), "No private knowledge policy found"

        except Exception as e:
            pytest.fail(f"RLS policy validation failed: {str(e)}")


def run_validation_tests():
    """Run all validation tests."""
    print("🧪 Running Vector Storage Consolidation Validation Tests...")
    print("=" * 60)

    # Run pytest
    exit_code = pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "--asyncio-mode=auto"
    ])

    if exit_code == 0:
        print("\n✅ All validation tests passed!")
        print("🎉 ChromaDB to Supabase pgvector consolidation is complete and functional!")
    else:
        print("\n❌ Some validation tests failed!")
        print("🔧 Please review the test output and fix any issues.")

    return exit_code


if __name__ == "__main__":
    # Run validation tests
    exit_code = run_validation_tests()
    sys.exit(exit_code)
