# Development Setup Guide

## Overview

This guide outlines the official development setup for the AI Coding Agent. The project uses the Supabase CLI for local database and authentication services, with <PERSON>er Compose managing the application services.

## Prerequisites

- Docker and Docker Compose installed
- Supabase CLI installed (`npm install -g supabase`)
- Node.js and Python 3.11+ installed (for local development)

## Official Startup Procedure

### Step 1: Start Supabase Services

First, start the Supabase local development stack:

```bash
supabase start
```

This will start:
- PostgreSQL database on `127.0.0.1:54322`
- Supabase API on `127.0.0.1:54321`
- Supabase Studio on `127.0.0.1:54323`
- Inbucket (email testing) on `127.0.0.1:54324`

## Step 2: Start Application Services

Once Supabase is running, start the application services:

```bash
# 🚀 RECOMMENDED: Use the provided startup script (safest option)
./scripts/start-dev.sh

# OR: Manual command for development with hot reload
docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# OR: Manual command for production-like setup (no code-server)
docker compose up -d
```

### ⚠️ **Important: Docker Compose File Explanation**

The project uses **multiple Docker Compose files** for different environments:

- **`docker-compose.yml`**: Base configuration with core services (ai-orchestrator, user-portal, redis, traefik, docker-proxy)
- **`docker-compose.dev.yml`**: Development overrides (adds code-server, exposes ports, enables hot-reload)
- **`docker-compose.prod.yml`**: Production overrides (security hardening, resource limits)

**Why multiple files?**

- **Development**: You need `code-server` for local development → Use both files
- **Production**: `code-server` runs as templates → Use base file only
- **Flexibility**: Different configurations for different deployment scenarios

**Common Mistake**: Running only `docker-compose up -d` will **miss code-server** in development!

### Step 3: Access Services

- **User Portal**: `http://portal.localhost`
- **AI Orchestrator API**: `http://api.localhost`
- **Code Server**: `http://localhost:8080`
- **Supabase Studio**: `http://127.0.0.1:54323`
- **Traefik Dashboard**: `http://traefik.localhost`

## Environment Configuration

### Required Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Supabase (from supabase start output)
SUPABASE_URL=http://host.docker.internal:54321
SUPABASE_ANON_KEY=your-anon-key-here
SERVICE_ROLE_KEY=your-service-role-key-here

# Database
DATABASE_URL=postgresql://postgres:<EMAIL>:54322/postgres

# AI Services
OPENROUTER_API_KEY=your-openrouter-key
# ... other AI service keys
```

### Supabase CLI Keys

After running `supabase start`, the keys are displayed. Update your `.env` file with:

- `ANON_KEY`: Public key for client-side authentication
- `SERVICE_ROLE_KEY`: Secret key for server-side operations
- `SUPABASE_URL`: API endpoint URL

## Development Workflow

### Database Migrations

```bash
# Create migration
supabase migration new your-migration-name

# Apply migrations
supabase db push

# Reset database
supabase db reset
```

### Database Access

Connect to the local database:

- **Host**: localhost
- **Port**: 54322
- **User**: postgres
- **Password**: postgres
- **Database**: postgres

### Logs and Debugging

```bash
# View Supabase logs
supabase status

# View application logs
docker-compose logs -f ai-orchestrator
docker-compose logs -f user-portal

# Debug with VS Code
# Attach to ai-orchestrator-dev:5678 for Python debugging
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure no other services are using ports 54321-54324
2. **Connection refused**: Verify Supabase is running with `supabase status`
3. **Database connection**: Check `DATABASE_URL` points to `host.docker.internal:54322`
4. **Container networking**: Use `host.docker.internal` for containers to reach host services

### Reset Everything

```bash
# Stop everything
docker-compose down
supabase stop

# Clean up
docker system prune -f
supabase db reset

# Restart
supabase start
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

## Architecture Notes

- **Supabase CLI**: Manages database, auth, and API services
- **Docker Compose**: Manages application containers (ai-orchestrator, user-portal, etc.)
- **Networking**: Containers use `host.docker.internal` to reach Supabase services on host
- **Development**: Use `docker-compose.dev.yml` for hot reload and debugging features

## Alternative Setup (Docker Compose Only)

If you prefer to manage Supabase via Docker Compose instead of the CLI:

1. Re-enable Supabase services in `docker-compose.yml`
2. Comment out the `host.docker.internal` URLs
3. Use `docker-compose.supabase.yml` for Supabase services

**Note**: Do not run both Supabase CLI and Docker Compose Supabase services simultaneously.
