"""
Authentication Service for AI Coding Agent with Supabase.

This module provides comprehensive authentication functionality including
JWT token management, user registration/login, role-based access control,
and integration with Supabase Auth.

Author: AI Coding Agent
Version: 1.0.0
"""

import os
from src.core.config import settings
import jwt
import json
from typing import Optional, Dict, Any, List, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import logging
from functools import wraps
from contextlib import asynccontextmanager

# FastAPI imports
from fastapi import HTTPException, Depends, Request, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

# Supabase imports
try:
    from supabase import Client
    from supabase_auth import User, Session
except ImportError:
    raise ImportError(
        "Supabase not installed. Install with: pip install supabase supabase-auth"
    )

# Internal imports
from src.services.supabase_service import SupabaseService, get_supabase_service

# Configure logging
logger = logging.getLogger(__name__)


class UserRole(str, Enum):
    """User roles with hierarchical permissions."""
    VIEWER = "viewer"
    USER = "user"
    DEVELOPER = "developer"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"


class AuthProvider(str, Enum):
    """Supported authentication providers."""
    EMAIL = "email"
    GOOGLE = "google"
    GITHUB = "github"
    OAUTH = "oauth"


@dataclass
class AuthConfig:
    """Authentication configuration."""
    jwt_secret: str
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    password_min_length: int = 8
    require_email_verification: bool = True
    allow_signup: bool = True
    max_login_attempts: int = 5
    login_attempt_window_minutes: int = 15

    @classmethod
    def from_env(cls) -> 'AuthConfig':
        """Create configuration from centralized settings."""
        # Validate required JWT secret
        jwt_secret = settings.JWT_SECRET
        if not jwt_secret:
            raise ValueError("JWT_SECRET is required but not configured in environment")

        # All configuration values from centralized settings
        return cls(
            jwt_secret=jwt_secret,
            jwt_algorithm=settings.JWT_ALGORITHM,
            access_token_expire_minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES,
            refresh_token_expire_days=settings.JWT_REFRESH_TOKEN_EXPIRE_DAYS,
            password_min_length=settings.PASSWORD_MIN_LENGTH,
            require_email_verification=settings.REQUIRE_EMAIL_VERIFICATION,
            allow_signup=settings.ALLOW_SIGNUP,
            max_login_attempts=settings.MAX_LOGIN_ATTEMPTS,
            login_attempt_window_minutes=settings.LOGIN_ATTEMPT_WINDOW_MINUTES
        )


@dataclass
class UserProfile:
    """User profile information."""
    id: str
    email: str
    username: Optional[str] = None
    full_name: Optional[str] = None
    role: UserRole = UserRole.USER
    avatar_url: Optional[str] = None
    preferences: Dict[str, Any] = field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    email_verified: bool = False
    is_active: bool = True
    last_login: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'email': self.email,
            'username': self.username,
            'full_name': self.full_name,
            'role': self.role.value,
            'avatar_url': self.avatar_url,
            'preferences': self.preferences,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'email_verified': self.email_verified,
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }


@dataclass
class AuthTokens:
    """Authentication tokens."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int = 1800  # 30 minutes default
    expires_at: Optional[datetime] = None


class AuthenticationError(Exception):
    """Authentication-related errors."""
    pass


class AuthorizationError(Exception):
    """Authorization-related errors."""
    pass


class TokenExpiredError(AuthenticationError):
    """Token expiration errors."""
    pass


class InvalidTokenError(AuthenticationError):
    """Invalid token errors."""
    pass


class AuthService:
    """
    Comprehensive authentication service providing:
    - User registration and login
    - JWT token management
    - Role-based access control
    - Password validation
    - Session management
    """

    def __init__(
        self,
        supabase_service: SupabaseService,
        config: Optional[AuthConfig] = None
    ):
        """
        Initialize authentication service.

        Args:
            supabase_service: Supabase service instance.
            config: Authentication configuration.
        """
        self.supabase_service = supabase_service
        self.config = config or AuthConfig.from_env()

        # Security bearer for FastAPI
        self.security = HTTPBearer(auto_error=False)

        logger.info("Authentication service initialized")

    def _get_environment_info(self) -> Dict[str, Any]:
        """
        Get authentication-related environment information for debugging.

        Returns:
            Dict[str, Any]: Environment information (secrets redacted)
        """
        return {
            "env_variables": {
                "SUPABASE_URL": "***" if os.getenv("SUPABASE_URL") else None,
                "SUPABASE_ANON_KEY": "***" if os.getenv("SUPABASE_ANON_KEY") else None,
                "JWT_SECRET": "***" if os.getenv("JWT_SECRET") else None,
                "ENVIRONMENT": os.getenv("ENVIRONMENT", "development"),
                "DEBUG": os.getenv("DEBUG", "false"),
            },
            "auth_config": {
                "algorithm": self.config.jwt_algorithm,
                "access_token_expire_minutes": self.config.access_token_expire_minutes,
                "require_email_verification": self.config.require_email_verification,
                "allow_signup": self.config.allow_signup,
                "max_login_attempts": self.config.max_login_attempts,
            }
        }

    async def cleanup(self) -> None:
        """
        Clean up authentication service resources.

        This method should be called when shutting down the service
        to ensure proper cleanup of any resources.
        """
        try:
            # Clear any cached tokens or sessions if needed
            logger.info("Authentication service cleanup completed")
        except Exception as e:
            logger.warning(f"Error during auth service cleanup: {str(e)}")

    # ==================================================================================
    # USER REGISTRATION AND LOGIN
    # ==================================================================================

    async def register_user(
        self,
        email: str,
        password: str,
        username: Optional[str] = None,
        full_name: Optional[str] = None,
        role: UserRole = UserRole.USER,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[UserProfile, AuthTokens]:
        """
        Register a new user.

        Args:
            email: User email address.
            password: User password.
            username: Optional username.
            full_name: Optional full name.
            role: User role.
            metadata: Additional metadata.

        Returns:
            Tuple of user profile and auth tokens.

        Raises:
            AuthenticationError: If registration fails.
        """
        if not self.config.allow_signup:
            raise AuthenticationError("User registration is disabled")

        try:
            # Validate password
            self._validate_password(password)

            # Prepare user metadata
            user_metadata = {
                'username': username,
                'full_name': full_name,
                'role': role.value,
                **(metadata or {})
            }

            # Register with Supabase Auth
            client = self.supabase_service.service_client
            response = client.auth.sign_up({
                'email': email,
                'password': password,
                'options': {
                    'data': user_metadata
                }
            })

            if not response.user:
                raise AuthenticationError("User registration failed")

            # Create user profile
            user_profile = await self._create_user_profile(response.user, user_metadata)

            # Generate tokens
            tokens = await self._generate_tokens(user_profile)

            logger.info(f"User registered successfully: {email}")
            return user_profile, tokens

        except Exception as e:
            logger.error(f"User registration failed: {str(e)}")
            raise AuthenticationError(f"Registration failed: {str(e)}") from e

    async def login_user(
        self,
        email: str,
        password: str,
        remember_me: bool = False
    ) -> Tuple[UserProfile, AuthTokens]:
        """
        Authenticate user login.

        Args:
            email: User email address.
            password: User password.
            remember_me: Whether to extend token expiration.

        Returns:
            Tuple of user profile and auth tokens.

        Raises:
            AuthenticationError: If login fails.
        """
        try:
            # Authenticate with Supabase
            client = self.supabase_service.service_client
            response = client.auth.sign_in_with_password({
                'email': email,
                'password': password
            })

            if not response.user or not response.session:
                raise AuthenticationError("Invalid email or password")

            # Get user profile
            user_profile = await self._get_user_profile(response.user.id)
            if not user_profile:
                # Create profile if it doesn't exist
                user_profile = await self._create_user_profile(response.user)

            # Update last login
            await self._update_last_login(user_profile.id)

            # Generate tokens
            expires_minutes = (
                self.config.refresh_token_expire_days * 24 * 60
                if remember_me
                else self.config.access_token_expire_minutes
            )
            tokens = await self._generate_tokens(user_profile, expires_minutes)

            logger.info(f"User login successful: {email}")
            return user_profile, tokens

        except Exception as e:
            logger.error(f"User login failed: {str(e)}")
            raise AuthenticationError(f"Login failed: {str(e)}") from e

    async def logout_user(self, access_token: str) -> bool:
        """
        Logout user and invalidate tokens.

        Args:
            access_token: User's access token.

        Returns:
            True if logout successful.
        """
        try:
            # Invalidate Supabase session
            client = self.supabase_service.get_client(access_token)
            client.auth.sign_out()

            logger.info("User logout successful")
            return True

        except Exception as e:
            logger.warning(f"Logout warning: {str(e)}")
            return False

    # ==================================================================================
    # TOKEN MANAGEMENT
    # ==================================================================================

    async def _generate_tokens(
        self,
        user_profile: UserProfile,
        expires_minutes: Optional[int] = None
    ) -> AuthTokens:
        """
        Generate JWT access and refresh tokens.

        Args:
            user_profile: User profile to generate tokens for.
            expires_minutes: Token expiration in minutes.

        Returns:
            Generated authentication tokens.

        Raises:
            AuthenticationError: If token generation fails.
        """
        expires_minutes = expires_minutes or self.config.access_token_expire_minutes
        expires_at = datetime.utcnow() + timedelta(minutes=expires_minutes)

        # Access token payload
        access_payload = {
            'sub': user_profile.id,
            'email': user_profile.email,
            'role': user_profile.role.value,
            'iat': datetime.utcnow(),
            'exp': expires_at,
            'type': 'access'
        }

        # Refresh token payload (longer expiration)
        refresh_expires_at = datetime.utcnow() + timedelta(days=self.config.refresh_token_expire_days)
        refresh_payload = {
            'sub': user_profile.id,
            'iat': datetime.utcnow(),
            'exp': refresh_expires_at,
            'type': 'refresh'
        }

        # Generate tokens
        access_token = jwt.encode(
            access_payload,
            self.config.jwt_secret,
            algorithm=self.config.jwt_algorithm
        )

        refresh_token = jwt.encode(
            refresh_payload,
            self.config.jwt_secret,
            algorithm=self.config.jwt_algorithm
        )

        return AuthTokens(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=expires_minutes * 60,
            expires_at=expires_at
        )

    async def verify_token(self, token: str) -> Dict[str, Any]:
        """
        Verify and decode JWT token.

        Args:
            token: JWT token to verify.

        Returns:
            Decoded token payload.

        Raises:
            TokenExpiredError: If token is expired.
            InvalidTokenError: If token is invalid.
        """
        try:
            payload = jwt.decode(
                token,
                self.config.jwt_secret,
                algorithms=[self.config.jwt_algorithm]
            )

            # Check token type
            if payload.get('type') not in ['access', 'refresh']:
                raise InvalidTokenError("Invalid token type")

            return payload

        except jwt.ExpiredSignatureError:
            raise TokenExpiredError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise InvalidTokenError(f"Invalid token: {str(e)}")

    def export_user_session_data(self, user_profile: UserProfile, tokens: AuthTokens) -> str:
        """
        Export user session data as JSON string for logging or analytics.

        Args:
            user_profile: User profile information
            tokens: Authentication tokens

        Returns:
            str: JSON string with session data (sensitive data redacted)
        """
        session_data = {
            "user": {
                "id": user_profile.id,
                "email": user_profile.email,
                "role": user_profile.role.value,
                "created_at": user_profile.created_at.isoformat() if user_profile.created_at else None,
                "last_login": user_profile.last_login.isoformat() if user_profile.last_login else None,
                "email_verified": user_profile.email_verified,
                "is_active": user_profile.is_active,
            },
            "session": {
                "expires_in": tokens.expires_in,
                "expires_at": tokens.expires_at.isoformat(),
                "token_type": "Bearer",
                "access_token": "***REDACTED***",  # Never log actual tokens
                "refresh_token": "***REDACTED***",
            },
            "timestamp": datetime.utcnow().isoformat(),
            "session_type": "login"
        }

        return json.dumps(session_data, indent=2)

    async def refresh_access_token(self, refresh_token: str) -> AuthTokens:
        """
        Refresh access token using refresh token.

        Args:
            refresh_token: Valid refresh token.

        Returns:
            New auth tokens.

        Raises:
            TokenExpiredError: If refresh token is expired.
            InvalidTokenError: If refresh token is invalid.
        """
        try:
            # Verify refresh token
            payload = await self.verify_token(refresh_token)

            if payload.get('type') != 'refresh':
                raise InvalidTokenError("Invalid refresh token")

            # Get user profile
            user_id = payload.get('sub')
            if not user_id:
                raise AuthenticationError("Invalid token: missing user ID")

            user_profile = await self._get_user_profile(user_id)

            if not user_profile:
                raise AuthenticationError("User not found")

            # Generate new tokens
            tokens = await self._generate_tokens(user_profile)

            logger.info(f"Token refreshed for user: {user_profile.email}")
            return tokens

        except (TokenExpiredError, InvalidTokenError):
            raise
        except Exception as e:
            logger.error(f"Token refresh failed: {str(e)}")
            raise AuthenticationError(f"Token refresh failed: {str(e)}") from e

    # ==================================================================================
    # USER PROFILE MANAGEMENT
    # ==================================================================================

    async def _create_user_profile(
        self,
        auth_user: User,
        metadata: Optional[Dict[str, Any]] = None
    ) -> UserProfile:
        """
        Create user profile in database.

        Args:
            auth_user: Authenticated user from Supabase.
            metadata: Additional user metadata.

        Returns:
            Created user profile.

        Raises:
            AuthenticationError: If profile creation fails or user data is invalid.
        """
        try:
            profile_data = {
                'id': auth_user.id,
                'username': metadata.get('username') if metadata else None,
                'full_name': metadata.get('full_name') if metadata else None,
                'role': metadata.get('role', UserRole.USER.value) if metadata else UserRole.USER.value,
                'preferences': metadata.get('preferences', {}) if metadata else {}
            }

            # Insert into database
            client = self.supabase_service.service_client
            response = client.table('user_profiles').insert(profile_data).execute()

            if not response.data:
                raise AuthenticationError("Failed to create user profile")

            # Validate email
            email = auth_user.email
            if not email:
                raise AuthenticationError("User email is required but not found")

            return UserProfile(
                id=auth_user.id,
                email=email,
                username=profile_data['username'],
                full_name=profile_data['full_name'],
                role=UserRole(profile_data['role']),
                preferences=profile_data['preferences'],
                email_verified=auth_user.email_confirmed_at is not None,
                created_at=datetime.utcnow()
            )

        except Exception as e:
            logger.error(f"Failed to create user profile: {str(e)}")
            raise AuthenticationError(f"Profile creation failed: {str(e)}") from e

    async def _get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """
        Get user profile from database.

        Args:
            user_id: User ID to retrieve profile for.

        Returns:
            User profile if found, None otherwise.

        Raises:
            ValueError: If user_id is empty or invalid.
        """
        try:
            # Validate input
            if not user_id or not user_id.strip():
                raise ValueError("User ID cannot be empty")

            # Get from Supabase
            client = self.supabase_service.service_client
            response = client.table('user_profiles').select('*').eq('id', user_id).execute()

            if not response.data:
                return None

            profile_data = response.data[0]

            # Get auth user for email
            auth_response = client.auth.admin.get_user_by_id(user_id)
            auth_user = auth_response.user if auth_response else None

            # Get email from auth user or fallback
            email = '<EMAIL>'
            if auth_user and auth_user.email:
                email = auth_user.email

            return UserProfile(
                id=profile_data['id'],
                email=email,
                username=profile_data.get('username'),
                full_name=profile_data.get('full_name'),
                role=UserRole(profile_data.get('role', UserRole.USER.value)),
                avatar_url=profile_data.get('avatar_url'),
                preferences=profile_data.get('preferences', {}),
                created_at=datetime.fromisoformat(profile_data['created_at'].replace('Z', '+00:00')),
                updated_at=datetime.fromisoformat(profile_data['updated_at'].replace('Z', '+00:00')) if profile_data.get('updated_at') else None,
                email_verified=auth_user.email_confirmed_at is not None if auth_user else False
            )

        except Exception as e:
            logger.error(f"Failed to get user profile: {str(e)}")
            return None

    async def _update_last_login(self, user_id: str) -> None:
        """Update user's last login timestamp."""
        try:
            client = self.supabase_service.service_client
            client.table('user_profiles').update({
                'updated_at': datetime.utcnow().isoformat()
            }).eq('id', user_id).execute()

        except Exception as e:
            logger.warning(f"Failed to update last login: {str(e)}")

    # ==================================================================================
    # PASSWORD AND VALIDATION
    # ==================================================================================

    def _validate_password(self, password: str) -> None:
        """
        Validate password strength according to security requirements.

        Args:
            password: Password to validate.

        Raises:
            AuthenticationError: If password doesn't meet requirements.
        """
        if not password:
            raise AuthenticationError("Password is required")

        if len(password) < self.config.password_min_length:
            raise AuthenticationError(
                f"Password must be at least {self.config.password_min_length} characters long"
            )

        # Check for uppercase letter
        if not any(c.isupper() for c in password):
            raise AuthenticationError("Password must contain at least one uppercase letter")

        # Check for lowercase letter
        if not any(c.islower() for c in password):
            raise AuthenticationError("Password must contain at least one lowercase letter")

        # Check for digit
        if not any(c.isdigit() for c in password):
            raise AuthenticationError("Password must contain at least one digit")

        # Check for special character
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            raise AuthenticationError("Password must contain at least one special character")

    # ==================================================================================
    # FASTAPI DEPENDENCIES
    # ==================================================================================

    async def get_current_user(
        self,
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
    ) -> UserProfile:
        """
        FastAPI dependency to get current authenticated user.

        Args:
            credentials: HTTP bearer credentials.

        Returns:
            Current user profile.

        Raises:
            HTTPException: If authentication fails.
        """
        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication credentials required",
                headers={"WWW-Authenticate": "Bearer"}
            )

        try:
            # Verify token
            payload = await self.verify_token(credentials.credentials)

            # Get user profile
            user_id = payload.get('sub')
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token: missing user ID"
                )

            user_profile = await self._get_user_profile(user_id)

            if not user_profile:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found"
                )

            return user_profile

        except (TokenExpiredError, InvalidTokenError) as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=str(e),
                headers={"WWW-Authenticate": "Bearer"}
            )
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication failed"
            )

    def require_role(self, required_role: UserRole):
        """
        Decorator to require specific user role.

        Args:
            required_role: Minimum required role.

        Returns:
            FastAPI dependency function.
        """
        async def role_checker(
            current_user: UserProfile = Depends(self.get_current_user)
        ) -> UserProfile:
            role_hierarchy = {
                UserRole.VIEWER: 0,
                UserRole.USER: 1,
                UserRole.DEVELOPER: 2,
                UserRole.ADMIN: 3,
                UserRole.SUPER_ADMIN: 4
            }

            if role_hierarchy.get(current_user.role, 0) < role_hierarchy.get(required_role, 0):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Insufficient permissions. Required role: {required_role.value}"
                )

            return current_user

        return role_checker

    def get_valid_roles_hierarchy(self, base_role: UserRole) -> List[UserRole]:
        """
        Get all roles that have equal or greater permissions than the base role.

        Args:
            base_role: The minimum required role

        Returns:
            List[UserRole]: List of valid roles in hierarchical order
        """
        role_hierarchy = [
            UserRole.VIEWER,
            UserRole.USER,
            UserRole.DEVELOPER,
            UserRole.ADMIN,
            UserRole.SUPER_ADMIN
        ]

        # Find base role index
        try:
            base_index = role_hierarchy.index(base_role)
            return role_hierarchy[base_index:]
        except ValueError:
            return [UserRole.SUPER_ADMIN]  # Default to highest permission

    def validate_role_permissions(
        self,
        current_role: UserRole,
        required_roles: Union[UserRole, List[UserRole]]
    ) -> bool:
        """
        Validate if current role has sufficient permissions.

        Args:
            current_role: User's current role
            required_roles: Required role(s) - single role or list of acceptable roles

        Returns:
            bool: True if user has sufficient permissions
        """
        # Convert single role to list for uniform processing
        if isinstance(required_roles, UserRole):
            required_roles = [required_roles]

        # Get valid roles for each required role and combine
        valid_roles: List[UserRole] = []
        for role in required_roles:
            valid_roles.extend(self.get_valid_roles_hierarchy(role))

        # Remove duplicates while preserving order
        unique_valid_roles = []
        for role in valid_roles:
            if role not in unique_valid_roles:
                unique_valid_roles.append(role)

        return current_role in unique_valid_roles

    @asynccontextmanager
    async def temporary_role_elevation(
        self,
        user_profile: UserProfile,
        elevated_role: UserRole
    ):
        """
        Temporarily elevate user role for specific operations.

        Args:
            user_profile: User profile to temporarily elevate
            elevated_role: Role to temporarily assign

        Yields:
            UserProfile: User profile with elevated role
        """
        original_role = user_profile.role
        try:
            # Validate elevation is allowed (can't elevate above current max)
            if not self.validate_role_permissions(original_role, [UserRole.ADMIN]):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions for role elevation"
                )

            # Temporarily elevate role
            user_profile.role = elevated_role
            logger.info(f"Temporarily elevated user {user_profile.id} from {original_role} to {elevated_role}")

            yield user_profile

        finally:
            # Always restore original role
            user_profile.role = original_role
            logger.info(f"Restored user {user_profile.id} role from {elevated_role} to {original_role}")

    def create_request_context_decorator(self, required_role: UserRole):
        """
        Create a decorator that includes request context and role validation.

        Args:
            required_role: Minimum required role for the decorated function

        Returns:
            Decorator function
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, request: Request = None, **kwargs):
                # Extract user from request if available
                auth_header = request.headers.get("Authorization") if request else None
                if auth_header and auth_header.startswith("Bearer "):
                    token = auth_header.split(" ")[1]
                    try:
                        payload = await self.verify_token(token)
                        user_id = payload.get('sub')
                        user_profile = await self._get_user_profile(user_id) if user_id else None

                        if user_profile and not self.validate_role_permissions(user_profile.role, required_role):
                            raise HTTPException(
                                status_code=status.HTTP_403_FORBIDDEN,
                                detail=f"Insufficient permissions. Required: {required_role.value}"
                            )

                        # Add user context to kwargs
                        kwargs['current_user'] = user_profile

                    except Exception as e:
                        logger.warning(f"Request context extraction failed: {e}")

                return await func(*args, **kwargs)
            return wrapper
        return decorator

    async def get_user_session_info(self, user_id: str) -> Optional[Session]:
        """
        Get current Supabase session information for a user.

        Args:
            user_id: User identifier

        Returns:
            Optional[Session]: Supabase session if active, None otherwise
        """
        try:
            # Get current session from Supabase
            response = await self.supabase_service._client.auth.get_session()
            if response and response.user and response.user.id == user_id:
                return response
            return None
        except Exception as e:
            logger.error(f"Failed to get session info for user {user_id}: {e}")
            return None

    async def create_supabase_client_for_user(self, access_token: str) -> Optional[Client]:
        """
        Create a Supabase client instance with user's access token.

        Args:
            access_token: User's access token

        Returns:
            Optional[Client]: Configured Supabase client or None if failed
        """
        try:
            from supabase import create_client

            # Create client with user's access token
            supabase_client = create_client(
                supabase_url=self.supabase_service.config.url,
                supabase_key=access_token  # Use user's token instead of anon key
            )

            return supabase_client

        except Exception as e:
            logger.error(f"Failed to create user Supabase client: {e}")
            return None


# Global service instance
_auth_service: Optional[AuthService] = None


async def get_auth_service() -> AuthService:
    """Get global authentication service instance."""
    global _auth_service

    if _auth_service is None:
        supabase_service = await get_supabase_service()
        _auth_service = AuthService(supabase_service)

    return _auth_service


# FastAPI dependencies
async def get_current_user(
    auth_service: AuthService = Depends(get_auth_service),
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> UserProfile:
    """FastAPI dependency for current user."""
    return await auth_service.get_current_user(credentials)


def require_role(role: UserRole):
    """FastAPI dependency for role-based access control."""
    async def role_dependency(
        auth_service: AuthService = Depends(get_auth_service)
    ):
        return auth_service.require_role(role)

    return role_dependency