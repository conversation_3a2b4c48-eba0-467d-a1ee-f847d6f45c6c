"""
Security Monitoring Service for AI Coding Agent.

Provides continuous security monitoring and threat detection capabilities
including failed authentication attempts, suspicious API usage patterns,
and security event logging.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict, deque


logger = logging.getLogger(__name__)


class SecurityMonitoringService:
    """
    Continuous security monitoring service with threat detection.

    Features:
    - Failed authentication tracking
    - Rate limiting violation detection
    - Suspicious API usage pattern detection
    - Security event logging and alerting
    - Automated threat response
    """

    def __init__(self, redis_client=None):
        """Initialize security monitoring service."""
        self.redis_client = redis_client
        self.is_running = False
        self._monitoring_task: Optional[asyncio.Task] = None

        # In-memory tracking for development/fallback
        self.failed_attempts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.suspicious_ips: Dict[str, List[datetime]] = defaultdict(list)
        self.rate_limit_violations: Dict[str, int] = defaultdict(int)

        # Configuration
        self.max_failed_attempts = 5
        self.lockout_duration = timedelta(minutes=15)
        self.monitoring_interval = 60  # seconds

    async def start_monitoring(self) -> None:
        """Start the background security monitoring task."""
        if self.is_running:
            logger.warning("Security monitoring already running")
            return

        self.is_running = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Security monitoring service started")

    async def stop_monitoring(self) -> None:
        """Stop the background security monitoring task."""
        self.is_running = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Security monitoring service stopped")

    async def _monitoring_loop(self) -> None:
        """Main monitoring loop that runs in the background."""
        while self.is_running:
            try:
                await self._check_security_metrics()
                await self._cleanup_old_data()
                await asyncio.sleep(self.monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in security monitoring loop: {str(e)}")
                await asyncio.sleep(5)  # Brief pause before retrying

    async def record_failed_auth(self, identifier: str, ip_address: str) -> None:
        """
        Record a failed authentication attempt.

        Args:
            identifier: User identifier (email, username, etc.)
            ip_address: Source IP address
        """
        timestamp = datetime.utcnow()

        # Track failed attempts
        self.failed_attempts[identifier].append(timestamp)
        self.suspicious_ips[ip_address].append(timestamp)

        # Check for lockout conditions
        recent_attempts = [
            t for t in self.failed_attempts[identifier]
            if timestamp - t < self.lockout_duration
        ]

        if len(recent_attempts) >= self.max_failed_attempts:
            await self._trigger_security_alert(
                "MULTIPLE_FAILED_AUTH",
                f"User {identifier} from IP {ip_address} has {len(recent_attempts)} failed attempts",
                {"identifier": identifier, "ip_address": ip_address, "attempts": len(recent_attempts)}
            )

    async def record_rate_limit_violation(self, identifier: str, endpoint: str) -> None:
        """
        Record a rate limiting violation.

        Args:
            identifier: User or IP identifier
            endpoint: API endpoint that was rate limited
        """
        self.rate_limit_violations[identifier] += 1

        if self.rate_limit_violations[identifier] > 10:  # Threshold
            await self._trigger_security_alert(
                "EXCESSIVE_RATE_LIMITING",
                f"Identifier {identifier} has {self.rate_limit_violations[identifier]} rate limit violations",
                {"identifier": identifier, "endpoint": endpoint, "violations": self.rate_limit_violations[identifier]}
            )

    async def is_ip_suspicious(self, ip_address: str) -> bool:
        """
        Check if an IP address shows suspicious behavior.

        Args:
            ip_address: IP address to check

        Returns:
            bool: True if IP is considered suspicious
        """
        if ip_address not in self.suspicious_ips:
            return False

        recent_time = datetime.utcnow() - timedelta(hours=1)
        recent_failures = [
            t for t in self.suspicious_ips[ip_address]
            if t > recent_time
        ]

        return len(recent_failures) > 10  # Threshold for suspicious activity

    async def _check_security_metrics(self) -> None:
        """Check and analyze current security metrics."""
        current_time = datetime.utcnow()

        # Check for patterns in failed attempts
        for identifier, attempts in self.failed_attempts.items():
            recent_attempts = [
                t for t in attempts
                if current_time - t < timedelta(hours=1)
            ]

            if len(recent_attempts) > 3:
                logger.warning(f"Elevated failed attempts for {identifier}: {len(recent_attempts)} in last hour")

        # Check for suspicious IP patterns
        for ip, failures in self.suspicious_ips.items():
            recent_failures = [
                t for t in failures
                if current_time - t < timedelta(hours=1)
            ]

            if len(recent_failures) > 15:
                await self._trigger_security_alert(
                    "SUSPICIOUS_IP_ACTIVITY",
                    f"IP {ip} has {len(recent_failures)} security events in last hour",
                    {"ip_address": ip, "events": len(recent_failures)}
                )

    async def _cleanup_old_data(self) -> None:
        """Clean up old security monitoring data."""
        cutoff_time = datetime.utcnow() - timedelta(days=7)

        # Clean old failed attempts
        for identifier in list(self.failed_attempts.keys()):
            attempts = self.failed_attempts[identifier]
            # Keep only recent attempts (deque automatically limits size)
            recent_attempts = deque(
                [t for t in attempts if t > cutoff_time],
                maxlen=100
            )
            if recent_attempts:
                self.failed_attempts[identifier] = recent_attempts
            else:
                del self.failed_attempts[identifier]

        # Clean old suspicious IP data
        for ip in list(self.suspicious_ips.keys()):
            recent_events = [
                t for t in self.suspicious_ips[ip]
                if t > cutoff_time
            ]
            if recent_events:
                self.suspicious_ips[ip] = recent_events
            else:
                del self.suspicious_ips[ip]

    async def _trigger_security_alert(self, alert_type: str, message: str, metadata: Dict[str, Any]) -> None:
        """
        Trigger a security alert.

        Args:
            alert_type: Type of security alert
            message: Alert message
            metadata: Additional alert metadata
        """
        alert_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "type": alert_type,
            "message": message,
            "metadata": metadata,
            "severity": "HIGH" if alert_type in ["MULTIPLE_FAILED_AUTH", "SUSPICIOUS_IP_ACTIVITY"] else "MEDIUM"
        }

        logger.warning(f"SECURITY ALERT [{alert_type}]: {message}")

        # Store alert in Redis if available
        if self.redis_client:
            try:
                await self.redis_client.lpush(
                    "security:alerts",
                    f"{alert_data['timestamp']}:{alert_type}:{message}"
                )
                await self.redis_client.expire("security:alerts", 86400)  # 24 hours
            except Exception as e:
                logger.error(f"Failed to store security alert in Redis: {str(e)}")

    async def get_security_summary(self) -> Dict[str, Any]:
        """
        Get a summary of current security status.

        Returns:
            dict: Security summary with metrics and alerts
        """
        current_time = datetime.utcnow()
        recent_time = current_time - timedelta(hours=24)

        # Count recent failed attempts
        total_failed_attempts = sum(
            len([t for t in attempts if t > recent_time])
            for attempts in self.failed_attempts.values()
        )

        # Count suspicious IPs
        suspicious_ip_count = len([
            ip for ip, events in self.suspicious_ips.items()
            if any(t > recent_time for t in events)
        ])

        # Count rate limit violations
        total_violations = sum(self.rate_limit_violations.values())

        return {
            "timestamp": current_time.isoformat(),
            "monitoring_active": self.is_running,
            "metrics": {
                "failed_attempts_24h": total_failed_attempts,
                "suspicious_ips": suspicious_ip_count,
                "rate_limit_violations": total_violations,
                "tracked_identifiers": len(self.failed_attempts),
            },
            "status": "healthy" if total_failed_attempts < 10 else "elevated"
        }


# Global instance
security_monitor = SecurityMonitoringService()


async def get_security_monitoring_service() -> SecurityMonitoringService:
    """Get the security monitoring service instance."""
    return security_monitor
