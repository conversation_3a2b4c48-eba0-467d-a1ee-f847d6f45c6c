# syntax=docker/dockerfile:1
# Optimized Nginx Dockerfile for hosting-server with dynamic configuration
# Uses official nginx image with security best practices and performance optimizations

FROM nginx:1.25-alpine

# Labels for better container management
LABEL org.opencontainers.image.title="AI Coding Agent - Hosting Server" \
  org.opencontainers.image.description="Optimized Nginx server for hosting dynamic project previews" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  maintainer="AI Coding Agent Team" \
  security.non-root="true" \
  security.user="nginx" \
  version="1.0.0"

# Install security updates and required packages in single layer
# Combine RUN commands to reduce layers and improve caching
RUN apk update && apk upgrade && \
  apk add --no-cache \
  curl \
  tzdata && \
  # Clean up package manager cache to reduce image size
  rm -rf /var/cache/apk/* && \
  # Create necessary directories with proper permissions
  mkdir -p /var/www/sites /var/cache/nginx /var/log/nginx && \
  # Set proper ownership (nginx user already exists in nginx:alpine)
  chown -R nginx:nginx /var/www/sites /var/cache/nginx /var/log/nginx && \
  # Create nginx pid file
  touch /var/run/nginx.pid && \
  chown nginx:nginx /var/run/nginx.pid && \
  # Set restrictive permissions on web directory
  chmod 755 /var/www/sites && \
  chmod 755 /var/cache/nginx /var/log/nginx

# Copy nginx configuration with validation
COPY nginx.dynamic.conf /etc/nginx/conf.d/default.conf

# Validate nginx configuration syntax
RUN nginx -t -c /etc/nginx/nginx.conf

# Switch to non-root user (nginx user already exists in nginx:alpine base image)
USER nginx

# Expose port 80
EXPOSE 80

# Health check with proper validation that doesn't interfere with user projects
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f -H "Host: health.localhost" http://localhost/ || exit 1

# Default environment variables for flexible configuration
ENV NGINX_WORKER_PROCESSES=auto \
  NGINX_WORKER_CONNECTIONS=1024 \
  NGINX_CLIENT_MAX_BODY_SIZE=50M \
  NGINX_KEEPALIVE_TIMEOUT=30

# Start nginx with daemon off for container compatibility
CMD ["nginx", "-g", "daemon off;"]
