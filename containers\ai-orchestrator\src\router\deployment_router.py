"""
Deployment Router for managing secure deployment integrations.

This router provides CRUD endpoints for managing deployment platform integrations
like Vercel, with encrypted storage of API keys and credentials.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from src.models.database import get_db
from src.models.deployment_integration import DeploymentIntegration, DeploymentProvider
from src.models.user import UserProfile
from src.utils.auth import get_current_user
from src.services.crypto_service import get_crypto_service, CryptoService

router = APIRouter(prefix="/deployments", tags=["deployments"])


class DeploymentIntegrationCreate(BaseModel):
    """Request schema for creating a new deployment integration."""

    provider: DeploymentProvider = Field(..., description="Deployment provider")
    api_key: str = Field(..., min_length=1, description="API key for the provider")
    team_id: Optional[str] = Field(None, max_length=255, description="Team/organization identifier")


class DeploymentIntegrationResponse(BaseModel):
    """Response schema for deployment integration data."""

    id: str
    provider: DeploymentProvider
    team_id: Optional[str]
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True


class DeploymentIntegrationListResponse(BaseModel):
    """Response schema for listing deployment integrations."""

    integrations: List[DeploymentIntegrationResponse]
    providers: List[dict] = Field(default_factory=list)


@router.post("", response_model=DeploymentIntegrationResponse, status_code=status.HTTP_201_CREATED)
async def create_deployment_integration(
    integration_data: DeploymentIntegrationCreate,
    db: Session = Depends(get_db),
    current_user: UserProfile = Depends(get_current_user),
    crypto_service: CryptoService = Depends(get_crypto_service)
):
    """
    Create a new deployment integration with encrypted API key storage.

    Args:
        integration_data: Integration creation data
        db: Database session
        current_user: Authenticated user
        crypto_service: Cryptography service for encryption

    Returns:
        DeploymentIntegrationResponse: Created integration details
    """
    if not crypto_service.is_initialized():
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Encryption service not available"
        )

    # Encrypt the API key
    encrypted_api_key = crypto_service.encrypt(integration_data.api_key)
    if not encrypted_api_key:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to encrypt API key"
        )

    # Check if integration already exists for this user and provider
    existing_integration = db.query(DeploymentIntegration).filter(
        DeploymentIntegration.user_id == current_user.id,
        DeploymentIntegration.provider == integration_data.provider,
        DeploymentIntegration.team_id == integration_data.team_id
    ).first()

    if existing_integration:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Integration already exists for {integration_data.provider.value}"
        )

    # Create new integration
    integration = DeploymentIntegration(
        user_id=current_user.id,
        provider=integration_data.provider,
        encrypted_api_key=encrypted_api_key,
        team_id=integration_data.team_id
    )

    db.add(integration)
    db.commit()
    db.refresh(integration)

    return integration


@router.get("", response_model=DeploymentIntegrationListResponse)
async def list_deployment_integrations(
    db: Session = Depends(get_db),
    current_user: UserProfile = Depends(get_current_user)
):
    """
    List all deployment integrations for the current user.

    Args:
        db: Database session
        current_user: Authenticated user

    Returns:
        DeploymentIntegrationListResponse: List of integrations and available providers
    """
    integrations = db.query(DeploymentIntegration).filter(
        DeploymentIntegration.user_id == current_user.id
    ).order_by(DeploymentIntegration.created_at.desc()).all()

    # Get available provider choices
    providers = DeploymentIntegration.get_provider_choices()

    return {
        "integrations": integrations,
        "providers": providers
    }


@router.get("/{integration_id}", response_model=DeploymentIntegrationResponse)
async def get_deployment_integration(
    integration_id: str,
    db: Session = Depends(get_db),
    current_user: UserProfile = Depends(get_current_user)
):
    """
    Get a specific deployment integration by ID.

    Args:
        integration_id: Integration UUID
        db: Database session
        current_user: Authenticated user

    Returns:
        DeploymentIntegrationResponse: Integration details
    """
    integration = db.query(DeploymentIntegration).filter(
        DeploymentIntegration.id == integration_id,
        DeploymentIntegration.user_id == current_user.id
    ).first()

    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Deployment integration not found"
        )

    return integration


@router.delete("/{integration_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_deployment_integration(
    integration_id: str,
    db: Session = Depends(get_db),
    current_user: UserProfile = Depends(get_current_user)
):
    """
    Delete a deployment integration.

    Args:
        integration_id: Integration UUID
        db: Database session
        current_user: Authenticated user
    """
    integration = db.query(DeploymentIntegration).filter(
        DeploymentIntegration.id == integration_id,
        DeploymentIntegration.user_id == current_user.id
    ).first()

    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Deployment integration not found"
        )

    db.delete(integration)
    db.commit()


@router.post("/{integration_id}/test", status_code=status.HTTP_200_OK)
async def test_deployment_integration(
    integration_id: str,
    db: Session = Depends(get_db),
    current_user: UserProfile = Depends(get_current_user),
    crypto_service: CryptoService = Depends(get_crypto_service)
):
    """
    Test a deployment integration by decrypting and validating credentials.

    Args:
        integration_id: Integration UUID
        db: Database session
        current_user: Authenticated user
        crypto_service: Cryptography service for decryption

    Returns:
        dict: Test result with status and message
    """
    integration = db.query(DeploymentIntegration).filter(
        DeploymentIntegration.id == integration_id,
        DeploymentIntegration.user_id == current_user.id
    ).first()

    if not integration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Deployment integration not found"
        )

    if not crypto_service.is_initialized():
        return {
            "success": False,
            "message": "Encryption service not available"
        }

    # Decrypt the API key to test encryption/decryption
    decrypted_api_key = crypto_service.decrypt(integration.encrypted_api_key)

    if not decrypted_api_key:
        return {
            "success": False,
            "message": "Failed to decrypt API key - encryption may be misconfigured"
        }

    # Note: Actual API validation would be done here against the provider's API
    # For now, we just confirm the encryption/decryption works

    return {
        "success": True,
        "message": f"{integration.provider.value} integration credentials decrypted successfully",
        "provider": integration.provider.value,
        "team_id": integration.team_id
    }


@router.get("/providers", response_model=List[dict])
async def get_deployment_providers():
    """
    Get available deployment provider options.

    Returns:
        List[dict]: Available deployment providers with display names
    """
    return DeploymentIntegration.get_provider_choices()
