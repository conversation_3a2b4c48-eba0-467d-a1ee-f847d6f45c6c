# AI Coding Agent - Embedding Setup Guide

## Overview

This document outlines the embedding configuration for the AI Coding Agent system, optimized for pgvector database operations and GPU acceleration on systems with 4GB VRAM.

## Current Embedding Configuration

### Primary Embedding Model

- **Model**: `bge-large-en-v1.5`
- **Dimensions**: 1024
- **Provider**: Ollama (local GPU acceleration)
- **Use Case**: Retrieval-augmented generation (RAG) and semantic search
- **Database**: PostgreSQL with pgvector extension

### Why BGE Large?

BGE (BAAI General Embedding) Large v1.5 was selected for the following reasons:

- **Superior retrieval performance** compared to general-purpose models
- **Optimized for pgvector** database operations
- **Excellent semantic understanding** for code and documentation
- **GPU acceleration support** via Ollama
- **1024 dimensions** provide rich semantic representation

## GPU Memory Optimization (4GB VRAM)

### Memory Constraints

- **GPU**: Quadro P1000 (4GB VRAM)
- **Driver**: NVIDIA 580.88
- **CUDA**: Version 13.0

### Optimized Model Selection

#### Primary Configuration (Recommended)

```yaml
# LLM: 2.0GB VRAM
OLLAMA_MODEL: llama3.2:3b

# Embedding: 0.6GB VRAM
EMBEDDING_MODEL: bge-large-en-v1.5
EMBEDDING_DIMENSION: 1024

# Total: ~2.6GB (Safe for 4GB GPU)
```

#### Alternative Configurations

**Lightweight Setup:**

```yaml
OLLAMA_MODEL: llama3.2:1b    # 1.0GB
EMBEDDING_MODEL: nomic-embed-text  # 0.3GB
# Total: ~1.3GB (Very safe)
```

**Balanced Performance:**

```yaml
OLLAMA_MODEL: deepseek-coder:6.7b  # 3.8GB
EMBEDDING_MODEL: mxbai-embed-large  # 0.6GB
# Total: ~4.4GB (Tight but functional)
```

## Available Embedding Models

### Ollama Models (GPU Accelerated)

| Model | Dimensions | Size | Use Case | Performance |
|-------|------------|------|----------|-------------|
| `bge-large-en-v1.5` | 1024 | ~600MB | **Retrieval** ⭐ | Excellent |
| `mxbai-embed-large` | 1024 | ~600MB | High quality | Very Good |
| `nomic-embed-text` | 768 | ~300MB | General purpose | Good |
| `all-minilm` | 384 | ~25MB | Lightweight | Basic |

### Sentence Transformers (CPU Fallback)

- `all-MiniLM-L6-v2` (384 dimensions)
- `paraphrase-MiniLM-L6-v2` (384 dimensions)

## Database Integration (pgvector)

### PostgreSQL Configuration

```sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create embeddings table
CREATE TABLE document_embeddings (
    id SERIAL PRIMARY KEY,
    content TEXT,
    embedding vector(1024),  -- Matches BGE dimensions
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create vector similarity search index
CREATE INDEX ON document_embeddings
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);
```

### Vector Operations

```sql
-- Store embedding
INSERT INTO document_embeddings (content, embedding)
VALUES ('code snippet...', '[0.1, 0.2, ...]');

-- Similarity search
SELECT content, 1 - (embedding <=> '[query_embedding]') AS similarity
FROM document_embeddings
ORDER BY embedding <=> '[query_embedding]'
LIMIT 10;
```

## Docker Configuration

### GPU Support Setup

```yaml
# docker-compose.yml
ollama:
  deploy:
    resources:
      reservations:
        devices:
          - driver: nvidia
            count: 1
            capabilities: [gpu]
  runtime: nvidia
  environment:
    - NVIDIA_VISIBLE_DEVICES=all
    - OLLAMA_NUM_GPU=1
    - OLLAMA_MAX_LOADED_MODELS=2
```

### Environment Variables

```bash
# Core embedding settings
EMBEDDING_PROVIDER=ollama
EMBEDDING_MODEL=bge-large-en-v1.5
EMBEDDING_DIMENSION=1024

# Performance tuning
OLLAMA_NUM_THREAD=4
OLLAMA_NUM_GPU=1
OLLAMA_MAX_LOADED_MODELS=2
```

## Performance Benchmarks

### Inference Speed (Approximate)

- **BGE Large**: 50-100ms per embedding
- **Nomic Embed**: 30-60ms per embedding
- **All-MiniLM**: 10-20ms per embedding

### Memory Usage

- **BGE Large**: ~600MB VRAM
- **Nomic Embed**: ~300MB VRAM
- **All-MiniLM**: ~25MB VRAM

### Retrieval Quality (pgvector)

- **BGE Large**: 0.85+ cosine similarity for relevant results
- **Nomic Embed**: 0.80+ cosine similarity
- **All-MiniLM**: 0.75+ cosine similarity

## Configuration Files

### Core Configuration (`config.py`)

```python
# Vector service configuration
@dataclass
class VectorConfig:
    embedding_provider: str = "ollama"
    embedding_model: str = "bge-large-en-v1.5"
    embedding_dimension: int = 1024
    chunk_size: int = 512
    chunk_overlap: int = 50
```

### Docker Compose Environment

```yaml
environment:
  - EMBEDDING_PROVIDER=ollama
  - EMBEDDING_MODEL=bge-large-en-v1.5
  - EMBEDDING_DIMENSION=1024
  - OLLAMA_NUM_GPU=1
```

## Model Manifest

### Available Models (`models.json`)

```json
{
  "models": [
    {
      "name": "bge-large-en-v1.5",
      "description": "BGE Large model optimized for retrieval tasks (1024 dimensions) - excellent for pgvector",
      "source": "BAAI"
    },
    {
      "name": "nomic-embed-text",
      "description": "Nomic text embedding model for dense vector embeddings (768 dimensions)",
      "source": "nomic"
    },
    {
      "name": "mxbai-embed-large",
      "description": "Mixed Bread AI large embedding model for high-quality embeddings (1024 dimensions)",
      "source": "mixedbread"
    },
    {
      "name": "all-minilm",
      "description": "Sentence Transformers all-MiniLM model for efficient embeddings (384 dimensions)",
      "source": "sentence-transformers"
    }
  ]
}
```

## Troubleshooting

### GPU Not Detected

```bash
# Check GPU status
nvidia-smi

# Test Docker GPU support
docker run --rm --gpus all nvidia/cuda:12.2.0-base nvidia-smi

# Check Ollama GPU usage
docker compose exec ollama nvidia-smi
```

### Memory Issues

```bash
# Monitor GPU memory
watch -n 1 nvidia-smi

# Check Ollama processes
ps aux | grep ollama
```

### Performance Issues

```bash
# Test embedding speed
time curl -X POST http://localhost:11434/api/embeddings \
  -H "Content-Type: application/json" \
  -d '{"model": "bge-large-en-v1.5", "input": "test text"}'
```

## Future Optimizations

### Potential Improvements

1. **Model Quantization**: Reduce memory footprint with 4-bit quantization
2. **Batch Processing**: Process multiple embeddings simultaneously
3. **Caching Strategy**: Cache frequently used embeddings in Redis
4. **Hybrid Search**: Combine semantic and keyword search

### Alternative Architectures

1. **Multi-GPU Setup**: Distribute models across multiple GPUs
2. **CPU Fallback**: Automatic fallback to CPU for memory-intensive tasks
3. **Cloud Integration**: OpenRouter fallback for GPU-intensive operations

## References

- [Ollama Documentation](https://github.com/ollama/ollama)
- [pgvector Documentation](https://github.com/pgvector/pgvector)
- [BGE Model Paper](https://arxiv.org/abs/2309.07597)
- [Docker GPU Support](https://docs.docker.com/compose/gpu-support/)

---

**Last Updated**: August 31, 2025
**GPU Configuration**: Quadro P1000 (4GB VRAM)
**Primary Model**: BGE Large v1.5 (1024 dimensions)
