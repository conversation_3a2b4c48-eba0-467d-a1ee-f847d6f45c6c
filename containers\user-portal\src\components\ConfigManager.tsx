/**
 * ConfigManager Component
 * Top-level component that orchestrates role-based LLM configuration management
 */

"use client";

import React from 'react';
import {
  PlusIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import clsx from 'clsx';
import RoleConfigPanel from './RoleConfigPanel';
import { useRoleManager } from '@/hooks/useRoleManager';

const ConfigManager: React.FC<{ className?: string }> = ({ className }) => {
  const {
    roles,
    activeRole,
    loading,
    errors,
    showCreateForm,
    showDeleteModal,
    newRoleName,
    setActiveRole,
    createRole,
    updateRole,
    deleteRole,
    saveRole,
    openCreateForm,
    closeCreateForm,
    openDeleteModal,
    closeDeleteModal,
    setNewRoleName,
    getRoleDisplayName,
    canDeleteRole,
  } = useRoleManager();

  // Handle role configuration changes
  const handleConfigurationChange = (roleName: string, updates: any) => {
    updateRole(roleName, updates);
  };

  // Handle saving configuration
  const handleSave = async (roleName: string) => {
    await saveRole(roleName);
  };

  // Handle role creation
  const handleCreateRole = async () => {
    if (!newRoleName.trim()) return;
    await createRole(newRoleName);
  };

  // Handle role deletion
  const handleDeleteRole = async (roleName: string) => {
    await deleteRole(roleName);
  };

  if (loading.global) {
    return (
      <div className={clsx('flex items-center justify-center min-h-96', className)}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading role configurations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('max-w-7xl mx-auto', className)}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Role Management</h1>
            <p className="mt-2 text-gray-600">
              Configure LLM providers and models for each AI agent role
            </p>
          </div>

          <button
            onClick={openCreateForm}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Role
          </button>
        </div>
      </div>

      {/* Role tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8" aria-label="Roles">
            {Object.keys(roles).map(roleName => (
              <button
                key={roleName}
                onClick={() => setActiveRole(roleName)}
                className={clsx(
                  'py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap',
                  {
                    'border-blue-500 text-blue-600': activeRole === roleName,
                    'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300':
                      activeRole !== roleName,
                  }
                )}
              >
                <div className="flex items-center space-x-2">
                  <span>{getRoleDisplayName(roleName)}</span>

                  {/* Status indicators */}
                  <div className="flex items-center space-x-1">
                    {roles[roleName].enabled ? (
                      <CheckCircleIcon className="h-4 w-4 text-green-500" />
                    ) : (
                      <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500" />
                    )}

                    {errors[roleName] && (
                      <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
                    )}
                  </div>

                  {/* Delete button */}
                  {canDeleteRole(roleName) && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        openDeleteModal(roleName);
                      }}
                      className="ml-2 text-gray-400 hover:text-red-500"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Active role configuration */}
      {activeRole && roles[activeRole] && (
        <RoleConfigPanel
          roleName={activeRole}
          configuration={roles[activeRole]}
          onConfigurationChange={(updates) =>
            handleConfigurationChange(activeRole, updates)
          }
          onSave={() => handleSave(activeRole)}
          isLoading={loading[activeRole] === 'loading'}
          error={errors[activeRole]}
        />
      )}

      {/* Create role modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Create New Role</h3>

            <div className="space-y-4">
              <div>
                <label htmlFor="role-name" className="block text-sm font-medium text-gray-700">
                  Role Name
                </label>
                <input
                  id="role-name"
                  type="text"
                  value={newRoleName}
                  onChange={(e) => setNewRoleName(e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., Code Reviewer"
                />
              </div>

              {errors.create && (
                <div className="text-sm text-red-600">
                  {errors.create.message}
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={closeCreateForm}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateRole}
                disabled={!newRoleName.trim() || loading.create === 'loading'}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-gray-400"
              >
                {loading.create === 'loading' ? 'Creating...' : 'Create Role'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete confirmation modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Delete Role</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete the "{getRoleDisplayName(showDeleteModal)}" role?
              This action cannot be undone.
            </p>

            {errors.delete && (
              <div className="text-sm text-red-600 mb-4">
                {errors.delete.message}
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <button
                onClick={closeDeleteModal}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteRole(showDeleteModal)}
                disabled={loading.delete === 'loading'}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 disabled:bg-gray-400"
              >
                {loading.delete === 'loading' ? 'Deleting...' : 'Delete Role'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConfigManager;