"""Fixtures to mock LLM providers and network calls for fast unit tests."""
"""Fixtures to mock LLM providers and network calls for fast unit tests."""

from unittest.mock import AsyncMock

import pytest


@pytest.fixture
def mock_llm(monkeypatch):
    """Monkeypatch common LLM provider functions/classes with fast stubs.

    The fixture below replaces the high-level `generate` function used by the
    codebase. Adjust the import target string to match your project's LLM
    client implementation if necessary.
    """
    # Example target; update to actual function path used in the repo
    target = "src.services.llm_client.generate"

    fake_generate = AsyncMock(return_value={"text": "mocked response", "tokens": 1})
    # Use raising=False so tests that don't import the target don't fail
    monkeypatch.setattr(target, fake_generate, raising=False)
    return fake_generate
