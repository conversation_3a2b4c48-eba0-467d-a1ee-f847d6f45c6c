# containers/ai-orchestrator/src/api/admin.py
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
import asyncio
import json
from datetime import datetime
from pathlib import Path

from src.core.config import Settings
from src.services.enhanced_llm_service import EnhancedLLMService
from src.services.auth_service import AuthService, get_current_user
from src.models.user import UserProfile

router = APIRouter(prefix="/api/admin", tags=["admin"])

class AgentConfiguration(BaseModel):
    provider: str = Field(..., description="Primary LLM provider")
    model: str = Field(..., description="Primary model name")
    temperature: float = Field(0.7, ge=0.0, le=2.0)
    max_tokens: int = Field(2048, ge=256, le=8192)
    fallback_provider: Optional[str] = Field(None, description="Fallback LLM provider")
    fallback_model: Optional[str] = Field(None, description="Fallback model name")
    cost_priority: str = Field("balanced", pattern="^(cost_effective|balanced|performance)$")
    enabled: bool = Field(True)
    custom_system_prompt: Optional[str] = Field(None)

class AgentConfigurationRequest(BaseModel):
    configurations: Dict[str, AgentConfiguration]

class ProviderStatus(BaseModel):
    name: str
    status: str  # connected, error, testing
    models: List[str]
    last_tested: Optional[datetime] = None
    error_message: Optional[str] = None

# Available providers and their models
PROVIDER_CONFIGS = {
    'openai': {
        'name': 'OpenAI',
        'models': ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4o', 'gpt-4o-mini'],
        'api_key_env': 'OPENAI_API_KEY',
        'endpoint': 'https://api.openai.com/v1'
    },
    'anthropic': {
        'name': 'Anthropic',
        'models': ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku', 'claude-3.5-sonnet'],
        'api_key_env': 'ANTHROPIC_API_KEY',
        'endpoint': 'https://api.anthropic.com/v1'
    },
    'openrouter': {
        'name': 'OpenRouter',
        'models': [
            'meta-llama/llama-3.1-70b-instruct',
            'anthropic/claude-3.5-sonnet',
            'google/gemini-pro-1.5',
            'mistralai/mixtral-8x7b-instruct'
        ],
        'api_key_env': 'OPENROUTER_API_KEY',
        'endpoint': 'https://openrouter.ai/api/v1'
    },
    'ollama': {
        'name': 'Ollama (Local)',
        'models': ['llama3.1:8b', 'codellama:7b', 'mistral:7b', 'deepseek-coder:6.7b'],
        'api_key_env': None,
        'endpoint': 'http://ollama:11434'
    },
    'groq': {
        'name': 'Groq',
        'models': ['llama-3.1-70b-versatile', 'llama-3.1-8b-instant', 'mixtral-8x7b-32768'],
        'api_key_env': 'GROQ_API_KEY',
        'endpoint': 'https://api.groq.com/openai/v1'
    }
}

# Default agent roles
DEFAULT_AGENT_ROLES = {
    'architect': {
        'name': 'Architect Agent',
        'description': 'System design and planning',
        'default_provider': 'openai',
        'default_model': 'gpt-4'
    },
    'backend': {
        'name': 'Backend Agent',
        'description': 'Server-side development',
        'default_provider': 'anthropic',
        'default_model': 'claude-3-sonnet'
    },
    'frontend': {
        'name': 'Frontend Agent',
        'description': 'UI/UX development',
        'default_provider': 'openai',
        'default_model': 'gpt-4'
    },
    'shell': {
        'name': 'Shell Agent',
        'description': 'Command execution',
        'default_provider': 'ollama',
        'default_model': 'codellama:7b'
    },
    'fixer': {
        'name': 'Issue Fixer Agent',
        'description': 'Bug fixes and debugging',
        'default_provider': 'anthropic',
        'default_model': 'claude-3.5-sonnet'
    },
    'test': {
        'name': 'Test Agent',
        'description': 'Testing and validation',
        'default_provider': 'openai',
        'default_model': 'gpt-3.5-turbo'
    },
    'security': {
        'name': 'Security Agent',
        'description': 'Security analysis',
        'default_provider': 'anthropic',
        'default_model': 'claude-3-opus'
    }
}

class AdminService:
    def __init__(self):
        self.config_file = Path("data/agent_configurations.json")
        self.config_file.parent.mkdir(exist_ok=True)
        self.llm_service = EnhancedLLMService()

    async def load_configurations(self) -> Dict[str, AgentConfiguration]:
        """Load agent configurations from file"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                return {role: AgentConfiguration(**config) for role, config in data.items()}
            except Exception as e:
                print(f"Error loading configurations: {e}")
                return self._get_default_configurations()
        return self._get_default_configurations()

    def _get_default_configurations(self) -> Dict[str, AgentConfiguration]:
        """Get default configurations for all agent roles"""
        configs = {}
        for role_id, role_info in DEFAULT_AGENT_ROLES.items():
            configs[role_id] = AgentConfiguration(
                provider=role_info['default_provider'],
                model=role_info['default_model'],
                temperature=0.7,
                max_tokens=2048,
                fallback_provider='openai' if role_info['default_provider'] != 'openai' else 'anthropic',
                fallback_model='gpt-3.5-turbo' if role_info['default_provider'] != 'openai' else 'claude-3-sonnet',
                enabled=True
            )
        return configs

    async def save_configurations(self, configurations: Dict[str, AgentConfiguration]):
        """Save agent configurations to file"""
        try:
            data = {role: config.dict() for role, config in configurations.items()}
            with open(self.config_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            return True
        except Exception as e:
            print(f"Error saving configurations: {e}")
            return False

    async def test_provider_connection(self, provider: str) -> ProviderStatus:
        """Test connection to a specific provider"""
        if provider not in PROVIDER_CONFIGS:
            raise ValueError(f"Unknown provider: {provider}")

        config = PROVIDER_CONFIGS[provider]

        try:
            # Test connection using a simple prompt
            test_prompt = "Hello, please respond with 'OK' if you can hear this."

            if provider == 'ollama':
                # Test Ollama connection
                response = await self._test_ollama_connection(config)
            else:
                # Test API-based provider
                response = await self._test_api_provider_connection(provider, config, test_prompt)

            return ProviderStatus(
                name=config['name'],
                status='connected',
                models=config['models'],
                last_tested=datetime.now()
            )

        except Exception as e:
            return ProviderStatus(
                name=config['name'],
                status='error',
                models=config['models'],
                last_tested=datetime.now(),
                error_message=str(e)
            )

    async def _test_ollama_connection(self, config: Dict) -> str:
        """Test Ollama connection specifically"""
        import httpx

        async with httpx.AsyncClient() as client:
            response = await client.get(f"{config['endpoint']}/api/tags")
            if response.status_code == 200:
                return "OK"
            else:
                raise Exception(f"Ollama connection failed: {response.status_code}")

    async def _test_api_provider_connection(self, provider: str, config: Dict, prompt: str) -> str:
        """Test API provider connection"""
        # Use your existing LLM service to test the connection
        try:
            response = await self.llm_service.generate_response(
                prompt=prompt,
                provider=provider,
                model=config['models'][0],
                temperature=0.1,
                max_tokens=50
            )
            return response
        except Exception as e:
            raise Exception(f"{provider} connection failed: {str(e)}")

# Initialize admin service
admin_service = AdminService()

@router.get("/providers", response_model=Dict[str, ProviderStatus])
async def get_providers(current_user: UserProfile = Depends(get_current_user)):
    """Get all available providers and their status"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    providers = {}
    for provider_id, config in PROVIDER_CONFIGS.items():
        providers[provider_id] = ProviderStatus(
            name=config['name'],
            status='unknown',
            models=config['models']
        )
    return providers

@router.post("/providers/{provider}/test", response_model=ProviderStatus)
async def test_provider(provider: str, current_user: UserProfile = Depends(get_current_user)):
    """Test connection to a specific provider"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    try:
        status = await admin_service.test_provider_connection(provider)
        return status
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Test failed: {str(e)}")

@router.get("/agent-roles")
async def get_agent_roles(current_user: UserProfile = Depends(get_current_user)):
    """Get all available agent roles"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    return DEFAULT_AGENT_ROLES

@router.get("/configurations", response_model=Dict[str, AgentConfiguration])
async def get_configurations(current_user: UserProfile = Depends(get_current_user)):
    """Get current agent configurations"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    return await admin_service.load_configurations()

@router.post("/configurations")
async def save_configurations(
    request: AgentConfigurationRequest,
    current_user: UserProfile = Depends(get_current_user)
):
    """Save agent configurations"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    # Validate configurations
    for role_id, config in request.configurations.items():
        if role_id not in DEFAULT_AGENT_ROLES:
            raise HTTPException(status_code=400, detail=f"Unknown agent role: {role_id}")

        if config.provider not in PROVIDER_CONFIGS:
            raise HTTPException(status_code=400, detail=f"Unknown provider: {config.provider}")

        if config.model not in PROVIDER_CONFIGS[config.provider]['models']:
            raise HTTPException(status_code=400, detail=f"Invalid model for provider {config.provider}: {config.model}")

    success = await admin_service.save_configurations(request.configurations)

    if success:
        return {"status": "success", "message": "Configurations saved successfully"}
    else:
        raise HTTPException(status_code=500, detail="Failed to save configurations")

@router.get("/configurations/export")
async def export_configurations(current_user: UserProfile = Depends(get_current_user)):
    """Export current configurations as JSON"""
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")

    configurations = await admin_service.load_configurations()
    data = {role: config.dict() for role, config in configurations.items()}

    return JSONResponse(
        content=data,
        headers={
            "Content-Disposition": "attachment; filename=agent-configurations.json"
        }
    )

@router.post("/configurations/import")
async def import_configurations(
    configurations: Dict[str, Any],
    current_