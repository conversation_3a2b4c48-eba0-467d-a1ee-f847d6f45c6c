#!/bin/bash

# Test script for Docker Hub rate limit avoidance configuration
echo "Testing Docker Hub rate limit avoidance configuration..."

# Check if .env.dockerhub exists
if [ -f .env.dockerhub ]; then
    echo "✅ .env.dockerhub file exists"

    # Test environment variable parsing
    echo "Testing environment variables..."
    if grep -q "REDIS_IMAGE=" .env.dockerhub; then
        echo "✅ REDIS_IMAGE variable found"
    else
        echo "❌ REDIS_IMAGE variable not found"
    fi

    if grep -q "POSTGRES_IMAGE=" .env.dockerhub; then
        echo "✅ POSTGRES_IMAGE variable found"
    else
        echo "❌ POSTGRES_IMAGE variable not found"
    fi

    if grep -q "NGINX_IMAGE=" .env.dockerhub; then
        echo "✅ NGINX_IMAGE variable found"
    else
        echo "❌ NGINX_IMAGE variable not found"
    fi
else
    echo "❌ .env.dockerhub file not found"
    exit 1
fi

# Test docker-compose configuration
echo "Testing docker-compose configuration..."
if docker-compose --env-file .env.dockerhub config > /dev/null 2>&1; then
    echo "✅ docker-compose configuration is valid"
else
    echo "❌ docker-compose configuration has errors"
    exit 1
fi

# Test image availability (optional - can be skipped if no network)
read -p "Test image availability? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Testing alternative image availability..."

    # Test Redis image
    if docker pull bitnami/redis:7.2 > /dev/null 2>&1; then
        echo "✅ Bitnami Redis image available"
    else
        echo "⚠️  Bitnami Redis image not available (may need authentication)"
    fi

    # Test PostgreSQL image
    if docker pull postgres:15.5-alpine > /dev/null 2>&1; then
        echo "✅ PostgreSQL specific version available"
    else
        echo "⚠️  PostgreSQL specific version not available"
    fi

    # Test Nginx image
    if docker pull nginx:1.25-alpine > /dev/null 2>&1; then
        echo "✅ Nginx specific version available"
    else
        echo "⚠️  Nginx specific version not available"
    fi
fi

echo ""
echo "✅ Docker Hub rate limit avoidance configuration test completed successfully!"
echo ""
echo "To use the alternative registries:"
echo "1. Copy the environment file: cp .env.dockerhub .env"
echo "2. Start services: docker-compose --env-file .env up -d"
echo "3. Or use directly: docker-compose --env-file .env.dockerhub up -d"
echo ""
echo "For best results, authenticate with Docker Hub:"
echo "  docker login"
