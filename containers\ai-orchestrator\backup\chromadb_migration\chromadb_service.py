# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: ChromaDB service for embedded vector database operations

import os
import logging
from pathlib import Path
from typing import Optional, Any

logger = logging.getLogger(__name__)

# Global ChromaDB client instance
_chromadb_client: Optional[Any] = None

def get_chromadb_client() -> Optional[Any]:
    """Get the global ChromaDB client instance."""
    global _chromadb_client
    return _chromadb_client


async def initialize_chromadb() -> Optional[Any]:
    """
    Initialize ChromaDB client in embedded mode.

    Returns:
        ChromaDB client instance if successful, None otherwise.
    """
    global _chromadb_client

    if _chromadb_client is not None:
        logger.info("ChromaDB client already initialized")
        return _chromadb_client

    try:
        # Import ChromaDB with fallback handling
        import chromadb
        from chromadb.config import Settings

        # Determine ChromaDB data path
        data_path = os.getenv("CHROMADB_DATA_PATH", "/app/data/chromadb")

        # Ensure data directory exists
        Path(data_path).mkdir(parents=True, exist_ok=True)

        # Initialize persistent client for embedded mode
        logger.info(f"Initializing ChromaDB with data path: {data_path}")

        _chromadb_client = chromadb.PersistentClient(
            path=data_path,
            settings=Settings(
                anonymized_telemetry=False,
                is_persistent=True
            )
        )

        # Test the connection
        collections = _chromadb_client.list_collections()
        logger.info(f"ChromaDB initialized successfully with {len(collections)} existing collections")

        return _chromadb_client

    except ImportError as e:
        logger.warning(f"ChromaDB not available: {e}")
        return None
    except Exception as e:
        logger.error(f"Failed to initialize ChromaDB: {e}")
        return None


async def cleanup_chromadb():
    """Cleanup ChromaDB resources."""
    global _chromadb_client

    if _chromadb_client is not None:
        try:
            # ChromaDB PersistentClient doesn't require explicit cleanup
            # but we can reset the global reference
            _chromadb_client = None
            logger.info("ChromaDB client cleaned up")
        except Exception as e:
            logger.warning(f"Error during ChromaDB cleanup: {e}")


def create_or_get_collection(collection_name: str, description: str = "") -> Optional[Any]:
    """
    Create or get a ChromaDB collection.

    Args:
        collection_name: Name of the collection
        description: Description for new collections

    Returns:
        Collection instance if successful, None otherwise.
    """
    if _chromadb_client is None:
        logger.warning("ChromaDB client not initialized")
        return None

    try:
        # Try to get existing collection
        collection = _chromadb_client.get_collection(collection_name)
        logger.debug(f"Retrieved existing collection: {collection_name}")
        return collection
    except Exception:
        # Collection doesn't exist, create it
        try:
            collection = _chromadb_client.create_collection(
                name=collection_name,
                metadata={"description": description}
            )
            logger.info(f"Created new collection: {collection_name}")
            return collection
        except Exception as e:
            logger.error(f"Failed to create collection {collection_name}: {e}")
            return None


def get_collection_stats(collection_name: str) -> dict:
    """
    Get statistics for a ChromaDB collection.

    Args:
        collection_name: Name of the collection

    Returns:
        Dictionary with collection statistics.
    """
    if _chromadb_client is None:
        return {"error": "ChromaDB client not initialized"}

    try:
        collection = _chromadb_client.get_collection(collection_name)
        count = collection.count()
        return {
            "collection_name": collection_name,
            "document_count": count,
            "status": "available"
        }
    except Exception as e:
        return {
            "collection_name": collection_name,
            "error": str(e),
            "status": "error"
        }


def list_collections() -> list:
    """
    List all ChromaDB collections.

    Returns:
        List of collection information.
    """
    if _chromadb_client is None:
        return []

    try:
        collections = _chromadb_client.list_collections()
        return [
            {
                "name": col.name,
                "metadata": getattr(col, 'metadata', {}),
                "document_count": col.count()
            }
            for col in collections
        ]
    except Exception as e:
        logger.error(f"Failed to list collections: {e}")
        return []
