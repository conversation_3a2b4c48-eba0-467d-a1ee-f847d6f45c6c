# Environment and Configuration
.env
.env.local
.env.production
.env.development

# Docker Secrets and Sensitive Data
secrets/
*.key
*.pem
*.p12
*.jks

# Database and Cache Data
postgres_data/
redis_data/
*.db
*.sqlite
*.sqlite3

# Supabase CLI binaries
supabase.exe
supabase.tar.gz
supabase

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing and Coverage
.pytest_cache/
.coverage
.coverage.*
htmlcov/
.tox/
.nox/
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Virtual Environments
.venv/
venv/
ENV/
env/
.env/

# IDEs and Editors
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Node.js
node_modules/
.npm
.eslintcache
.next/
out/
.nuxt
dist

# Temporary files
tmp/
temp/
.tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.docker/
docker-compose.override.yml

# Backup files
*.bak
*.backup
*.old

# AI/ML Models and Data
ai_models/
ml_models/
model_weights/
*.pkl
*.joblib
*.h5
*.onnx

# Monitoring and Analytics
grafana/data/
prometheus/data/
elasticsearch/data/

# Documentation builds
docs/_build/
site/

# Git
*.origsecrets/

# Additional Python ignores
pip-log.txt
pip-delete-this-directory.txt
local_settings.py
instance/
.webassets-cache
.scrapy
target/
.ipynb_checkpoints
profile_default/
ipython_config.py
.python-version
celerybeat-schedule
*.sage.py
.spyderproject
.spyproject
.ropeproject
/site
.mypy_cache/
.dmypy.json
dmypy.json

# Additional Node.js ignores
lerna-debug.log*
.pnpm-debug.log*
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json
.cache/
public
.out
.storybook-out
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/
.tern-port
.vscode-test
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Additional Docker ignores
# (Dockerfiles are typically tracked)

# Additional OS and IDE
ehthumbs.db
.Spotlight-V100
.Trashes
._*

# Additional temporary and build
*.tmp
*.swp
*~
coverage/
.nyc_output
build/
dist/
.cache/

# Project-specific ignores
.augment/
.clinerules/
.qoder/
.zencoder/
volumes/
local_dev.py
local_env.py
run_tests.py
test_agent_workflow.db
test_ollama_gpu.ps1
test_ollama_gpu.sh
temp_disable_supabase.env
supabase_keys.json
.env.migration
.env.supabase
tsconfig.tsbuildinfo
.next-volume/


#Ignore vscode AI rules
.github\instructions\codacy.instructions.md
