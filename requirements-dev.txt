# Development dependencies for AI Coding Agent Project

# Pre-commit hooks
pre-commit>=3.6.0
isort>=5.13.2
mypy>=1.8.0

# Validation script dependencies
pathlib2>=2.3.7.post1

# Docker linting (hadolint is a separate binary, not a Python package)
# Install hadolint separately: https://github.com/hadolint/hadolint#install

# Test environment loader
pytest-dotenv>=0.5.2
pytest-asyncio>=0.21.0
aiosqlite>=0.19.0
psycopg2-binary
sqlalchemy-redshift
