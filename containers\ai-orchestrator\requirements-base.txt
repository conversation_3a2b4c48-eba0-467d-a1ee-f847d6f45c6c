# Base requirements (excluding PyTorch which is installed separately as CPU-only)
fastapi>=0.104.0
uvicorn>=0.24.0
python-multipart>=0.0.6
sqlalchemy>=2.0.0
alembic>=1.13.0
psycopg2-binary>=2.9.7
redis>=5.0.0
redis[hiredis]>=5.0.0
python-dotenv>=1.0.0
pydantic>=2.5.0
email-validator>=2.0.0
aiofiles>=23.2.0
requests>=2.31.0
aiohttp>=3.9.0
docker>=6.1.0

# Supabase and Authentication
supabase>=2.0.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
PyJWT>=2.8.0

# Database and Vector Operations
asyncpg>=0.29.0
pgvector>=0.2.4
numpy>=1.24.0,<2.0.0  # Pin to numpy 1.x for torch 2.1.2 compatibility

# AI/ML components (CPU-only versions to avoid CUDA downloads)
langchain>=0.1.0
langgraph==0.2.0
langchain-community>=0.0.10
langchain-openai>=0.0.5

# Embedding and Vector Search (CPU-optimized) - excluding PyTorch dependencies
openai>=1.3.0
scikit-learn>=1.3.0
# Note: sentence-transformers and transformers will be installed after PyTorch

# Monitoring and rate limiting
prometheus-client>=0.19.0
slowapi>=0.1.9
psutil>=5.9.0

# Development and debugging
debugpy>=1.8.0
watchdog>=3.0.0

# Testing dependencies
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-asyncio>=0.23.0
httpx>=0.25.0
pytest-mock>=3.12.0
PyJWT>=2.8.0
