#!/bin/bash
# Database Initialization Script for AI Coding Agent
# This script runs during PostgreSQL container initialization
# to create the application database and set up initial schema

set -e

echo "Initializing AI Coding Agent database..."

# Wait for PostgreSQL to be ready
until pg_isready -U "$POSTGRES_USER" -h localhost; do
  echo "Waiting for PostgreSQL to be ready..."
  sleep 2
done

echo "PostgreSQL is ready. Creating database..."

# Create the application database if it doesn't exist
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "postgres" <<-EOSQL
    -- Create database if it doesn't exist
    SELECT 'CREATE DATABASE ai_coding_agent OWNER postgres ENCODING ''UTF8'''
    WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'ai_coding_agent')\gexec

    -- Grant permissions
    GRANT ALL PRIVILEGES ON DATABASE ai_coding_agent TO postgres;

    -- Switch to the application database and enable extensions
    \c ai_coding_agent;

    -- Enable required extensions
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    CREATE EXTENSION IF NOT EXISTS "vector";

    -- Create a simple health check table
    CREATE TABLE IF NOT EXISTS database_health (
        id SERIAL PRIMARY KEY,
        check_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        status TEXT DEFAULT 'healthy',
        version TEXT DEFAULT '1.0.0'
    );

    -- Insert initial health record
    INSERT INTO database_health (status, version) VALUES ('initialized', '1.0.0');

    -- Set up basic permissions
    GRANT USAGE ON SCHEMA public TO postgres;
    GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
    GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

    -- Allow future grants
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO postgres;
    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres;
EOSQL

echo "Database initialization completed successfully!"
echo "Database: ai_coding_agent"
echo "Extensions enabled: uuid-ossp, pgcrypto, vector"
echo "Ready for Alembic migrations."
