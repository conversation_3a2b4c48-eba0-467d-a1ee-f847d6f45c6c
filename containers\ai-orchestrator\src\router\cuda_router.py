import logging
from fastapi import APIRouter, Depends
from src.services.cuda_accelerated_service import (
    CUDA_AVAILABLE,
    get_cuda_accelerated_service,
    CUDAAcceleratedService,
)

logger = logging.getLogger(__name__)
cuda_router = APIRouter()

if CUDA_AVAILABLE:

    @cuda_router.get("/health", tags=["CUDA"])
    async def cuda_health(
        cuda_service: CUDAAcceleratedService = Depends(get_cuda_accelerated_service),
    ):
        """
        Health check for the CUDA accelerated service.
        """
        return await cuda_service.health_check()


else:
    logger.warning("CUDA dependencies not found. The CUDA router will be disabled.")
