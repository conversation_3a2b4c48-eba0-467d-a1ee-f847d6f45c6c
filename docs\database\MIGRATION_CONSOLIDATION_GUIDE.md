# Database Migration Consolidation Guide

## Overview

This document describes the consolidation of individual database migration files (001-006) into a single authoritative schema file (000_consolidated_initial_schema.sql) for the AI Coding Agent project.

## Migration Files Consolidated

The following migration files have been consolidated:

1. **001_initial_schema.sql** - Base tables and indexes
2. **002_vector_functions.sql** - Vector search functions and operations
3. **003_secure_rls_policies.sql** - Row Level Security policies
4. **004_incremental_scanning.sql** - Document hash tracking and incremental processing
5. **005_hybrid_roadmap_schema.sql** - Roadmap and AI-generated summary tables
6. **006_explicit_user_context.sql** - Enhanced user context and security functions

## Consolidated Schema Features

### Tables (8 total)
- `user_profiles` - Extended user information beyond auth.users
- `projects` - User projects and workspaces
- `documents` - Documents and files within projects
- `document_sections` - Document sections with vector embeddings
- `agent_executions` - AI agent task execution logs
- `roadmaps` - Project roadmaps with versioning
- `roadmap_summaries` - AI-generated roadmap phase summaries
- `roadmap_source_references` - Audit trail of source documents

### Indexes (33 total)
- Performance indexes on all major tables
- Vector similarity search index (`idx_document_sections_embedding`)
- Hash-based lookup indexes for incremental scanning
- Project file path indexing for efficient scanning

### Functions (16 total)
- **Vector Search Functions**:
  - `match_document_sections()` - Basic vector similarity search
  - `match_document_sections_by_project()` - Project-scoped search
  - `hybrid_search_documents()` - Combined vector + full-text search

- **Document Processing Functions**:
  - `add_document_section()` - Add sections with embeddings and hash tracking
  - `update_document_embedding()` - Update existing embeddings
  - `get_user_document_stats()` - User document statistics
  - `get_project_document_hashes()` - Project document hash management
  - `mark_documents_deleted()` - Soft delete with cleanup
  - `update_document_hash()` - Update document content hash
  - `document_needs_processing()` - Incremental processing detection

- **Utility Functions**:
  - `calculate_sha256_hash()` - SHA-256 hash calculation
  - `cosine_similarity()` - Vector similarity calculation
  - `find_similar_sections_in_document()` - Intra-document similarity
  - `analyze_document_topics()` - Content clustering analysis
  - `refresh_vector_index_stats()` - Performance optimization

### Security Features
- **Row Level Security (RLS)** enabled on all tables (27 policies)
- **Explicit user scoping** in all vector search functions
- **Security DEFINER** functions with proper access controls
- **Service role permissions** for administrative operations

### Enhanced Features in Consolidated Schema

The consolidated schema includes several improvements over the individual migrations:

1. **Updated Function Signatures**:
   - All vector search functions now include explicit `user_id` parameters
   - Enhanced parameter validation and error handling
   - Improved security with explicit user ownership verification

2. **Hash Tracking Integration**:
   - Complete file hash management system
   - Incremental processing capabilities
   - Change detection and optimization

3. **Comprehensive Documentation**:
   - Complete COMMENT statements for all functions and tables
   - Detailed parameter descriptions
   - Usage examples and best practices

## Verification Process

A verification script (`scripts/verify_db_schema.py`) has been created to ensure the consolidated schema contains all components from the individual migrations. The script:

1. Extracts schema components from both consolidated and individual files
2. Compares tables, indexes, functions, policies, triggers, grants, and comments
3. Handles function signature differences intelligently
4. Provides detailed reporting of any discrepancies

## Usage

### Applying the Consolidated Schema

```bash
# Apply the consolidated schema
psql -h your-supabase-host -U postgres -d your-database -f supabase/migrations/000_consolidated_initial_schema.sql
```

### Verifying Schema Consistency

```bash
# Run the verification script
python scripts/verify_db_schema.py
```

### Development Workflow

1. **New Development**: Use the consolidated schema as the authoritative source
2. **Schema Changes**: Modify the consolidated schema directly
3. **Verification**: Run the verification script after changes
4. **Documentation**: Update this guide when making significant changes

## Benefits of Consolidation

1. **Single Source of Truth**: One authoritative schema file
2. **Improved Maintainability**: Easier to understand and modify
3. **Better Performance**: Optimized index creation and function definitions
4. **Enhanced Security**: Consistent security practices throughout
5. **Comprehensive Documentation**: Complete comments and usage guidance

## Future Considerations

1. **Alembic Integration**: Consider migrating to Alembic for more complex migration management
2. **Schema Versioning**: Implement proper version tracking for production deployments
3. **Automated Testing**: Add schema validation to CI/CD pipeline
4. **Backward Compatibility**: Ensure new changes don't break existing functionality

## Troubleshooting

If the verification script reports issues:

1. Check that all individual migration files exist
2. Verify function signatures match between consolidated and individual files
3. Ensure all grants and comments are properly included
4. Run the script with detailed output to identify specific discrepancies

The consolidated schema represents the complete, production-ready database structure for the AI Coding Agent project, incorporating all security, performance, and functionality requirements from the individual migration files.
