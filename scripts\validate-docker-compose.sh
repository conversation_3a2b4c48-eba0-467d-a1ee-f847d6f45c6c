#!/bin/bash

# Docker Compose Configuration Validator
# This script validates all Docker Compose configurations and checks for common issues

set -e

echo "🔍 Docker Compose Configuration Validator"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
        "WARNING") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "ERROR") echo -e "${RED}❌ $message${NC}" ;;
        "INFO") echo -e "${BLUE}ℹ️  $message${NC}" ;;
    esac
}

# Check if Docker and Docker Compose are available
check_docker() {
    print_status "INFO" "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_status "ERROR" "Docker is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_status "ERROR" "Docker daemon is not running"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_status "ERROR" "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    print_status "SUCCESS" "Docker and Docker Compose are available"
}

# Validate individual compose files
validate_compose_file() {
    local file=$1
    local description=$2
    
    print_status "INFO" "Validating $description ($file)..."
    
    if [[ ! -f "$file" ]]; then
        print_status "ERROR" "File $file does not exist"
        return 1
    fi
    
    if docker-compose -f "$file" config &> /dev/null; then
        print_status "SUCCESS" "$description is valid"
        return 0
    else
        print_status "ERROR" "$description has configuration errors"
        echo "Error details:"
        docker-compose -f "$file" config 2>&1 | head -10
        return 1
    fi
}

# Validate compose file combinations
validate_compose_combination() {
    local files=("$@")
    local file_list=$(printf " -f %s" "${files[@]}")
    local description=$(printf ", %s" "${files[@]}")
    description=${description:2} # Remove leading ", "
    
    print_status "INFO" "Validating combination: $description..."
    
    if docker-compose $file_list config &> /dev/null; then
        print_status "SUCCESS" "Combination is valid: $description"
        return 0
    else
        print_status "ERROR" "Combination has errors: $description"
        echo "Error details:"
        docker-compose $file_list config 2>&1 | head -10
        return 1
    fi
}

# Check for required secrets
check_secrets() {
    print_status "INFO" "Checking required secrets..."
    
    local secrets_dir="./secrets"
    local required_secrets=(
        "postgres_password.txt"
        "jwt_secret.txt"
        "supabase_url.txt"
        "supabase_key.txt"
        "supabase_service_key.txt"
        "code_server_password.txt"
        "grafana_admin_password.txt"
        "redis_password.txt"
    )
    
    local missing_secrets=()
    
    for secret in "${required_secrets[@]}"; do
        if [[ ! -f "$secrets_dir/$secret" ]]; then
            missing_secrets+=("$secret")
        fi
    done
    
    if [[ ${#missing_secrets[@]} -eq 0 ]]; then
        print_status "SUCCESS" "All required secrets are present"
    else
        print_status "WARNING" "Missing secrets: ${missing_secrets[*]}"
        print_status "INFO" "Run './scripts/setup-secrets.sh' to generate missing secrets"
    fi
}

# Check for external networks
check_networks() {
    print_status "INFO" "Checking external networks..."
    
    local required_networks=(
        "ai-coding-agent_web_network"
        "ai-coding-agent_internal_network"
        "ai-coding-agent-dev_monitoring-network"
    )
    
    local missing_networks=()
    
    for network in "${required_networks[@]}"; do
        if ! docker network ls --format "{{.Name}}" | grep -q "^$network$"; then
            missing_networks+=("$network")
        fi
    done
    
    if [[ ${#missing_networks[@]} -eq 0 ]]; then
        print_status "SUCCESS" "All required networks exist"
    else
        print_status "WARNING" "Missing networks: ${missing_networks[*]}"
        print_status "INFO" "Networks will be created automatically when starting services"
    fi
}

# Main validation
main() {
    echo
    check_docker
    echo
    
    # Validate individual files
    validate_compose_file "docker-compose.yml" "Main configuration"
    validate_compose_file "docker-compose.dev.yml" "Development overrides"
    validate_compose_file "docker-compose.prod.yml" "Production overrides"
    validate_compose_file "docker-compose.debug.yml" "Debug configuration"
    validate_compose_file "docker-compose.registry.yml" "Registry configuration"
    echo
    
    # Validate common combinations
    print_status "INFO" "Validating common file combinations..."
    validate_compose_combination "docker-compose.yml" "docker-compose.dev.yml"
    validate_compose_combination "docker-compose.yml" "docker-compose.prod.yml"
    validate_compose_combination "docker-compose.yml" "docker-compose.debug.yml"
    echo
    
    # Check dependencies
    check_secrets
    echo
    check_networks
    echo
    
    print_status "SUCCESS" "Docker Compose configuration validation complete!"
    echo
    print_status "INFO" "To start the development environment:"
    echo "  docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch"
    echo
    print_status "INFO" "To start the production environment:"
    echo "  docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d"
}

# Run main function
main "$@"
