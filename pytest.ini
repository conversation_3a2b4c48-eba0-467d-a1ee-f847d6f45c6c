[pytest]
env =
    TESTING=1
pythonpath = .
testpaths = containers/ai-orchestrator/tests
python_files = test_*.py
addopts = -v --tb=short -m "not integration"
asyncio_mode = auto
filterwarnings =
    ignore::sqlalchemy.exc.SAWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning

markers =
    integration: marks tests as integration tests (deselect with -m 'not integration')
