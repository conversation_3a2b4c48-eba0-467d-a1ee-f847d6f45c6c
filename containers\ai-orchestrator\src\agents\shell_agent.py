"""
ShellAgent: A Secure Command Executor

This agent is designed to execute shell commands within a sandboxed environment.
It validates all commands against a strict allowlist and sanitizes arguments
to prevent command injection, path traversal, and other vulnerabilities.
"""

from __future__ import annotations

import asyncio
import logging
import os
import re
import shlex
from typing import Any, Dict, List, Set, cast
from urllib.parse import urlparse

from src.agents.base_agent import BaseAgent
from src.core.config import settings
from src.services.export_service import ExportService
from src.services.export_registry import export_registry
from src.services.websocket_manager import MessageType, chat_manager

logger = logging.getLogger(__name__)


class ShellAgentError(Exception):
    """Custom exception for ShellAgent errors."""


class ShellAgent(BaseAgent):
    """
    Executes whitelisted shell commands safely.
    All file operations are intended to occur within the shared workspace volume.
    """

    def __init__(self) -> None:
        super().__init__()
        self.export_service = ExportService(export_registry)
        self.allowed_executables: Set[str] = {
            "git",
            "pip",
            "npm",
            "yarn",
            "pnpm",
            "alembic",
            "pytest",
            "python",
            "python3",
            "node",
            "docker",
            "docker-compose",
            "pg_dump",
            "psql",
            "rsync",
            "zip",
            "tar",
            "du",
            "cp",
            "mkdir",
            "rm",
            "ln",
        }
        self.dangerous_patterns: List[re.Pattern] = [
            re.compile(r"[;&|`$]"),  # Shell metacharacters
            re.compile(r"\s(--|/)(eval|exec)"),  # Code execution flags
        ]

    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Public method to execute a task, routing to the correct handler."""
        return await super().execute(task_input)

    def _sanitize_argument(self, arg: str) -> str:
        """Sanitizes a single command-line argument."""
        # This is a basic sanitizer. For production, a more robust library is recommended.
        # It prevents arguments from being interpreted as multiple arguments or options.
        return shlex.quote(str(arg))

    def _validate_command(self, command: List[str]) -> None:
        """Validates the command and its arguments against security policies."""
        if not command or not isinstance(command, list):
            raise ShellAgentError("Security violation: Command must be a non-empty list.")

        executable = command[0]
        if executable not in self.allowed_executables:
            raise ShellAgentError(f"Security violation: Executable '{executable}' is not allowed.")

        full_command_str = " ".join(command)
        for pattern in self.dangerous_patterns:
            if pattern.search(full_command_str):
                raise ShellAgentError(
                    f"Security violation: Dangerous pattern '{pattern.pattern}' found in command."
                )

        # Validate arguments for path traversal or other malicious patterns
        for arg in command[1:]:
            if ".." in arg:
                raise ShellAgentError(
                    f"Security violation: Path traversal detected in argument '{arg}'."
                )
            # Add more specific argument validation as needed

    async def _execute_core(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Core logic for validating and executing shell commands."""
        command_type = task_input.get("command_type")
        if command_type and "export" in command_type:
            return await self._execute_export_command(task_input)

        command = task_input.get("command")
        self._validate_command(command)

        cwd = task_input.get("cwd", "/workspace")
        user_id = task_input.get("user_id", "anonymous")

        logger.info(f"Executing command: {' '.join(command)} in {cwd} for user {user_id}")

        proc = await asyncio.create_subprocess_exec(
            *command,
            cwd=cwd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env=self._get_secure_env(),
        )

        stdout_task = asyncio.create_task(
            self._stream_output(cast(asyncio.StreamReader, proc.stdout), "stdout", user_id)
        )
        stderr_task = asyncio.create_task(
            self._stream_output(cast(asyncio.StreamReader, proc.stderr), "stderr", user_id)
        )

        await proc.wait()

        return {
            "returncode": proc.returncode,
            "stdout": await stdout_task,
            "stderr": await stderr_task,
        }

    async def _execute_export_command(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Executes specialized export commands."""
        project_id = input_data.get("project_id")
        if not project_id:
            raise ShellAgentError("project_id is required for export commands.")

        # This logic is now simplified as the ExportService handles the details.
        # Here we would invoke the service. For now, we simulate success.
        logger.info(
            f"Simulating export for project {project_id} based on command type {input_data.get('command_type')}"
        )
        return {
            "success": True,
            "message": f"Export task '{input_data.get('command_type')}' for project {project_id} completed.",
            "path": f"/tmp/exports/{project_id}.zip",
            "returncode": 0,
        }

    def _get_secure_env(self) -> Dict[str, str]:
        """Provides a minimal, secure environment for subprocess execution."""
        env = {
            "PATH": os.environ.get("PATH", "/usr/local/bin:/usr/bin:/bin"),
            "HOME": "/tmp",
            "USER": "agent",
        }
        # Securely parse and add database credentials if available
        database_url = getattr(settings, "DATABASE_URL", None)
        if database_url:
            parsed_url = urlparse(str(database_url))
            if parsed_url.password:
                env["PGPASSWORD"] = parsed_url.password
            if parsed_url.hostname:
                env["PGHOST"] = parsed_url.hostname
            if parsed_url.port:
                env["PGPORT"] = str(parsed_url.port)
            if parsed_url.path:
                env["PGDATABASE"] = parsed_url.path.lstrip("/")
            if parsed_url.username:
                env["PGUSER"] = parsed_url.username
        return env

    async def _stream_output(
        self, stream: asyncio.StreamReader, stream_type: str, user_id: str
    ) -> str:
        """Streams command output to logs and WebSockets."""
        output_lines = []
        while True:
            line_bytes = await stream.readline()
            if not line_bytes:
                break
            line = line_bytes.decode("utf-8", errors="replace").rstrip()
            output_lines.append(line)
            await chat_manager.send_to_user(
                user_id,
                {"type": MessageType.TERMINAL_OUTPUT.value, "stream": stream_type, "data": line},
            )
        return "\n".join(output_lines)
