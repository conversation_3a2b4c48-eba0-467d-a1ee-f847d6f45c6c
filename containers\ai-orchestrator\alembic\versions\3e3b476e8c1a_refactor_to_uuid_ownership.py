"""refactor_to_uuid_ownership

Revision ID: 3e3b476e8c1a
Revises: a1b2c3d4e5f6
Create Date: 2025-09-05 16:38:07.311952

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3e3b476e8c1a'
down_revision: Union[str, None] = 'a1b2c3d4e5f6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add owner_id column to projects table
    op.add_column('projects', sa.Column('owner_id', sa.UUID(), nullable=True))

    # Create index for owner_id
    op.create_index('ix_projects_owner_id', 'projects', ['owner_id'])

    # Drop the user_projects association table
    op.drop_table('user_projects')

    # Update roadmaps project_id to Integer (was incorrectly UUID)
    op.alter_column('roadmaps', 'project_id',
                    existing_type=sa.UUID(),
                    type_=sa.Integer(),
                    existing_nullable=False)

    # Add foreign key constraint for projects.owner_id
    op.create_foreign_key('fk_projects_owner_id_user_profiles', 'projects', 'user_profiles', ['owner_id'], ['supabase_user_id'], ondelete='CASCADE')

    # Note: Foreign keys for deployment_integrations, ingestion_errors, roadmaps, roadmap_summaries,
    # and roadmap_source_references are created in later migrations after those tables are created


def downgrade() -> None:
    # Drop foreign key constraints
    op.drop_constraint('fk_projects_owner_id_user_profiles', 'projects', type_='foreignkey')

    # Note: Foreign keys for deployment_integrations, ingestion_errors, roadmaps, roadmap_summaries,
    # and roadmap_source_references are dropped in later migrations

    # Revert column types
    op.alter_column('roadmaps', 'project_id',
                    existing_type=sa.Integer(),
                    type_=sa.UUID(),
                    existing_nullable=False)

    # Recreate user_projects association table
    op.create_table('user_projects',
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('project_id', sa.Integer(), nullable=False),
        sa.Column('role', sa.String(length=50), nullable=True, default='member'),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True, default=sa.func.now()),
        sa.ForeignKeyConstraint(['user_id'], ['user_profiles.id'], name='fk_user_projects_user_id_user_profiles'),
        sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name='fk_user_projects_project_id_projects'),
        sa.PrimaryKeyConstraint('user_id', 'project_id')
    )

    # Drop owner_id column from projects
    op.drop_index('ix_projects_owner_id', table_name='projects')
    op.drop_column('projects', 'owner_id')
