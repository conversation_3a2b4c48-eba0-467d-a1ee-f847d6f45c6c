"""
Health Checker for AI Coding Agent monitoring.

This module provides health checking functionality for monitoring services.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime
import time

logger = logging.getLogger(__name__)


class HealthChecker:
    """
    Health checker for monitoring services.

    Provides functionality to check the health of various components
    and services in the AI Coding Agent platform.
    """

    def __init__(self):
        """Initialize the health checker."""
        self.health_status: Dict[str, Any] = {}
        self.last_check_time: Dict[str, float] = {}
        self.check_interval: int = 30  # seconds

    async def check_service_health(self, service_name: str, check_func) -> Dict[str, Any]:
        """
        Check the health of a service.

        Args:
            service_name: Name of the service to check
            check_func: Async function that returns health status

        Returns:
            Dictionary with health status information
        """
        try:
            current_time = time.time()
            last_check = self.last_check_time.get(service_name, 0)

            # Only check if enough time has passed since last check
            if current_time - last_check < self.check_interval:
                return self.health_status.get(service_name, {"status": "unknown"})

            # Perform the health check
            status = await check_func()
            status["last_check"] = datetime.now().isoformat()
            status["response_time_ms"] = int((time.time() - current_time) * 1000)

            # Update health status
            self.health_status[service_name] = status
            self.last_check_time[service_name] = current_time

            return status

        except Exception as e:
            logger.error(f"Health check failed for {service_name}: {str(e)}")
            error_status = {
                "status": "unhealthy",
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }
            self.health_status[service_name] = error_status
            return error_status

    def get_service_health(self, service_name: str) -> Dict[str, Any]:
        """
        Get the cached health status of a service.

        Args:
            service_name: Name of the service

        Returns:
            Cached health status or unknown status
        """
        return self.health_status.get(service_name, {
            "status": "unknown",
            "last_check": None
        })

    def get_all_health_status(self) -> Dict[str, Any]:
        """
        Get health status for all monitored services.

        Returns:
            Dictionary with all service health statuses
        """
        return {
            service: self.get_service_health(service)
            for service in self.health_status.keys()
        }

    async def check_database_health(self) -> Dict[str, Any]:
        """
        Check database health status.

        Returns:
            Database health information
        """
        # This would be implemented with actual database checks
        # For now, return a mock response
        return {
            "status": "healthy",
            "connection_count": 5,
            "query_time_ms": 15,
            "database_size_mb": 1024
        }

    async def check_redis_health(self) -> Dict[str, Any]:
        """
        Check Redis health status.

        Returns:
            Redis health information
        """
        # This would be implemented with actual Redis checks
        # For now, return a mock response
        return {
            "status": "healthy",
            "memory_used_mb": 128,
            "connected_clients": 3,
            "keys_count": 1500
        }

    async def check_llm_provider_health(self, provider: str) -> Dict[str, Any]:
        """
        Check LLM provider health status.

        Args:
            provider: LLM provider name (ollama, openrouter, etc.)

        Returns:
            Provider health information
        """
        # This would be implemented with actual provider checks
        # For now, return a mock response
        return {
            "status": "healthy",
            "provider": provider,
            "models_available": 10,
            "response_time_ms": 250
        }

    def set_check_interval(self, interval: int) -> None:
        """
        Set the health check interval.

        Args:
            interval: Check interval in seconds
        """
        self.check_interval = interval


# Global health checker instance
_health_checker: Optional[HealthChecker] = None


def get_health_checker() -> HealthChecker:
    """
    Get the global health checker instance.

    Returns:
        HealthChecker instance
    """
    global _health_checker
    if _health_checker is None:
        _health_checker = HealthChecker()
    return _health_checker


async def initialize_health_checker() -> None:
    """
    Initialize the health checker with default services.
    """
    health_checker = get_health_checker()

    # Register default health checks
    health_checker.health_status["database"] = {
        "status": "pending",
        "last_check": None
    }
    health_checker.health_status["redis"] = {
        "status": "pending",
        "last_check": None
    }
    health_checker.health_status["ollama"] = {
        "status": "pending",
        "last_check": None
    }

    logger.info("Health checker initialized")


async def shutdown_health_checker() -> None:
    """
    Shutdown the health checker.
    """
    global _health_checker
    if _health_checker:
        _health_checker.health_status.clear()
        _health_checker.last_check_time.clear()
        _health_checker = None
        logger.info("Health checker shutdown")
