import logging
import uuid
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy.orm import Session

try:
    from src.agents.architect_agent import ArchitectAgent
    from src.models.database import get_db
    from src.models.roadmap import Roadmap, RoadmapItem
    from src.repository.roadmap_repository import RoadmapRepository
    from src.repository.task_repository import TaskRepository
    from src.services.auth_service import UserProfile
    from src.services.enhanced_llm_service import get_llm_service
    from src.services.lock_manager import LockManager
    from src.services.rag_service import get_rag_service
    from src.services.redis_service import get_redis_client
    from src.utils.auth import get_current_user
except ImportError:
    # Fallback to absolute imports for direct module execution
    from src.agents.architect_agent import ArchitectAgent
    from src.models.database import get_db
    from src.models.roadmap import Roadmap
    from src.repository.roadmap_repository import RoadmapRepository
    from src.repository.task_repository import TaskRepository
    from src.services.auth_service import UserProfile
    from src.services.enhanced_llm_service import get_llm_service
    from src.services.lock_manager import LockManager
    from src.services.rag_service import get_rag_service
    from src.services.redis_service import get_redis_client
    from src.utils.auth import get_current_user


logger = logging.getLogger(__name__)

router = APIRouter(prefix="/roadmaps", tags=["Roadmaps"])


# Pydantic models for request/response
class RoadmapStatusUpdate(BaseModel):
    status: str


class RoadmapItemResponse(BaseModel):
    id: int
    project_id: int
    parent_id: Optional[int]
    level: int
    sequence_order: int
    title: str
    description: Optional[str]
    item_type: str
    status: str
    agent_role: Optional[str]
    estimated_effort: Optional[str]
    priority: str
    dependencies: List[int]
    started_at: Optional[str]
    completed_at: Optional[str]
    assigned_to: Optional[str]
    created_at: str


@router.get("/projects/{project_id}/roadmap")
async def get_project_roadmap(
    project_id: int,
    include_completed: bool = True,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """Get the hierarchical roadmap for a project.

    Args:
        project_id: ID of the project
        include_completed: Whether to include completed items

    Returns:
        Dict containing the hierarchical roadmap
    """
    try:
        # Get roadmap items
        roadmap_items = await RoadmapRepository.get_project_roadmap(
            db, project_id, include_completed
        )

        # Convert to response format
        roadmap_data = []
        for item in roadmap_items:
            roadmap_data.append(
                {
                    "id": item.id,
                    "project_id": item.project_id,
                    "parent_id": item.parent_id,
                    "level": item.level,
                    "sequence_order": item.sequence_order,
                    "title": item.title,
                    "description": item.description,
                    "item_type": item.item_type,
                    "status": item.status,
                    "agent_role": item.agent_role,
                    "estimated_effort": item.estimated_effort,
                    "priority": item.priority,
                    "dependencies": item.dependencies,
                    "started_at": item.started_at.isoformat() if item.started_at else None,
                    "completed_at": item.completed_at.isoformat() if item.completed_at else None,
                    "assigned_to": item.assigned_to,
                    "created_at": item.created_at.isoformat(),
                }
            )

        return {"project_id": project_id, "roadmap": roadmap_data, "total_items": len(roadmap_data)}

    except Exception as e:
        logger.error(f"Failed to get project roadmap: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get project roadmap: {str(e)}",
        )


@router.put("/{item_id}/status")
async def update_roadmap_item_status(
    item_id: int,
    status_update: RoadmapStatusUpdate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
) -> RoadmapItemResponse:
    """Update the status of a roadmap item.

    Args:
        item_id: ID of the roadmap item
        status_update: New status information

    Returns:
        Updated roadmap item
    """
    try:
        # Validate status
        valid_statuses = ["pending", "in_progress", "completed", "blocked"]
        if status_update.status not in valid_statuses:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}",
            )

        # Get the roadmap item first to verify ownership
        item = await RoadmapRepository.get_roadmap_item(db, item_id)
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Roadmap item not found"
            )

        # Update the status
        updated_item = await RoadmapRepository.update_item_status(db, item_id, status_update.status)

        # If status changed to completed, set completed_at
        if status_update.status == "completed" and not updated_item.completed_at:
            updated_item.completed_at = db.func.now()
            db.commit()
            db.refresh(updated_item)

        # If status changed to in_progress, set started_at
        if status_update.status == "in_progress" and not updated_item.started_at:
            updated_item.started_at = db.func.now()
            db.commit()
            db.refresh(updated_item)

        return RoadmapItemResponse(
            id=updated_item.id,
            project_id=updated_item.project_id,
            parent_id=updated_item.parent_id,
            level=updated_item.level,
            sequence_order=updated_item.sequence_order,
            title=updated_item.title,
            description=updated_item.description,
            item_type=updated_item.item_type,
            status=updated_item.status,
            agent_role=updated_item.agent_role,
            estimated_effort=updated_item.estimated_effort,
            priority=updated_item.priority,
            dependencies=updated_item.dependencies,
            started_at=updated_item.started_at.isoformat() if updated_item.started_at else None,
            completed_at=updated_item.completed_at.isoformat()
            if updated_item.completed_at
            else None,
            assigned_to=updated_item.assigned_to,
            created_at=updated_item.created_at.isoformat(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update roadmap item status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update roadmap item status: {str(e)}",
        )


@router.get("/{item_id}")
async def get_roadmap_item(
    item_id: int, current_user=Depends(get_current_user), db: Session = Depends(get_db)
) -> RoadmapItemResponse:
    """Get a specific roadmap item by ID.

    Args:
        item_id: ID of the roadmap item

    Returns:
        Roadmap item details
    """
    try:
        item = await RoadmapRepository.get_roadmap_item(db, item_id)
        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Roadmap item not found"
            )

        return RoadmapItemResponse(
            id=item.id,
            project_id=item.project_id,
            parent_id=item.parent_id,
            level=item.level,
            sequence_order=item.sequence_order,
            title=item.title,
            description=item.description,
            item_type=item.item_type,
            status=item.status,
            agent_role=item.agent_role,
            estimated_effort=item.estimated_effort,
            priority=item.priority,
            dependencies=item.dependencies,
            started_at=item.started_at.isoformat() if item.started_at else None,
            completed_at=item.completed_at.isoformat() if item.completed_at else None,
            assigned_to=item.assigned_to,
            created_at=item.created_at.isoformat(),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get roadmap item: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get roadmap item: {str(e)}",
        )


@router.post("/{roadmap_id}/improve", status_code=status.HTTP_202_ACCEPTED)
async def improve_roadmap_endpoint(
    roadmap_id: uuid.UUID,
    current_user: UserProfile = Depends(get_current_user),
    db: Session = Depends(get_db),
    redis_client: Depends = Depends(get_redis_client),
    rag_service: Depends = Depends(get_rag_service),
    llm_service: Depends = Depends(get_llm_service),
):
    """
    Initiates the process of improving a roadmap using the Architect and Indexer agents.
    This endpoint orchestrates the entire workflow, including locking, fetching data,
    calling the thinking agent, and dispatching the storing task.
    """
    # Step 1: Fetch Existing Roadmap from the database
    # Querying the main 'roadmaps' table directly as RoadmapRepository works with 'roadmap_items'
    roadmap = (
        db.query(Roadmap)
        .filter(Roadmap.id == roadmap_id, Roadmap.owner_id == current_user.supabase_user_id)
        .first()
    )

    if not roadmap:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Roadmap not found or access denied for the current user.",
        )

    project_id = roadmap.project_id

    # Step 2: Acquire a Project-Level Lock
    lock_manager = LockManager(redis_client, tenant_id=str(current_user.supabase_user_id))
    lock_acquired = await lock_manager.acquire_lock(
        project_id=project_id,
        agent_role="improve_roadmap_orchestrator",
        task_id=0,  # Orchestration is not a formal task
    )

    if not lock_acquired:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="A process is already running for this project's roadmap. Please try again later.",
        )

    try:
        # Step 3: Call the "Thinker" (ArchitectAgent)
        architect_agent = ArchitectAgent(rag_service=rag_service, llm_service=llm_service)
        architect_result = await architect_agent.improve_roadmap(
            owner_id=current_user.supabase_user_id, existing_roadmap=roadmap.content
        )

        # Step 4: Validate the output from the ArchitectAgent
        if "improved_roadmap" not in architect_result or "source_refs" not in architect_result:
            logger.error(f"Invalid output from ArchitectAgent for roadmap {roadmap_id}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Architect agent returned an invalid data structure.",
            )

        # Step 5: Dispatch a task to the "Storer" (IndexerAgent)
        # This is done by creating a new task in the database for the dispatcher to pick up.
        task_payload = {
            "roadmap_id": str(roadmap.id),
            "project_id": project_id,
            "owner_id": str(current_user.supabase_user_id),
            "improved_roadmap": architect_result["improved_roadmap"],
            "source_refs": architect_result["source_refs"],
        }

        await TaskRepository.create_task(
            db, project_id=project_id, agent_role="indexer", input_data=task_payload
        )

    finally:
        # Step 6: Release the Project Lock
        await lock_manager.release_lock(project_id)

    # Step 7: Return a success response
    # The actual result will be processed asynchronously by the IndexerAgent.
    return {
        "status": "improvement_process_started",
        "message": "The roadmap improvement process has been successfully initiated.",
    }
