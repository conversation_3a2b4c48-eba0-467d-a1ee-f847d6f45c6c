"""
Unit tests for WebSocket chat functionality and ArchitectAgent chat methods.

These tests verify the proper functioning of:
- WebSocket connection management
- Chat message handling
- ArchitectAgent chat integration
- Error handling and edge cases
"""

import asyncio
import json
from datetime import datetime
from typing import Any, Dict
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi.testclient import TestClient
from src.agents.architect_agent import ArchitectAgent
from src.agents.base_agent import ValidationResult
from src.main import app

# Import classes to test
from src.services.websocket_manager import ChatMessage, MessageType, WebSocketChatManager


class TestArchitectAgent(ArchitectAgent):
    """Concrete test subclass of ArchitectAgent that implements abstract methods."""

    async def _execute_core(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Implement abstract method for testing."""
        return await super().execute(task_input)

    async def _validate_agent_specific_prerequisites(
        self, task_input: Dict[str, Any]
    ) -> ValidationResult:
        """Implement abstract method for testing."""
        return ValidationResult.success("Test validation passed")

    async def _validate_agent_specific_completion(
        self, task_input: Dict[str, Any], result: Dict[str, Any]
    ) -> ValidationResult:
        """Implement abstract method for testing."""
        return ValidationResult.success("Test completion validation passed")


class TestWebSocketChatManager:
    """Test cases for WebSocketChatManager."""

    @pytest.fixture
    def chat_manager(self):
        """Create a WebSocketChatManager instance for testing."""
        return WebSocketChatManager()

    @pytest.fixture
    def mock_websocket(self):
        """Create a mock WebSocket for testing."""
        mock_ws = AsyncMock()
        mock_ws.send_text = AsyncMock()
        mock_ws.close = AsyncMock()
        return mock_ws

    @pytest.mark.asyncio
    async def test_connect_user(self, chat_manager, mock_websocket):
        """Test connecting a user to the chat manager."""
        user_id = "test_user_123"

        connection_id = await chat_manager.connect(mock_websocket, user_id)

        assert connection_id is not None
        assert len(connection_id) > 0
        assert connection_id in chat_manager.connections
        assert chat_manager.connections[connection_id].user_id == user_id
        assert chat_manager.connections[connection_id].websocket == mock_websocket

    @pytest.mark.asyncio
    async def test_disconnect_user(self, chat_manager, mock_websocket):
        """Test disconnecting a user from the chat manager."""
        user_id = "test_user_123"

        # Connect first
        connection_id = await chat_manager.connect(mock_websocket, user_id)
        assert connection_id in chat_manager.connections

        # Disconnect
        await chat_manager.disconnect(connection_id)
        assert connection_id not in chat_manager.connections

    @pytest.mark.asyncio
    async def test_send_message_to_connection(self, chat_manager, mock_websocket):
        """Test sending a message to a specific connection."""
        user_id = "test_user_123"

        # Connect user
        connection_id = await chat_manager.connect(mock_websocket, user_id)

        # Send message
        test_message = {
            "type": "test_message",
            "content": "Hello, World!",
            "timestamp": datetime.utcnow().isoformat(),
        }

        await chat_manager.send_to_connection(connection_id, test_message)

        # Verify WebSocket send was called
        mock_websocket.send_text.assert_called()
        # Check that at least one call was made with the expected message
        calls = mock_websocket.send_text.call_args_list
        assert len(calls) >= 1
        # Find the call with our test message
        found_test_message = False
        for call in calls:
            sent_data = call[0][0]
            sent_message = json.loads(sent_data)
            if sent_message.get("content") == "Hello, World!":
                found_test_message = True
                break
        assert found_test_message, "Test message was not sent"

    @pytest.mark.asyncio
    async def test_broadcast_to_all(self, chat_manager):
        """Test broadcasting a message to all connected users."""
        # Connect multiple users
        user1_ws = AsyncMock()
        user2_ws = AsyncMock()

        connection1 = await chat_manager.connect(user1_ws, "user1")
        connection2 = await chat_manager.connect(user2_ws, "user2")

        # Broadcast message
        broadcast_message = {
            "type": "broadcast",
            "content": "System announcement",
            "timestamp": datetime.utcnow().isoformat(),
        }

        sent_count = await chat_manager.broadcast_to_all(broadcast_message)

        # Verify message was sent to all connections
        assert sent_count == 2
        user1_ws.send_text.assert_called()
        user2_ws.send_text.assert_called()
        # Check that both users received the broadcast message
        user1_calls = user1_ws.send_text.call_args_list
        user2_calls = user2_ws.send_text.call_args_list
        assert len(user1_calls) >= 1
        assert len(user2_calls) >= 1

    @pytest.mark.asyncio
    async def test_handle_ping_message(self, chat_manager, mock_websocket):
        """Test handling ping messages."""
        user_id = "test_user_123"
        connection_id = await chat_manager.connect(mock_websocket, user_id)

        ping_message = json.dumps(
            {"type": "ping", "content": "", "timestamp": datetime.utcnow().isoformat()}
        )

        await chat_manager.handle_message(connection_id, ping_message)

        # Should respond with pong
        mock_websocket.send_text.assert_called()
        calls = mock_websocket.send_text.call_args_list
        assert len(calls) >= 1
        # Find the pong response
        found_pong = False
        for call in calls:
            response_data = call[0][0]
            response = json.loads(response_data)
            if response.get("type") == MessageType.PONG.value:
                found_pong = True
                break
        assert found_pong, "Pong response was not sent"

    @pytest.mark.asyncio
    async def test_get_connection_stats(self, chat_manager, mock_websocket):
        """Test getting connection statistics."""
        # Connect some users
        await chat_manager.connect(mock_websocket, "user1")
        await chat_manager.connect(AsyncMock(), "user2")

        stats = chat_manager.get_connection_stats()

        assert stats["total_connections"] == 2
        assert stats["unique_users"] == 2
        assert len(stats["connections"]) == 2


class TestArchitectAgentChat:
    """Test cases for ArchitectAgent chat functionality."""

    @pytest.fixture
    def architect_agent(self):
        """Create an ArchitectAgent instance for testing."""
        agent = TestArchitectAgent()
        # Mock the LLM service
        agent._llm_service = AsyncMock()
        return agent

    @pytest.mark.asyncio
    async def test_handle_chat_message_success(self, architect_agent):
        """Test successful chat message handling."""
        user_id = "test_user_123"
        message = "Hello, can you help me with a coding problem?"

        # Mock LLM service response
        mock_llm_response = MagicMock()
        mock_llm_response.content = "Of course! I'd be happy to help you with your coding problem. What specific issue are you facing?"
        architect_agent._llm_service.generate.return_value = mock_llm_response

        result = await architect_agent.handle_chat_message(user_id, message)

        assert result["response"] == mock_llm_response.content
        assert result["user_id"] == user_id
        assert result["agent_type"] == "architect"
        assert "timestamp" in result
        assert "conversation_length" in result

    @pytest.mark.asyncio
    async def test_handle_chat_message_conversation_history(self, architect_agent):
        """Test that conversation history is maintained."""
        user_id = "test_user_123"

        # Mock LLM service
        mock_llm_response = MagicMock()
        mock_llm_response.content = "I understand your question."
        architect_agent._llm_service.generate.return_value = mock_llm_response

        # Send first message
        await architect_agent.handle_chat_message(user_id, "First message")

        # Send second message
        await architect_agent.handle_chat_message(user_id, "Second message")

        # Check conversation history
        history = await architect_agent.get_conversation_history(user_id)
        assert len(history) == 4  # 2 user messages + 2 assistant responses
        assert history[0]["role"] == "user"
        assert history[0]["content"] == "First message"
        assert history[1]["role"] == "assistant"
        assert history[2]["role"] == "user"
        assert history[2]["content"] == "Second message"

    @pytest.mark.asyncio
    async def test_handle_chat_message_error_handling(self, architect_agent):
        """Test error handling in chat message processing."""
        user_id = "test_user_123"
        message = "Test message"

        # Make LLM service raise an exception
        architect_agent._llm_service.generate.side_effect = Exception("LLM service error")

        result = await architect_agent.handle_chat_message(user_id, message)

        assert "error processing your message" in result["response"].lower()
        assert result["user_id"] == user_id
        assert "error" in result

    @pytest.mark.asyncio
    async def test_clear_conversation_history(self, architect_agent):
        """Test clearing conversation history."""
        user_id = "test_user_123"

        # Mock LLM service
        mock_llm_response = MagicMock()
        mock_llm_response.content = "Response"
        architect_agent._llm_service.generate.return_value = mock_llm_response

        # Add some conversation history
        await architect_agent.handle_chat_message(user_id, "Test message")
        history = await architect_agent.get_conversation_history(user_id)
        assert len(history) > 0

        # Clear history
        result = await architect_agent.clear_conversation_history(user_id)
        assert result is True

        # Verify history is cleared
        history = await architect_agent.get_conversation_history(user_id)
        assert len(history) == 0

    @pytest.mark.asyncio
    async def test_conversation_history_limit(self, architect_agent):
        """Test that conversation history respects the maximum limit."""
        user_id = "test_user_123"

        # Mock LLM service
        mock_llm_response = MagicMock()
        mock_llm_response.content = "Response"
        architect_agent._llm_service.generate.return_value = mock_llm_response

        # Set a small limit for testing
        architect_agent._max_conversation_history = 4

        # Send multiple messages (more than the limit)
        for i in range(10):
            await architect_agent.handle_chat_message(user_id, f"Message {i}")

        # Check that history is limited
        history = await architect_agent.get_conversation_history(user_id)
        assert len(history) <= architect_agent._max_conversation_history

    @pytest.mark.asyncio
    async def test_get_conversation_context(self, architect_agent):
        """Test getting conversation context for LLM."""
        user_id = "test_user_123"

        # Add some messages to history manually
        architect_agent._conversation_history[user_id] = [
            {"role": "user", "content": "Hello", "timestamp": "2023-01-01T00:00:00"},
            {"role": "assistant", "content": "Hi there!", "timestamp": "2023-01-01T00:00:01"},
            {"role": "user", "content": "How are you?", "timestamp": "2023-01-01T00:00:02"},
            {"role": "assistant", "content": "I'm doing well!", "timestamp": "2023-01-01T00:00:03"},
        ]

        context = architect_agent._get_conversation_context(user_id, max_messages=2)

        assert len(context) == 2
        assert context[0]["content"] == "How are you?"
        assert context[1]["content"] == "I'm doing well!"
        # Timestamps should be excluded from context
        assert "timestamp" not in context[0]


class TestWebSocketRouter:
    """Test cases for WebSocket router endpoints."""

    @pytest.fixture
    def client(self):
        """Create a test client for FastAPI application."""
        return TestClient(app)

    def test_websocket_stats_endpoint(self, client):
        """Test the WebSocket stats endpoint."""
        response = client.get("/ws/stats")
        assert response.status_code == 200

        data = response.json()
        assert "websocket_stats" in data
        assert "architect_agent_status" in data
        assert "chat_endpoint" in data
        assert "timestamp" in data

    @pytest.mark.asyncio
    async def test_broadcast_message_endpoint(self, client):
        """Test the broadcast message endpoint."""
        broadcast_data = {"content": "Test broadcast message", "type": "system_message"}

        response = client.post("/ws/broadcast", json=broadcast_data)
        assert response.status_code == 200

        data = response.json()
        assert data["success"] is True
        assert "recipients" in data
        assert "timestamp" in data


class TestChatMessage:
    """Test cases for ChatMessage model."""

    def test_chat_message_creation(self):
        """Test creating a ChatMessage instance."""
        message = ChatMessage(
            message_id="test_123",
            user_id="user_456",
            content="Hello, world!",
            message_type=MessageType.USER_MESSAGE,
            timestamp=datetime.utcnow(),
            metadata={"source": "test"},
        )

        assert message.message_id == "test_123"
        assert message.user_id == "user_456"
        assert message.content == "Hello, world!"
        assert message.message_type == MessageType.USER_MESSAGE
        assert message.metadata["source"] == "test"

    def test_chat_message_to_dict(self):
        """Test converting ChatMessage to dictionary."""
        message = ChatMessage(
            message_id="test_123",
            user_id="user_456",
            content="Hello, world!",
            message_type=MessageType.USER_MESSAGE,
            timestamp=datetime.utcnow(),
            metadata={},
        )

        message_dict = message.to_dict()

        assert message_dict["message_id"] == "test_123"
        assert message_dict["user_id"] == "user_456"
        assert message_dict["content"] == "Hello, world!"
        assert message_dict["type"] == MessageType.USER_MESSAGE.value


class TestIntegration:
    """Integration tests for the complete chat system."""

    @pytest.mark.asyncio
    async def test_end_to_end_chat_flow(self):
        """Test a complete chat flow from WebSocket to ArchitectAgent and back."""
        # This would be a more complex integration test
        # For now, we'll test the key components work together

        chat_manager = WebSocketChatManager()
        architect_agent = TestArchitectAgent()

        # Mock dependencies
        mock_websocket = AsyncMock()
        architect_agent._llm_service = AsyncMock()

        mock_llm_response = MagicMock()
        mock_llm_response.content = "Hello! How can I help you today?"
        architect_agent._llm_service.generate.return_value = mock_llm_response

        # Connect user
        connection_id = await chat_manager.connect(mock_websocket, "test_user")

        # Simulate user message
        user_message = {
            "type": "user_message",
            "content": "Hello, I need help with Python",
            "metadata": {},
        }

        # Process through chat manager (this would normally call the agent)
        chat_message = ChatMessage.from_dict(
            {
                **user_message,
                "message_id": "test_msg_1",
                "user_id": "test_user",
                "timestamp": datetime.utcnow().isoformat(),
            }
        )

        # Get agent response
        agent_response = await architect_agent.handle_chat_message(
            "test_user", "Hello, I need help with Python"
        )

        # Verify response
        assert agent_response["response"] == mock_llm_response.content
        assert agent_response["agent_type"] == "architect"

        # Verify connection is maintained
        assert connection_id in chat_manager.connections


class TestArchitectAgentTemplateSelection:
    """Test cases for ArchitectAgent template selection logic."""

    @pytest.fixture
    def architect_agent(self):
        """Create an ArchitectAgent instance for testing."""
        return TestArchitectAgent()

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = MagicMock()
        return session

    @pytest.fixture
    def mock_session(self):
        """Mock interview session."""
        session = MagicMock()
        session.session_id = "test_session_123"
        session.project_id = "test_project_456"
        session.user_id = "test_user_789"
        return session

    @pytest.fixture
    def mock_conversation_repo(self):
        """Mock conversation repository."""
        repo = MagicMock()
        return repo

    @pytest.mark.asyncio
    async def test_template_selection_fastapi_backend(
        self, architect_agent, mock_db_session, mock_session, mock_conversation_repo
    ):
        """Test template selection for a backend/API project."""
        # Mock interview responses for a backend project
        responses = {
            "project_type": "backend",
            "key_features": ["API development", "data processing", "REST endpoints"],
            "project_name": "My API Service",
        }

        # Mock LLM response
        mock_llm = AsyncMock()
        mock_response = MagicMock()
        mock_response.content = '"fastapi-starter"'
        mock_llm.generate.return_value = mock_response

        # Mock imports
        mock_imports = {"GenerateRequest": MagicMock, "TaskRepository": MagicMock()}

        # Mock project lookup
        mock_project = MagicMock()
        mock_project.name = "my-api-service"
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_project

        # Mock task creation
        mock_task_repo = MagicMock()
        mock_imports["TaskRepository"] = mock_task_repo

        with (
            patch.object(architect_agent, "_get_responses_for_roadmap", return_value=responses),
            patch.object(architect_agent, "_get_llm", return_value=mock_llm),
            patch.object(architect_agent, "_get_imports", return_value=mock_imports),
            patch.object(architect_agent, "_generate_roadmap", return_value="Test roadmap"),
            patch("src.agents.architect_agent.logger") as mock_logger,
        ):
            # Execute the method that contains template selection logic
            result = await architect_agent._handle_generating_roadmap_state(
                mock_db_session, mock_session, mock_conversation_repo
            )

            # Verify LLM was called with correct prompt
            mock_llm.generate.assert_called_once()
            call_args = mock_llm.generate.call_args[0][0]
            assert "fastapi-starter" in call_args.prompt
            assert "backend" in call_args.prompt
            assert json.dumps(responses, indent=2) in call_args.prompt

            # Verify template was selected correctly
            mock_logger.info.assert_any_call(
                "LLM chose template: 'fastapi-starter' for project test_project_456"
            )

            # Verify ShellAgent task was created
            mock_task_repo.create_task.assert_called_once()
            task_call_args = mock_task_repo.create_task.call_args
            assert task_call_args[0][1] == "test_project_456"  # project_id
            assert task_call_args[0][2] == "shell"  # agent_role

            # Verify commands include template copying
            task_input_data = task_call_args[0][3]
            commands = task_input_data["commands"]
            assert any("mkdir -p /app/workspace/my-api-service" in cmd for cmd in commands)
            assert any(
                "cp -r /app/templates/project-templates/fastapi-starter/. /app/workspace/my-api-service/"
                in cmd
                for cmd in commands
            )

    @pytest.mark.asyncio
    async def test_template_selection_react_frontend(
        self, architect_agent, mock_db_session, mock_session, mock_conversation_repo
    ):
        """Test template selection for a frontend/React project."""
        # Mock interview responses for a frontend project
        responses = {
            "project_type": "frontend",
            "key_features": ["user interface", "dashboard", "responsive design"],
            "project_name": "My Dashboard App",
        }

        # Mock LLM response
        mock_llm = AsyncMock()
        mock_response = MagicMock()
        mock_response.content = '"react-vite-tailwind"'
        mock_llm.generate.return_value = mock_response

        # Mock imports and project
        mock_imports = {"GenerateRequest": MagicMock, "TaskRepository": MagicMock()}

        mock_project = MagicMock()
        mock_project.name = "my-dashboard-app"
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_project

        with (
            patch.object(architect_agent, "_get_responses_for_roadmap", return_value=responses),
            patch.object(architect_agent, "_get_llm", return_value=mock_llm),
            patch.object(architect_agent, "_get_imports", return_value=mock_imports),
            patch.object(architect_agent, "_generate_roadmap", return_value="Test roadmap"),
            patch("src.agents.architect_agent.logger") as mock_logger,
        ):
            result = await architect_agent._handle_generating_roadmap_state(
                mock_db_session, mock_session, mock_conversation_repo
            )

            # Verify React template was selected
            mock_logger.info.assert_any_call(
                "LLM chose template: 'react-vite-tailwind' for project test_project_456"
            )

            # Verify correct template path in copy command
            mock_task_repo = mock_imports["TaskRepository"]
            task_input_data = mock_task_repo.create_task.call_args[0][3]
            commands = task_input_data["commands"]
            assert any(
                "cp -r /app/templates/project-templates/react-vite-tailwind/. /app/workspace/my-dashboard-app/"
                in cmd
                for cmd in commands
            )

    @pytest.mark.asyncio
    async def test_template_selection_blank_fallback(
        self, architect_agent, mock_db_session, mock_session, mock_conversation_repo
    ):
        """Test fallback to blank template when LLM response is invalid."""
        responses = {
            "project_type": "unknown",
            "key_features": ["unclear requirements"],
            "project_name": "Test Project",
        }

        # Mock invalid LLM response
        mock_llm = AsyncMock()
        mock_response = MagicMock()
        mock_response.content = '"invalid-template"'
        mock_llm.generate.return_value = mock_response

        mock_imports = {"GenerateRequest": MagicMock, "TaskRepository": MagicMock()}

        with (
            patch.object(architect_agent, "_get_responses_for_roadmap", return_value=responses),
            patch.object(architect_agent, "_get_llm", return_value=mock_llm),
            patch.object(architect_agent, "_get_imports", return_value=mock_imports),
            patch.object(architect_agent, "_generate_roadmap", return_value="Test roadmap"),
            patch("src.agents.architect_agent.logger") as mock_logger,
        ):
            result = await architect_agent._handle_generating_roadmap_state(
                mock_db_session, mock_session, mock_conversation_repo
            )

            # Verify warning about invalid template
            mock_logger.warning.assert_any_call(
                "LLM chose an invalid template: 'invalid-template'. Defaulting to 'blank'."
            )

            # Verify no ShellAgent task was created (since template is blank)
            mock_task_repo = mock_imports["TaskRepository"]
            mock_task_repo.create_task.assert_not_called()

    @pytest.mark.asyncio
    async def test_template_selection_llm_error_handling(
        self, architect_agent, mock_db_session, mock_session, mock_conversation_repo
    ):
        """Test error handling when LLM fails."""
        responses = {
            "project_type": "backend",
            "key_features": ["API development"],
            "project_name": "Test Project",
        }

        # Mock LLM to raise exception
        mock_llm = AsyncMock()
        mock_llm.generate.side_effect = Exception("LLM service unavailable")

        mock_imports = {"GenerateRequest": MagicMock, "TaskRepository": MagicMock()}

        with (
            patch.object(architect_agent, "_get_responses_for_roadmap", return_value=responses),
            patch.object(architect_agent, "_get_llm", return_value=mock_llm),
            patch.object(architect_agent, "_get_imports", return_value=mock_imports),
            patch.object(architect_agent, "_generate_roadmap", return_value="Test roadmap"),
            patch("src.agents.architect_agent.logger") as mock_logger,
        ):
            result = await architect_agent._handle_generating_roadmap_state(
                mock_db_session, mock_session, mock_conversation_repo
            )

            # Verify error was logged
            mock_logger.error.assert_any_call(
                "Failed to determine project template via LLM: LLM service unavailable. Defaulting to 'blank'."
            )

            # Verify no ShellAgent task was created
            mock_task_repo = mock_imports["TaskRepository"]
            mock_task_repo.create_task.assert_not_called()

    @pytest.mark.asyncio
    async def test_template_selection_project_not_found(
        self, architect_agent, mock_db_session, mock_session, mock_conversation_repo
    ):
        """Test handling when project is not found in database."""
        responses = {
            "project_type": "backend",
            "key_features": ["API development"],
            "project_name": "Test Project",
        }

        # Mock LLM response
        mock_llm = AsyncMock()
        mock_response = MagicMock()
        mock_response.content = '"fastapi-starter"'
        mock_llm.generate.return_value = mock_response

        # Mock project lookup to return None
        mock_db_session.query.return_value.filter.return_value.first.return_value = None

        mock_imports = {"GenerateRequest": MagicMock, "TaskRepository": MagicMock()}

        with (
            patch.object(architect_agent, "_get_responses_for_roadmap", return_value=responses),
            patch.object(architect_agent, "_get_llm", return_value=mock_llm),
            patch.object(architect_agent, "_get_imports", return_value=mock_imports),
            patch.object(architect_agent, "_generate_roadmap", return_value="Test roadmap"),
            patch("src.agents.architect_agent.logger") as mock_logger,
        ):
            result = await architect_agent._handle_generating_roadmap_state(
                mock_db_session, mock_session, mock_conversation_repo
            )

            # Verify error about project not found
            mock_logger.error.assert_any_call(
                "Could not find project with ID test_project_456 to apply template."
            )

            # Verify no ShellAgent task was created
            mock_task_repo = mock_imports["TaskRepository"]
            mock_task_repo.create_task.assert_not_called()

    @pytest.mark.asyncio
    async def test_template_selection_parsing_edge_cases(
        self, architect_agent, mock_db_session, mock_session, mock_conversation_repo
    ):
        """Test parsing of various LLM response formats."""
        responses = {
            "project_type": "frontend",
            "key_features": ["UI components"],
            "project_name": "Test Project",
        }

        test_cases = [
            ('"react-vite-tailwind"', "react-vite-tailwind"),  # Normal case
            ("react-vite-tailwind", "react-vite-tailwind"),  # No quotes
            ('"fastapi-starter" extra text', "fastapi-starter"),  # Extra text
            ("invalid response", None),  # No valid template found
        ]

        for llm_content, expected_template in test_cases:
            mock_llm = AsyncMock()
            mock_response = MagicMock()
            mock_response.content = llm_content
            mock_llm.generate.return_value = mock_response

            mock_imports = {"GenerateRequest": MagicMock, "TaskRepository": MagicMock()}

            with (
                patch.object(architect_agent, "_get_responses_for_roadmap", return_value=responses),
                patch.object(architect_agent, "_get_llm", return_value=mock_llm),
                patch.object(architect_agent, "_get_imports", return_value=mock_imports),
                patch.object(architect_agent, "_generate_roadmap", return_value="Test roadmap"),
                patch("src.agents.architect_agent.logger") as mock_logger,
            ):
                result = await architect_agent._handle_generating_roadmap_state(
                    mock_db_session, mock_session, mock_conversation_repo
                )

                if expected_template and expected_template != "blank":
                    # Should have created a task
                    mock_task_repo = mock_imports["TaskRepository"]
                    if expected_template in ["fastapi-starter", "react-vite-tailwind"]:
                        mock_task_repo.create_task.assert_called()
                    else:
                        mock_task_repo.create_task.assert_not_called()
                else:
                    # Should not have created a task
                    mock_task_repo = mock_imports["TaskRepository"]
                    mock_task_repo.create_task.assert_not_called()


# Pytest configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create an event loop for async tests."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
