"""
Test Script for Advanced AI Orchestrator

Demonstrates the integration of LangGraph orchestration with CUDA acceleration.

Usage:
    python test_advanced_orchestrator.py
"""

import asyncio
import logging
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'containers', 'ai-orchestrator', 'src'))

from services.advanced_ai_orchestrator import get_advanced_ai_orchestrator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_basic_orchestration():
    """Test basic orchestration functionality."""
    print("🧪 Testing Advanced AI Orchestrator...")
    print("=" * 50)

    try:
        # Get orchestrator instance
        orchestrator = await get_advanced_ai_orchestrator()
        print("✅ Orchestrator initialized successfully")

        # Test performance analytics
        analytics = await orchestrator.get_performance_analytics()
        print(f"📊 Initial analytics: {analytics}")

        # Test complex task execution
        test_task = """
        Create a simple web application with the following requirements:
        1. User authentication system
        2. Dashboard with user profile
        3. Basic CRUD operations for tasks
        4. Responsive design
        5. RESTful API backend
        """

        print(f"🚀 Executing complex task: {test_task[:100]}...")

        result = await orchestrator.execute_complex_task(
            task=test_task,
            use_cuda=True,
            thread_id="test_workflow_001"
        )

        print("✅ Task execution completed")
        print(f"📈 Performance metrics: {result.get('performance', {})}")
        print(f"🤖 Agent results: {len(result.get('results', {}))} agents executed")

        # Show some results
        for agent, agent_result in result.get('results', {}).items():
            print(f"  • {agent}: {agent_result.get('status', 'unknown')}")

        # Test resource optimization
        await orchestrator.optimize_resources()
        print("🧹 Resources optimized")

        # Final analytics
        final_analytics = await orchestrator.get_performance_analytics()
        print(f"📊 Final analytics: {final_analytics}")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.exception("Test execution error")
        return False


async def test_parallel_execution():
    """Test parallel task execution."""
    print("\n🧪 Testing Parallel Task Execution...")
    print("=" * 50)

    try:
        orchestrator = await get_advanced_ai_orchestrator()

        # Define multiple tasks
        tasks = [
            "Create a user authentication API with JWT tokens",
            "Design a responsive dashboard UI component",
            "Implement a task management system with database",
            "Set up CI/CD pipeline for deployment"
        ]

        print(f"🚀 Executing {len(tasks)} tasks in parallel...")

        results = await orchestrator.execute_parallel_tasks(tasks, max_concurrent=2)

        print("✅ Parallel execution completed")
        for i, result in enumerate(results):
            success = result.get('success', False)
            duration = result.get('performance', {}).get('total_duration', 0)
            print(f"  Task {i+1}: {'✅' if success else '❌'} ({duration:.2f}s)")
        return True

    except Exception as e:
        print(f"❌ Parallel test failed: {e}")
        logger.exception("Parallel test error")
        return False


async def main():
    """Main test function."""
    print("🚀 Advanced AI Orchestrator Integration Test")
    print("=" * 60)

    # Run tests
    test1_success = await test_basic_orchestration()
    test2_success = await test_parallel_execution()

    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"  Basic Orchestration: {'✅ PASSED' if test1_success else '❌ FAILED'}")
    print(f"  Parallel Execution: {'✅ PASSED' if test2_success else '❌ FAILED'}")

    if test1_success and test2_success:
        print("\n🎉 All tests passed! Advanced AI Orchestrator is working correctly.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check logs for details.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
