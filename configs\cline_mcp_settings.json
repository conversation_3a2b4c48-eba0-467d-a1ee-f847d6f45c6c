{"mcpServers": {"magic-mcp": {"url": "https://server.smithery.ai/@21st-dev/magic-mcp/mcp", "transport": "http"}, "github.com/21st-dev/magic-mcp": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest"], "env": {"TWENTY_FIRST_API_KEY": "31fb8f7725ecb00b1aff63e34af8700259ffdf8c6b77d3f89bb6c2837c7187b6"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "c:/Users/<USER>/Downloads/codingagenttwo", "c:/Users/<USER>"], "disabled": false, "autoApprove": []}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "c:/Users/<USER>/Downloads/codingagenttwo/.cline_memory.json"}}}}