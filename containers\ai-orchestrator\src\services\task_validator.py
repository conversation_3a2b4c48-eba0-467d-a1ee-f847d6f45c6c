# Project: AI Coding Agent
# Purpose: Comprehensive task validation system with multiple verification methods

import logging
import os
import re
import subprocess
import time
from typing import Dict, List
from pathlib import Path

from src.models.validation_models import (
    Task, TaskResult, ValidationResult, TaskType
)
# Note: Use UniversalLLMService instead of specific implementations
# from src.utils.llm_service import UniversalLLMService


class TaskValidator:
    """
    Comprehensive task validation system that validates task completion
    using multiple verification methods including file system checks,
    code syntax validation, functional testing, and integration validation.
    """

    def __init__(self, project_root: str = "/workspace"):
        self.project_root = Path(project_root)
        self.logger = logging.getLogger("task_validator")
        self._llm_service = None  # Lazy-loaded to avoid circular imports

        # Validation cache for performance
        self._validation_cache: Dict[str, ValidationResult] = {}
        self._cache_ttl = 300  # 5 minutes

    @property
    def llm_service(self):
        """Lazy-load LLM service to avoid circular imports."""
        if self._llm_service is None:
            try:
                from src.utils.llm_service import UniversalLLMService
                self._llm_service = UniversalLLMService()
            except ImportError:
                try:
                    from utils.llm_service import UniversalLLMService
                    self._llm_service = UniversalLLMService()
                except ImportError:
                    self.logger.warning("LLM service not available for validation")
                    self._llm_service = None
        return self._llm_service

    async def validate_task_completion(self, task: Task, result: TaskResult) -> ValidationResult:
        """
        Validates task completion with multiple verification methods.

        Args:
            task: The completed task to validate
            result: The result of task execution

        Returns:
            ValidationResult: Comprehensive validation result
        """
        self.logger.info(f"Validating task completion: {task.title} (Type: {task.type})")

        validations = []
        start_time = time.time()

        try:
            # 1. File System Validation
            if task.expected_files:
                file_validation = await self._validate_files_exist(task.expected_files)
                validations.append(file_validation)
                self.logger.debug(f"File validation: {file_validation.is_valid}")

            # 2. Code Syntax Validation
            if task.code_files:
                syntax_validation = await self._validate_code_syntax(task.code_files)
                validations.append(syntax_validation)
                self.logger.debug(f"Syntax validation: {syntax_validation.is_valid}")

            # 3. Functional Testing
            if task.test_command:
                test_validation = await self._run_functional_tests(task.test_command)
                validations.append(test_validation)
                self.logger.debug(f"Test validation: {test_validation.is_valid}")

            # 4. Integration Validation
            if task.integration_checks:
                integration_validation = await self._validate_integrations(task.integration_checks)
                validations.append(integration_validation)
                self.logger.debug(f"Integration validation: {integration_validation.is_valid}")

            # 5. Task-Type Specific Validation
            type_validation = await self._validate_task_type_specific(task, result)
            validations.append(type_validation)
            self.logger.debug(f"Type-specific validation: {type_validation.is_valid}")

            # Aggregate results
            final_result = ValidationResult.from_checks(validations)

            # Add performance metrics
            validation_time = time.time() - start_time
            final_result.metrics.update({
                "validation_time_seconds": validation_time,
                "checks_performed": len(validations),
                "task_type": task.type,
                "agent_type": task.agent_type
            })

            self.logger.info(f"Task validation completed: {final_result.is_valid} "
                           f"({validation_time:.2f}s, {len(validations)} checks)")

            return final_result

        except Exception as e:
            self.logger.error(f"Task validation error: {str(e)}")
            return ValidationResult.failure(
                error=f"Validation system error: {str(e)}",
                details="Task validation could not be completed due to internal error"
            )

    async def _validate_files_exist(self, expected_files: List[str]) -> ValidationResult:
        """Validate that all expected files exist and are accessible"""
        missing_files = []
        invalid_files = []

        for file_path in expected_files:
            full_path = self.project_root / file_path if not os.path.isabs(file_path) else Path(file_path)

            if not full_path.exists():
                missing_files.append(str(file_path))
            elif not full_path.is_file():
                invalid_files.append(f"{file_path} (not a file)")
            elif not os.access(full_path, os.R_OK):
                invalid_files.append(f"{file_path} (not readable)")

        if missing_files or invalid_files:
            error_parts = []
            if missing_files:
                error_parts.append(f"Missing files: {', '.join(missing_files)}")
            if invalid_files:
                error_parts.append(f"Invalid files: {', '.join(invalid_files)}")

            return ValidationResult.failure(
                error="; ".join(error_parts),
                details=f"File validation failed for {len(missing_files + invalid_files)} files"
            )

        return ValidationResult.success(f"All {len(expected_files)} expected files exist and are accessible")

    async def _validate_code_syntax(self, code_files: List[str]) -> ValidationResult:
        """Validate syntax of code files based on file extension"""
        validation_results = []

        for file_path in code_files:
            full_path = self.project_root / file_path if not os.path.isabs(file_path) else Path(file_path)

            if not full_path.exists():
                validation_results.append(
                    ValidationResult.failure(f"Code file not found: {file_path}")
                )
                continue

            try:
                file_extension = full_path.suffix.lower()

                if file_extension == '.py':
                    result = await self._validate_python_syntax(full_path)
                elif file_extension in ['.js', '.jsx', '.ts', '.tsx']:
                    result = await self._validate_javascript_syntax(full_path)
                elif file_extension in ['.html', '.htm']:
                    result = await self._validate_html_syntax(full_path)
                elif file_extension in ['.css', '.scss', '.sass']:
                    result = await self._validate_css_syntax(full_path)
                else:
                    # Generic text file validation
                    result = await self._validate_text_file(full_path)

                validation_results.append(result)

            except Exception as e:
                validation_results.append(
                    ValidationResult.failure(
                        error=f"Syntax validation error for {file_path}: {str(e)}"
                    )
                )

        return ValidationResult.from_checks(validation_results)

    async def _validate_python_syntax(self, file_path: Path) -> ValidationResult:
        """Validate Python file syntax"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()

            # Compile the code to check for syntax errors
            compile(code, str(file_path), 'exec')

            # Additional Python-specific checks
            issues = []

            # Check for common issues
            if 'import *' in code:
                issues.append("Contains wildcard imports (not recommended)")

            # Check for proper encoding declaration if needed
            lines = code.split('\n')
            if len(lines) > 1 and not any('coding:' in line or 'encoding=' in line for line in lines[:2]):
                if any(ord(c) > 127 for c in code):
                    issues.append("Contains non-ASCII characters but no encoding declaration")

            if issues:
                return ValidationResult(
                    is_valid=True,
                    details=f"Python syntax valid: {file_path.name}",
                    warnings=issues
                )

            return ValidationResult.success(f"Python syntax valid: {file_path.name}")

        except SyntaxError as e:
            return ValidationResult.failure(
                error=f"Python syntax error in {file_path.name}: {str(e)} (line {e.lineno})"
            )
        except Exception as e:
            return ValidationResult.failure(
                error=f"Error validating Python file {file_path.name}: {str(e)}"
            )

    async def _validate_javascript_syntax(self, file_path: Path) -> ValidationResult:
        """Validate JavaScript/TypeScript file syntax using Node.js if available"""
        try:
            # Try to use Node.js to validate syntax
            result = subprocess.run(
                ['node', '-c', str(file_path)],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                return ValidationResult.success(f"JavaScript syntax valid: {file_path.name}")
            else:
                return ValidationResult.failure(
                    error=f"JavaScript syntax error in {file_path.name}: {result.stderr}"
                )

        except subprocess.TimeoutExpired:
            return ValidationResult.failure(
                error=f"JavaScript validation timeout for {file_path.name}"
            )
        except FileNotFoundError:
            # Node.js not available, do basic text validation
            return await self._validate_text_file(file_path)

    async def _validate_html_syntax(self, file_path: Path) -> ValidationResult:
        """Basic HTML syntax validation"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            issues = []

            # Basic HTML structure checks
            if not content.strip():
                return ValidationResult.failure(f"HTML file is empty: {file_path.name}")

            # Check for basic HTML structure
            if '<html' not in content.lower():
                issues.append("Missing <html> tag")

            # Check for matching tags (basic)
            open_tags = re.findall(r'<([a-zA-Z][a-zA-Z0-9]*)[^>]*>', content)
            close_tags = re.findall(r'</([a-zA-Z][a-zA-Z0-9]*)>', content)

            # Filter out self-closing tags
            self_closing = {'img', 'br', 'hr', 'input', 'meta', 'link', 'area', 'base', 'col', 'embed', 'source', 'track', 'wbr'}
            open_tags = [tag for tag in open_tags if tag.lower() not in self_closing]

            if len(open_tags) != len(close_tags):
                issues.append("Mismatched opening/closing tags detected")

            if issues:
                return ValidationResult(
                    is_valid=True,
                    details=f"HTML file readable: {file_path.name}",
                    warnings=issues
                )

            return ValidationResult.success(f"HTML syntax valid: {file_path.name}")

        except Exception as e:
            return ValidationResult.failure(
                error=f"Error validating HTML file {file_path.name}: {str(e)}"
            )

    async def _validate_css_syntax(self, file_path: Path) -> ValidationResult:
        """Basic CSS syntax validation"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if not content.strip():
                return ValidationResult.failure(f"CSS file is empty: {file_path.name}")

            issues = []

            # Basic CSS structure checks
            open_braces = content.count('{')
            close_braces = content.count('}')

            if open_braces != close_braces:
                issues.append("Mismatched curly braces")

            # Check for common syntax issues
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line and not line.startswith('/*') and not line.endswith('*/'):
                    if ':' in line and not line.endswith(';') and not line.endswith('{') and not line.endswith('}'):
                        if not any(line.startswith(prefix) for prefix in ['@import', '@media', '@keyframes']):
                            issues.append(f"Missing semicolon on line {i}")
                            break  # Don't overwhelm with too many issues

            if issues:
                return ValidationResult(
                    is_valid=True,
                    details=f"CSS file readable: {file_path.name}",
                    warnings=issues
                )

            return ValidationResult.success(f"CSS syntax valid: {file_path.name}")

        except Exception as e:
            return ValidationResult.failure(
                error=f"Error validating CSS file {file_path.name}: {str(e)}"
            )

    async def _validate_text_file(self, file_path: Path) -> ValidationResult:
        """Basic text file validation"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if not content.strip():
                return ValidationResult(
                    is_valid=True,
                    details=f"Text file is empty: {file_path.name}",
                    warnings=["File is empty"]
                )

            return ValidationResult.success(f"Text file readable: {file_path.name}")

        except UnicodeDecodeError:
            return ValidationResult.failure(
                error=f"Text file encoding error: {file_path.name}"
            )
        except Exception as e:
            return ValidationResult.failure(
                error=f"Error reading text file {file_path.name}: {str(e)}"
            )

    async def _run_functional_tests(self, test_command: str) -> ValidationResult:
        """Run functional tests for the task"""
        self.logger.info(f"Running functional tests: {test_command}")

        try:
            # Execute test command with timeout
            result = subprocess.run(
                test_command.split(),
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                cwd=str(self.project_root)
            )

            if result.returncode == 0:
                return ValidationResult.success(
                    details=f"Functional tests passed: {test_command}"
                )
            else:
                return ValidationResult.failure(
                    error=f"Functional tests failed: {result.stderr or result.stdout}",
                    details=f"Command: {test_command}, Exit code: {result.returncode}"
                )

        except subprocess.TimeoutExpired:
            return ValidationResult.failure(
                error=f"Functional tests timed out: {test_command}",
                details="Tests took longer than 5 minutes to complete"
            )
        except Exception as e:
            return ValidationResult.failure(
                error=f"Error running functional tests: {str(e)}",
                details=f"Command: {test_command}"
            )

    async def _validate_integrations(self, integration_checks: List[str]) -> ValidationResult:
        """Validate integration points"""
        validation_results = []

        for check in integration_checks:
            try:
                if check.startswith('http'):
                    # HTTP endpoint check
                    result = await self._validate_http_endpoint(check)
                elif check.startswith('db:'):
                    # Database check
                    result = await self._validate_database_connection(check[3:])
                elif check.startswith('file:'):
                    # File system check
                    file_path = check[5:]
                    result = await self._validate_files_exist([file_path])
                else:
                    # Generic integration check
                    result = ValidationResult(
                        is_valid=True,
                        details=f"Integration check noted: {check}",
                        warnings=["Integration check type not recognized"]
                    )

                validation_results.append(result)

            except Exception as e:
                validation_results.append(
                    ValidationResult.failure(
                        error=f"Integration check error for {check}: {str(e)}"
                    )
                )

        return ValidationResult.from_checks(validation_results)

    async def _validate_http_endpoint(self, url: str) -> ValidationResult:
        """Validate HTTP endpoint availability"""
        try:
            import aiohttp

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.get(url) as response:
                    if response.status < 400:
                        return ValidationResult.success(f"HTTP endpoint accessible: {url}")
                    else:
                        return ValidationResult.failure(
                            error=f"HTTP endpoint error: {url} returned {response.status}"
                        )

        except Exception as e:
            return ValidationResult.failure(
                error=f"HTTP endpoint check failed: {url} - {str(e)}"
            )

    async def _validate_database_connection(self, connection_string: str) -> ValidationResult:
        """Validate database connection"""
        # This would be implemented based on the specific database being used
        return ValidationResult(
            is_valid=True,
            details=f"Database connection check noted: {connection_string}",
            warnings=["Database validation not fully implemented"]
        )

    async def _validate_task_type_specific(self, task: Task, result: TaskResult) -> ValidationResult:
        """Perform task-type specific validation"""
        if task.type == TaskType.CREATE_COMPONENT:
            return await self._validate_component_task(task, result)
        elif task.type == TaskType.CREATE_API_ENDPOINT:
            return await self._validate_api_endpoint_task(task, result)
        elif task.type == TaskType.DATABASE_MIGRATION:
            return await self._validate_database_migration_task(task, result)
        else:
            return ValidationResult.success("No specific validation required for this task type")

    async def _validate_component_task(self, task: Task, result: TaskResult) -> ValidationResult:
        """Validate component creation task"""
        checks = []

        # Check if component file was created
        if result.files_created:
            for file_path in result.files_created:
                if file_path.endswith(('.jsx', '.tsx', '.vue')):
                    checks.append(ValidationResult.success(f"Component file created: {file_path}"))
                    # Additional component-specific checks could go here

        if not checks:
            checks.append(ValidationResult(
                is_valid=True,
                details="Component task completed",
                warnings=["No component files detected in result"]
            ))

        return ValidationResult.from_checks(checks)

    async def _validate_api_endpoint_task(self, task: Task, result: TaskResult) -> ValidationResult:
        """Validate API endpoint creation task"""
        checks = []

        # Check if endpoint files were created
        if result.files_created:
            for file_path in result.files_created:
                if any(pattern in file_path for pattern in ['router', 'endpoint', 'api']):
                    checks.append(ValidationResult.success(f"API file created: {file_path}"))

        if not checks:
            checks.append(ValidationResult(
                is_valid=True,
                details="API endpoint task completed",
                warnings=["No API-related files detected in result"]
            ))

        return ValidationResult.from_checks(checks)

    async def _validate_database_migration_task(self, task: Task, result: TaskResult) -> ValidationResult:
        """Validate database migration task"""
        checks = []

        # Check if migration files were created
        if result.files_created:
            for file_path in result.files_created:
                if 'migration' in file_path or 'alembic' in file_path:
                    checks.append(ValidationResult.success(f"Migration file created: {file_path}"))

        if not checks:
            checks.append(ValidationResult(
                is_valid=True,
                details="Database migration task completed",
                warnings=["No migration files detected in result"]
            ))

        return ValidationResult.from_checks(checks)