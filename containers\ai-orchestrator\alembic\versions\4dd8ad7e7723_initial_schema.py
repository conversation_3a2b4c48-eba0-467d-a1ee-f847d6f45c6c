"""initial schema

Revision ID: 4dd8ad7e7723
Revises: 
Create Date: 2025-08-29 21:30:17.340009

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '4dd8ad7e7723'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('llm_providers',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('display_name', sa.String(length=128), nullable=True),
    sa.Column('api_key_reference', sa.String(length=255), nullable=True),
    sa.Column('api_key_last4', sa.String(length=4), nullable=True),
    sa.Column('base_url', sa.String(length=255), nullable=True),
    sa.Column('enabled', sa.Boolean(), server_default='true', nullable=False),
    sa.Column('rate_limit_rpm', sa.Integer(), nullable=True),
    sa.Column('cost_limit_usd', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_llm_providers'))
    )
    op.create_index(op.f('ix_llm_providers_id'), 'llm_providers', ['id'], unique=False)
    op.create_index(op.f('ix_llm_providers_name'), 'llm_providers', ['name'], unique=True)
    op.create_table('projects',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('db_schema_name', sa.String(length=255), nullable=True),
    sa.Column('db_connection_url', sa.String(length=500), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_projects')),
    sa.UniqueConstraint('db_schema_name', name=op.f('uq_projects_db_schema_name'))
    )
    op.create_table('users',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='Unique user identifier'),
    sa.Column('supabase_user_id', sa.UUID(), nullable=True, comment='Foreign key to Supabase auth.users table'),
    sa.Column('username', sa.String(length=50), nullable=False, comment='Unique username for login'),
    sa.Column('email', sa.String(length=255), nullable=False, comment='User email address'),
    sa.Column('hashed_password', sa.String(length=255), nullable=False, comment='Bcrypt hashed password'),
    sa.Column('full_name', sa.String(length=255), nullable=True, comment="User's full display name"),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='Account activation status'),
    sa.Column('is_superuser', sa.Boolean(), nullable=False, comment='Admin privileges flag'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Account creation timestamp'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Last modification timestamp'),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True, comment='Last successful login timestamp'),
    sa.Column('profile_data', sa.Text(), nullable=True, comment='JSON field for additional profile information'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_users'))
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_supabase_user_id'), 'users', ['supabase_user_id'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('workspaces',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.String(), nullable=False),
    sa.Column('container_id', sa.String(), nullable=True),
    sa.Column('container_name', sa.String(), nullable=False),
    sa.Column('subdomain', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('CREATING', 'RUNNING', 'STOPPING', 'STOPPED', 'ERROR', 'DELETED', name='workspacestatus'), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('workspace_config', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_workspaces'))
    )
    op.create_index(op.f('ix_workspaces_container_id'), 'workspaces', ['container_id'], unique=True)
    op.create_index(op.f('ix_workspaces_container_name'), 'workspaces', ['container_name'], unique=True)
    op.create_index(op.f('ix_workspaces_id'), 'workspaces', ['id'], unique=False)
    op.create_index(op.f('ix_workspaces_status'), 'workspaces', ['status'], unique=False)
    op.create_index(op.f('ix_workspaces_subdomain'), 'workspaces', ['subdomain'], unique=True)
    op.create_index(op.f('ix_workspaces_user_id'), 'workspaces', ['user_id'], unique=False)
    op.create_table('conversation_history',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.String(length=255), nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=False),
    sa.Column('question_key', sa.String(length=255), nullable=False),
    sa.Column('question_text', sa.Text(), nullable=False),
    sa.Column('user_response', sa.Text(), nullable=True),
    sa.Column('sequence_order', sa.Integer(), nullable=False),
    sa.Column('is_followup', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name=op.f('fk_conversation_history_project_id_projects'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_conversation_history'))
    )
    op.create_index(op.f('ix_conversation_history_id'), 'conversation_history', ['id'], unique=False)
    op.create_index(op.f('ix_conversation_history_project_id'), 'conversation_history', ['project_id'], unique=False)
    op.create_index(op.f('ix_conversation_history_session_id'), 'conversation_history', ['session_id'], unique=False)
    op.create_index(op.f('ix_conversation_history_user_id'), 'conversation_history', ['user_id'], unique=False)
    op.create_table('interview_sessions',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.String(length=255), nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=False),
    sa.Column('current_state', sa.String(length=50), nullable=False),
    sa.Column('current_question_index', sa.Integer(), nullable=False),
    sa.Column('total_questions', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('session_metadata', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name=op.f('fk_interview_sessions_project_id_projects'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_interview_sessions'))
    )
    op.create_index(op.f('ix_interview_sessions_id'), 'interview_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_interview_sessions_project_id'), 'interview_sessions', ['project_id'], unique=False)
    op.create_index(op.f('ix_interview_sessions_session_id'), 'interview_sessions', ['session_id'], unique=True)
    op.create_index(op.f('ix_interview_sessions_user_id'), 'interview_sessions', ['user_id'], unique=False)
    op.create_table('llm_models',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('provider_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('display_name', sa.String(length=128), nullable=True),
    sa.Column('status', sa.String(length=32), server_default='available', nullable=False),
    sa.Column('context_length', sa.Integer(), nullable=True),
    sa.Column('cost_per_token', sa.Numeric(precision=10, scale=5), nullable=True),
    sa.Column('parameters', sa.String(length=64), nullable=True),
    sa.Column('is_default', sa.Boolean(), server_default='false', nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['provider_id'], ['llm_providers.id'], name=op.f('fk_llm_models_provider_id_llm_providers'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_llm_models')),
    sa.UniqueConstraint('provider_id', 'name', name='uq_llm_models_provider_id_name')
    )
    op.create_index(op.f('ix_llm_models_id'), 'llm_models', ['id'], unique=False)
    op.create_index(op.f('ix_llm_models_provider_id'), 'llm_models', ['provider_id'], unique=False)
    op.create_table('project_exports',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('export_id', sa.String(length=255), nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('format', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=True),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('progress_percentage', sa.Float(), nullable=True),
    sa.Column('current_step', sa.String(length=255), nullable=True),
    sa.Column('total_steps', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('export_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name=op.f('fk_project_exports_project_id_projects')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_project_exports_user_id_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_project_exports'))
    )
    op.create_index(op.f('ix_project_exports_export_id'), 'project_exports', ['export_id'], unique=True)
    op.create_table('roadmap_items',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=False),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('level', sa.Integer(), nullable=False),
    sa.Column('sequence_order', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=500), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('item_type', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('agent_role', sa.String(length=50), nullable=True),
    sa.Column('estimated_effort', sa.String(length=50), nullable=True),
    sa.Column('dependencies', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['roadmap_items.id'], name=op.f('fk_roadmap_items_parent_id_roadmap_items'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name=op.f('fk_roadmap_items_project_id_projects'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_roadmap_items'))
    )
    op.create_index(op.f('ix_roadmap_items_agent_role'), 'roadmap_items', ['agent_role'], unique=False)
    op.create_index(op.f('ix_roadmap_items_id'), 'roadmap_items', ['id'], unique=False)
    op.create_index(op.f('ix_roadmap_items_item_type'), 'roadmap_items', ['item_type'], unique=False)
    op.create_index(op.f('ix_roadmap_items_level'), 'roadmap_items', ['level'], unique=False)
    op.create_index(op.f('ix_roadmap_items_parent_id'), 'roadmap_items', ['parent_id'], unique=False)
    op.create_index(op.f('ix_roadmap_items_project_id'), 'roadmap_items', ['project_id'], unique=False)
    op.create_index(op.f('ix_roadmap_items_status'), 'roadmap_items', ['status'], unique=False)
    op.create_table('tasks',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(length=32), nullable=False),
    sa.Column('agent_role', sa.String(length=64), nullable=False),
    sa.Column('input_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('output_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name=op.f('fk_tasks_project_id_projects'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_tasks'))
    )
    op.create_index(op.f('ix_tasks_agent_role'), 'tasks', ['agent_role'], unique=False)
    op.create_index(op.f('ix_tasks_id'), 'tasks', ['id'], unique=False)
    op.create_index(op.f('ix_tasks_project_id'), 'tasks', ['project_id'], unique=False)
    op.create_index(op.f('ix_tasks_status'), 'tasks', ['status'], unique=False)
    op.create_table('user_projects',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=False),
    sa.Column('role', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name=op.f('fk_user_projects_project_id_projects')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_user_projects_user_id_users')),
    sa.PrimaryKeyConstraint('user_id', 'project_id', name=op.f('pk_user_projects'))
    )
    op.create_table('agent_model_assignments',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('agent_role', sa.String(length=64), nullable=False),
    sa.Column('provider_id', sa.Integer(), nullable=False),
    sa.Column('primary_model_id', sa.Integer(), nullable=False),
    sa.Column('fallback_model_id', sa.Integer(), nullable=True),
    sa.Column('temperature', sa.Float(), server_default='0.7', nullable=False),
    sa.Column('max_tokens', sa.Integer(), nullable=True),
    sa.Column('enabled', sa.Boolean(), server_default='true', nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['fallback_model_id'], ['llm_models.id'], name=op.f('fk_agent_model_assignments_fallback_model_id_llm_models'), ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['primary_model_id'], ['llm_models.id'], name=op.f('fk_agent_model_assignments_primary_model_id_llm_models'), ondelete='RESTRICT'),
    sa.ForeignKeyConstraint(['provider_id'], ['llm_providers.id'], name=op.f('fk_agent_model_assignments_provider_id_llm_providers'), ondelete='RESTRICT'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_agent_model_assignments'))
    )
    op.create_index(op.f('ix_agent_model_assignments_agent_role'), 'agent_model_assignments', ['agent_role'], unique=True)
    op.create_index(op.f('ix_agent_model_assignments_fallback_model_id'), 'agent_model_assignments', ['fallback_model_id'], unique=False)
    op.create_index(op.f('ix_agent_model_assignments_id'), 'agent_model_assignments', ['id'], unique=False)
    op.create_index(op.f('ix_agent_model_assignments_primary_model_id'), 'agent_model_assignments', ['primary_model_id'], unique=False)
    op.create_index(op.f('ix_agent_model_assignments_provider_id'), 'agent_model_assignments', ['provider_id'], unique=False)
    op.create_table('agent_state',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('project_id', sa.Integer(), nullable=False),
    sa.Column('active_agent_role', sa.String(length=64), nullable=True),
    sa.Column('current_task_id', sa.Integer(), nullable=True),
    sa.Column('lock_acquired_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['current_task_id'], ['tasks.id'], name=op.f('fk_agent_state_current_task_id_tasks'), ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], name=op.f('fk_agent_state_project_id_projects'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_agent_state'))
    )
    op.create_index(op.f('ix_agent_state_active_agent_role'), 'agent_state', ['active_agent_role'], unique=False)
    op.create_index(op.f('ix_agent_state_current_task_id'), 'agent_state', ['current_task_id'], unique=False)
    op.create_index(op.f('ix_agent_state_project_id'), 'agent_state', ['project_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_agent_state_project_id'), table_name='agent_state')
    op.drop_index(op.f('ix_agent_state_current_task_id'), table_name='agent_state')
    op.drop_index(op.f('ix_agent_state_active_agent_role'), table_name='agent_state')
    op.drop_table('agent_state')
    op.drop_index(op.f('ix_agent_model_assignments_provider_id'), table_name='agent_model_assignments')
    op.drop_index(op.f('ix_agent_model_assignments_primary_model_id'), table_name='agent_model_assignments')
    op.drop_index(op.f('ix_agent_model_assignments_id'), table_name='agent_model_assignments')
    op.drop_index(op.f('ix_agent_model_assignments_fallback_model_id'), table_name='agent_model_assignments')
    op.drop_index(op.f('ix_agent_model_assignments_agent_role'), table_name='agent_model_assignments')
    op.drop_table('agent_model_assignments')
    op.drop_table('user_projects')
    op.drop_index(op.f('ix_tasks_status'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_project_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_id'), table_name='tasks')
    op.drop_index(op.f('ix_tasks_agent_role'), table_name='tasks')
    op.drop_table('tasks')
    op.drop_index(op.f('ix_roadmap_items_status'), table_name='roadmap_items')
    op.drop_index(op.f('ix_roadmap_items_project_id'), table_name='roadmap_items')
    op.drop_index(op.f('ix_roadmap_items_parent_id'), table_name='roadmap_items')
    op.drop_index(op.f('ix_roadmap_items_level'), table_name='roadmap_items')
    op.drop_index(op.f('ix_roadmap_items_item_type'), table_name='roadmap_items')
    op.drop_index(op.f('ix_roadmap_items_id'), table_name='roadmap_items')
    op.drop_index(op.f('ix_roadmap_items_agent_role'), table_name='roadmap_items')
    op.drop_table('roadmap_items')
    op.drop_index(op.f('ix_project_exports_export_id'), table_name='project_exports')
    op.drop_table('project_exports')
    op.drop_index(op.f('ix_llm_models_provider_id'), table_name='llm_models')
    op.drop_index(op.f('ix_llm_models_id'), table_name='llm_models')
    op.drop_table('llm_models')
    op.drop_index(op.f('ix_interview_sessions_user_id'), table_name='interview_sessions')
    op.drop_index(op.f('ix_interview_sessions_session_id'), table_name='interview_sessions')
    op.drop_index(op.f('ix_interview_sessions_project_id'), table_name='interview_sessions')
    op.drop_index(op.f('ix_interview_sessions_id'), table_name='interview_sessions')
    op.drop_table('interview_sessions')
    op.drop_index(op.f('ix_conversation_history_user_id'), table_name='conversation_history')
    op.drop_index(op.f('ix_conversation_history_session_id'), table_name='conversation_history')
    op.drop_index(op.f('ix_conversation_history_project_id'), table_name='conversation_history')
    op.drop_index(op.f('ix_conversation_history_id'), table_name='conversation_history')
    op.drop_table('conversation_history')
    op.drop_index(op.f('ix_workspaces_user_id'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_subdomain'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_status'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_id'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_container_name'), table_name='workspaces')
    op.drop_index(op.f('ix_workspaces_container_id'), table_name='workspaces')
    op.drop_table('workspaces')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_supabase_user_id'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_table('projects')
    op.drop_index(op.f('ix_llm_providers_name'), table_name='llm_providers')
    op.drop_index(op.f('ix_llm_providers_id'), table_name='llm_providers')
    op.drop_table('llm_providers')
    # ### end Alembic commands ###
