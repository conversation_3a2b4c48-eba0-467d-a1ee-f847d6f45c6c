#!/usr/bin/env python3
"""
API Endpoints Test Script

This script tests the FastAPI endpoints for the export feature.
It can be run against a running FastAPI server.
"""

import asyncio
import sys
from pathlib import Path
import aiohttp

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def print_header(title: str):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🌐 {title}")
    print(f"{'='*60}")

def print_step(step: str, description: str):
    """Print a test step."""
    print(f"\n{step} {description}")
    print("-" * 50)

def print_success(message: str):
    """Print success message."""
    print(f"   ✅ {message}")

def print_error(message: str):
    """Print error message."""
    print(f"   ❌ {message}")

def print_info(message: str):
    """Print info message."""
    print(f"   ℹ️  {message}")

class APITester:
    """API testing class."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url.rstrip('/')
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_server_health(self):
        """Test if the server is running."""
        print_step("1️⃣", "Testing Server Health")
        
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    print_success("Server is running and healthy")
                    return True
                else:
                    print_error(f"Server health check failed: {response.status}")
                    return False
        except aiohttp.ClientConnectorError:
            print_error("Cannot connect to server")
            print_info(f"Make sure the server is running at {self.base_url}")
            return False
        except Exception as e:
            print_error(f"Health check failed: {e}")
            return False
    
    async def test_export_initiation(self, project_id: str = "test-project-123"):
        """Test export initiation endpoint."""
        print_step("2️⃣", "Testing Export Initiation")
        
        export_request = {
            "include_database": True,
            "include_files": True,
            "export_format": "zip"
        }
        
        try:
            url = f"{self.base_url}/api/v1/projects/{project_id}/export"
            print_info(f"POST {url}")
            
            async with self.session.post(
                url,
                json=export_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                response_text = await response.text()
                print_info(f"Response status: {response.status}")
                print_info(f"Response: {response_text[:200]}...")
                
                if response.status == 200:
                    data = await response.json()
                    export_id = data.get("export_id")
                    if export_id:
                        print_success(f"Export initiated successfully: {export_id}")
                        return export_id
                    else:
                        print_error("No export_id in response")
                        return None
                elif response.status == 401:
                    print_error("Authentication required")
                    print_info("This is expected if authentication is enabled")
                    return None
                elif response.status == 404:
                    print_error("Project not found")
                    print_info("This is expected if the project doesn't exist")
                    return None
                else:
                    print_error(f"Unexpected response status: {response.status}")
                    return None
                    
        except Exception as e:
            print_error(f"Export initiation test failed: {e}")
            return None
    
    async def test_export_status(self, project_id: str, export_id: str):
        """Test export status endpoint."""
        print_step("3️⃣", "Testing Export Status")
        
        try:
            url = f"{self.base_url}/api/v1/projects/{project_id}/export/{export_id}/status"
            print_info(f"GET {url}")
            
            async with self.session.get(url) as response:
                response_text = await response.text()
                print_info(f"Response status: {response.status}")
                print_info(f"Response: {response_text[:200]}...")
                
                if response.status == 200:
                    data = await response.json()
                    status = data.get("status")
                    print_success(f"Export status retrieved: {status}")
                    return data
                elif response.status == 404:
                    print_error("Export not found")
                    return None
                else:
                    print_error(f"Unexpected response status: {response.status}")
                    return None
                    
        except Exception as e:
            print_error(f"Export status test failed: {e}")
            return None
    
    async def test_export_download(self, project_id: str, export_id: str):
        """Test export download endpoint."""
        print_step("4️⃣", "Testing Export Download")
        
        try:
            url = f"{self.base_url}/api/v1/projects/{project_id}/export/{export_id}/download"
            print_info(f"GET {url}")
            
            async with self.session.get(url) as response:
                print_info(f"Response status: {response.status}")
                
                if response.status == 200:
                    content_type = response.headers.get('content-type', '')
                    content_length = response.headers.get('content-length', 'unknown')
                    print_success("Download endpoint accessible")
                    print_info(f"Content-Type: {content_type}")
                    print_info(f"Content-Length: {content_length}")
                    return True
                elif response.status == 404:
                    print_error("Export file not found")
                    print_info("This is expected if the export hasn't completed")
                    return False
                else:
                    print_error(f"Unexpected response status: {response.status}")
                    return False
                    
        except Exception as e:
            print_error(f"Export download test failed: {e}")
            return False
    
    async def test_invalid_requests(self):
        """Test invalid request handling."""
        print_step("5️⃣", "Testing Invalid Request Handling")
        
        test_cases = [
            {
                "name": "Invalid export format",
                "url": "/api/v1/projects/test/export",
                "data": {
                    "include_database": True,
                    "include_files": True,
                    "export_format": "invalid_format"
                },
                "expected_status": 422
            },
            {
                "name": "Both includes false",
                "url": "/api/v1/projects/test/export",
                "data": {
                    "include_database": False,
                    "include_files": False,
                    "export_format": "zip"
                },
                "expected_status": 422
            },
            {
                "name": "Non-existent export status",
                "url": "/api/v1/projects/test/export/non-existent-id/status",
                "data": None,
                "expected_status": 404
            }
        ]
        
        for test_case in test_cases:
            try:
                url = f"{self.base_url}{test_case['url']}"
                print_info(f"Testing: {test_case['name']}")
                
                if test_case['data']:
                    async with self.session.post(url, json=test_case['data']) as response:
                        status = response.status
                else:
                    async with self.session.get(url) as response:
                        status = response.status
                
                if status == test_case['expected_status']:
                    print_success(f"Correctly returned {status} for {test_case['name']}")
                elif status == 401:
                    print_info(f"Authentication required (got {status}, expected {test_case['expected_status']})")
                else:
                    print_error(f"Expected {test_case['expected_status']}, got {status} for {test_case['name']}")
                    
            except Exception as e:
                print_error(f"Test case '{test_case['name']}' failed: {e}")
        
        return True

async def test_fastapi_app_directly():
    """Test the FastAPI app directly without HTTP server."""
    print_step("6️⃣", "Testing FastAPI App Directly")
    
    try:
        from fastapi.testclient import TestClient
        from router.project_router import router
        from fastapi import FastAPI
        
        # Create test app
        app = FastAPI()
        app.include_router(router, prefix="/api/v1")
        
        client = TestClient(app)
        print_success("Test client created")
        
        # Test export endpoint structure
        export_request = {
            "include_database": True,
            "include_files": True,
            "export_format": "zip"
        }
        
        # This will likely fail due to authentication, but we can test the structure
        response = client.post("/api/v1/projects/test/export", json=export_request)
        print_info(f"Direct API test status: {response.status_code}")
        
        if response.status_code in [200, 401, 404, 422]:
            print_success("API endpoint is properly structured")
        else:
            print_error(f"Unexpected status code: {response.status_code}")
        
        return True
        
    except ImportError as e:
        print_error(f"Cannot import required modules: {e}")
        return False
    except Exception as e:
        print_error(f"Direct API test failed: {e}")
        return False

async def main():
    """Main test function."""
    print_header("API Endpoints Test")
    
    # Get server URL from user
    server_url = input("Enter server URL (default: http://localhost:8000): ").strip()
    if not server_url:
        server_url = "http://localhost:8000"
    
    print_info(f"Testing server at: {server_url}")
    
    # Test direct FastAPI app first
    await test_fastapi_app_directly()
    
    # Test HTTP endpoints
    async with APITester(server_url) as tester:
        # Test server health
        if not await tester.test_server_health():
            print_error("Server is not accessible, skipping HTTP tests")
            print_info("You can still run the direct FastAPI tests above")
            return False
        
        # Test export workflow
        project_id = "test-project-123"
        export_id = await tester.test_export_initiation(project_id)
        
        if export_id:
            # Test status endpoint
            status_data = await tester.test_export_status(project_id, export_id)
            
            # Test download endpoint
            await tester.test_export_download(project_id, export_id)
        
        # Test invalid requests
        await tester.test_invalid_requests()
    
    print_header("API Test Complete")
    print_success("API endpoint tests completed")
    print_info("Note: Some tests may fail due to authentication or missing data, which is expected")
    
    return True

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  API tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 API tests crashed: {e}")
        sys.exit(1)
