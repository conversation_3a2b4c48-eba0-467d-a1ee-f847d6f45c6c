/**
 * TypeScript types for role-based LLM configuration management
 * Mirrors the Pydantic models from the backend API
 */

// Import and re-export ApiError from the central API types
import type { ApiError } from "./api";
export type { ApiError } from "./api";

export enum LLMProvider {
  OLLAMA = "ollama",
  OPENROUTER = "openrouter",
  OPENAI = "openai",
  ANTHROPIC = "anthropic",
}

export interface RoleConfiguration {
  provider: LLMProvider;
  available_models: string[];
  selected_model: string;
  api_key?: string | null;
  cost_limit?: number | null;
  max_tokens?: number;
  temperature?: number;
  enabled?: boolean;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface RoleConfigurationUpdate {
  provider?: LLMProvider;
  available_models?: string[];
  selected_model?: string;
  api_key?: string | null;
  cost_limit?: number | null;
  max_tokens?: number;
  temperature?: number;
  enabled?: boolean;
}

export interface RoleConfigurationList {
  roles: Record<string, RoleConfiguration>;
  version: string;
  updated_at?: string | null;
}

export interface RoleConfigurationResponse {
  success: boolean;
  message: string;
  role_name?: string;
  configuration?: RoleConfiguration;
}

export interface RoleListResponse {
  success: boolean;
  message: string;
  roles: Record<string, RoleConfiguration>;
  total_roles: number;
}

export interface ProviderModelsResponse {
  provider: LLMProvider;
  models: string[];
  status: string;
}

// Frontend-specific types
export interface ModelOption {
  value: string;
  label: string;
  cost_per_token?: number;
  context_length?: number;
  description?: string;
}

export interface ProviderOption {
  value: LLMProvider;
  label: string;
  requiresApiKey: boolean;
  description: string;
}

export interface FormState {
  provider: LLMProvider;
  selected_model: string;
  available_models: string[];
  api_key: string;
  cost_limit: number | null;
  max_tokens: number;
  temperature: number;
  enabled: boolean;
}

// This file is for role-specific types.
// General API types like ApiError and ApiResponse are imported from ./api.
export type { ApiResponse } from "./api";

// Component props types
export interface RoleConfigPanelProps {
  roleName: string;
  configuration: RoleConfiguration;
  onConfigurationChange: (config: RoleConfigurationUpdate) => void;
  onSave: () => void;
  isLoading?: boolean;
  error?: ApiError | null;
}

export interface ProviderSelectorProps {
  value: LLMProvider;
  onChange: (provider: LLMProvider) => void;
  providers?: ProviderOption[];
  disabled?: boolean;
  error?: string;
}

export interface ModelSelectorProps {
  value: string;
  onChange: (model: string) => void;
  models: ModelOption[];
  provider: LLMProvider;
  disabled?: boolean;
  error?: string;
}

export interface ApiKeyInputProps {
  value: string;
  onChange: (apiKey: string) => void;
  provider: LLMProvider;
  required?: boolean;
  disabled?: boolean;
  error?: string;
}

export interface ConfigManagerProps {
  className?: string;
}
