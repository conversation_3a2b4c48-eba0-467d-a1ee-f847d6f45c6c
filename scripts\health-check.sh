#!/bin/bash
# Health check script for AI Coding Agent services

echo "=== AI Coding Agent Service Health Check ==="
echo ""

# Check user-portal via Traefik
echo "1. Testing user-portal via Traefik..."
if curl -s -f "http://portal.localhost" > /dev/null; then
    echo "[OK] User Portal (via Traefik): HEALTHY"
else
    echo "[FAIL] User Portal (via Traefik): FAILED"
fi

# Check NextAuth API
echo "2. Testing NextAuth API..."
if curl -s -f "http://portal.localhost/api/auth/providers" > /dev/null; then
    echo "[OK] NextAuth API: HEALTHY"
else
    echo "[FAIL] NextAuth API: FAILED"
fi

# Check AI Orchestrator
echo "3. Testing AI Orchestrator..."
if curl -s -f "http://localhost:9001/health" > /dev/null; then
    echo "[OK] AI Orchestrator: HEALTHY"
else
    echo "[FAIL] AI Orchestrator: FAILED"
fi

# Check Ollama
echo "4. Testing Ollama..."
if curl -s -f "http://localhost:9434/api/tags" > /dev/null; then
    echo "[OK] Ollama: HEALTHY"
else
    echo "[FAIL] Ollama: FAILED"
fi

echo ""
echo "=== Service URLs ==="
echo "User Portal: http://portal.localhost"
echo "Login Page: http://portal.localhost/login"
echo "Dashboard: http://portal.localhost/dashboard"
echo "Traefik Dashboard: http://localhost:9091"
echo "Grafana: http://localhost:9000"
echo "AI Orchestrator: http://localhost:9001"
echo ""
echo "=== NextAuth Integration Complete! ==="
echo ""
echo "To test the authentication flow:"
echo "1. Visit: http://portal.localhost/login"
echo "2. Enter credentials (will authenticate against AI Orchestrator backend)"
echo "3. On success, you'll be redirected to: http://portal.localhost/dashboard"
echo "4. Use the AuthStatus component anywhere to show login/logout state"
echo ""
