"""
SQLAlchemy AgentState model for the Sequential Agent Framework.

Tracks which agent is currently active per project and the currently executing task.
"""
from __future__ import annotations

from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship

from src.models.database import Base


class AgentState(Base):
    """Per-project agent activity state.

    Ensures we can record and inspect which agent is active for a project and
    which task it is working on, supporting distributed locking enforcement.

    Attributes:
        id: Primary key.
        project_id: Unique FK to projects.id; only one AgentState per project.
        active_agent_role: The agent role currently active for this project.
        current_task_id: Optional FK to tasks.id for the task being executed.
        lock_acquired_at: Timestamp when the lock was acquired.
    """

    __tablename__ = "agent_state"

    id: int = Column(Integer, primary_key=True, autoincrement=True)

    project_id: int = Column(
        Integer,
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True,
    )

    active_agent_role: Optional[str] = Column(String(64), nullable=True, index=True)

    current_task_id: Optional[int] = Column(
        Integer,
        ForeignKey("tasks.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
    )

    lock_acquired_at: Optional[datetime] = Column(
        DateTime(timezone=True), nullable=True, server_default=None
    )

    # Relationships
    project = relationship("Project", backref="agent_state", lazy="selectin")
    current_task = relationship("Task", lazy="selectin")

    def __repr__(self) -> str:
        return (
            f"<AgentState id={self.id} project_id={self.project_id} "
            f"role={self.active_agent_role} task_id={self.current_task_id}>"
        )