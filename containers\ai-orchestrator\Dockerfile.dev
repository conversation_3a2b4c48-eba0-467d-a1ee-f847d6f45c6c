# syntax=docker/dockerfile:1
# Optimized Development Dockerfile for AI Orchestrator
# Layer caching optimization + hot-reload ready

FROM python:3.12-slim-bullseye

# Prevent debconf warnings during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies for development
RUN apt-get update && apt-get install -y --no-install-recommends \
  curl \
  ca-certificates \
  git \
  build-essential \
  wget \
  && apt-get upgrade -y \
  && rm -rf /var/lib/apt/lists/*

# ============================================================================
# CRITICAL FIX: Install modern SQLite3 for ChromaDB compatibility
# ChromaDB requires sqlite3 >= 3.35.0, but debian-slim has older version
# ============================================================================
RUN wget https://www.sqlite.org/2023/sqlite-autoconf-3420000.tar.gz && \
  tar xvfz sqlite-autoconf-3420000.tar.gz && \
  cd sqlite-autoconf-3420000 && \
  ./configure && \
  make && \
  make install && \
  cd .. && \
  rm -rf sqlite-autoconf-3420000.tar.gz sqlite-autoconf-3420000

# Update library path so Python can find the new sqlite3
ENV LD_LIBRARY_PATH="/usr/local/lib"

# Create non-root user early
RUN useradd -m appuser

# Set the working directory
WORKDIR /app

# ============================================================================
# LAYER CACHING OPTIMIZATION: Install dependencies BEFORE copying source code
# ============================================================================

# 1. Copy ONLY requirements files first (this layer will be cached)
COPY requirements.txt .

# 2. Install Python dependencies with BuildKit cache mounting
# This layer will only rebuild if requirements.txt changes
RUN --mount=type=cache,target=/root/.cache/pip \
  pip install --root-user-action=ignore -r requirements.txt

# ============================================================================
# SOURCE CODE: Copy after dependencies (for hot-reload compatibility)
# ============================================================================

# 3. Copy application code (this layer will rebuild on source changes)
# Note: In development with compose watch, this will be volume-mounted
COPY src ./src
COPY alembic.ini ./alembic.ini
COPY alembic ./alembic
COPY validate_project_service.py ./validate_project_service.py
COPY setup_config.py ./setup_config.py

# Copy entrypoint script
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Create data directory and set permissions
RUN mkdir -p /app/data && chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Ensure Python packages are in PATH for appuser
ENV PATH="/home/<USER>/.local/bin:$PATH"

# Expose both application and debug ports
EXPOSE 8000 5678

# Development-specific environment variables
ENV PYTHONPATH=/app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Health check optimized for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:8000/health', timeout=5)" || exit 1

# Use entrypoint and enable hot-reload with uvicorn
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["python", "-m", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "info"]
