/**
 * useApprovals Hook
 * Custom hook for managing approval requests state and API interactions
 */

import { useCallback, useEffect, useState } from "react";
import { LoadingState } from "../types/api";
import type { ApiError, AsyncState } from "../types/api";

export interface ApprovalRequest {
  id: string;
  user_id: string;
  approval_type: string;
  title: string;
  description: string;
  item_type: string;
  item_id: string;
  roadmap_id?: string;
  risk_level: "low" | "medium" | "high" | "critical";
  risk_assessment?: string;
  impact_summary: string[];
  changes_summary: string[];
  files_affected: string[];
  services_affected: string[];
  status: "pending" | "approved" | "rejected" | "timeout" | "cancelled";
  created_at: string;
  expires_at: string;
  timeout_minutes: number;
  estimated_duration?: number;
  preview_data?: Record<string, unknown>;
  rollback_plan?: string;
}

export interface ApprovalResponse {
  decision: "approved" | "rejected";
  comments?: string;
  conditions?: string[];
}

interface UseApprovalsReturn {
  // State
  approvals: ApprovalRequest[];
  loading: LoadingState;
  error: ApiError | null;
  submitting: boolean;

  // Actions
  refreshApprovals: () => Promise<void>;
  approveRequest: (
    requestId: string,
    response: ApprovalResponse,
  ) => Promise<boolean>;
  rejectRequest: (
    requestId: string,
    response: ApprovalResponse,
  ) => Promise<boolean>;
  clearError: () => void;

  // Utilities
  getPendingApprovals: () => ApprovalRequest[];
  getApprovalById: (id: string) => ApprovalRequest | null;
  getTimeRemaining: (expiresAt: string) => string;
}

/**
 * Custom hook for approval management
 */
export const useApprovals = (
  userId: string,
  apiBaseUrl = "/api/v1",
): UseApprovalsReturn => {
  const [state, setState] = useState<AsyncState<ApprovalRequest[]>>({
    data: [],
    loading: LoadingState.IDLE,
    error: null,
  });
  const [submitting, setSubmitting] = useState(false);

  /**
   * Load pending approvals from the API
   */
  const refreshApprovals = useCallback(async (): Promise<void> => {
    setState((prev) => ({
      ...prev,
      loading: LoadingState.LOADING,
      error: null,
    }));

    try {
      const response = await fetch(
        `${apiBaseUrl}/approvals/pending?user_id=${userId}`,
      );
      if (response.ok) {
        const data = await response.json();
        setState((prev) => ({
          ...prev,
          data: data,
          loading: LoadingState.SUCCESS,
          error: null,
        }));
      } else {
        setState((prev) => ({
          ...prev,
          loading: LoadingState.ERROR,
          error: {
            message: "Failed to fetch pending approvals",
            details: `HTTP ${response.status}`,
          },
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: LoadingState.ERROR,
        error: {
          message: "Network error fetching approvals",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
    }
  }, [apiBaseUrl, userId]);

  /**
   * Submit approval decision
   */
  const submitDecision = useCallback(async (
    requestId: string,
    decision: "approved" | "rejected",
    response: ApprovalResponse,
  ): Promise<boolean> => {
    setSubmitting(true);
    setState((prev) => ({ ...prev, error: null }));

    try {
      const fetchResponse = await fetch(
        `${apiBaseUrl}/approvals/${requestId}/${decision}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_id: userId,
            comments: response.comments || undefined,
            conditions: response.conditions?.length
              ? response.conditions
              : undefined,
          }),
        },
      );

      if (fetchResponse.ok) {
        // Remove from pending list
        setState((prev) => ({
          ...prev,
          data: prev.data?.filter((a) => a.id !== requestId) || [],
        }));
        return true;
      } else {
        const errorData = await fetchResponse.json();
        setState((prev) => ({
          ...prev,
          error: {
            message: errorData.detail || `Failed to ${decision} approval`,
            details: errorData,
          },
        }));
        return false;
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: {
          message: `Network error ${
            decision === "approved" ? "approving" : "rejecting"
          } request`,
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
      return false;
    } finally {
      setSubmitting(false);
    }
  }, [apiBaseUrl, userId]);

  /**
   * Approve a request
   */
  const approveRequest = useCallback(async (
    requestId: string,
    response: ApprovalResponse,
  ): Promise<boolean> => {
    return submitDecision(requestId, "approved", response);
  }, [submitDecision]);

  /**
   * Reject a request
   */
  const rejectRequest = useCallback(async (
    requestId: string,
    response: ApprovalResponse,
  ): Promise<boolean> => {
    return submitDecision(requestId, "rejected", response);
  }, [submitDecision]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  /**
   * Get pending approvals
   */
  const getPendingApprovals = useCallback(() => {
    return state.data || [];
  }, [state.data]);

  /**
   * Get approval by ID
   */
  const getApprovalById = useCallback((id: string) => {
    return state.data?.find((a) => a.id === id) || null;
  }, [state.data]);

  /**
   * Calculate time remaining for an approval
   */
  const getTimeRemaining = useCallback((expiresAt: string) => {
    const now = new Date();
    const expires = new Date(expiresAt);
    const diff = expires.getTime() - now.getTime();

    if (diff <= 0) return "Expired";

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`;
    } else {
      return `${minutes}m remaining`;
    }
  }, []);

  // Initial load
  useEffect(() => {
    refreshApprovals();
  }, [refreshApprovals]);

  return {
    approvals: state.data || [],
    loading: state.loading,
    error: state.error,
    submitting,
    refreshApprovals,
    approveRequest,
    rejectRequest,
    clearError,
    getPendingApprovals,
    getApprovalById,
    getTimeRemaining,
  };
};
