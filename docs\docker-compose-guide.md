# Docker Compose Configuration Guide

## Overview

The AI Coding Agent uses a **multi-file Docker Compose architecture** to manage different deployment scenarios. This guide explains the purpose of each file and when to use them.

## Compose Files

### `docker-compose.yml` (Base Configuration)

- **Purpose**: Core services that run in all environments
- **Services**:
  - `ai-orchestrator` (FastAPI backend)
  - `user-portal` (Next.js frontend)
  - `redis` (caching and state management)
  - `traefik` (reverse proxy and load balancer)
  - `docker-proxy` (Docker API access)
  - `code-server-template` (template for dynamic workspace creation)
- **Profile**: `template-only` (code-server-template only starts when explicitly requested)

### `docker-compose.dev.yml` (Development Overrides)

- **Purpose**: Development-specific configurations
- **Adds/Modifies**:
  - `code-server` service (direct access for development)
  - Exposed ports for debugging
  - Volume mounts for hot-reloading
  - Development environment variables
- **Use Case**: Local development with direct access to services

### `docker-compose.prod.yml` (Production Overrides)

- **Purpose**: Production hardening and optimization
- **Adds/Modifies**:
  - Security hardening
  - Resource limits
  - Production logging
  - Optimized networking
- **Use Case**: Production deployment

### `docker-compose.supabase.yml` (Supabase Integration)

- **Purpose**: Local Supabase development stack
- **Services**:
  - PostgreSQL with pgvector
  - Supabase API
  - Supabase Studio
  - Inbucket (email testing)
- **Use Case**: Local database development

## Usage Scenarios

### Development Environment

```bash
# ✅ RECOMMENDED: Use the startup script
./scripts/start-dev.sh

# OR: Manual command
docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Result: All services + code-server for direct development access
```

### Production Environment

```bash
# ✅ RECOMMENDED: Use the startup script
./scripts/start-prod.sh

# OR: Manual command
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Result: All services with production hardening, code-server as templates only
```

### Full Local Stack (Development + Supabase)

```bash
# Start Supabase first
supabase start

# Then start application services
docker compose -f docker-compose.yml -f docker-compose.dev.yml -f docker-compose.supabase.yml up -d
```

## Common Mistakes

### ❌ Wrong: Using single file in development

```bash
docker compose up -d  # Missing code-server!
```

### ✅ Correct: Multi-file for development

```bash
docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

### ❌ Wrong: Using dev overrides in production

```bash
docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d  # Exposed ports in prod!
```

### ✅ Correct: Base file only for production

```bash
docker compose -f docker-compose.yml up -d
```

## Service Access Points

| Service | Development | Production |
|---------|-------------|------------|
| User Portal | `http://portal.localhost` | `http://portal.localhost` |
| AI Orchestrator API | `http://api.localhost` | `http://api.localhost` |
| Code Server | `http://localhost:8080` | Dynamic (via API) |
| Traefik Dashboard | `http://traefik.localhost` | `http://traefik.localhost` |
| Supabase Studio | `http://127.0.0.1:54323` | N/A (external) |

## Network Security Architecture

### Two-Network Security Model

The AI Coding Agent implements a **two-network security model** following the principle of least privilege:

#### Web Network (DMZ - Public-Facing)

- **Purpose**: Services that need to be reached by Traefik reverse proxy
- **Members**: `traefik`, `ai-orchestrator`, `user-portal`, `code-server`, monitoring services
- **Security Zone**: Demilitarized zone for internet-facing traffic

#### Internal Network (Secure Backend)

- **Purpose**: Services that should never be exposed to the outside world
- **Members**: `ai-orchestrator`, `postgresql`, `redis`, `ollama`, `docker-proxy`
- **Security Zone**: Secure backend for internal service communication

#### Network Isolation Benefits

1. **Security (Principle of Least Privilege)**: If a user-facing container is compromised, attackers have no network path to databases or Redis cache
2. **Isolation**: Services on the internal network are completely isolated from external access
3. **Organization**: Clear architectural separation between public and private services
4. **Performance**: Efficient Docker networking with no overhead

#### Service Network Assignments

| Service | Web Network | Internal Network | Purpose |
|---------|-------------|------------------|---------|
| `traefik` | ✅ | ❌ | Reverse proxy (public entry point) |
| `ai-orchestrator` | ✅ | ✅ | Bridge service (needs both networks) |
| `user-portal` | ✅ | ❌ | Frontend (public via Traefik) |
| `code-server` | ✅ | ❌ | IDE access (public via Traefik) |
| `postgresql` | ❌ | ✅ | Database (internal only) |
| `redis` | ❌ | ✅ | Cache (internal only) |
| `ollama` | ❌ | ✅ | LLM service (internal only) |
| `docker-proxy` | ❌ | ✅ | Container management (internal only) |

## Architecture Notes

- **code-server-template**: Used for dynamic workspace provisioning in production
- **code-server** (dev only): Direct access for development and testing
- **Traefik**: Handles all routing and SSL termination (web network only)
- **Redis**: Shared state and caching across all services (internal network only)
- **Docker Proxy**: Enables container management from within the application (internal network only)
- **ai-orchestrator**: Bridge service connecting public web requests to secure backend services

## Troubleshooting

### Network Security Validation

#### Verify Network Isolation

```bash
# Check network assignments
docker compose ps --format "table {{.Name}}\t{{.Networks}}"

# Verify internal services are not exposed
docker compose port redis 6379    # Should show: No port mappings
docker compose port ollama 11434  # Should show: No port mappings

# Test network connectivity (from ai-orchestrator)
docker compose exec ai-orchestrator curl -f http://redis:6379/ping
docker compose exec ai-orchestrator curl -f http://ollama:11434/api/tags
```

#### Security Checklist

- [ ] Database services have no exposed ports
- [ ] Redis has no exposed ports
- [ ] Ollama has no exposed ports (internal network only)
- [ ] Only Traefik accepts external traffic
- [ ] ai-orchestrator is the only bridge between networks

### Services not starting

1. Check Docker is running: `docker info`
2. Clean up: `docker compose down --volumes --remove-orphans`
3. Rebuild: Add `--build` flag

### Port conflicts

1. Check what's using ports: `netstat -tulpn | grep :8080`
2. Modify port mappings in override files
3. Use `docker compose ps` to see current mappings

### Permission issues

1. Ensure Docker daemon is accessible
2. Check user is in docker group: `groups $USER`
3. On Windows, ensure Docker Desktop is running with WSL2

### Network connectivity issues

1. Verify networks exist: `docker network ls`
2. Check service network assignments: `docker compose config`
3. Test internal connectivity: `docker compose exec ai-orchestrator ping redis`
4. Ensure no hardcoded IPs in configurations
