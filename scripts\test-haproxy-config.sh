#!/bin/bash
# Test script to verify HAProxy configuration

echo "Testing HAProxy configuration..."

# Check if haproxy is available
if ! command -v haproxy &> /dev/null; then
    echo "HAProxy not found, installing..."
    if command -v apt-get &> /dev/null; then
        sudo apt-get update && sudo apt-get install -y haproxy
    elif command -v apk &> /dev/null; then
        apk add --no-cache haproxy
    else
        echo "Cannot install haproxy - no package manager found"
        exit 1
    fi
fi

# Test the configuration
echo "Testing HAProxy configuration syntax..."
haproxy -c -f containers/docker-proxy/haproxy.cfg

if [ $? -eq 0 ]; then
    echo " HAProxy configuration is valid!"
else
    echo " HAProxy configuration has errors!"
    exit 1
fi

echo "Configuration test complete."
