"""
Pydantic schemas for Memory API requests and responses.

These schemas are intentionally separate from SQLAlchemy models to
avoid leaking persistence concerns into API validation. All owner
identification is performed server-side via authenticated user context.
"""

from __future__ import annotations

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class MemoryType(str, Enum):
    USER_PREFERENCE = "user_preference"
    CODE_PATTERN = "code_pattern"
    PROJECT_CONTEXT = "project_context"
    LEARNING_OUTCOME = "learning_outcome"
    ERROR_SOLUTION = "error_solution"
    ARCHITECTURE_DECISION = "architecture_decision"
    BEST_PRACTICE = "best_practice"


class MemoryPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class MemoryMetadata(BaseModel):
    tags: List[str] = Field(default_factory=list)
    priority: MemoryPriority = MemoryPriority.MEDIUM
    expires_at: Optional[datetime] = None
    source: str = "ai_agent"
    confidence: float = Field(default=1.0, ge=0.0, le=1.0)


class MemoryEntry(BaseModel):
    id: str
    content: str
    memory_type: MemoryType
    metadata: MemoryMetadata = Field(default_factory=MemoryMetadata)
    created_at: datetime
    updated_at: datetime


class MemorySearchRequest(BaseModel):
    query: Optional[str] = None
    memory_type: Optional[MemoryType] = None
    tags: Optional[List[str]] = None
    limit: int = Field(default=10, ge=1, le=100)
    include_metadata: bool = True
    use_semantic: bool = True


class MemorySearchResponse(BaseModel):
    memories: List[MemoryEntry]
    total_count: int
    search_criteria: Dict[str, Any]
    execution_time: float


class MemoryStoreRequest(BaseModel):
    content: str = Field(..., min_length=1)
    memory_type: MemoryType
    tags: Optional[List[str]] = None
    priority: MemoryPriority = MemoryPriority.MEDIUM
    expires_at: Optional[datetime] = None
    source: str = "ai_agent"


class PatternStoreRequest(BaseModel):
    name: str
    description: str
    language: str
    framework: Optional[str] = None
    pattern_type: str
    template: str
    variables: Dict[str, str] = Field(default_factory=dict)
    examples: List[str] = Field(default_factory=list)
    best_practices: List[str] = Field(default_factory=list)


class ValidationResult(BaseModel):
    is_valid: bool
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    suggestions: List[str] = Field(default_factory=list)


class MemoryCleanupRequest(BaseModel):
    older_than_days: Optional[int] = None
    memory_types: Optional[List[MemoryType]] = None
    tags_to_remove: Optional[List[str]] = None
    dry_run: bool = True


class MemoryCleanupResponse(BaseModel):
    memories_removed: int
    patterns_removed: int
    learnings_removed: int
    errors: List[str]
    dry_run: bool
    executed_at: datetime


class CodePattern(BaseModel):
    """A reusable code pattern."""

    name: str
    description: str
    language: str
    framework: Optional[str] = None
    pattern_type: str
    template: str
    variables: Dict[str, str] = Field(default_factory=dict)
    examples: List[str] = Field(default_factory=list)
    best_practices: List[str] = Field(default_factory=list)


class LearningOutcome(BaseModel):
    """A learning outcome from interactions."""

    topic: str
    lesson: str
    context: str
    confidence: float
    applied_count: int = 0
    last_applied: Optional[datetime] = None


class ContextResponse(BaseModel):
    memories: List[MemoryEntry]
    patterns: List[CodePattern]
    learnings: List[LearningOutcome]
    context_summary: str


class MemoryStatistics(BaseModel):
    total_memories: int
    memory_types: Dict[str, int]
    unique_tags: int
    code_patterns: int
    learning_outcomes: int
    storage_path: str
