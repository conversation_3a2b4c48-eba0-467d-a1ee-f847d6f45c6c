{"name": "ai-chat-extension", "displayName": "AI Coding Agent <PERSON>", "description": "Real-time chat interface with AI Coding Agents", "version": "1.0.0", "publisher": "ai-coding-agent", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["ai", "chat", "assistant", "coding", "websocket"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "browser": "./dist/extension.js", "contributes": {"commands": [{"command": "aiChatExtension.openChat", "title": "Open AI Chat", "category": "AI Chat"}, {"command": "aiChatExtension.clearHistory", "title": "Clear Chat History", "category": "AI Chat"}, {"command": "aiChatExtension.reconnect", "title": "Reconnect to AI Service", "category": "AI Chat"}], "views": {"explorer": [{"id": "aiChatView", "name": "AI Chat", "when": "true", "icon": "$(comment-discussion)", "contextualTitle": "AI Coding Agent <PERSON>"}]}, "viewsWelcome": [{"view": "aiChatView", "contents": "Welcome to AI Coding Agent Cha<PERSON>!\n\n[Open Chat](command:aiChatExtension.openChat)\n\nChat with AI agents to get help with your coding tasks.", "when": "true"}], "configuration": {"title": "AI Chat", "properties": {"aiChat.serverUrl": {"type": "string", "default": "ws://localhost:8000", "description": "AI Orchestrator WebSocket server URL"}, "aiChat.autoConnect": {"type": "boolean", "default": true, "description": "Automatically connect to AI service on startup"}, "aiChat.maxMessages": {"type": "number", "default": 100, "description": "Maximum number of messages to keep in history"}, "aiChat.maxReconnectAttempts": {"type": "number", "default": 5, "description": "Maximum number of WebSocket reconnection attempts"}, "aiChat.reconnectDelay": {"type": "number", "default": 1000, "description": "Base delay in milliseconds between reconnection attempts"}, "aiChat.heartbeatInterval": {"type": "number", "default": 30000, "description": "Heartbeat interval in milliseconds to keep connection alive"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package", "test": "echo \"No tests specified\" && exit 0"}, "devDependencies": {"@types/node": "^18.15.0", "@types/vscode": "^1.74.0", "@vscode/vsce": "^2.15.0", "eslint": "^9.35.0", "typescript": "^4.9.4"}, "dependencies": {"acorn": "^8.15.0", "acorn-jsx": "^5.3.2", "agent-base": "^7.1.4", "ajv": "^6.12.6", "ansi-styles": "^3.2.1", "argparse": "^2.0.1", "asynckit": "^0.4.0", "azure-devops-node-api": "^12.5.0", "balanced-match": "^1.0.2", "base64-js": "^1.5.1", "bl": "^4.1.0", "boolbase": "^1.0.0", "brace-expansion": "^1.1.12", "buffer": "^5.7.1", "buffer-crc32": "^0.2.13", "buffer-equal-constant-time": "^1.0.1", "bundle-name": "^4.1.0", "call-bind-apply-helpers": "^1.0.2", "call-bound": "^1.0.4", "callsites": "^3.1.0", "chalk": "^2.4.2", "cheerio": "^1.1.2", "cheerio-select": "^2.1.0", "chownr": "^1.1.4", "cockatiel": "^3.2.1", "color-convert": "^1.9.3", "color-name": "^1.1.3", "combined-stream": "^1.0.8", "commander": "^6.2.1", "concat-map": "^0.0.1", "cross-spawn": "^7.0.6", "css-select": "^5.2.2", "css-what": "^6.2.2", "debug": "^4.4.1", "decompress-response": "^6.0.0", "deep-extend": "^0.6.0", "deep-is": "^0.1.4", "default-browser": "^5.2.1", "default-browser-id": "^5.0.0", "define-lazy-prop": "^3.0.0", "delayed-stream": "^1.0.0", "detect-libc": "^2.0.4", "dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.2.2", "dunder-proto": "^1.0.1", "ecdsa-sig-formatter": "^1.0.11", "encoding-sniffer": "^0.2.1", "end-of-stream": "^1.4.5", "entities": "^4.5.0", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "escape-string-regexp": "^1.0.5", "eslint-scope": "^8.4.0", "eslint-visitor-keys": "^4.2.1", "espree": "^10.4.0", "esquery": "^1.6.0", "esrecurse": "^4.3.0", "estraverse": "^5.3.0", "esutils": "^2.0.3", "expand-template": "^2.0.3", "fast-deep-equal": "^3.1.3", "fast-json-stable-stringify": "^2.1.0", "fast-levenshtein": "^2.0.6", "fd-slicer": "^1.1.0", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "flat-cache": "^4.0.1", "flatted": "^3.3.3", "form-data": "^4.0.4", "fs-constants": "^1.0.0", "fs.realpath": "^1.0.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "github-from-package": "^0.0.0", "glob": "^7.2.3", "glob-parent": "^6.0.2", "globals": "^14.0.0", "gopd": "^1.2.0", "has-flag": "^3.0.0", "has-symbols": "^1.1.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2", "hosted-git-info": "^4.1.0", "htmlparser2": "^10.0.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "iconv-lite": "^0.6.3", "ieee754": "^1.2.1", "ignore": "^5.3.2", "import-fresh": "^3.3.1", "imurmurhash": "^0.1.4", "inflight": "^1.0.6", "inherits": "^2.0.4", "ini": "^1.3.8", "is-docker": "^3.0.0", "is-extglob": "^2.1.1", "is-glob": "^4.0.3", "is-inside-container": "^1.0.0", "is-wsl": "^3.1.0", "isexe": "^2.0.0", "js-yaml": "^4.1.0", "json-buffer": "^3.0.1", "json-schema-traverse": "^0.4.1", "json-stable-stringify-without-jsonify": "^1.0.1", "jsonc-parser": "^3.3.1", "jsonwebtoken": "^9.0.2", "jwa": "^1.4.2", "jws": "^3.2.2", "keytar": "^7.9.0", "keyv": "^4.5.4", "leven": "^3.1.0", "levn": "^0.4.1", "linkify-it": "^3.0.3", "locate-path": "^6.0.0", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.merge": "^4.6.2", "lodash.once": "^4.1.1", "lru-cache": "^6.0.0", "markdown-it": "^12.3.2", "math-intrinsics": "^1.1.0", "mdurl": "^1.0.1", "mime": "^1.6.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "mimic-response": "^3.1.0", "minimatch": "^3.1.2", "minimist": "^1.2.8", "mkdirp-classic": "^0.5.3", "ms": "^2.1.3", "mute-stream": "^0.0.8", "napi-build-utils": "^2.0.0", "natural-compare": "^1.4.0", "node-abi": "^3.75.0", "node-addon-api": "^4.3.0", "nth-check": "^2.1.1", "object-inspect": "^1.13.4", "once": "^1.4.0", "open": "^10.2.0", "optionator": "^0.9.4", "p-limit": "^3.1.0", "p-locate": "^5.0.0", "parent-module": "^1.0.1", "parse-semver": "^1.1.1", "parse5": "^7.3.0", "parse5-htmlparser2-tree-adapter": "^7.1.0", "parse5-parser-stream": "^7.1.2", "path-exists": "^4.0.0", "path-is-absolute": "^1.0.1", "path-key": "^3.1.1", "pend": "^1.2.0", "prebuild-install": "^7.1.3", "prelude-ls": "^1.2.1", "pump": "^3.0.3", "punycode": "^2.3.1", "qs": "^6.14.0", "rc": "^1.2.8", "read": "^1.0.7", "readable-stream": "^3.6.2", "resolve-from": "^4.0.0", "run-applescript": "^7.0.0", "safe-buffer": "^5.2.1", "safer-buffer": "^2.1.2", "sax": "^1.4.1", "semver": "^7.7.2", "shebang-command": "^2.0.0", "shebang-regex": "^3.0.0", "side-channel": "^1.1.0", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2", "simple-concat": "^1.0.1", "simple-get": "^4.0.1", "string_decoder": "^1.3.0", "strip-json-comments": "^2.0.1", "supports-color": "^5.5.0", "tar-fs": "^2.1.3", "tar-stream": "^2.2.0", "tmp": "^0.2.5", "tslib": "^2.8.1", "tunnel": "^0.0.6", "tunnel-agent": "^0.6.0", "type-check": "^0.4.0", "typed-rest-client": "^1.8.11", "uc.micro": "^1.0.6", "underscore": "^1.13.7", "undici": "^7.15.0", "undici-types": "^5.26.5", "uri-js": "^4.4.1", "url-join": "^4.0.1", "util-deprecate": "^1.0.2", "uuid": "^8.3.2", "whatwg-encoding": "^3.1.1", "whatwg-mimetype": "^4.0.0", "which": "^2.0.2", "word-wrap": "^1.2.5", "wrappy": "^1.0.2", "wsl-utils": "^0.1.0", "xml2js": "^0.5.0", "xmlbuilder": "^11.0.1", "yallist": "^4.0.0", "yauzl": "^2.10.0", "yazl": "^2.5.1", "yocto-queue": "^0.1.0"}, "author": "", "license": "ISC", "type": "commonjs"}