"""
Ingestion Errors Model for AI Coding Agent.

This module defines the SQLAlchemy IngestionError model for tracking
file processing errors during project ingestion.
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from src.models.database import Base
from src.models.custom_types import UuidVariant


class IngestionError(Base):
    """
    IngestionError model for tracking file processing errors.

    This model stores errors that occur during project file ingestion,
    allowing for debugging and monitoring of the ingestion process.
    """

    __tablename__ = "ingestion_errors"

    id = Column(
        Integer,
        primary_key=True,
        index=True,
        autoincrement=True,
        comment="Unique error identifier"
    )

    project_id = Column(
        Integer,
        ForeignKey('projects.id', ondelete='CASCADE'),
        nullable=False,
        index=True,
        comment="Project where error occurred"
    )

    user_id = Column(
        UuidVariant,
        ForeignKey('user_profiles.supabase_user_id', ondelete='CASCADE'),
        nullable=False,
        index=True,
        comment="UUID foreign key to user_profiles.supabase_user_id"
    )

    file_path = Column(
        String(500),
        nullable=False,
        comment="Relative path of the file that failed"
    )

    error_type = Column(
        String(100),
        nullable=False,
        comment="Type of error (e.g., 'processing', 'embedding', 'storage')"
    )

    error_message = Column(
        Text,
        nullable=False,
        comment="Detailed error message"
    )

    stack_trace = Column(
        Text,
        nullable=True,
        comment="Full stack trace if available"
    )

    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Timestamp when error occurred"
    )

    # Relationships
    project = relationship("Project", backref="ingestion_errors")
    user = relationship("UserProfile", backref="ingestion_errors")

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"<IngestionError(id={self.id}, project_id={self.project_id}, file_path='{self.file_path}', error_type='{self.error_type}')>"
