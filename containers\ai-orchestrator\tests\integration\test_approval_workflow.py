import pytest
from httpx import AsyncClient

@pytest.mark.asyncio
async def test_request_changes_requires_auth(async_client: AsyncClient):
    """
    Tests that the /request-changes endpoint requires authentication.
    This is a simple test that uses the async_client fixture to make a request
    to the API without providing any authentication. It expects a 401 or 403
    status code in response.
    """
    # We don't provide an auth token, so this should fail.
    response = await async_client.post(
        "/api/projects/1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed/request-changes",
        json={"prompt": "test prompt"},
    )
    assert response.status_code in [401, 403]
