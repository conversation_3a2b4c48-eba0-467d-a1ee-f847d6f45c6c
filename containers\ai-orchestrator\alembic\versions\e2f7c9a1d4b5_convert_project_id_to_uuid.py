"""convert_project_id_to_uuid

Revision ID: e2f7c9a1d4b5
Revises: d1e5f7a2b3c4
Create Date: 2025-09-09 20:30:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "e2f7c9a1d4b5"
down_revision: Union[str, None] = "d1e5f7a2b3c4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Convert projects.id from INTEGER to UUID and update dependent FKs."""
    # Ensure UUID extension exists
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";')

    # 1) Add new UUID column to projects
    op.add_column(
        "projects",
        sa.Column("id_uuid", sa.dialects.postgresql.UUID(as_uuid=True),
                  server_default=sa.text("uuid_generate_v4()"), nullable=False)
    )

    # 2) Add new UUID columns to dependent tables
    if op.get_bind().dialect.name == 'postgresql':
        uuid_type = sa.dialects.postgresql.UUID(as_uuid=True)
    else:
        uuid_type = sa.String(36)

    # user_project_association
    op.add_column(
        "user_project_association",
        sa.Column("project_id_uuid", uuid_type, nullable=True)
    )

    # project_exports
    if op.get_bind().dialect.name == 'postgresql':
        op.add_column(
            "project_exports",
            sa.Column("project_id_uuid", sa.dialects.postgresql.UUID(as_uuid=True), nullable=True)
        )
    else:
        op.add_column(
            "project_exports",
            sa.Column("project_id_uuid", sa.String(36), nullable=True)
        )

    # 3) Populate UUID columns based on current integer IDs
    op.execute(
        """
        UPDATE user_project_association ua
        SET project_id_uuid = p.id_uuid
        FROM projects p
        WHERE ua.project_id = p.id;
        """
    )

    op.execute(
        """
        UPDATE project_exports pe
        SET project_id_uuid = p.id_uuid
        FROM projects p
        WHERE pe.project_id = p.id;
        """
    )

    # 4) Drop FKs referencing old integer project id
    try:
        op.drop_constraint("user_project_association_project_id_fkey", "user_project_association", type_="foreignkey")
    except Exception:
        pass
    try:
        op.drop_constraint("project_exports_project_id_fkey", "project_exports", type_="foreignkey")
    except Exception:
        pass

    # 5) Drop old integer columns and rename new UUID columns
    op.drop_column("user_project_association", "project_id")
    op.alter_column("user_project_association", "project_id_uuid", new_column_name="project_id")

    op.drop_column("project_exports", "project_id")
    op.alter_column("project_exports", "project_id_uuid", new_column_name="project_id")

    # 6) Switch primary key on projects to UUID
    try:
        op.drop_constraint("projects_pkey", "projects", type_="primary")
    except Exception:
        pass
    op.drop_column("projects", "id")
    op.alter_column("projects", "id_uuid", new_column_name="id")
    op.create_primary_key("projects_pkey", "projects", ["id"])

    # 7) Recreate FKs with new UUID types
    op.create_foreign_key(
        "user_project_association_project_id_fkey",
        source_table="user_project_association",
        referent_table="projects",
        local_cols=["project_id"],
        remote_cols=["id"],
        ondelete="CASCADE",
    )

    op.create_foreign_key(
        "project_exports_project_id_fkey",
        source_table="project_exports",
        referent_table="projects",
        local_cols=["project_id"],
        remote_cols=["id"],
        ondelete="CASCADE",
    )


def downgrade() -> None:
    """Attempt to revert projects.id back to INTEGER and restore dependencies."""
    # Add integer columns back
    op.add_column("projects", sa.Column("id_int", sa.Integer(), autoincrement=True, nullable=False))

    op.add_column("user_project_association", sa.Column("project_id_int", sa.Integer(), nullable=True))
    op.add_column("project_exports", sa.Column("project_id_int", sa.Integer(), nullable=True))

    # Populate integer IDs by creating a temporary mapping table
    op.execute(
        """
        CREATE TEMP TABLE project_id_map AS
        SELECT ROW_NUMBER() OVER (ORDER BY id) AS id_int, id AS id_uuid
        FROM projects;
        """
    )

    # Update projects.id_int based on map
    op.execute(
        """
        UPDATE projects p
        SET id_int = m.id_int
        FROM project_id_map m
        WHERE p.id = m.id_uuid;
        """
    )

    # Update dependents
    op.execute(
        """
        UPDATE user_project_association ua
        SET project_id_int = m.id_int
        FROM project_id_map m
        WHERE ua.project_id = m.id_uuid;
        """
    )

    op.execute(
        """
        UPDATE project_exports pe
        SET project_id_int = m.id_int
        FROM project_id_map m
        WHERE pe.project_id = m.id_uuid;
        """
    )

    # Drop current FKs
    try:
        op.drop_constraint("user_project_association_project_id_fkey", "user_project_association", type_="foreignkey")
    except Exception:
        pass
    try:
        op.drop_constraint("project_exports_project_id_fkey", "project_exports", type_="foreignkey")
    except Exception:
        pass

    # Switch back primary key
    try:
        op.drop_constraint("projects_pkey", "projects", type_="primary")
    except Exception:
        pass
    op.drop_column("projects", "id")
    op.alter_column("projects", "id_int", new_column_name="id")
    op.create_primary_key("projects_pkey", "projects", ["id"])

    # Rename dependent columns back
    op.drop_column("user_project_association", "project_id")
    op.alter_column("user_project_association", "project_id_int", new_column_name="project_id")

    op.drop_column("project_exports", "project_id")
    op.alter_column("project_exports", "project_id_int", new_column_name="project_id")

    # Recreate FKs
    op.create_foreign_key(
        "user_project_association_project_id_fkey",
        source_table="user_project_association",
        referent_table="projects",
        local_cols=["project_id"],
        remote_cols=["id"],
        ondelete="CASCADE",
    )

    op.create_foreign_key(
        "project_exports_project_id_fkey",
        source_table="project_exports",
        referent_table="projects",
        local_cols=["project_id"],
        remote_cols=["id"],
        ondelete="CASCADE",
    )
