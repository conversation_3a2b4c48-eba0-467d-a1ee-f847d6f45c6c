# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: Pydantic models for role-based LLM configuration management

import os
from typing import Dict, List, Optional
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict

from src.core.config import LLMProvider


class RoleConfigError(Exception):
    """Base exception for role configuration errors."""
    pass


class InvalidRoleConfigError(RoleConfigError):
    """Raised when role configuration validation fails."""
    pass


class RoleNotFoundError(RoleConfigError):
    """Raised when requested role does not exist."""
    pass


class ConfigPersistenceError(RoleConfigError):
    """Raised when configuration persistence operations fail."""
    pass


class RoleConfiguration(BaseModel):
    """Configuration for a single AI agent role.

    This model defines how an AI agent role is configured with its
    LLM provider, available models, selected model, API credentials,
    and cost controls.
    """

    provider: LLMProvider = Field(
        ...,
        description="LLM provider for this role"
    )

    available_models: List[str] = Field(
        description="List of models available for this role",
        min_length=1
    )

    selected_model: str = Field(
        ...,
        min_length=1,
        description="Currently selected model for this role"
    )

    api_key: Optional[str] = Field(
        default=None,
        description="API key for cloud providers (encrypted or vaulted reference)"
    )

    cost_limit: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=10000.0,
        description="Monthly cost limit in USD for this role"
    )

    max_tokens: Optional[int] = Field(
        default=4096,
        ge=1,
        le=32768,
        description="Maximum tokens per request for this role"
    )

    temperature: float = Field(
        default=0.7,
        ge=0.0,
        le=2.0,
        description="Default temperature for this role"
    )

    enabled: bool = Field(
        default=True,
        description="Whether this role is enabled"
    )

    created_at: Optional[str] = Field(
        default=None,
        description="Role creation timestamp"
    )

    updated_at: Optional[str] = Field(
        default=None,
        description="Last update timestamp"
    )

    @field_validator('selected_model')
    @classmethod
    def validate_selected_model(cls, v, info):
        """Validate that selected_model is in available_models."""
        if info.data:
            available_models = info.data.get('available_models', [])
            if available_models and v not in available_models:
                raise ValueError(
                    f"selected_model '{v}' must be in available_models {available_models}"
                )
        return v

    @model_validator(mode='after')
    def validate_api_key_for_cloud_providers(self):
        """Validate that cloud providers have API keys."""
        provider = self.provider
        api_key = self.api_key

        cloud_providers = {LLMProvider.OPENROUTER, LLMProvider.OPENAI, LLMProvider.ANTHROPIC}

        # In development, allow placeholder keys or missing keys
        is_development = os.getenv("DEBUG", "false").lower() == "true"
        placeholder_keys = {"your_openai_key_here", "your_anthropic_key_here", None, ""}

        if provider in cloud_providers:
            if not api_key or (is_development and api_key in placeholder_keys):
                # In development, warn but don't fail
                if is_development:
                    import logging
                    logging.getLogger(__name__).warning(
                        f"Provider {provider} has missing/placeholder API key - will skip in development"
                    )
                else:
                    raise ValueError(f"api_key is required for cloud provider {provider}")

        return self

    model_config = ConfigDict(
        use_enum_values=True,
        validate_assignment=True,
        extra="forbid"
    )


class RoleConfigurationUpdate(BaseModel):
    """Model for updating role configuration.

    All fields are optional to support partial updates.
    """

    provider: Optional[LLMProvider] = Field(
        default=None,
        description="LLM provider for this role"
    )

    available_models: Optional[List[str]] = Field(
        default=None,
        description="List of models available for this role",
        min_length=1
    )

    selected_model: Optional[str] = Field(
        default=None,
        min_length=1,
        description="Currently selected model for this role"
    )

    api_key: Optional[str] = Field(
        default=None,
        description="API key for cloud providers"
    )

    cost_limit: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=10000.0,
        description="Monthly cost limit in USD for this role"
    )

    max_tokens: Optional[int] = Field(
        default=None,
        ge=1,
        le=32768,
        description="Maximum tokens per request for this role"
    )

    temperature: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=2.0,
        description="Default temperature for this role"
    )

    enabled: Optional[bool] = Field(
        default=None,
        description="Whether this role is enabled"
    )

    model_config = ConfigDict(
        use_enum_values=True,
        extra="forbid"
    )


class RoleConfigurationList(BaseModel):
    """Complete role configuration container."""

    roles: Dict[str, RoleConfiguration] = Field(
        default_factory=dict,
        description="Dictionary of role names to their configurations"
    )

    version: str = Field(
        default="1.0",
        description="Configuration schema version"
    )

    updated_at: Optional[str] = Field(
        default=None,
        description="Last global update timestamp"
    )

    @field_validator('roles')
    @classmethod
    def validate_role_names(cls, v):
        """Validate role names follow naming conventions."""
        for role_name in v.keys():
            if not role_name.replace('_', '').isalnum():
                raise ValueError(
                    f"Role name '{role_name}' must contain only alphanumeric characters and underscores"
                )
            if len(role_name) > 50:
                raise ValueError(f"Role name '{role_name}' must be 50 characters or less")
        return v

    model_config = ConfigDict(extra="forbid")


class RoleConfigurationResponse(BaseModel):
    """Response model for role configuration operations."""

    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Operation result message")
    role_name: Optional[str] = Field(default=None, description="Role name")
    configuration: Optional[RoleConfiguration] = Field(
        default=None,
        description="Role configuration data"
    )


class RoleListResponse(BaseModel):
    """Response model for listing all roles."""

    success: bool = Field(..., description="Operation success status")
    message: str = Field(..., description="Operation result message")
    roles: Dict[str, RoleConfiguration] = Field(
        default_factory=dict,
        description="Dictionary of all role configurations"
    )
    total_roles: int = Field(..., description="Total number of roles")


class ProviderModelsResponse(BaseModel):
    """Response model for available models by provider."""

    provider: LLMProvider = Field(..., description="Provider name")
    models: List[str] = Field(..., description="Available models for this provider")
    status: str = Field(..., description="Provider status")