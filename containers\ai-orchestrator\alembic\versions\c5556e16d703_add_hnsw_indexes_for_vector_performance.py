"""add_hnsw_indexes_for_vector_performance

Revision ID: c5556e16d703
Revises: 878495c9450c
Create Date: 2025-09-06 18:25:26.498797

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c5556e16d703'
down_revision: Union[str, None] = '878495c9450c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add HNSW indexes for vector search performance optimization."""

    # Note: These indexes should be created on vector columns in your Supabase tables
    # The exact table and column names depend on your Supabase schema
    # Uncomment and modify the following lines based on your actual vector table structure:

    # Example for a vector_chunks table with embedding column:
    # op.execute("""
    #     CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vector_chunks_embedding_hnsw
    #     ON vector_chunks USING hnsw (embedding vector_cosine_ops)
    #     WITH (m = 16, ef_construction = 64);
    # """)

    # Example for a documents table with embedding column:
    # op.execute("""
    #     CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_embedding_hnsw
    #     ON documents USING hnsw (embedding vector_cosine_ops)
    #     WITH (m = 16, ef_construction = 64);
    # """)

    # Enable pgvector extension if not already enabled
    op.execute("CREATE EXTENSION IF NOT EXISTS vector;")

    # Add performance optimization comments
    op.execute("""
        -- Vector Search Performance Optimizations
        -- HNSW indexes provide fast approximate nearest neighbor search
        -- m = 16: Number of connections per layer (higher = better recall, slower build)
        -- ef_construction = 64: Size of dynamic candidate list during build
        -- For production workloads, consider m = 24-32 and ef_construction = 128-256
        -- Adjust based on your dataset size and performance requirements
    """)


def downgrade() -> None:
    """Remove HNSW indexes for vector search."""

    # Drop the HNSW indexes (uncomment and modify based on your actual indexes):
    # op.execute("DROP INDEX CONCURRENTLY IF EXISTS idx_vector_chunks_embedding_hnsw;")
    # op.execute("DROP INDEX CONCURRENTLY IF EXISTS idx_documents_embedding_hnsw;")

    # Note: Keep the vector extension as it may be used by other parts of the system
    pass
