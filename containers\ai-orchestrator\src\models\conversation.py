# Project: AI Coding Agent
# Purpose: ConversationHistory model for storing user interview responses

"""
SQLAlchemy ConversationHistory model for the Architect Interview System.

Tracks user responses during the interactive project planning interview process.
"""

from __future__ import annotations

from datetime import datetime
from typing import Optional
from enum import Enum

from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    Text,
    ForeignKey,
    Boolean,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from src.models.database import Base


class InterviewState(Enum):
    """Enumeration of possible interview states."""
    GREETING = "greeting"
    ASKING_QUESTIONS = "asking_questions"
    GENERATING_ROADMAP = "generating_roadmap"
    COMPLETE = "complete"
    ERROR = "error"


class InterviewSession(Base):
    """Database model representing an interview session state.

    Attributes:
        id: Primary key.
        project_id: FK to projects.id indicating the project being planned.
        user_id: ID of the user participating in the interview.
        session_id: Unique identifier for the interview session.
        current_state: Current state of the interview.
        current_question_index: Index of the current question being asked.
        total_questions: Total number of questions in the interview.
        is_active: Whether the session is currently active.
        session_metadata: Additional session metadata (JSON).
        created_at: Creation timestamp.
        updated_at: Update timestamp.
    """

    __tablename__ = "interview_sessions"
    __table_args__ = {"extend_existing": True}

    id: int = Column(Integer, primary_key=True, index=True, autoincrement=True)

    project_id: int = Column(
        Integer,
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    user_id: str = Column(String(255), nullable=False, index=True)
    session_id: str = Column(String(255), nullable=False, unique=True, index=True)

    # Interview state management
    current_state: str = Column(String(50), nullable=False, default=InterviewState.GREETING.value)
    current_question_index: int = Column(Integer, nullable=False, default=0)
    total_questions: int = Column(Integer, nullable=False, default=0)
    is_active: bool = Column(Boolean, default=True, nullable=False)

    # Additional metadata
    session_metadata: Optional[str] = Column(Text, nullable=True)  # JSON string

    # Timestamps
    created_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    project = relationship("Project", backref="interview_sessions")

    def __repr__(self) -> str:
        return (
            f"<InterviewSession(id={self.id}, "
            f"session_id={self.session_id}, "
            f"state={self.current_state}, "
            f"question_index={self.current_question_index})>"
        )


class ConversationHistory(Base):
    """Database model representing a user response during an architect interview.

    Attributes:
        id: Primary key.
        project_id: FK to projects.id indicating the project being planned.
        user_id: ID of the user participating in the interview.
        session_id: Unique identifier for the interview session.
        question_key: Identifier for the question being asked (e.g., 'website_type').
        question_text: The actual question text presented to the user.
        user_response: The user's response to the question.
        sequence_order: Order of the question in the interview flow.
        is_followup: Whether this is a dynamically generated follow-up question.
        created_at: Creation timestamp.
        updated_at: Update timestamp.
    """

    __tablename__ = "conversation_history"
    __table_args__ = {"extend_existing": True}

    id: int = Column(Integer, primary_key=True, index=True, autoincrement=True)

    project_id: int = Column(
        Integer,
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    user_id: str = Column(String(255), nullable=False, index=True)

    # Interview session management
    session_id: str = Column(String(255), nullable=False, index=True)
    question_key: str = Column(String(255), nullable=False)
    question_text: str = Column(Text, nullable=False)
    user_response: Optional[str] = Column(Text, nullable=True)

    # Interview flow metadata
    sequence_order: int = Column(Integer, nullable=False)
    is_followup: bool = Column(Boolean, default=False)

    # Timestamps
    created_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    project = relationship("Project", backref="conversations")

    def __repr__(self) -> str:
        return (
            f"<ConversationHistory(id={self.id}, "
            f"project_id={self.project_id}, "
            f"session_id={self.session_id}, "
            f"question_key={self.question_key}, "
            f"user_response={self.user_response[:50] if self.user_response else None}...)>"
        )
