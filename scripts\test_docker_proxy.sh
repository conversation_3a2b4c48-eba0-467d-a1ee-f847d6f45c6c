#!/bin/bash
# Test script for Docker Proxy HAProxy configuration
# Verifies that the custom HAProxy config resolves timeout warnings

set -e

echo "Testing Docker Proxy HAProxy configuration..."
echo ""

# Check if docker-proxy container is running
if ! docker ps | grep -q docker-proxy; then
    echo " Docker Proxy container is not running"
    echo "Start it with: docker compose up docker-proxy"
    exit 1
fi

echo " Docker Proxy container is running"

# Check HAProxy configuration syntax
echo ""
echo "Checking HAProxy configuration syntax..."
if docker exec docker-proxy haproxy -c -f /usr/local/etc/haproxy/haproxy.cfg; then
    echo " HAProxy configuration syntax is valid"
else
    echo " HAProxy configuration has syntax errors"
    exit 1
fi

# Check for timeout warnings in logs
echo ""
echo "Checking for timeout warnings in recent logs..."
if docker logs docker-proxy --tail 50 2>&1 | grep -q "missing timeouts"; then
    echo " Timeout warnings still present in logs"
    echo "Check the HAProxy configuration"
    exit 1
else
    echo " No timeout warnings found in recent logs"
fi

# Test Docker API connectivity through proxy
echo ""
echo "Testing Docker API connectivity through proxy..."
if docker exec docker-proxy curl -s http://localhost:2375/_ping >/dev/null; then
    echo " Docker API proxy is responding"
else
    echo " Docker API proxy is not responding"
    exit 1
fi

echo ""
echo " Docker Proxy configuration test completed successfully!"
echo ""
echo "The following issues have been resolved:"
echo "- Missing timeouts for backend 'docker-events'"
echo "- Server state file configuration"
echo "- HAProxy configuration validation"
