import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { logger } from "@/lib/logger";

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Make request to backend API to get templates
    const backendResponse = await fetch(
      `${process.env.BACKEND_API_URL}/api/projects/templates`,
      {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${session.accessToken}`,
        },
      },
    );

    if (!backendResponse.ok) {
      const error = await backendResponse.text();
      logger.error("Backend templates fetch failed", { error });
      return NextResponse.json({ error: "Failed to fetch templates" }, {
        status: backendResponse.status,
      });
    }

    const templates = await backendResponse.json();
    return NextResponse.json(templates);
  } catch (error) {
    logger.error("Templates fetch error", { error });
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
