# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: Pydantic models for LLM service validation and serialization

from typing import List, Optional, Literal
from pydantic import BaseModel, Field, field_validator
from enum import Enum
import time


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OLLAMA = "ollama"
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"


class ModelStatus(str, Enum):
    """Model availability status."""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    PULLING = "pulling"
    ERROR = "error"


class LLMModel(BaseModel):
    """Model information schema."""
    name: str = Field(..., description="Model name")
    provider: LLMProvider = Field(..., description="Provider name")
    status: ModelStatus = Field(default=ModelStatus.AVAILABLE, description="Model status")
    size: Optional[int] = Field(default=None, description="Model size in bytes")
    modified_at: Optional[str] = Field(default=None, description="Last modified timestamp")
    parameters: Optional[str] = Field(default=None, description="Model parameters (e.g., 7B, 13B)")
    context_length: Optional[int] = Field(default=None, description="Maximum context length")
    cost_per_token: Optional[float] = Field(default=None, description="Cost per token for cloud models")


class GenerateRequest(BaseModel):
    """Request model for text generation."""
    prompt: str = Field(..., min_length=1, max_length=50000, description="Input prompt")
    model: Optional[str] = Field(default=None, description="Model name to use")
    provider: Optional[LLMProvider] = Field(default=None, description="Provider to use")
    system_prompt: Optional[str] = Field(default=None, max_length=10000, description="System prompt")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Generation temperature")
    max_tokens: Optional[int] = Field(default=None, ge=1, le=4096, description="Maximum tokens to generate")
    top_p: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Nucleus sampling parameter")
    frequency_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: Optional[float] = Field(default=None, ge=-2.0, le=2.0, description="Presence penalty")
    stream: bool = Field(default=False, description="Enable streaming response")

    @field_validator('prompt')
    @classmethod
    def prompt_must_not_be_empty(cls, v):
        """Validate prompt is not empty or whitespace only."""
        if not v or not v.strip():
            raise ValueError('Prompt cannot be empty')
        return v.strip()


class UsageStats(BaseModel):
    """Usage statistics for LLM requests."""
    prompt_tokens: int = Field(default=0, description="Number of prompt tokens")
    completion_tokens: int = Field(default=0, description="Number of completion tokens")
    total_tokens: int = Field(default=0, description="Total tokens used")
    cost: Optional[float] = Field(default=None, description="Cost in USD")
    duration_ms: Optional[int] = Field(default=None, description="Request duration in milliseconds")

    # Ollama-specific fields
    prompt_eval_count: Optional[int] = Field(default=None, description="Ollama prompt evaluation count")
    eval_count: Optional[int] = Field(default=None, description="Ollama evaluation count")
    total_duration: Optional[int] = Field(default=None, description="Ollama total duration in nanoseconds")


class LLMResponse(BaseModel):
    """Response model for LLM generation."""
    content: str = Field(..., description="Generated content")
    model: str = Field(..., description="Model used for generation")
    provider: LLMProvider = Field(..., description="Provider used")
    usage: UsageStats = Field(default_factory=UsageStats, description="Usage statistics")
    created_at: float = Field(default_factory=time.time, description="Response timestamp")
    request_id: Optional[str] = Field(default=None, description="Request ID for tracking")
    finish_reason: Optional[str] = Field(default=None, description="Why generation stopped")


class ProviderStatus(BaseModel):
    """Status of a provider."""
    provider: LLMProvider
    available: bool
    error_message: Optional[str] = None
    api_key_configured: bool = False
    last_checked: float = Field(default_factory=time.time)
    response_time_ms: Optional[int] = None


class ModelPullRequest(BaseModel):
    """Request to pull a model."""
    model_name: str = Field(..., min_length=1, description="Name of model to pull")
    provider: LLMProvider = Field(default=LLMProvider.OLLAMA, description="Provider to pull from")

    @field_validator('model_name')
    @classmethod
    def model_name_must_be_valid(cls, v):
        """Validate model name format."""
        if not v or not v.strip():
            raise ValueError('Model name cannot be empty')
        # Basic validation for model name format
        if len(v.strip()) < 2:
            raise ValueError('Model name too short')
        return v.strip()


class ModelPullResponse(BaseModel):
    """Response for model pull operation."""
    success: bool
    message: str
    model_name: str
    provider: LLMProvider


class HealthCheckResponse(BaseModel):
    """Health check response."""
    status: Literal["healthy", "unhealthy", "degraded"] = "healthy"
    providers: List[ProviderStatus] = Field(default_factory=list)
    total_requests: int = 0
    failed_requests: int = 0
    average_response_time_ms: Optional[float] = None


class RateLimitInfo(BaseModel):
    """Rate limiting information."""
    requests_per_minute: int
    requests_remaining: int
    reset_time: float
    cost_limit_usd: Optional[float] = None
    cost_used_usd: Optional[float] = None


class LLMError(Exception):
    """Base exception for LLM service errors."""

    def __init__(self, message: str, provider: Optional[str] = None, error_code: Optional[str] = None):
        self.message = message
        self.provider = provider
        self.error_code = error_code
        super().__init__(self.message)


class ProviderUnavailableError(LLMError):
    """Raised when a provider is unavailable."""
    pass


class InvalidAPIKeyError(LLMError):
    """Raised when API key is invalid or missing."""
    pass


class RateLimitExceededError(LLMError):
    """Raised when rate limit is exceeded."""
    pass


class ModelNotFoundError(LLMError):
    """Raised when requested model is not found."""
    pass


class GenerationError(LLMError):
    """Raised when text generation fails."""
    pass