"""
SQLAlchemy Task model for the Sequential Agent Framework.

Tracks unit-of-work items for the agent system on a per-project basis.
"""

from __future__ import annotations

from datetime import datetime
from typing import Any, Optional

from sqlalchemy import (
    Column,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from src.models.custom_types import JsonVariant
from src.models.database import Base


class Task(Base):
    """Database model representing a queued or executing task.

    Attributes:
        id: Primary key.
        project_id: FK to projects.id indicating the owning project.
        status: Execution status ('pending', 'running', 'completed', 'failed').
        agent_role: Role of the agent intended to execute this task (e.g., 'backend', 'frontend').
        input_data: Arbitrary input/prompt payload for the task.
        output_data: Arbitrary output/result payload from task execution.
        created_at: Creation timestamp.
        updated_at: Update timestamp.
    """

    __tablename__ = "tasks"

    id: int = Column(Integer, primary_key=True, index=True, autoincrement=True)

    project_id: int = Column(
        <PERSON>te<PERSON>,
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    status: str = Column(String(32), nullable=False, index=True, default="pending")
    agent_role: str = Column(String(64), nullable=False, index=True)

    # Use database-agnostic JSON type
    input_data: Optional[Any] = Column(JsonVariant, nullable=True)
    output_data: Optional[Any] = Column(JsonVariant, nullable=True)

    created_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    project = relationship("Project", backref="tasks", lazy="selectin")

    def __repr__(self) -> str:
        return f"<Task id={self.id} project_id={self.project_id} status={self.status} role={self.agent_role}>"
