# Template-Based Provisioning System

## Overview

The AI Coding Agent now implements a **template-based, verifiable provisioning system** that ensures maximum reliability and zero errors when setting up user project environments. This system replaces ad-hoc project generation with a structured, tested approach.

## Architecture

### Core Components

1. **Template System** (`/templates/webapp/`)
   - Pre-configured, production-ready templates
   - Placeholder-based customization (`__PROJECT_NAME__`)
   - Comprehensive file coverage (Docker, FastAPI, database, docs)

2. **Enhanced ProjectRepository** 
   - Template-aware project creation
   - Automated file copying and placeholder replacement
   - Integrated error handling and cleanup

3. **ArchitectAgent Provisioning Logic**
   - Automatic new project detection
   - Docker container orchestration via ShellAgent
   - Comprehensive health verification
   - Error recovery with IssueFixAgent integration

## Template Structure

```
templates/webapp/
├── Dockerfile.template              # Multi-stage production container
├── docker-compose.yml.template      # Complete orchestration setup
├── .env.example.template           # Environment configuration
├── src/main.py.template            # FastAPI app with /health endpoint
├── requirements.txt.template       # Production dependencies
├── init-db.sql.template           # Database initialization
└── README.md.template             # Complete documentation
```

### Template Features

- **Security-First**: Non-root containers, minimal base images, health checks
- **Production-Ready**: Multi-stage builds, proper logging, error handling
- **Comprehensive**: Database, Redis, API, documentation all included
- **Customizable**: Placeholder system allows easy project-specific configuration

## Provisioning Workflow

### Phase 1: Project Creation
```python
# Enhanced ProjectRepository.create_project()
1. Validate project name
2. Create project directory
3. Copy template files from /templates/webapp/
4. Replace __PROJECT_NAME__ placeholders
5. Create database record
6. Link user as project owner
```

### Phase 2: Container Provisioning
```python
# ArchitectAgent provisioning phase
1. Detect new project roadmap
2. Delegate to ShellAgent: docker-compose up -d --build
3. Monitor container health via docker-compose ps
4. Test /health endpoint availability
5. Proceed with roadmap OR dispatch to IssueFixAgent
```

### Phase 3: Verification & Validation
```python
# Comprehensive health checks
1. Container status verification (running/healthy)
2. Service connectivity tests
3. Application endpoint validation
4. Timeout handling with detailed logging
5. Automatic error recovery workflows
```

## Usage Examples

### Creating a New Project

```python
from src.repository.project_repository import ProjectRepository

repo = ProjectRepository()
project = await repo.create_project(
    db=db_session,
    user=current_user,
    project_name="my-awesome-app",
    description="E-commerce platform"
)
# Result: Fully provisioned project with all template files
```

### ArchitectAgent Integration

```python
# Roadmap with new project detection
roadmap = Roadmap(
    title="Create new e-commerce platform",
    metadata={"is_new_project": True}
)

architect = ArchitectAgent()
success = await architect.execute_roadmap_with_strict_validation(roadmap, user_id)
# Result: Complete provisioning + roadmap execution
```

## Template Customization

### Adding New Templates

1. Create new template directory: `/templates/{template_name}/`
2. Add template files with `.template` extension
3. Use `__PROJECT_NAME__` placeholder for customization
4. Update `ProjectRepository._provision_project_from_template()` if needed

### Placeholder System

- **Primary Placeholder**: `__PROJECT_NAME__` (replaced with actual project name)
- **Future Extensions**: `__USER_NAME__`, `__PROJECT_TYPE__`, etc.
- **Replacement Logic**: Automatic during template copying process

## Health Check System

### Container Health Verification

```bash
# ArchitectAgent runs via ShellAgent
docker-compose ps --format json

# Parses output for:
# - Container state: "running"
# - Health status: "healthy" (if health checks exist)
# - Service availability
```

### Application Health Endpoint

```python
# Template includes /health endpoint
GET http://localhost:8000/health

# Expected response:
{
    "status": "healthy",
    "timestamp": "2024-01-01T12:00:00Z",
    "service": "project-name",
    "version": "1.0.0",
    "checks": {
        "application": "ok"
    }
}
```

## Error Recovery

### Provisioning Failure Handling

1. **Container Issues**: IssueFixAgent receives Docker logs
2. **Health Check Failures**: Detailed error context provided
3. **Timeout Scenarios**: Graceful degradation with user notification
4. **Cleanup**: Automatic filesystem cleanup on database failures

### Monitoring & Logging

```python
# Structured logging throughout provisioning
logger.info("Starting project provisioning phase")
logger.info("Docker containers started successfully")
logger.info("Provisioning verification successful")

# Error context for troubleshooting
logger.error("Provisioning failed", extra={
    "project_name": project_name,
    "user_id": user_id,
    "error_type": "container_health_check_failed",
    "logs": docker_logs
})
```

## Testing & Validation

### Automated Testing

```bash
# Run template validation
python scripts/validate-template-provisioning.py

# Run unit tests
pytest tests/test_template_provisioning.py -v
```

### Manual Validation

```bash
# 1. Create test project
curl -X POST /api/projects \
  -H "Content-Type: application/json" \
  -d '{"name": "test-project", "description": "Test"}'

# 2. Verify containers
docker-compose -f workspace/users/{user_id}/test-project/docker-compose.yml ps

# 3. Test health endpoint
curl http://localhost:8000/health
```

## Security Considerations

### Template Security

- **Non-root containers**: All services run as non-privileged users
- **Minimal base images**: Python slim, Alpine Linux variants
- **Secret management**: Environment variable based configuration
- **Network isolation**: Custom Docker networks per project

### Runtime Security

- **Input validation**: Project names sanitized for filesystem safety
- **Path traversal protection**: No `..` or `/` allowed in project names
- **Resource limits**: Container CPU/memory constraints
- **Audit logging**: All provisioning actions logged

## Performance Optimization

### Template Caching

- Templates loaded once and cached in memory
- Placeholder replacement optimized for large files
- Parallel file processing where possible

### Container Optimization

- **Multi-stage builds**: Smaller production images
- **Layer caching**: Optimized Dockerfile layer ordering
- **Health check tuning**: Appropriate intervals and timeouts

## Troubleshooting

### Common Issues

1. **Template Not Found**
   ```
   Error: Template directory not found: /templates/webapp
   Solution: Ensure templates directory exists in project root
   ```

2. **Placeholder Not Replaced**
   ```
   Error: __PROJECT_NAME__ still present in generated files
   Solution: Check _copy_template_files() method implementation
   ```

3. **Container Health Check Failed**
   ```
   Error: Containers not healthy after 300 seconds
   Solution: Check docker-compose logs for service issues
   ```

4. **Health Endpoint Unreachable**
   ```
   Error: Failed to reach health endpoint
   Solution: Verify port mapping and service startup
   ```

### Debug Commands

```bash
# Check template structure
ls -la templates/webapp/

# Verify placeholder replacement
grep -r "__PROJECT_NAME__" workspace/users/{user_id}/{project_name}/

# Monitor container health
docker-compose -f {project_path}/docker-compose.yml logs -f

# Test health endpoint manually
curl -v http://localhost:8000/health
```

## Migration Guide

### From Manual Project Creation

1. **Backup existing projects**: Ensure all user projects are backed up
2. **Update API endpoints**: Modify project creation endpoints to use new system
3. **Test thoroughly**: Validate with test projects before production deployment
4. **Monitor rollout**: Watch for provisioning failures and address quickly

### Template Updates

1. **Version templates**: Use git tags for template versions
2. **Backward compatibility**: Ensure new templates work with existing projects
3. **Gradual rollout**: Test template changes with subset of users first
4. **Rollback plan**: Keep previous template versions available

## Future Enhancements

### Planned Features

- **Multiple template types**: React, Vue, Django, etc.
- **Template marketplace**: Community-contributed templates
- **Advanced placeholders**: User preferences, project type specific
- **Template versioning**: Semantic versioning for templates
- **Custom template creation**: User-defined project templates

### Integration Opportunities

- **CI/CD integration**: Automatic deployment pipeline setup
- **Monitoring integration**: Automatic observability stack provisioning
- **Security scanning**: Automated vulnerability assessment
- **Performance testing**: Load testing setup in templates

## Conclusion

The template-based provisioning system provides a robust, reliable foundation for AI Coding Agent project creation. By standardizing on proven templates and comprehensive verification, we ensure every user gets a production-ready environment that works consistently.

This system dramatically reduces provisioning failures, improves user experience, and provides a solid foundation for future enhancements to the AI Coding Agent platform.
