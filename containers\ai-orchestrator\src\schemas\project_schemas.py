# Project: AI Coding Agent
# Purpose: Project-related Pydantic schemas for data validation and serialization

from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Optional, List, Dict, Any
from enum import Enum
import re


class ExportFormat(str, Enum):
    """Supported export formats."""
    ZIP = "zip"
    TAR_GZ = "tar.gz"


class ExportStatus(str, Enum):
    """Export status values."""
    INITIATED = "initiated"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"


class ProjectExportRequest(BaseModel):
    """Project export request model."""
    include_database: bool = Field(
        default=True,
        description="Include database export in the archive"
    )
    include_files: bool = Field(
        default=True,
        description="Include project files in the archive"
    )
    export_format: ExportFormat = Field(
        default=ExportFormat.ZIP,
        description="Export archive format"
    )

    @field_validator('include_database', 'include_files')
    @classmethod
    def validate_at_least_one_included(cls, v, info):
        """Ensure at least one export type is selected."""
        if not v and not info.data.get('include_database'):
            raise ValueError('At least one of include_database or include_files must be True')
        return v


class ProjectExportResponse(BaseModel):
    """Project export response model."""
    export_id: str = Field(..., description="Unique export identifier")
    status: ExportStatus = Field(..., description="Current export status")
    message: str = Field(..., description="Status message")
    download_url: Optional[str] = Field(None, description="Download URL when ready")
    file_size: Optional[int] = Field(None, description="Archive file size in bytes")
    filename: Optional[str] = Field(None, description="Archive filename")
    created_at: str = Field(..., description="Export creation timestamp")
    completed_at: Optional[str] = Field(None, description="Export completion timestamp")
    expires_at: Optional[str] = Field(None, description="Export expiration timestamp")
    error_message: Optional[str] = Field(None, description="Error message if failed")

    model_config = ConfigDict(use_enum_values=True)


class ProjectExportStatusResponse(BaseModel):
    """Detailed export status response."""
    export_id: str
    project_id: str
    project_name: str
    status: ExportStatus
    progress_percentage: Optional[int] = Field(None, ge=0, le=100)
    current_step: Optional[str] = None
    steps_completed: List[str] = Field(default_factory=list)
    steps_remaining: List[str] = Field(default_factory=list)
    export_format: ExportFormat
    include_database: bool
    include_files: bool
    file_size: Optional[int] = None
    filename: Optional[str] = None
    download_url: Optional[str] = None
    created_at: str
    completed_at: Optional[str] = None
    expires_at: Optional[str] = None
    error_message: Optional[str] = None

    model_config = ConfigDict(use_enum_values=True)


class ProjectExportMetadata(BaseModel):
    """Export metadata for internal tracking."""
    export_id: str
    project_id: str
    user_id: str
    project_name: str
    export_format: ExportFormat
    include_database: bool
    include_files: bool
    staging_dir: Optional[str] = None
    archive_path: Optional[str] = None
    task_ids: List[str] = Field(default_factory=list)

    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class ExportTaskInput(BaseModel):
    """Input data for export tasks."""
    command_type: str = Field(..., description="Type of export command")
    export_id: str = Field(..., description="Export identifier")
    project_id: str = Field(..., description="Project identifier")
    user_id: str = Field(..., description="User identifier")
    project_name: str = Field(..., description="Project name")
    export_format: Optional[ExportFormat] = None
    staging_dir: Optional[str] = None
    dependencies: List[str] = Field(default_factory=list)

    model_config = ConfigDict(use_enum_values=True)


class ExportTaskResult(BaseModel):
    """Result from export task execution."""
    success: bool
    message: str
    export_id: str
    command_type: str
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ProjectListResponse(BaseModel):
    """Response for listing user projects."""
    projects: List[Dict[str, Any]]
    total_count: int
    page: int = 1
    page_size: int = 50


class ProjectCreateRequest(BaseModel):
    """Request for creating a new project."""
    name: str = Field(..., min_length=1, max_length=100, description="Project name")
    description: Optional[str] = Field(None, max_length=500, description="Project description")
    template_name: Optional[str] = Field(None, description="Name of the template to use for the project")
    project_type: Optional[str] = Field(None, description="Type of project")
    is_public: bool = Field(default=False, description="Whether project is public")
    tags: List[str] = Field(default_factory=list, description="Project tags")

    @field_validator('name')
    @classmethod
    def validate_project_name(cls, v: str) -> str:
        """Validate project name for filesystem safety."""
        if '..' in v or '/' in v or '\\' in v:
            raise ValueError('Project name cannot contain path separators or relative paths')
        return v.strip()


class ProjectResponse(BaseModel):
    """Response for project operations."""
    id: str
    name: str
    description: Optional[str]
    owner_id: str
    status: str
    project_type: Optional[str]
    is_public: bool
    tags: List[str] = Field(default_factory=list)
    created_at: str
    updated_at: str


class ProjectUpdateRequest(BaseModel):
    """Request for updating project details."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    project_type: Optional[str] = None
    is_public: Optional[bool] = None
    tags: Optional[List[str]] = None

    @field_validator('name')
    @classmethod
    def validate_project_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate project name for filesystem safety."""
        if v is not None:
            if '..' in v or '/' in v or '\\' in v:
                raise ValueError('Project name cannot contain path separators or relative paths')
            return v.strip()
        return v


class FileData(BaseModel):
    """File data for project operations."""
    name: str = Field(..., description="File name")
    path: str = Field(..., description="File path within project")
    content: str = Field(..., description="File content")
    type: Optional[str] = Field(None, description="File type/extension")

    @field_validator('name', 'path')
    @classmethod
    def validate_file_paths(cls, v: str) -> str:
        """Validate file paths for security."""
        if '..' in v or v.startswith('/'):
            raise ValueError('Invalid file path: path traversal not allowed')
        return v


class ProjectUploadResponse(BaseModel):
    """Response for project file upload."""
    message: str
    files_uploaded: int
    project_path: str
    total_size: int


class GitCloneRequest(BaseModel):
    """Request for Git repository cloning."""
    repository_url: str = Field(..., description="Git repository URL")
    target_directory: Optional[str] = Field(None, description="Target directory name")
    branch: Optional[str] = Field(None, description="Branch to checkout")

    @field_validator('repository_url')
    @classmethod
    def validate_repository_url(cls, v: str) -> str:
        """Basic validation for repository URL."""
        if not (v.startswith('https://') or v.startswith('git@')):
            raise ValueError('Repository URL must use HTTPS or SSH protocol')
        return v


class GitCloneResponse(BaseModel):
    """Response for Git repository cloning."""
    message: str
    path: str
    repository_url: str


class CustomDomainRequest(BaseModel):
    """Request for adding a custom domain to a project."""
    custom_domain: str = Field(..., description="The custom domain to associate with the project.")

    @field_validator('custom_domain')
    @classmethod
    def validate_custom_domain(cls, v: str) -> str:
        """
        Validate the custom domain to ensure it is a valid hostname.
        A simple regex is used here for basic validation.
        """
        if not re.match(r"^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}$", v):
            raise ValueError("Invalid custom domain format.")
        return v


class ApproveChangesRequest(BaseModel):
    """Request model for approving changes."""
    approval_token: str = Field(..., description="The unique token for approving a change.")


class LaunchProjectResponse(BaseModel):
    """Response for launching a project environment."""
    environment_url: str = Field(..., description="The URL of the launched project environment.")


class ProjectEnvironment(BaseModel):
    """Represents a single project environment."""
    project_id: str = Field(..., description="The ID of the project.")
    environment_url: str = Field(..., description="The URL of the project environment.")


class ProjectEnvironmentListResponse(BaseModel):
    """Response for listing active project environments."""
    environments: List[ProjectEnvironment]
