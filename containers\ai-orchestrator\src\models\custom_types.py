import uuid
from sqlalchemy import TypeDecorator, CHAR, TEXT
from sqlalchemy.dialects.postgresql import UUID, JSON
import json


class UuidVariant(TypeDecorator):
    """
    Custom UUID type for cross-database compatibility.
    It uses PostgreSQL's UUID type, but falls back to a CHAR(32) for other
    databases. This allows for efficient storage on PostgreSQL while
    maintaining compatibility with databases like SQLite for testing.
    """
    impl = CHAR
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(UUID())
        else:
            return dialect.type_descriptor(CHAR(32))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return "%.32x" % uuid.UUID(value).int
            else:
                # hexstring
                return "%.32x" % value.int

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                value = uuid.UUID(value)
            return value


class JsonVariant(TypeDecorator):
    """
    Custom JSON type for cross-database compatibility.
    It uses PostgreSQL's JSON type, but falls back to a TEXT field for other
    databases.
    """
    impl = TEXT

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(JSON())
        else:
            return dialect.type_descriptor(TEXT())

    def process_bind_param(self, value, dialect):
        if value is None:
            return value

        if dialect.name == 'postgresql':
            return value
        else:
            return json.dumps(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value

        if dialect.name == 'postgresql':
            return value
        else:
            return json.loads(value)
