import os

from dotenv import load_dotenv


def main():
    """Validate configuration by loading environment variables and instantiating settings."""
    print("Attempting to load environment variables from .env file...")
    dotenv_path = os.path.join(os.path.dirname(__file__), "..", "..", "..", ".env")
    if os.path.exists(dotenv_path):
        load_dotenv(dotenv_path=dotenv_path, override=True)
        print(f"Loaded environment variables from: {os.path.abspath(dotenv_path)}")
    else:
        print(f"Root .env file not found at {os.path.abspath(dotenv_path)}")

    portal_dotenv_path = os.path.join(os.path.dirname(__file__), "..", "..", "user-portal", ".env")
    if os.path.exists(portal_dotenv_path):
        load_dotenv(dotenv_path=portal_dotenv_path, override=False)
        print(f"Loaded environment variables from: {os.path.abspath(portal_dotenv_path)}")
    else:
        print(f"User-portal .env file not found at {os.path.abspath(portal_dotenv_path)}")

    print("\nAttempting to import and instantiate settings...")
    try:
        from src.core.config import settings

        print("Successfully imported 'settings' from 'src.core.config'")

        print("--- Configuration Validation ---")
        # Use flat settings attributes
        print(f"Deployment Environment: {settings.ENVIRONMENT}")
        db_url = getattr(settings, "DATABASE_URL", None)
        print(f"Database URL: {str(db_url)[:25]}... (sensitive value masked)")
        print(f"Redis URL: {getattr(settings, 'REDIS_URL', None)}")
        print(f"Default Local LLM Provider: {getattr(settings, 'DEFAULT_LOCAL_PROVIDER', None)}")
        print(f"Supabase URL: {getattr(settings, 'SUPABASE_URL', None)}")
        print(f"JWT Secret Loaded: {'Yes' if getattr(settings, 'JWT_SECRET', None) else 'No'}")

        # Basic assertions adapted for flat settings
        assert getattr(settings, "DEFAULT_LOCAL_PROVIDER", None) in ("ollama",)
        db_url = str(getattr(settings, "DATABASE_URL", ""))
        assert db_url != ""
        assert getattr(settings, "JWT_SECRET", None) is not None

        print("\nConfiguration loaded and validated successfully!")

    except ImportError as e:
        print(f"Error: Failed to import settings. Check PYTHONPATH. Details: {e}")
    except Exception as e:
        print(f"Error: An unexpected error occurred during settings validation. Details: {e}")


if __name__ == "__main__":
    # For direct execution, ensure src is in the python path
    import sys

    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
    main()
