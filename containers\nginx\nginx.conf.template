# Optimized Nginx Configuration Template for Reverse Proxy
# Supports environment variables and dynamic configuration
# Use with: envsubst < nginx.conf.template > nginx.conf

user nginx;
worker_processes ${NGINX_WORKER_PROCESSES:-auto};
error_log /var/log/nginx/error.log ${NGINX_ERROR_LOG_LEVEL:-warn};
pid /var/run/nginx.pid;

# Worker configuration optimized for containers
worker_rlimit_nofile 65535;

events {
    worker_connections ${NGINX_WORKER_CONNECTIONS:-4096};
    use epoll;
    multi_accept on;
    accept_mutex off;
}

http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Character encoding
    charset utf-8;

    # Logging format with structured logging
    log_format main escape=json
        '{'
            '"time":"$time_iso8601",'
            '"remote_addr":"$remote_addr",'
            '"remote_user":"$remote_user",'
            '"request":"$request",'
            '"status":"$status",'
            '"body_bytes_sent":"$body_bytes_sent",'
            '"http_referer":"$http_referer",'
            '"http_user_agent":"$http_user_agent",'
            '"http_x_forwarded_for":"$http_x_forwarded_for",'
            '"request_time":"$request_time",'
            '"upstream_connect_time":"$upstream_connect_time",'
            '"upstream_header_time":"$upstream_header_time",'
            '"upstream_response_time":"$upstream_response_time"'
        '}';

    # Access log
    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout ${NGINX_KEEPALIVE_TIMEOUT:-65};
    keepalive_requests 1000;
    types_hash_max_size 2048;
    client_max_body_size ${NGINX_CLIENT_MAX_BODY_SIZE:-64M};
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;

    # Hide nginx version for security
    server_tokens off;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        application/atom+xml
        application/geo+json
        application/javascript
        application/x-javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rdf+xml
        application/rss+xml
        application/xhtml+xml
        application/xml
        font/eot
        font/otf
        font/ttf
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:;" always;

    # Rate limiting zones
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=30r/m;
    limit_req_zone $binary_remote_addr zone=login_limit:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=general_limit:10m rate=60r/m;

    # Upstream servers with environment variable support
    upstream ai_orchestrator_backend {
        least_conn;
        server ${AI_ORCHESTRATOR_HOST:-ai-orchestrator}:${AI_ORCHESTRATOR_PORT:-8000} max_fails=3 fail_timeout=30s;
        keepalive 32;
        keepalive_requests 1000;
        keepalive_timeout 60s;
    }

    upstream user_portal_backend {
        least_conn;
        server ${USER_PORTAL_HOST:-user-portal}:${USER_PORTAL_PORT:-3000} max_fails=3 fail_timeout=30s;
        keepalive 32;
        keepalive_requests 1000;
        keepalive_timeout 60s;
    }

    upstream hosting_server_backend {
        least_conn;
        server ${HOSTING_SERVER_HOST:-hosting-server}:${HOSTING_SERVER_PORT:-80} max_fails=3 fail_timeout=30s;
        keepalive 16;
        keepalive_requests 100;
        keepalive_timeout 30s;
    }

    # Default server (catch-all)
    server {
        listen 80 default_server;
        listen 443 ssl default_server;
        server_name _;

        # SSL configuration (if certificates are available)
        ssl_certificate ${SSL_CERT_PATH:-/etc/nginx/ssl/cert.pem};
        ssl_certificate_key ${SSL_KEY_PATH:-/etc/nginx/ssl/key.pem};
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        return 444;  # Close connection without response
    }

    # HTTP to HTTPS redirect (only if SSL is enabled)
    server {
        listen 80;
        server_name ${API_DOMAIN:-api.localhost} ${ADMIN_DOMAIN:-admin.localhost} ${PROJECTS_DOMAIN:-*.localhost};

        # Let's Encrypt ACME challenge
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
            try_files $uri =404;
        }

        # Redirect all other HTTP requests to HTTPS (if SSL enabled)
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    # API Server (ai-orchestrator)
    server {
        listen 443 ssl http2;
        server_name ${API_DOMAIN:-api.localhost};

        # SSL configuration
        ssl_certificate ${API_SSL_CERT_PATH:-/etc/nginx/ssl/api_cert.pem};
        ssl_certificate_key ${API_SSL_KEY_PATH:-/etc/nginx/ssl/api_key.pem};
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # HSTS
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # API-specific security headers
        add_header X-API-Version "1.0" always;
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;

        # Handle preflight requests
        location / {
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*" always;
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
                add_header Access-Control-Allow-Credentials "true" always;
                add_header Access-Control-Max-Age "1728000" always;
                add_header Content-Length "0" always;
                add_header Content-Type "text/plain charset=UTF-8" always;
                return 204;
            }

            # Rate limiting for API endpoints
            limit_req zone=api_limit burst=10 nodelay;

            # Proxy to ai-orchestrator
            proxy_pass http://ai_orchestrator_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_cache_bypass $http_upgrade;

            # Timeout settings
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;

            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;
        }

        # Health check endpoint (no rate limiting)
        location /health {
            proxy_pass http://ai_orchestrator_backend/health;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            access_log off;
        }

        # Metrics endpoint (restricted access)
        location /metrics {
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;

            proxy_pass http://ai_orchestrator_backend/metrics;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
        }
    }

    # User Portal (admin interface)
    server {
        listen 443 ssl http2;
        server_name ${ADMIN_DOMAIN:-admin.localhost};

        # SSL configuration
        ssl_certificate ${ADMIN_SSL_CERT_PATH:-/etc/nginx/ssl/admin_cert.pem};
        ssl_certificate_key ${ADMIN_SSL_KEY_PATH:-/etc/nginx/ssl/admin_key.pem};
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # HSTS
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Root location
        location / {
            limit_req zone=general_limit burst=20 nodelay;

            proxy_pass http://user_portal_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name;
            proxy_cache_bypass $http_upgrade;

            # Timeout settings
            proxy_connect_timeout 5s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;

            # Next.js specific settings
            proxy_buffering on;
            proxy_buffer_size 64k;
            proxy_buffers 8 64k;
            proxy_busy_buffers_size 64k;
        }

        # Static assets caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://user_portal_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;

            # Cache static assets
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Cache-Status "STATIC";

            # Gzip compression for static assets
            gzip_static on;
        }

        # API routes
        location /api/ {
            limit_req zone=api_limit burst=15 nodelay;

            proxy_pass http://user_portal_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # No caching for API routes
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Health check endpoint
        location /api/health {
            proxy_pass http://user_portal_backend/api/health;
            proxy_http_version 1.1;
            proxy_set_header Host $host;

            access_log off;
        }
    }

    # Dynamic Project Hosting
    server {
        listen 443 ssl http2;
        server_name ${PROJECTS_DOMAIN:-*.localhost};

        # SSL configuration
        ssl_certificate ${PROJECTS_SSL_CERT_PATH:-/etc/nginx/ssl/projects_cert.pem};
        ssl_certificate_key ${PROJECTS_SSL_KEY_PATH:-/etc/nginx/ssl/projects_key.pem};
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # Security headers for user projects
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # Proxy to hosting server
        location / {
            limit_req zone=general_limit burst=30 nodelay;

            proxy_pass http://hosting_server_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $server_name;

            # Timeout settings
            proxy_connect_timeout 5s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;

            # Buffer settings for file serving
            proxy_buffering on;
            proxy_buffer_size 64k;
            proxy_buffers 8 64k;
            proxy_busy_buffers_size 64k;
        }
    }

    # Development-only server (when not using custom domains)
    server {
        listen 80;
        server_name localhost 127.0.0.1;

        # API endpoints
        location /api/ {
            limit_req zone=api_limit burst=10 nodelay;

            proxy_pass http://ai_orchestrator_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # User portal
        location / {
            limit_req zone=general_limit burst=20 nodelay;

            proxy_pass http://user_portal_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
    }
}
