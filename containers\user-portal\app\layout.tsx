import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { AuthSessionProvider } from '../src/components/AuthSessionProvider'
// Temporarily disabled due to compatibility issues
// import { TwentyFirstToolbar } from '@21st-extension/toolbar-next'
// import { ReactPlugin } from '@21st-extension/react'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'AI Coding Agent - User Portal',
  description: 'Manage your projects and embeddings',
  other: { charset: 'utf-8' },
  icons: { icon: '/favicon.ico' },
};

export const viewport = { width: 'device-width', initialScale: 1 };

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthSessionProvider>
          {children}
        </AuthSessionProvider>
        {/* Temporarily disabled due to compatibility issues */}
        {/* <TwentyFirstToolbar config={{ plugins: [ReactPlugin] }} /> */}
      </body>
    </html>
  )
}
