#!/bin/bash
# Pre-commit hook to prevent Unicode characters in code files
# Place this file in .git/hooks/pre-commit and make it executable

echo "[INFO] Checking for Unicode characters in code files..."

# Get list of staged files that are code files
staged_files=$(git diff --cached --name-only | grep -E '\.(py|ps1|yml|yaml|sh|js|ts|dockerfile)$')

if [ -z "$staged_files" ]; then
    echo "[OK] No code files to check"
    exit 0
fi

# Check each staged file for Unicode characters
unicode_found=false

for file in $staged_files; do
    if [ -f "$file" ]; then
        # Check if file contains non-ASCII characters
        if grep -q '[^\x00-\x7F]' "$file"; then
            echo "[ERROR] Unicode characters found in: $file"
            # Show the specific lines with Unicode
            grep -n '[^\x00-\x7F]' "$file" | head -5
            unicode_found=true
        fi
    fi
done

if $unicode_found; then
    echo ""
    echo "[FAIL] Commit blocked: Unicode characters detected in code files!"
    echo ""
    echo "Please replace Unicode characters with ASCII alternatives:"
    echo "   → [OK]      → [FAIL]     → [WARN]"
    echo "   → [LOCK]    → [BLOCK]    → [SUCCESS]"
    echo ""
    echo "See docs/CHARACTER_ENCODING_GUIDELINES.md for complete list"
    exit 1
else
    echo "[OK] No Unicode characters found in code files"
    exit 0
fi
