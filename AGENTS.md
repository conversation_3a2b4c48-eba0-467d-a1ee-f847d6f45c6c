# AI Coding Agent Architecture Guide

## Overview

The AI Coding Agent is a comprehensive multi-agent system that transforms natural language descriptions into production-ready web applications. This guide provides the architectural context needed to understand and work with the system effectively.

## Core Components

### Agent Layer

- **ArchitectAgent**: Project planning and task orchestration
  - Conducts user interviews to understand requirements
  - Generates hierarchical roadmaps (Phases → Steps → Tasks)
  - Acts as central coordinator for all other agents
- **BackendAgent**: API development and database operations
  - Creates FastAPI endpoints and business logic
  - Manages database schemas and migrations
  - Implements authentication and security features
- **FrontendAgent**: UI/UX development
  - Generates React components with Tailwind CSS
  - Implements responsive design patterns
  - Integrates with backend APIs
- **ShellAgent**: System operations and file management
  - Executes file system operations
  - Manages package installations and dependencies
  - Runs build scripts and deployment commands
- **IssueFixAgent**: Error diagnosis and resolution
  - Analyzes error logs and stack traces
  - Implements automated fixes for common issues
  - Provides debugging recommendations

### Service Layer

- **RAGService**: Intelligent context retrieval
  - Unified knowledge system with Supabase pgvector
  - Public and private knowledge stored in Supabase with RLS policies
  - Single vector storage solution for all semantic search needs
- **MemoryManagementService**: Comprehensive memory system for AI learning
  - Persistent storage of user preferences, code patterns, and learning outcomes
  - Intelligent context retrieval for code generation tasks
  - Learning loops that improve agent responses over time
  - Pattern recognition and best practice enforcement
  - MCP integration for external memory persistence
- **VectorStorageService**: Embedding management gateway
  - All embedding requests MUST go through this service
  - Intelligent caching to prevent redundant operations
  - Automatic fallback between local Ollama and cloud providers
- **SupabaseService**: Database operations and authentication
  - Async SQLAlchemy 2.0 with proper connection pooling
  - Row Level Security (RLS) enforcement
  - Session management and cleanup operations
- **EnhancedLLMService**: Multi-provider LLM routing
  - Supports Ollama (local), OpenRouter, OpenAI, Anthropic
  - Automatic fallback and retry logic with exponential backoff
  - Request/response caching and rate limiting
- **LockManager**: Distributed task coordination
  - Redis-based distributed mutex for project-level locking
  - Ensures "one agent at a time" rule enforcement
  - Critical safety feature preventing race conditions
- **Dispatcher**: Workflow orchestration engine
  - Sequential task execution from roadmaps
  - Agent assignment and context injection
  - Progress tracking and error handling

### Infrastructure Layer

- **ai-orchestrator**: FastAPI backend (Python 3.12+) with Supabase pgvector integration
  - Central brain containing all agent logic and APIs
  - Async/await patterns with proper error handling
  - Comprehensive logging with structured format
- **user-portal**: Next.js 14+ frontend
  - User dashboard for project management
  - Real-time WebSocket communication
  - Responsive design with Tailwind CSS
- **code-server**: Browser-based VS Code
  - Primary interactive development environment
  - AI chat integration for real-time assistance
  - Live code preview and debugging capabilities
- **Database Stack**:
  - **PostgreSQL 15+ with pgvector**: Internal application state
  - **Supabase**: User project backends with auth
  - **Redis 7+**: Caching, locking, and task queues
- **Reverse Proxy**: Traefik 2.x for secure traffic management

## System Architecture Patterns

### Build Loop Workflow

1. **User Intake**: ArchitectAgent conducts structured interview
2. **Roadmap Generation**: Creates hierarchical project plan
3. **Task Execution**: Dispatcher processes tasks sequentially
4. **Context Injection**: RAGService provides relevant information
5. **Specialist Assignment**: Tasks routed to appropriate agents
6. **User Feedback**: Approval checkpoints at major milestones

### Preview & Atomic Deployment Workflow

1. **Change Request**: A user initiates a change request via the `/api/projects/{project_id}/request-changes` endpoint.
2. **Preview Deployment**: The system creates a preview deployment with a unique URL.
3. **Approval Request**: The `ArchitectAgent` sends a WebSocket message to the user with the preview URL and a unique, expiring approval token stored in Redis.
4. **User Approval**: The user reviews the preview and approves the changes by sending the approval token to the `/api/projects/{project_id}/approve-changes` endpoint.
5. **Final Deployment**: Upon successful validation of the approval token, the `DeploymentAgent` is triggered to deploy the changes to the main production site.

### Project Provisioning and Deployment Architecture

The AI Coding Agent now features an enhanced project provisioning and deployment system with the following key capabilities:

**Template-Based Provisioning**:
- Dynamic project creation using configurable templates for consistent environments
- Automated setup of project structure, dependencies, and initial configurations
- Support for multiple project types and technology stacks
- Git repository initialization with proper branching strategies

**Atomic Deployment**:
- Zero-downtime deployment strategy using atomic symlink swapping for production reliability
- Automatic rollback capabilities in case of deployment failures
- Timestamped releases with automated cleanup of old versions
- Health checks and validation before switching to new deployments

**Git Integration**:
- Automated branch management for feature development and preview deployments
- Secure branch merging with approval workflows
- Git history preservation and conflict resolution
- Integration with popular Git hosting platforms

**Environment Isolation**:
- Distinct development, staging, and production environments with proper resource allocation
- Container-based isolation for secure multi-tenant deployments
- Environment-specific configuration management
- Resource limits and security policies per environment

**Deployment Agent Enhancements**:
- **POST** `/deployment/preview` - Create preview deployment from commit or branch
- **POST** `/deployment/approve` - Approve and merge preview deployment to production
- **POST** `/deployment/provision` - Provision new project with template-based setup
- **POST** `/deployment/configure` - Configure environment-specific settings
- **POST** `/deployment/health` - Check deployment health and status

### Security Model

- **Container-First**: All development in isolated containers
- **Non-Root Execution**: All containers run as non-privileged users
- **Network Isolation**: Two-network security model (web/internal)
- **Secret Management**: Docker secrets for sensitive configuration
- **Row Level Security**: Database access protected by RLS policies

#### Authentication System

**Current Authentication Stack**:

```python
# Authentication imports (auth_service.py)
from supabase_auth import User, Session
from supabase import create_client

# Requirements (containers/ai-orchestrator/requirements.txt)
supabase>=2.0.0          # Core Supabase client
supabase-auth>=2.0.0     # Modern auth package
```

**Authentication Features**:
- JWT token management with automatic refresh
- User registration and login workflows
- Role-based access control (RBAC)
- Session management and cleanup
- Multi-factor authentication support
- OAuth provider integration

### Data Flow Architecture

```
User Input → ArchitectAgent → Roadmap → Dispatcher → SpecialistAgent
     ↓                                      →
RAGService ← VectorStorageService ← Context Retrieval
     ↓                                      ↓
Supabase pgvector (Unified Knowledge Storage) → Code Generation
```

### 📈 Architectural Diagrams

```mermaid
sequenceDiagram
    actor User
    participant A as ArchitectAgent
    participant S as ShellAgent
    participant F as FrontendAgent
    participant D as DeploymentAgent

    User->>A: POST /request-changes (prompt)
    A->>S: Execute("git checkout -b feature/...")
    A->>F: ExecuteTask("Implement change")
    F->>S: Execute("git commit ...")
    S-->>A: CommitHash
    A->>D: create_preview_deployment(CommitHash)
    D-->>A: PreviewURL
    A->>User: WebSocket: "Review at PreviewURL. Type 'approve'."
    User->>A: POST /approve-changes
    A->>S: Execute("git merge main; git branch -d ...")
    A->>D: deploy_main_branch()
    D->>D: Performs Atomic Symlink Swap
    D->>D: Cleans up preview route
    A->>User: WebSocket: "Changes are now live!"
```

**Diagram for the "Advanced Orchestration & Services":**
```mermaid
graph TD
    subgraph "Orchestration & AI Services"
        LO[LangGraph Orchestrator] -->|uses| ELS[EnhancedLLMService]
        LO -->|uses| RAG[RAGService]
        LO -->|uses| MEM[MemoryManagementService]

        subgraph "GPU-Accelerated Operations (Optional)"
            CUDA[CUDA Accelerated Service]
        end

        RAG -->|uses| VSS[VectorStorageService]
        VSS -->|can use| CUDA
        VSS -->|stores in| PG[(Supabase pgvector)]
        ELS -->|can use| Ollama[Ollama]
        ELS -->|can use| OpenRouter[OpenRouter]
        MEM -->|stores in| PG
    end
```

## Agent Interaction Interfaces

### Agent API Endpoints

Each agent exposes standardized REST endpoints for integration:

**Base URL**: `http://localhost:8000/api/agents`

#### ArchitectAgent
**File**: `containers/ai-orchestrator/src/agents/architect_agent.py`

The ArchitectAgent is responsible for both user interviews and project planning with memory-aware capabilities for personalized interactions and continuous learning. It now features an intelligent interview-first workflow that automatically selects optimal project templates based on user requirements.

**Key Features:**
- **Interactive Interviewing**: Conducts structured interviews to gather project requirements
- **Intelligent Template Selection**: Uses LLM analysis to automatically choose between FastAPI, React-Vite-Tailwind, or blank templates based on interview responses
- **Memory-Aware Personalization**: Remembers user preferences and past interactions
- **Learning Loops**: Stores successful patterns and outcomes for future reference
- **Project Planning**: Creates detailed roadmaps and delegates tasks to specialist agents
- **Phase Management**: Handles project phases with approval workflows
- **Automated Template Application**: Creates ShellAgent tasks to apply selected templates to new projects

**Constructor:**
```python
# Memory-aware instantiation
architect = ArchitectAgent(memory_service=memory_service)

# Legacy instantiation (memory features disabled)
architect = ArchitectAgent()
```

**API Endpoints:**
- **POST** `/architect/interview` - Conduct user requirements interview
  - Input: `{"user_description": "string", "context": "object"}`
  - Output: `{"requirements": "object", "clarifications": "array", "user_preferences": "string"}`
- **POST** `/architect/roadmap` - Generate project roadmap with intelligent template selection
  - Input: `{"requirements": "object", "preferences": "object"}`
  - Output: `{"roadmap": "object", "phases": "array", "timeline": "string", "selected_template": "string", "learning_stored": "boolean"}`

**Template Selection Logic:**
The ArchitectAgent analyzes interview responses to automatically select the most appropriate starter template:
- **FastAPI Template**: Selected for backend/API projects, data processing, or backend services
- **React-Vite-Tailwind Template**: Selected for frontend applications, user interfaces, dashboards, or web applications
- **Blank Template**: Default fallback for unclear requirements or custom project structures

**Memory Integration:**
- **User Preferences**: Remembers technology choices, project preferences, and interaction patterns
- **Learning Outcomes**: Stores successful roadmap patterns and project outcomes
- **Template Selection History**: Tracks successful template choices for similar project types
- **Personalized Interviews**: Tailors questions based on previous user responses
- **Context Awareness**: Considers past projects when generating new roadmaps

#### BackendAgent
- **POST** `/backend/api` - Create API endpoints
  - Input: `{"specification": "object", "database_schema": "object"}`
  - Output: `{"endpoints": "array", "models": "array", "tests": "array"}`
- **POST** `/backend/database` - Manage database operations
  - Input: `{"action": "string", "schema": "object", "migrations": "array"}`
  - Output: `{"status": "string", "changes": "array", "rollback": "object"}`

#### FrontendAgent
- **POST** `/frontend/component` - Generate React components
  - Input: `{"specification": "object", "design_system": "object"}`
  - Output: `{"components": "array", "styles": "object", "tests": "array"}`
- **POST** `/frontend/integration` - Connect to backend APIs
  - Input: `{"api_spec": "object", "components": "array"}`
  - Output: `{"integrations": "array", "state_management": "object"}`

#### ShellAgent
- **POST** `/shell/execute` - Execute system commands
  - Input: `{"commands": "array", "working_directory": "string"}`
  - Input (extended): `{"commands": "array", "working_directory": "string", "command_type": "string (optional)"}`
    - `command_type` (optional): when provided the agent will perform specialized export or cleanup workflows. Supported values: `"database_export"`, `"filesystem_export"`, `"package_export"`, `"cleanup_export"`.
  - Output: `{"results": "array", "exit_codes": "array", "logs": "string"}`
- **POST** `/shell/file` - File system operations
  - Input: `{"operation": "string", "paths": "array", "content": "string"}`
  - Output: `{"status": "string", "affected_files": "array"}`

#### DeploymentAgent
- **POST** `/deployment/deploy` - Deploy project to hosting platform
  - Input: `{"platform": "string", "project_config": "object", "environment": "string"}`
  - Output: `{"deployment_url": "string", "status": "string", "deployment_id": "string"}`
- **POST** `/deployment/rollback` - Rollback to previous deployment
  - Input: `{"deployment_id": "string", "target_version": "string"}`
  - Output: `{"status": "string", "rollback_url": "string"}`
- **POST** `/deployment/preview` - Create preview deployment
  - Input: `{"commit_hash": "string", "branch_name": "string"}`
  - Output: `{"preview_url": "string", "status": "string", "deployment_id": "string"}`
- **POST** `/deployment/approve` - Approve and merge preview deployment
  - Input: `{"deployment_id": "string", "approval_token": "string"}`
  - Output: `{"status": "string", "live_url": "string"}`

#### IndexerAgent
- **POST** `/indexer/scan` - Scan and index codebase
  - Input: `{"project_path": "string", "file_patterns": "array"}`
  - Output: `{"indexed_files": "number", "embeddings_generated": "number", "status": "string"}`
- **POST** `/indexer/search` - Search indexed code
  - Input: `{"query": "string", "file_types": "array", "limit": "number"}`
  - Output: `{"results": "array", "relevance_scores": "array"}`

#### IssueFixAgent
- **POST** `/issue-fix/analyze` - Analyze error logs and stack traces
  - Input: `{"error_log": "string", "context": "object", "stack_trace": "string"}`
  - Output: `{"analysis": "object", "suggested_fixes": "array", "confidence": "number"}`
- **POST** `/issue-fix/apply` - Apply automated fix
  - Input: `{"fix_id": "string", "target_files": "array", "dry_run": "boolean"}`
  - Output: `{"changes_made": "array", "status": "string", "backup_created": "boolean"}`

### Data Schemas

#### Common Request Format
```json
{
  "task_id": "string",
  "user_id": "string",
  "project_id": "string",
  "context": {
    "previous_tasks": "array",
    "project_state": "object",
    "user_preferences": "object"
  },
  "payload": "object"
}
```

#### Common Response Format
```json
{
  "success": "boolean",
  "task_id": "string",
  "agent": "string",
  "result": "object",
  "metadata": {
    "execution_time": "number",
    "resources_used": "object",
    "next_suggested_actions": "array"
  },
  "errors": "array"
}
```

## Memory Management System - AI Learning & Context Awareness

**Files**:
- `containers/ai-orchestrator/src/services/memory_management_service.py`
- `containers/ai-orchestrator/src/models/memory_models.py`
- `containers/ai-orchestrator/src/router/memory_router.py`
- `containers/ai-orchestrator/src/core/config.py` (memory settings)

**Overview**: Comprehensive memory system that enables the AI coding agent to learn from interactions, remember user preferences, store code patterns, and provide intelligent context for code generation tasks.

### Core Components

**Memory Types**:
- **User Preferences**: Coding style, framework preferences, naming conventions
- **Code Patterns**: Reusable templates for common development tasks
- **Project Context**: Project-specific knowledge and requirements
- **Learning Outcomes**: Lessons learned from successful/failed interactions
- **Error Solutions**: Known fixes for common development issues
- **Architecture Decisions**: Design patterns and architectural choices
- **Best Practices**: Coding standards and quality guidelines

**Key Features**:
- **Persistent Storage**: JSON-based storage with backup capabilities
- **Intelligent Retrieval**: Context-aware memory search and ranking
- **Learning Loops**: Continuous improvement through interaction analysis
- **Pattern Recognition**: Automatic identification of coding patterns
- **MCP Integration**: External memory persistence via Model Context Protocol
- **Configuration-Driven**: All settings managed through central config

### API Endpoints

**Base URL**: `http://localhost:8000/api/memory`

**Memory Operations**:
- **POST** `/store` - Store a new memory entry
  - Input: `{"content": "string", "memory_type": "enum", "tags": ["array"], "metadata": "object"}`
  - Output: `{"success": "boolean", "memory_id": "string"}`

- **POST** `/search` - Search memories with filtering
  - Input: `{"query": "string", "memory_type": "enum", "tags": ["array"], "limit": "number"}`
  - Output: `{"memories": "array", "total_count": "number"}`

- **GET** `/patterns` - Retrieve code patterns
  - Query: `?language=python&framework=fastapi&pattern_type=route`
  - Output: `{"patterns": "array"}`

- **POST** `/patterns` - Store code pattern
  - Input: `{"name": "string", "language": "string", "template": "string", "variables": "object"}`
  - Output: `{"success": "boolean", "pattern_name": "string"}`

- **POST** `/learn` - Learn from interaction
  - Input: `{"interaction_type": "string", "content": "string", "outcome": "string", "context": "object"}`
  - Output: `{"success": "boolean", "learning_id": "string"}`

- **POST** `/context` - Get relevant context for task
  - Input: `{"task": "string", "language": "string", "framework": "string"}`
  - Output: `{"memories": "array", "patterns": "array", "learnings": "array"}`

- **GET** `/statistics` - Get memory system statistics
  - Output: `{"total_memories": "number", "memory_types": "object", "total_patterns": "number"}`

- **POST** `/validate` - Validate memory content
  - Input: `{"content": "string", "memory_type": "enum"}`
  - Output: `{"is_valid": "boolean", "errors": "array", "warnings": "array"}`

- **POST** `/cleanup` - Cleanup expired memories
  - Input: `{"dry_run": "boolean"}`
  - Output: `{"cleaned_count": "number", "errors": "array"}`

### Configuration Settings

**Memory Storage**:
```python
MEMORY_FILE_PATH = "/app/data/memories.json"
MEMORY_BACKUP_PATH = "/app/data/memories_backup.json"
```

**Limits & Cleanup**:
```python
MAX_MEMORY_ENTRIES = 10000
MEMORY_CLEANUP_INTERVAL_HOURS = 24
MEMORY_RETENTION_DAYS = 90
MEMORY_COMPRESSION_THRESHOLD = 5000
```

**Search & Learning**:
```python
MEMORY_SEARCH_SIMILARITY_THRESHOLD = 0.8
MEMORY_MAX_SEARCH_RESULTS = 20
ENABLE_LEARNING_LOOPS = True
LEARNING_CONFIDENCE_THRESHOLD = 0.7
LEARNING_PATTERN_MIN_OCCURRENCES = 3
```

**Persistence**:
```python
MEMORY_AUTO_SAVE = True
MEMORY_SAVE_INTERVAL_SECONDS = 300
MEMORY_ENABLE_BACKUP = True
MEMORY_BACKUP_INTERVAL_HOURS = 6
```

### Integration Points

**With Code Generation**:
```python
# Get relevant context before code generation
context = await memory_service.get_relevant_context(
    task="Create FastAPI route handler",
    language="python",
    framework="fastapi"
)

# Use patterns for consistent code generation
patterns = await memory_service.get_code_patterns(
    language="python",
    framework="fastapi"
)
```

**With Learning Loops**:
```python
# Learn from successful interactions
await memory_service.learn_from_interaction({
    "interaction_type": "code_generation",
    "content": "Generated FastAPI route with proper error handling",
    "outcome": "successful",
    "context": {"language": "python", "framework": "fastapi"}
})
```

**With Agent Workflow**:
```python
# Store user preferences
await memory_service.store_memory(
    content="User prefers async/await patterns in Python",
    memory_type=MemoryType.USER_PREFERENCE,
    tags=["python", "async", "coding_style"]
)
```

### Usage Examples

**Storing User Preferences**:
```python
from src.services.memory_management_service import MemoryType

# Store coding style preference
await memory_service.store_memory(
    content="Always use type hints in Python functions",
    memory_type=MemoryType.USER_PREFERENCE,
    tags=["python", "type_hints", "coding_style"],
    metadata={"priority": "high", "confidence": 0.9}
)
```

**Retrieving Code Patterns**:
```python
# Get FastAPI route patterns
patterns = await memory_service.get_code_patterns(
    language="python",
    framework="fastapi",
    pattern_type="route"
)

# Use pattern for code generation
for pattern in patterns:
    generated_code = pattern.template.format(**pattern.variables)
```

**Learning from Interactions**:
```python
# Record successful code generation
await memory_service.learn_from_interaction({
    "interaction_type": "code_generation",
    "content": "Successfully generated CRUD endpoints with validation",
    "outcome": "successful",
    "context": {
        "language": "python",
        "framework": "fastapi",
        "pattern": "crud_operations"
    }
})
```

### Benefits & Use Cases

**For Code Generation**:
- Consistent coding patterns across projects
- User preference enforcement
- Best practice application
- Error prevention through learned solutions

**For Agent Learning**:
- Continuous improvement through interaction analysis
- Pattern recognition for similar tasks
- Context-aware responses
- Reduced repetition of common fixes

**For Project Management**:
- Project-specific knowledge retention
- Team preference standardization
- Architecture decision tracking
- Quality assurance through learned best practices

## Advanced Orchestration Services

### LangGraph Orchestrator - Multi-Agent Workflow Management

**File**: `containers/ai-orchestrator/src/services/langgraph_orchestrator.py`

A sophisticated agent orchestration system using LangGraph for complex, stateful multi-agent workflows:

**Key Features:**
- **Stateful Execution**: Durable workflow management with persistence
- **Parallel Processing**: Concurrent agent execution for improved throughput
- **Error Recovery**: Intelligent retry mechanisms and fallback strategies
- **Performance Caching**: Node-level caching for expensive operations
- **Human-in-the-Loop**: Support for human intervention in complex workflows

**API Endpoints:**
- **POST** `/api/orchestrator/workflow` - Execute complex coding workflows
  - Input: `{"task": "string", "context": "object", "use_cuda": "boolean"}`
  - Output: `{"success": "boolean", "results": "object", "performance": "object"}`

**Workflow Types:**
- **Coding Tasks**: Full-stack application development with agent coordination
- **Code Review**: Multi-agent code analysis and improvement suggestions
- **Testing**: Automated test generation and execution workflows
- **Deployment**: CI/CD pipeline orchestration and deployment management

### CUDA Accelerated Service - GPU-Optimized ML Operations

**File**: `containers/ai-orchestrator/src/services/cuda_accelerated_service.py`

High-performance GPU acceleration for machine learning workloads:

**Key Features:**
- **GPU Embeddings**: Fast text vectorization using SentenceTransformers
- **CUDA Optimization**: Memory-efficient batch processing with cuPy
- **FAISS GPU Indexing**: Accelerated similarity search with GPU support
- **Automatic Fallback**: CPU fallback when GPU is unavailable
- **Memory Management**: Intelligent GPU memory optimization

**API Endpoints:**
- **POST** `/api/cuda/embeddings` - Generate text embeddings
  - Input: `{"texts": "array", "batch_size": "number"}`
  - Output: `{"embeddings": "array", "performance": "object"}`
- **POST** `/api/cuda/similarity` - Perform vector similarity search
  - Input: `{"query": "array", "index": "array", "top_k": "number"}`
  - Output: `{"results": "array", "scores": "array"}`

**Performance Benefits:**
- **3-5x faster** embedding generation with GPU acceleration
- **Memory efficient** batch processing for large datasets
- **Optimized vector operations** for similarity search
- **Automatic resource management** with cleanup

## Critical Development Conventions

### Mandatory Practices

- **Absolute Imports Only**: `from src.services.vector_service import VectorStorageService`
- **Container-Only Development**: All work must occur within Docker containers
- **Type Annotations**: Full type hints required for all Python code
- **Async/Await**: Use for all I/O-bound operations
- **Error Handling**: Catch specific exceptions with structured logging
- **Testing**: 90%+ coverage requirement with pytest

### 📜 Documentation as Code
This `AGENTS.md` document is the single source of truth for the project's architecture. It must be kept up-to-date. Any pull request that changes the architecture, API contracts, service responsibilities, or core development conventions **must** include a corresponding update to this file.

### Forbidden Patterns

- **Relative Imports**: `from ..services import VectorStorageService`
- **Direct Embedding Calls**: Must use VectorStorageService gateway
- **Root User Containers**: All Dockerfiles must specify non-root USER
- **Hardcoded Secrets**: Use Docker secrets or environment variables
- **Generic Exception Handling**: Catch specific exception types

### Database Conventions

- **Internal DB (ai-orchestrator)**: Use Alembic for schema changes
- **User DBs (Supabase)**: Use SQL migration files in `supabase/migrations/`
- **Connection Management**: Async connection pooling with timeout protection
- **Query Optimization**: Use prepared statements and proper indexing

### Database Migration Configuration

#### Alembic Migration Files Setup

**File Location**: `containers/ai-orchestrator/alembic/versions/`

**Migration File Naming Convention**:
```python
# File: {timestamp}_{descriptive_name}.py
# Example: 3e3b476e8c1a_refactor_to_uuid_ownership.py
```

**Standard Migration Template**:
```python
"""migration_description

Revision ID: {unique_revision_id}
Revises: {previous_revision_id}
Create Date: {timestamp}

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic
revision: str = '{unique_revision_id}'
down_revision: Union[str, None] = '{previous_revision_id}'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    """Apply the migration changes."""
    # Migration logic here
    pass

def downgrade() -> None:
    """Revert the migration changes."""
    # Rollback logic here
    pass
```

#### Foreign Key Reference Rules

**Correct - Use actual table names**:
```python
# For user_profiles table
op.create_foreign_key(
    'fk_projects_owner_id_user_profiles',
    'projects',
    'user_profiles',  # Correct table name
    ['owner_id'],
    ['supabase_user_id'],
    ondelete='CASCADE'
)
```

**Incorrect - Old table references**:
```python
# Don't use old table names
op.create_foreign_key(
    'fk_projects_owner_id_users',  # Wrong constraint name
    'projects',
    'users',  # Wrong table name (should be user_profiles)
    ['owner_id'],
    ['supabase_user_id'],
    ondelete='CASCADE'
)
```

#### Migration Best Practices

1. **Table Name Consistency**:
   - Always use actual table names (`user_profiles`, not `users`)
   - Update both `upgrade()` and `downgrade()` functions
   - Match constraint names between creation and deletion

2. **Foreign Key Naming**:
   - Format: `fk_{table}_{column}_{referenced_table}`
   - Example: `fk_projects_owner_id_user_profiles`

3. **UUID Column Handling**:
   - Use `sa.UUID()` for PostgreSQL UUID columns
   - Use `UuidVariant` in SQLAlchemy models for cross-database compatibility

4. **Migration Testing**:
   - Test both upgrade and downgrade operations
   - Verify foreign key constraints work correctly
   - Check data integrity after migration

### Code Style Standards

- **Formatting**: Ruff for Python, Prettier for TypeScript/JavaScript
- **Documentation**: Google-style docstrings for all public APIs
- **Logging**: Structured format with correlation IDs and context
- **Variable Naming**: Descriptive names following language conventions

## Agent Operational Protocols

### Technical Implementation Guidelines

- **Memory Management**: Always cleanup large objects and close connections
- **Retry Logic**: Implement exponential backoff for external service calls
- **Circuit Breakers**: Use timeout protection for all network operations
- **Graceful Degradation**: Handle service failures with appropriate fallbacks

### Quality Assurance

- **Unit Tests**: Required for all new functionality
- **Integration Tests**: End-to-end testing for critical workflows
- **Error Scenarios**: Test failure modes and recovery mechanisms
- **Performance**: Monitor and optimize for container resource constraints

## Environment & Deployment

### Startup Sequence

1. **Environment Setup**: `.jules/setup.sh` installs dependencies
2. **Secret Configuration**: Docker secrets for sensitive data
3. **Database Initialization**: Supabase and internal DB setup
4. **Service Health Checks**: Verify all components are operational
5. **Agent Registration**: Initialize agent system and workflows

### Monitoring & Observability

- **Structured Logging**: Correlation IDs for request tracing
- **Health Endpoints**: Service availability monitoring
- **Performance Metrics**: Resource usage and response times
- **Error Tracking**: Comprehensive error collection and analysis

## 🤖 Mandatory Protocols for AI Assistants

### Jules Environment Setup

**IMPORTANT**: Before working with this codebase, run the environment setup script:

```bash
# Navigate to the .jules directory and run the setup script
cd .jules
./setup.sh
```

This script will:
- Install all required dependencies and tools
- Configure the development environment
- Set up necessary permissions and configurations
- Validate that all services are accessible

### Task Execution Rules

1. **Scope Adherence**: Execute only specified tasks, no unsolicited changes
2. **Incrementalism**: Make minimal, focused changes representing single logical steps
3. **Clarification First**: Ask for clarification rather than making assumptions
4. **Context Awareness**: Always check existing code patterns before implementing

### Import Management Guidelines

When encountering imports that appear unused by linting tools:

1. **Investigate First**: Check for dynamic usage, type hints, or runtime dependencies
2. **Context Analysis**: Use `git blame` and `git log` to understand the import's purpose
3. **Decision Framework**:
   - **For future features**: Add `# TODO: Feature #123 - Will be used for X`
   - **For type hints**: Ensure proper usage in annotations
   - **For runtime dependencies**: Document the runtime usage pattern
   - **For truly dead code**: Remove in separate commit with clear justification

**Example of proper import preservation**:
```python
import json  # Used for debug logging and config serialization
from typing import Optional  # Required for type annotations

def process_data(data: Optional[dict]) -> str:
    logger.debug(f"Processing: {json.dumps(data, indent=2)}")
    return "processed"
```

## Database Refactoring Notes

### Completed Changes

**User Model Refactoring**:
- Renamed `User` class to `UserProfile` in `src/models/user.py`
- Updated `__tablename__` from `"users"` to `"user_profiles"`
- Maintained backward compatibility in `__init__.py`

**Foreign Key Updates**:
- Updated all models to reference `user_profiles.supabase_user_id` instead of `users.id`
- Affected models: Project, DeploymentIntegration, ProjectEmbedding, UserContext, etc.
- All foreign keys now use UUID-based ownership

**Router Refactoring**:
- Fixed `src/router/project_router.py` anti-patterns
- Removed try-except blocks around database operations
- Corrected `user_id` reference to `current_user.supabase_user_id`

**Migration Generation**:
- Created Alembic migration: `3e3b476e8c1a_refactor_to_uuid_ownership.py`
- Includes all foreign key updates and column type changes
- Tested upgrade/downgrade operations

### Migration Details

**Migration File**: `containers/ai-orchestrator/alembic/versions/3e3b476e8c1a_refactor_to_uuid_ownership.py`

**Key Operations**:
```python
# Add UUID columns to existing tables
op.add_column('projects', sa.Column('owner_id', sa.UUID(), nullable=True))
op.add_column('deployment_integrations', sa.Column('user_id', sa.UUID(), nullable=True))

# Create foreign key constraints
op.create_foreign_key(
    'fk_projects_owner_id_user_profiles',
    'projects', 'user_profiles',
    ['owner_id'], ['supabase_user_id'],
    ondelete='CASCADE'
)

# Update column types
op.alter_column(
    'deployment_integrations', 'user_id',
    existing_type=sa.Integer(),
    type_=sa.UUID(),
    existing_nullable=False
)
```

**Verification Steps**:
1. Run `alembic upgrade head` to apply migration
2. Verify foreign key constraints are active
3. Test data integrity across all affected tables
4. Confirm router endpoints work with new UUID references

This architecture enables scalable, secure, and maintainable AI-powered development workflows while ensuring consistent code quality and operational reliability.
