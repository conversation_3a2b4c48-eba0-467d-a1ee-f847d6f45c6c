import logging

import httpx
import redis.asyncio as redis
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.config import settings
from src.models.database_async import get_async_db as get_session

logger = logging.getLogger(__name__)
router = APIRouter()


class DependencyStatus(BaseModel):
    status: str
    message: str


class HealthDetails(BaseModel):
    database: DependencyStatus
    redis: DependencyStatus
    ollama: DependencyStatus


class HealthResponse(BaseModel):
    status: str
    details: HealthDetails


async def get_redis_client_for_health():
    client = None
    try:
        client = redis.from_url(str(settings.REDIS_URL), decode_responses=True)
        yield client
    finally:
        if client is not None:
            await client.close()


@router.get(
    "/health",
    response_model=HealthResponse,
    summary="Perform a deep health check of the service and its dependencies.",
    tags=["Health"],
)
async def health_check(
    db: AsyncSession = Depends(get_session),
    redis_client: redis.Redis = Depends(get_redis_client_for_health),
):
    """
    Checks the health of the application and its critical dependencies:
    - **Database**: Pings the database to check connectivity.
    - **Redis**: Pings Redis to check connectivity.
    - **Ollama**: Checks if the Ollama API is reachable.

    Returns a 200 OK status if all dependencies are healthy, otherwise returns
    a 503 Service Unavailable status with a detailed JSON body.
    """
    is_healthy = True
    details = {
        "database": {"status": "healthy", "message": "Successfully connected to the database."},
        "redis": {"status": "healthy", "message": "Successfully connected to Redis."},
        "ollama": {"status": "healthy", "message": "Successfully connected to Ollama API."},
    }

    # Check Database
    try:
        await db.execute(text("SELECT 1"))
    except Exception as e:
        is_healthy = False
        details["database"]["status"] = "unhealthy"
        details["database"]["message"] = f"Database connection failed: {str(e)}"
        logger.error("Health check: Database connection failed.", exc_info=True)

    # Check Redis
    try:
        await redis_client.ping()
    except Exception as e:
        is_healthy = False
        details["redis"]["status"] = "unhealthy"
        details["redis"]["message"] = f"Redis connection failed: {str(e)}"
        logger.error("Health check: Redis connection failed.", exc_info=True)

    # Check Ollama
    try:
        async with httpx.AsyncClient() as client:
            # Use hardcoded Ollama URL for containerized setup
            ollama_url = "http://ollama:11434"
            response = await client.get(ollama_url)
            response.raise_for_status()
    except (httpx.RequestError, httpx.HTTPStatusError) as e:
        is_healthy = False
        details["ollama"]["status"] = "unhealthy"
        details["ollama"]["message"] = f"Ollama API connection failed: {str(e)}"
        logger.error("Health check: Ollama API connection failed.", exc_info=True)

    if is_healthy:
        return HealthResponse(status="ok", details=details)
    else:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=HealthResponse(status="unhealthy", details=details).model_dump(),
        )
