# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: ChromaDB management router for embedded vector database operations

import logging
from typing import List, Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel

from src.services.chromadb_service import (
    get_chromadb_client,
    create_or_get_collection,
    get_collection_stats,
    list_collections
)
from src.utils.auth import get_current_user

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/chromadb",
    tags=["ChromaDB Management"],
    responses={
        404: {"description": "Collection not found"},
        500: {"description": "ChromaDB service error"}
    }
)


class ChromaDBStatus(BaseModel):
    """ChromaDB service status response."""
    available: bool
    mode: str = "embedded"
    collections_count: int
    collections: List[Dict[str, Any]]


class CollectionInfo(BaseModel):
    """Collection information response."""
    name: str
    document_count: int
    metadata: Dict[str, Any]


class CollectionStats(BaseModel):
    """Collection statistics response."""
    collection_name: str
    document_count: int
    status: str
    error: Optional[str] = None


@router.get(
    "/status",
    response_model=ChromaDBStatus,
    summary="Get ChromaDB service status",
    description="Check the status of the embedded ChromaDB service"
)
async def get_chromadb_status(
    current_user=Depends(get_current_user)
) -> ChromaDBStatus:
    """Get ChromaDB service status and basic statistics."""
    try:
        chromadb_client = get_chromadb_client()

        if chromadb_client is None:
            return ChromaDBStatus(
                available=False,
                mode="embedded",
                collections_count=0,
                collections=[]
            )

        collections = list_collections()

        return ChromaDBStatus(
            available=True,
            mode="embedded",
            collections_count=len(collections),
            collections=collections
        )

    except Exception as e:
        logger.error(f"Error getting ChromaDB status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get ChromaDB status: {str(e)}"
        )


@router.get(
    "/collections",
    response_model=List[CollectionInfo],
    summary="List all collections",
    description="Get a list of all ChromaDB collections"
)
async def list_chromadb_collections(
    current_user=Depends(get_current_user)
) -> List[CollectionInfo]:
    """List all ChromaDB collections with their information."""
    try:
        chromadb_client = get_chromadb_client()

        if chromadb_client is None:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="ChromaDB service not available"
            )

        collections = list_collections()

        return [
            CollectionInfo(
                name=col["name"],
                document_count=col["document_count"],
                metadata=col["metadata"]
            )
            for col in collections
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing ChromaDB collections: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list collections: {str(e)}"
        )


@router.get(
    "/collections/{collection_name}/stats",
    response_model=CollectionStats,
    summary="Get collection statistics",
    description="Get detailed statistics for a specific collection"
)
async def get_collection_statistics(
    collection_name: str,
    current_user=Depends(get_current_user)
) -> CollectionStats:
    """Get statistics for a specific ChromaDB collection."""
    try:
        stats = get_collection_stats(collection_name)

        if "error" in stats:
            return CollectionStats(
                collection_name=collection_name,
                document_count=0,
                status="error",
                error=stats["error"]
            )

        return CollectionStats(
            collection_name=collection_name,
            document_count=stats["document_count"],
            status=stats["status"]
        )

    except Exception as e:
        logger.error(f"Error getting collection stats for {collection_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get collection statistics: {str(e)}"
        )


@router.post(
    "/collections/{collection_name}",
    response_model=CollectionInfo,
    summary="Create or get collection",
    description="Create a new collection or get existing one"
)
async def create_or_get_chromadb_collection(
    collection_name: str,
    description: Optional[str] = None,
    current_user=Depends(get_current_user)
) -> CollectionInfo:
    """Create a new ChromaDB collection or get existing one."""
    try:
        chromadb_client = get_chromadb_client()

        if chromadb_client is None:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="ChromaDB service not available"
            )

        collection_description = description or f"Collection {collection_name}"
        collection = create_or_get_collection(collection_name, collection_description)

        if collection is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create or access collection: {collection_name}"
            )

        # Get collection info
        count = collection.count() if hasattr(collection, 'count') else 0
        metadata = getattr(collection, 'metadata', {})

        return CollectionInfo(
            name=collection_name,
            document_count=count,
            metadata=metadata
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating/getting collection {collection_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create or get collection: {str(e)}"
        )


@router.get(
    "/health",
    summary="ChromaDB health check",
    description="Check if ChromaDB embedded service is healthy"
)
async def chromadb_health_check() -> Dict[str, Any]:
    """Health check endpoint for ChromaDB service."""
    try:
        chromadb_client = get_chromadb_client()

        if chromadb_client is None:
            return {
                "status": "unavailable",
                "message": "ChromaDB client not initialized",
                "available": False
            }

        # Test basic operations
        collections = list_collections()

        return {
            "status": "healthy",
            "message": "ChromaDB embedded service is operational",
            "available": True,
            "mode": "embedded",
            "collections_count": len(collections),
            "data_path": "/app/data/chromadb"
        }

    except Exception as e:
        logger.error(f"ChromaDB health check failed: {e}")
        return {
            "status": "unhealthy",
            "message": f"ChromaDB service error: {str(e)}",
            "available": False,
            "error": str(e)
        }
