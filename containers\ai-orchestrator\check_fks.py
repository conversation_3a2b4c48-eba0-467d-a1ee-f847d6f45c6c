#!/usr/bin/env python3
"""
Check foreign key constraints in database
"""
from sqlalchemy import create_engine, inspect
import os

def main():
    url = os.environ.get('DATABASE_URL')
    if not url:
        print("DATABASE_URL not found")
        return

    engine = create_engine(url)
    inspector = inspect(engine)

    print('All foreign keys in database:')
    try:
        tables = inspector.get_table_names()
        for table in sorted(tables):
            fks = inspector.get_foreign_keys(table)
            if fks:
                print(f'\n{table}:')
                for fk in fks:
                    print(f'  {fk["constrained_columns"]} -> {fk["referred_table"]}.{fk["referred_columns"]}')
    except Exception as e:
        print(f'Error getting foreign keys: {e}')

    print('\nChecking specific tables for our changes:')
    tables_to_check = ['projects', 'deployment_integrations', 'ingestion_errors', 'roadmaps', 'roadmap_summaries', 'roadmap_source_references']

    for table in tables_to_check:
        try:
            columns = inspector.get_columns(table)
            print(f'\n{table} columns:')
            for col in columns:
                if 'user' in col['name'].lower() or 'owner' in col['name'].lower():
                    print(f'  {col["name"]}: {col["type"]}')

            fks = inspector.get_foreign_keys(table)
            if fks:
                print(f'{table} foreign keys:')
                for fk in fks:
                    print(f'  {fk["constrained_columns"]} -> {fk["referred_table"]}.{fk["referred_columns"]}')
        except Exception as e:
            print(f'Error checking {table}: {e}')

if __name__ == '__main__':
    main()
