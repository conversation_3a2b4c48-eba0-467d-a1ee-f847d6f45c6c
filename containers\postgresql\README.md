# PostgreSQL Database Container

High-performance PostgreSQL database container with pgvector extension for AI Coding Agent.

## 🎯 Overview

This container provides a PostgreSQL database with pgvector extension for vector similarity search operations used by the AI Coding Agent's RAG (Retrieval-Augmented Generation) system.

## 🏗️ Architecture

### Features

- **PostgreSQL 15+** with pgvector extension
- **Vector Search**: Optimized for high-dimensional vector similarity searches
- **Row Level Security (RLS)**: Multi-tenant data isolation
- **Connection Pooling**: Optimized for concurrent AI operations
- **Backup & Recovery**: Automated backup strategies
- **Performance Monitoring**: Built-in query performance insights

### Extensions

- **pgvector**: Vector similarity search and indexing
- **pg_stat_statements**: Query performance monitoring
- **uuid-ossp**: UUID generation support
- **pgcrypto**: Cryptographic functions

## 🚀 Configuration

### Environment Variables

```bash
# Database Configuration
POSTGRES_DB=ai_coding_agent
POSTGRES_USER=ai_user
POSTGRES_PASSWORD=secure_password

# Connection Settings
POSTGRES_MAX_CONNECTIONS=100
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_WORK_MEM=4MB

# Vector Search Optimization
POSTGRES_MAINTENANCE_WORK_MEM=64MB
POSTGRES_WAL_BUFFERS=16MB

# Security
POSTGRES_LISTEN_ADDRESSES=*
POSTGRES_LOG_STATEMENT=all
```

### Volume Mounts

```yaml
volumes:
  - ./volumes/postgresql/data:/var/lib/postgresql/data
  - ./volumes/postgresql/backups:/backups
  - ./init-scripts:/docker-entrypoint-initdb.d
```

## 🔧 Usage

### Building the Container

```bash
docker build -t ai-coding-agent-postgresql .
```

### Running with Docker Compose

```bash
docker-compose up postgresql
```

### Database Initialization

The container includes initialization scripts that:

1. Create the main database and user
2. Install required PostgreSQL extensions
3. Set up vector search indexes
4. Configure Row Level Security policies
5. Create performance monitoring views

## 📊 Performance Optimization

### Vector Search Indexes

```sql
-- HNSW index for fast vector similarity search
CREATE INDEX ON document_sections USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- IVFFlat index for larger datasets
CREATE INDEX ON document_sections USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);
```

### Query Optimization

- **Connection Pooling**: PgBouncer for efficient connection management
- **Query Caching**: Prepared statements for repeated queries
- **Index Maintenance**: Automated reindexing during low-usage periods

## 🔒 Security

### Row Level Security (RLS)

All tables implement RLS policies ensuring:

- Users can only access their own data
- Project ownership verification
- Secure multi-tenant isolation
- Audit trail for all operations

### Network Security

- **Internal Networking**: Only accessible from other containers
- **SSL/TLS**: Encrypted connections for production
- **Firewall Rules**: Restricted access patterns

## 📈 Monitoring

### Health Checks

```bash
# Database connectivity
docker exec postgresql pg_isready -U ai_user -d ai_coding_agent

# Vector extension status
docker exec postgresql psql -U ai_user -d ai_coding_agent -c "SELECT * FROM pg_extension WHERE extname = 'vector';"
```

### Metrics

- **Connection Count**: Active and idle connections
- **Query Performance**: Slow query identification
- **Vector Search Latency**: Similarity search response times
- **Storage Usage**: Database size and growth trends

## 🔄 Backup & Recovery

### Automated Backups

```bash
# Daily backup script
docker exec postgresql pg_dump -U ai_user -d ai_coding_agent > backup_$(date +%Y%m%d).sql

# Vector data backup
docker exec postgresql pg_dump -U ai_user -d ai_coding_agent --table=document_sections > vectors_$(date +%Y%m%d).sql
```

### Recovery Procedures

1. **Point-in-time Recovery**: WAL-based recovery
2. **Vector Data Recovery**: Specialized vector index rebuilding
3. **Multi-tenant Recovery**: User-specific data restoration

## 🐛 Troubleshooting

### Common Issues

1. **Vector Search Performance**
   - Check index status: `SELECT * FROM pg_stat_user_indexes WHERE schemaname = 'public';`
   - Rebuild indexes: `REINDEX INDEX CONCURRENTLY document_sections_embedding_idx;`

2. **Connection Pooling**
   - Monitor active connections: `SELECT count(*) FROM pg_stat_activity;`
   - Adjust pool settings in PgBouncer configuration

3. **Memory Issues**
   - Check memory usage: `SELECT name, setting FROM pg_settings WHERE name LIKE '%mem%';`
   - Adjust PostgreSQL memory parameters

## 📚 Additional Resources

- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [pgvector GitHub](https://github.com/pgvector/pgvector)
- [PgBouncer Documentation](https://www.pgbouncer.org/)
- [PostgreSQL Performance Tuning](https://www.postgresql.org/docs/current/runtime-config.html)

## 🤝 Contributing

When modifying this container:

1. Update initialization scripts for schema changes
2. Test vector search performance after modifications
3. Ensure RLS policies remain intact
4. Update backup procedures for new data structures
5. Validate monitoring and health check endpoints
