# Project: AI Coding Agent
# Purpose: Authentication dependencies for FastAPI
# Author: AI Coding Agent Team

"""
Authentication Dependencies for AI Coding Agent.

This module provides FastAPI dependencies for authentication and authorization,
specifically designed for Supabase Auth integration with JWT token validation.
"""

import logging
import os
from typing import Optional
from datetime import datetime, timezone
from uuid import UUID

# FastAPI and HTTP imports
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt
import httpx

# Internal imports
from src.schemas.user_schemas import SupabaseUser
from src.core.config import settings

logger = logging.getLogger(__name__)

# Security scheme for JWT Bearer tokens
security = HTTPBearer()

# Startup validation flag
_auth_startup_validated = False

# Supabase configuration via centralized settings with startup validation
SUPABASE_URL = str(settings.SUPABASE_URL) if settings.SUPABASE_URL else None
SUPABASE_ANON_KEY = settings.SUPABASE_ANON_KEY or os.getenv("SUPABASE_ANON_KEY") or os.getenv("SUPABASE_KEY")
SUPABASE_JWT_SECRET = settings.JWT_SECRET
# Configurable JWT audience with fallback
JWT_AUDIENCE = os.getenv("JWT_AUDIENCE", "authenticated")


def validate_auth_startup() -> None:
    """
    Validate required Supabase credentials at startup.

    Raises:
        RuntimeError: If required authentication configuration is missing.
    """
    global _auth_startup_validated

    if _auth_startup_validated:
        return

    errors = []

    if not SUPABASE_URL:
        errors.append("SUPABASE_URL is required for authentication")
    if not SUPABASE_ANON_KEY:
        errors.append("SUPABASE_ANON_KEY or SUPABASE_KEY is required")
    if not SUPABASE_JWT_SECRET:
        errors.append("JWT_SECRET is required for token validation")

    if errors:
        error_msg = "Authentication startup validation failed:\n" + "\n".join(f"  - {err}" for err in errors)
        logger.error(error_msg)
        raise RuntimeError(error_msg)

    _auth_startup_validated = True
    logger.info("Authentication startup validation completed successfully")


# Robust HTTP client with retries and timeout
_http_client_instance = None


async def get_robust_http_client() -> httpx.AsyncClient:
    """
    Get a robust HTTP client instance with retries and proper timeout configuration.

    Returns:
        Configured httpx.AsyncClient with retry logic and timeouts.
    """
    global _http_client_instance

    if _http_client_instance is None:
        timeout = httpx.Timeout(
            connect=10.0,  # Connection timeout
            read=30.0,     # Read timeout
            write=10.0,    # Write timeout
            pool=5.0       # Pool timeout
        )

        # Configure retry transport
        retries = httpx.Limits(
            max_keepalive_connections=20,
            max_connections=100,
            keepalive_expiry=30.0
        )

        _http_client_instance = httpx.AsyncClient(
            timeout=timeout,
            limits=retries,
            follow_redirects=True
        )

        logger.debug("Robust HTTP client initialized with retries and timeouts")

    return _http_client_instance


class AuthenticationError(Exception):
    """Base authentication error."""
    pass


class TokenExpiredError(AuthenticationError):
    """Token has expired."""
    pass


class InvalidTokenError(AuthenticationError):
    """Token is invalid or malformed."""
    pass


class InsufficientPermissionsError(AuthenticationError):
    """User doesn't have required permissions."""
    pass


async def get_current_user_from_supabase(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> SupabaseUser:
    """
    Extract and validate current user from Supabase JWT token with enhanced security.

    This dependency:
    1. Validates startup configuration
    2. Extracts JWT token from Authorization header
    3. Validates token signature with Supabase secret using configurable audience
    4. Parses user data from token payload
    5. Returns SupabaseUser instance

    Args:
        credentials: HTTP Bearer token from Authorization header

    Returns:
        SupabaseUser: Validated user data from token

    Raises:
        HTTPException: If token is invalid, expired, or malformed
    """
    try:
        # Validate startup configuration
        validate_auth_startup()

        token = credentials.credentials

        # Security: Log authentication attempt without token data
        logger.debug("Processing JWT token authentication request")

        # Decode and validate JWT token with configurable audience
        try:
            if not SUPABASE_JWT_SECRET:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="JWT secret not configured"
                )

            payload = jwt.decode(
                token,
                SUPABASE_JWT_SECRET,
                algorithms=["HS256"],
                audience=JWT_AUDIENCE,
                options={
                    "verify_signature": True,
                    "verify_exp": True,
                    "verify_aud": True,
                    "require": ["sub", "email", "exp", "iat"]
                }
            )
        except jwt.ExpiredSignatureError:
            logger.warning("Authentication failed: JWT token has expired")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.InvalidAudienceError:
            logger.warning(f"Authentication failed: Invalid audience (expected: {JWT_AUDIENCE})")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token audience",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.InvalidTokenError as e:
            logger.warning(f"Authentication failed: Invalid JWT token - {type(e).__name__}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Extract and validate required user data from JWT payload
        user_id = payload.get("sub")
        email = payload.get("email")

        if not user_id or not email:
            logger.error("Authentication failed: JWT token missing required user data")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Validate user_id format (should be UUID)
        try:
            uuid_obj = UUID(user_id)
        except ValueError:
            logger.error("Authentication failed: Invalid user ID format")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid user identifier",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Parse additional user metadata with safe defaults
        user_metadata = payload.get("user_metadata", {})
        app_metadata = payload.get("app_metadata", {})

        # Ensure metadata are dictionaries
        if not isinstance(user_metadata, dict):
            user_metadata = {}
        if not isinstance(app_metadata, dict):
            app_metadata = {}

        # Convert timestamps with proper timezone handling
        iat = payload.get("iat")
        exp = payload.get("exp")
        email_confirmed_at = payload.get("email_confirmed_at")
        last_sign_in_at = payload.get("last_sign_in_at")

        # Validate token expiration for security logging
        if exp:
            exp_datetime = datetime.fromtimestamp(exp, timezone.utc)
            time_to_expiry = (exp_datetime - datetime.now(timezone.utc)).total_seconds()

            # Log if token expires soon (less than 5 minutes)
            if time_to_expiry < 300:
                logger.info(f"Token for user {email} expires in {int(time_to_expiry)}s")

        # Create SupabaseUser instance with validated data
        supabase_user = SupabaseUser(
            id=uuid_obj,
            email=email,
            email_confirmed_at=datetime.fromtimestamp(email_confirmed_at, timezone.utc) if email_confirmed_at else None,
            created_at=datetime.fromtimestamp(iat, timezone.utc) if iat else datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            last_sign_in_at=datetime.fromtimestamp(last_sign_in_at, timezone.utc) if last_sign_in_at else None,
            user_metadata=user_metadata,
            app_metadata=app_metadata
        )

        # Security: Log successful authentication without sensitive data
        logger.info(f"Authentication successful for user: {email}")
        return supabase_user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected authentication error: {type(e).__name__}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error"
        )


async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[SupabaseUser]:
    """
    Optional authentication dependency that returns None if no token provided.

    Useful for endpoints that work for both authenticated and anonymous users.

    Args:
        credentials: Optional HTTP Bearer token

    Returns:
        Optional[SupabaseUser]: User data if authenticated, None otherwise
    """
    if not credentials:
        return None

    try:
        return await get_current_user_from_supabase(credentials)
    except HTTPException:
        # Return None instead of raising exception for optional auth
        return None


async def require_admin_user(
    current_user: SupabaseUser = Depends(get_current_user_from_supabase)
) -> SupabaseUser:
    """
    Dependency that ensures current user has admin privileges with enhanced validation.

    Checks for admin role in both app_metadata and user_metadata with proper
    role hierarchy and permission validation.

    Args:
        current_user: Current authenticated user from Supabase

    Returns:
        SupabaseUser: Verified admin user

    Raises:
        HTTPException: If user is not an admin or lacks required permissions
    """
    # Enhanced admin role validation with multiple checks
    admin_indicators = [
        # Primary admin role indicators (preferred)
        current_user.app_metadata.get("role") == "admin",
        current_user.app_metadata.get("admin") is True,
        current_user.app_metadata.get("is_admin") is True,

        # Secondary admin indicators (fallback)
        current_user.user_metadata.get("role") == "admin",
        current_user.user_metadata.get("admin") is True,
        current_user.user_metadata.get("is_admin") is True,

        # Permission-based admin check
        "admin" in current_user.app_metadata.get("roles", []),
        "administrator" in current_user.app_metadata.get("roles", []),
    ]

    is_admin = any(admin_indicators)

    if not is_admin:
        # Security: Log admin access attempt without exposing user data structure
        logger.warning(
            f"Admin access denied for user {current_user.email}: insufficient privileges"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Administrative privileges required"
        )

    # Additional security check for critical admin operations
    # Verify account is email confirmed for admin actions
    if not current_user.email_confirmed_at:
        logger.warning(
            f"Admin access denied for user {current_user.email}: email not confirmed"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email verification required for administrative access"
        )

    logger.info(f"Admin access granted to verified user: {current_user.email}")
    return current_user


async def require_verified_email(
    current_user: SupabaseUser = Depends(get_current_user_from_supabase)
) -> SupabaseUser:
    """
    Dependency that ensures current user has verified their email.

    Args:
        current_user: Current authenticated user

    Returns:
        SupabaseUser: User with verified email

    Raises:
        HTTPException: If email is not verified
    """
    if not current_user.email_confirmed_at:
        logger.warning(f"Unverified user {current_user.email} attempted verified-only operation")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email verification required"
        )

    return current_user


async def require_user_permissions(permissions: list[str]):
    """
    Factory function to create permission-checking dependencies.

    Args:
        permissions: List of required permissions

    Returns:
        Dependency function that checks permissions
    """
    async def permission_checker(
        current_user: SupabaseUser = Depends(get_current_user_from_supabase)
    ) -> SupabaseUser:
        # Get user permissions from metadata
        user_permissions = (
            current_user.app_metadata.get("permissions", []) +
            current_user.user_metadata.get("permissions", [])
        )

        # Check if user has all required permissions
        missing_permissions = [p for p in permissions if p not in user_permissions]

        if missing_permissions:
            logger.warning(
                f"User {current_user.email} missing permissions: {missing_permissions}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )

        return current_user

    return permission_checker


# ==================================================================================
# DEVELOPMENT/TESTING DEPENDENCIES
# ==================================================================================

async def get_mock_current_user() -> SupabaseUser:
    """
    Mock authentication dependency for development/testing.

    Returns a mock user for development environments when ENVIRONMENT=development.
    Should never be used in production!

    Returns:
        SupabaseUser: Mock user data
    """
    environment = os.getenv("ENVIRONMENT", "production").lower()

    if environment != "development":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Mock authentication only available in development"
        )

    from uuid import uuid4

    return SupabaseUser(
        id=uuid4(),
        email="<EMAIL>",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        email_confirmed_at=datetime.now(timezone.utc),
        user_metadata={
            "full_name": "Development User",
            "username": "devuser"
        },
        app_metadata={"role": "admin"}  # Give dev user admin rights
    )


async def get_mock_admin_user() -> SupabaseUser:
    """
    Mock admin user for development/testing.

    Returns:
        SupabaseUser: Mock admin user
    """
    user = await get_mock_current_user()
    user.app_metadata["role"] = "admin"
    user.email = "<EMAIL>"
    user.user_metadata["username"] = "adminuser"
    user.user_metadata["full_name"] = "Admin User"
    return user


# ==================================================================================
# ENVIRONMENT-AWARE DEPENDENCY SELECTION
# ==================================================================================

def get_auth_dependency():
    """
    Get the appropriate authentication dependency based on environment.

    Returns:
        Callable: Authentication dependency function
    """
    environment = os.getenv("ENVIRONMENT", "production").lower()

    if environment == "development" and os.getenv("USE_MOCK_AUTH", "false").lower() == "true":
        logger.warning("Using mock authentication in development mode!")
        return get_mock_current_user

    return get_current_user_from_supabase


def get_admin_dependency():
    """
    Get the appropriate admin authentication dependency based on environment.

    Returns:
        Callable: Admin authentication dependency function
    """
    environment = os.getenv("ENVIRONMENT", "production").lower()

    if environment == "development" and os.getenv("USE_MOCK_AUTH", "false").lower() == "true":
        logger.warning("Using mock admin authentication in development mode!")
        return get_mock_admin_user

    return require_admin_user


# ==================================================================================
# TOKEN UTILITIES
# ==================================================================================

def create_access_token(user_data: dict, expires_delta: Optional[int] = None) -> str:
    """
    Create a JWT access token with enhanced security (for testing/development purposes).

    Note: In production, tokens should be created by Supabase Auth service.
    This function is primarily for testing and development environments.

    Args:
        user_data: User data to encode in token
        expires_delta: Token expiration in seconds (default: 3600)

    Returns:
        str: JWT token

    Raises:
        ValueError: If required configuration is missing
        RuntimeError: If token creation fails
    """
    if not SUPABASE_JWT_SECRET:
        raise ValueError("JWT secret not configured - cannot create tokens")

    try:
        now = datetime.now(timezone.utc)
        expire_seconds = expires_delta or 3600  # Default 1 hour
        expire = now.timestamp() + expire_seconds

        # Validate required user data
        if not user_data.get("id") or not user_data.get("email"):
            raise ValueError("User data must include 'id' and 'email'")

        payload = {
            "sub": str(user_data.get("id")),
            "email": user_data.get("email"),
            "iat": now.timestamp(),
            "exp": expire,
            "aud": JWT_AUDIENCE,
            "iss": "ai-coding-agent",  # Add issuer for additional validation
            "user_metadata": user_data.get("user_metadata", {}),
            "app_metadata": user_data.get("app_metadata", {})
        }

        # Optional fields with defaults
        if user_data.get("email_confirmed_at"):
            payload["email_confirmed_at"] = user_data["email_confirmed_at"]
        if user_data.get("last_sign_in_at"):
            payload["last_sign_in_at"] = user_data["last_sign_in_at"]

        token = jwt.encode(payload, SUPABASE_JWT_SECRET, algorithm="HS256")

        logger.debug(f"Created JWT token for user {user_data.get('email')} (expires in {expire_seconds}s)")
        return token

    except Exception as e:
        logger.error(f"Failed to create JWT token: {str(e)}")
        raise RuntimeError(f"Token creation failed: {str(e)}")


async def require_service_account(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> None:
    """
    Dependency that ensures the request is from a trusted service account with enhanced validation.

    Validates service account tokens with additional security checks including
    issuer validation and service-specific claims.

    Args:
        credentials: HTTP Bearer token from Authorization header

    Raises:
        HTTPException: If token is invalid or not from a service account
    """
    try:
        validate_auth_startup()

        token = credentials.credentials
        logger.debug("Validating service account token")

        # Decode with strict validation
        if not SUPABASE_JWT_SECRET:
            logger.error("JWT secret not configured for service account validation")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication service misconfigured"
            )

        payload = jwt.decode(
            token,
            SUPABASE_JWT_SECRET,
            algorithms=["HS256"],
            audience=JWT_AUDIENCE,
            options={
                "verify_signature": True,
                "verify_exp": True,
                "verify_aud": True,
                "require": ["sub", "exp", "iat", "aud"]
            }
        )

        # Validate service account claims
        is_service_account = (
            payload.get("is_service_account") is True or
            payload.get("role") == "service_account" or
            payload.get("aud") == "service_account" or
            "service_account" in payload.get("roles", [])
        )

        if not is_service_account:
            logger.warning("Invalid service account token: missing service account claims")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Valid service account token required"
            )

        # Additional validation for service account tokens
        service_name = payload.get("service_name")
        if service_name:
            logger.info(f"Service account access granted to service: {service_name}")
        else:
            logger.info("Service account access granted to unnamed service")

    except jwt.ExpiredSignatureError:
        logger.warning("Service account token expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Service account token has expired"
        )
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid service account token: {type(e).__name__}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid service account token"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Service account validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Service account validation failed"
        )

async def validate_supabase_connection() -> dict:
    """
    Validate connection to Supabase Auth service with comprehensive health checking.

    Returns:
        dict: Detailed connection status and service health metadata
    """
    try:
        # Initial configuration validation
        if not SUPABASE_URL or not SUPABASE_ANON_KEY:
            return {
                "status": "misconfigured",
                "message": "Supabase credentials not properly configured",
                "missing_config": [
                    "SUPABASE_URL" if not SUPABASE_URL else None,
                    "SUPABASE_ANON_KEY" if not SUPABASE_ANON_KEY else None
                ],
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        # Test connection to Supabase Auth with enhanced error handling
        client = await get_robust_http_client()

        # Test auth service health endpoint
        health_response = await client.get(
            f"{SUPABASE_URL}/auth/v1/health",
            headers={
                "apikey": SUPABASE_ANON_KEY,
                "Authorization": f"Bearer {SUPABASE_ANON_KEY}"
            },
            timeout=10.0
        )

        if health_response.status_code == 200:
            health_data = health_response.json()

            # Also test settings endpoint for additional validation
            settings_response = await client.get(
                f"{SUPABASE_URL}/auth/v1/settings",
                headers={
                    "apikey": SUPABASE_ANON_KEY,
                    "Authorization": f"Bearer {SUPABASE_ANON_KEY}"
                },
                timeout=5.0
            )

            return {
                "status": "healthy",
                "message": "Supabase Auth service is accessible and responding",
                "auth_health": health_data,
                "settings_accessible": settings_response.status_code == 200,
                "response_time_ms": health_response.elapsed.total_seconds() * 1000,
                "supabase_url": SUPABASE_URL,
                "jwt_audience": JWT_AUDIENCE,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        else:
            return {
                "status": "unhealthy",
                "message": f"Supabase Auth health check failed with status {health_response.status_code}",
                "status_code": health_response.status_code,
                "response_text": health_response.text[:200] if health_response.text else None,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    except httpx.TimeoutException:
        logger.error("Supabase connection timeout")
        return {
            "status": "timeout",
            "message": "Supabase Auth service connection timeout",
            "supabase_url": SUPABASE_URL,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except httpx.ConnectError as e:
        logger.error(f"Supabase connection error: {str(e)}")
        return {
            "status": "connection_error",
            "message": f"Cannot connect to Supabase Auth service: {str(e)}",
            "supabase_url": SUPABASE_URL,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f"Supabase connection test failed: {str(e)}")
        return {
            "status": "error",
            "message": f"Supabase connection test failed: {str(e)}",
            "error_type": type(e).__name__,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }