# syntax=docker/dockerfile:1
# Optimized Development Dockerfile for User Portal (Next.js)
# Layer caching optimization + hot-reload ready

FROM node:20-alpine AS development

# Accept build arguments for UID/GID to match host user
ARG UID=1000
ARG GID=1000

# Install necessary utilities for compatibility
RUN apk add --no-cache libc6-compat curl

# Create non-root user with unique UID/GID to avoid conflicts
RUN addgroup -g 1001 -S nextjs \
  && adduser -u 1001 -S nextjs -G nextjs

# Set working directory
WORKDIR /home/<USER>/app

# ============================================================================
# LAYER CACHING OPTIMIZATION: Install dependencies BEFORE copying source code
# ============================================================================

# 1. Copy ONLY package manifest files first (this layer will be cached)
COPY --chown=nextjs:nextjs package*.json ./

# Create essential directories with proper permissions BEFORE switching user
RUN mkdir -p .next node_modules public \
  && chown -R nextjs:nextjs /home/<USER>/app

# Switch to nextjs user for dependency installation
USER nextjs

# 2. Install dependencies (this layer will only rebuild if package.json changes)
RUN npm install --legacy-peer-deps

# ============================================================================
# SOURCE CODE: Handled by compose watch in development
# ============================================================================

# Create placeholder files to prevent permission issues
USER root
RUN printf '// Generated by Docker build\n/// <reference types="next" />\n/// <reference types="next/image-types/global" />\n' > ./next-env.d.ts \
  && chown nextjs:nextjs ./next-env.d.ts \
  && chmod 644 ./next-env.d.ts

# Switch back to nextjs user
USER nextjs

# ============================================================================
# DEVELOPMENT CONFIGURATION
# ============================================================================

# Development environment variables
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
ENV FAST_REFRESH=true
ENV WATCHPACK_POLLING=true
ENV CHOKIDAR_USEPOLLING=true
ENV CHOKIDAR_INTERVAL=1000

# Expose application port
EXPOSE 3000

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Labels for development
LABEL org.opencontainers.image.title="AI Coding Agent - User Portal (Dev Optimized)" \
  org.opencontainers.image.description="Next.js development environment with layer caching optimization" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  security.non-root="true" \
  security.user="nextjs" \
  development.hot-reload="enabled"

# Note: Source code will be mounted via compose watch
# Start development server with hot-reload
CMD ["npm", "run", "dev"]
