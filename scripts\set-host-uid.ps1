# Get host user ID and group ID for Docker UID synchronization
# Run this in PowerShell to set environment variables for docker-compose

# On Windows, the default user is typically UID 1000, GID 1000
# You can verify with: id (if using WSL) or check your user properties

$env:HOST_UID = "1000"
$env:HOST_GID = "1000"

# Export for docker-compose
Write-Host "Set HOST_UID=$env:HOST_UID and HOST_GID=$env:HOST_GID"
Write-Host "Run: docker compose -f docker-compose.dev.yml up --build user-portal"
