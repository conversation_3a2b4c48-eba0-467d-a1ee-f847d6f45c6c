"""
Storage Cleanup Service for AI Coding Agent.

This module provides automatic cleanup functionality for managing storage
quotas and removing soft-deleted or inactive project resources.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

# Internal imports
try:
    from src.models.database import get_db
    from src.models.project import Project
    from src.models.user import UserProfile
    from src.services.supabase_service import SupabaseService
    from src.core.config import settings
except ImportError:
    # Fallback for direct execution
    from src.models.database import get_db
    from src.models.project import Project
    from src.models.user import UserProfile
    from src.services.supabase_service import SupabaseService
    from src.core.config import settings

logger = logging.getLogger(__name__)


class StorageCleanupService:
    """
    Service for automatic cleanup of storage resources.

    Handles periodic cleanup of:
    - Soft-deleted projects and their documents
    - Inactive projects beyond retention period
    - Orphaned vector embeddings and chunks
    """

    def __init__(
        self,
        db: Session,
        supabase_service: SupabaseService
    ):
        """
        Initialize storage cleanup service.

        Args:
            db: SQLAlchemy database session
            supabase_service: Supabase service instance
        """
        self.db = db
        self.supabase_service = supabase_service

    async def cleanup_expired_projects(self) -> Dict[str, Any]:
        """
        Clean up soft-deleted or inactive projects beyond retention period.

        Returns:
            Dict with cleanup statistics or error message
        """
        try:
            logger.info("Starting cleanup of expired projects")

            retention_days = settings.STORAGE_CLEANUP_RETENTION_DAYS
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)

            # Find projects to clean up
            expired_projects = self.db.query(Project).filter(
                Project.status.in_(['deleted', 'inactive']),
                Project.updated_at < cutoff_date
            ).all()

            cleanup_stats: Dict[str, Any] = {
                'projects_cleaned': 0,
                'documents_deleted': 0,
                'chunks_deleted': 0,
                'storage_freed_bytes': 0
            }

            # Create task list for concurrent cleanup if needed
            project_tasks: List[Dict[str, Any]] = []

            for project in expired_projects:
                try:
                    project_stats = await self._cleanup_project(project)
                    cleanup_stats['projects_cleaned'] += 1
                    cleanup_stats['documents_deleted'] += project_stats['documents_deleted']
                    cleanup_stats['chunks_deleted'] += project_stats['chunks_deleted']
                    cleanup_stats['storage_freed_bytes'] += project_stats['storage_freed_bytes']

                    # Permanently delete project from database
                    self.db.delete(project)

                    # Track completed tasks
                    project_tasks.append({
                        'project_id': project.id,
                        'status': 'completed',
                        'stats': project_stats
                    })

                except Exception as e:
                    logger.error(f"Failed to cleanup project {project.id}: {e}")
                    project_tasks.append({
                        'project_id': project.id,
                        'status': 'failed',
                        'error': str(e)
                    })
                    continue

            self.db.commit()

            # Add task summary to cleanup stats
            cleanup_stats['task_summary'] = project_tasks

            logger.info(f"Cleanup completed: {cleanup_stats}")
            return cleanup_stats

        except Exception as e:
            logger.error(f"Failed to cleanup expired projects: {e}")
            self.db.rollback()
            return {'error': str(e)}

    async def _cleanup_project(self, project: Project) -> Dict[str, int]:
        """
        Clean up all resources for a specific project.

        Args:
            project: Project to clean up

        Returns:
            Dict with project cleanup statistics
        """
        try:
            project_id = str(project.id)
            stats = {
                'documents_deleted': 0,
                'chunks_deleted': 0,
                'storage_freed_bytes': 0
            }

            # Get all documents for this project
            query = """
                SELECT id, content
                FROM documents
                WHERE project_id = $1
            """

            documents = await self.supabase_service.execute_query(
                query,
                project_id,
                fetch_type='all'
            )

            if documents and isinstance(documents, list):
                # Use asyncio for cleanup timing and task coordination
                cleanup_start = asyncio.get_event_loop().time()
                document_tasks: List[str] = []  # Track processed document IDs

                # Extract document IDs safely
                document_ids = []
                for doc in documents:
                    if isinstance(doc, dict) and 'id' in doc:
                        document_ids.append(doc['id'])
                        document_tasks.append(f"doc_{doc['id']}")  # Track processing

                if document_ids:
                    # Delete document chunks
                    chunks_query = """
                        DELETE FROM document_chunks
                        WHERE document_id = ANY($1)
                    """
                    await self.supabase_service.execute_query(
                        chunks_query,
                        document_ids,
                        fetch_type='none'
                    )
                    stats['chunks_deleted'] = len(document_ids)

                    # Delete documents
                    docs_query = """
                        DELETE FROM documents
                        WHERE id = ANY($1)
                    """
                    await self.supabase_service.execute_query(
                        docs_query,
                        document_ids,
                        fetch_type='none'
                    )
                    stats['documents_deleted'] = len(documents)

                    # Calculate storage freed
                    for doc in documents:
                        if isinstance(doc, dict) and doc.get('content') and isinstance(doc['content'], str):
                            stats['storage_freed_bytes'] += len(doc['content'].encode('utf-8'))

                    cleanup_duration = asyncio.get_event_loop().time() - cleanup_start
                    logger.debug(f"Processed document tasks: {len(document_tasks)} in {cleanup_duration:.2f}s")

            logger.info(f"Cleaned up project {project_id}: {stats}")
            return stats

        except Exception as e:
            logger.error(f"Failed to cleanup project {project.id}: {e}")
            return {'documents_deleted': 0, 'chunks_deleted': 0, 'storage_freed_bytes': 0}

    async def update_user_storage_usage(self) -> None:
        """
        Update storage usage for all users based on their documents.
        """
        try:
            logger.info("Updating storage usage for all users")

            # Get all users
            users = self.db.query(UserProfile).all()

            for user in users:
                try:
                    # Calculate total storage usage from documents
                    query = """
                        SELECT COALESCE(SUM(CHAR_LENGTH(d.content)), 0) as total_bytes
                        FROM documents d
                        JOIN projects p ON d.project_id = p.id
                        WHERE p.owner_id = $1
                    """

                    result = await self.supabase_service.execute_query(
                        query,
                        str(user.id),
                        fetch_type='one'
                    )

                    total_bytes = 0
                    if isinstance(result, dict) and 'total_bytes' in result:
                        total_bytes = result['total_bytes'] or 0

                    # Update user's storage usage
                    user.storage_usage_bytes = total_bytes
                    logger.debug(f"Updated storage usage for user {user.id}: {total_bytes} bytes")

                except Exception as e:
                    logger.error(f"Failed to update storage usage for user {user.id}: {e}")
                    continue

            self.db.commit()
            logger.info("Storage usage update completed")

        except Exception as e:
            logger.error(f"Failed to update user storage usage: {e}")
            self.db.rollback()

    async def get_storage_report(self) -> Dict[str, Any]:
        """
        Generate a storage usage report.

        Returns:
            Dict with storage statistics
        """
        try:
            # Get total storage usage
            query = """
                SELECT
                    COUNT(DISTINCT p.owner_id) as total_users,
                    COUNT(DISTINCT p.id) as total_projects,
                    COUNT(d.id) as total_documents,
                    COALESCE(SUM(CHAR_LENGTH(d.content)), 0) as total_bytes
                FROM projects p
                LEFT JOIN documents d ON p.id = d.project_id
            """

            result = await self.supabase_service.execute_query(
                query,
                fetch_type='one'
            )

            report: Dict[str, Any] = {
                'total_users': 0,
                'total_projects': 0,
                'total_documents': 0,
                'total_storage_bytes': 0,
                'average_storage_per_user': 0.0
            }

            if isinstance(result, dict):
                report.update({
                    'total_users': result.get('total_users', 0),
                    'total_projects': result.get('total_projects', 0),
                    'total_documents': result.get('total_documents', 0),
                    'total_storage_bytes': result.get('total_bytes', 0)
                })

                if report['total_users'] > 0:
                    report['average_storage_per_user'] = report['total_storage_bytes'] / report['total_users']

            return report

        except Exception as e:
            logger.error(f"Failed to generate storage report: {e}")
            return {'error': str(e)}


# Dependency injection function
async def get_cleanup_service_async(
    db: Session = get_db(),
    supabase_service: Optional[SupabaseService] = None
) -> StorageCleanupService:
    """Async dependency injection for StorageCleanupService."""
    if supabase_service is None:
        from src.services.supabase_service import get_supabase_service
        supabase_service = await get_supabase_service()
    return StorageCleanupService(db=db, supabase_service=supabase_service)

def get_cleanup_service(
    db: Session = get_db(),
    supabase_service: Optional[SupabaseService] = None
) -> StorageCleanupService:
    """Synchronous dependency injection for StorageCleanupService."""
    if supabase_service is None:
        from src.services.supabase_service import SupabaseService
        # Create service with default config (loads from environment)
        supabase_service = SupabaseService()
    return StorageCleanupService(db=db, supabase_service=supabase_service)
