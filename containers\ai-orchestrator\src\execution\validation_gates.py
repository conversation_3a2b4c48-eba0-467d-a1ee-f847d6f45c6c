# Project: AI Coding Agent
# Purpose: Validation gates system for controlling pipeline progression

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from enum import Enum

from src.models.validation_models import (
    ValidationGate, ValidationGateStatus, ValidationResult, ExecutionState,
    ExecutionPipeline, ApprovalRequest, ApprovalStatus
)
from src.validation.validation_rules import ValidationRuleEngine
from src.core.retry_strategies import with_retry, RetryStrategy


class GateType(str, Enum):
    """Types of validation gates"""
    AUTOMATIC = "automatic"
    MANUAL = "manual"
    APPROVAL = "approval"
    CONDITIONAL = "conditional"
    HYBRID = "hybrid"


class ValidationGateError(Exception):
    """Exception raised when validation gate fails"""
    pass


class ValidationGateManager:
    """
    Manages validation gates and their execution in pipelines.

    Provides:
    - Automatic validation gates with rule execution
    - Manual validation gates requiring human intervention
    - Approval gates with user approval workflows
    - Conditional gates with custom logic
    - Gate timeout and fallback handling
    """

    def __init__(self):
        self.logger = logging.getLogger("validation_gate_manager")

        # Gate storage and tracking
        self.gates: Dict[str, ValidationGate] = {}
        self.gate_handlers: Dict[str, Callable] = {}
        self.pending_approvals: Dict[str, ApprovalRequest] = {}

        # Validation engine
        self.validation_engine = ValidationRuleEngine()

        # Gate execution tracking
        self.gate_execution_history: List[Dict[str, Any]] = []

        # Performance metrics
        self.gate_metrics = {
            "total_gates_executed": 0,
            "gates_passed": 0,
            "gates_failed": 0,
            "gates_bypassed": 0,
            "average_execution_time": 0.0
        }

        # Register default gate handlers
        self._register_default_handlers()

        self.logger.info("Validation Gate Manager initialized")

    def _register_default_handlers(self):
        """Register default gate type handlers"""

        self.gate_handlers = {
            GateType.AUTOMATIC: self._execute_automatic_gate,
            GateType.MANUAL: self._execute_manual_gate,
            GateType.APPROVAL: self._execute_approval_gate,
            GateType.CONDITIONAL: self._execute_conditional_gate,
            GateType.HYBRID: self._execute_hybrid_gate
        }

    async def execute_gate(self,
                          gate: ValidationGate,
                          pipeline: ExecutionPipeline,
                          execution_state: ExecutionState,
                          context: Optional[Dict[str, Any]] = None) -> bool:
        """
        Execute validation gate.

        Args:
            gate: Validation gate to execute
            pipeline: Pipeline context
            execution_state: Current execution state
            context: Additional context data

        Returns:
            bool: True if gate passes, False if it fails
        """

        self.logger.info(f"Executing validation gate: {gate.name} (type: {gate.gate_type})")

        gate.status = ValidationGateStatus.VALIDATING
        gate.opened_at = datetime.now()

        start_time = time.time()

        try:
            # Get gate handler
            handler = self.gate_handlers.get(gate.gate_type)
            if not handler:
                raise ValueError(f"Unknown gate type: {gate.gate_type}")

            # Execute gate with timeout
            if gate.timeout_seconds:
                result = await asyncio.wait_for(
                    handler(gate, pipeline, execution_state, context or {}),
                    timeout=gate.timeout_seconds
                )
            else:
                result = await handler(gate, pipeline, execution_state, context or {})

            # Update gate status
            if result:
                gate.status = ValidationGateStatus.PASSED
                self.gate_metrics["gates_passed"] += 1
                self.logger.info(f"Validation gate passed: {gate.name}")
            else:
                gate.status = ValidationGateStatus.FAILED
                self.gate_metrics["gates_failed"] += 1
                self.logger.warning(f"Validation gate failed: {gate.name}")

            gate.closed_at = datetime.now()

            # Record execution
            execution_time = time.time() - start_time
            self._record_gate_execution(gate, result, execution_time, context)

            return result

        except asyncio.TimeoutError:
            gate.status = ValidationGateStatus.FAILED
            gate.closed_at = datetime.now()

            self.logger.error(f"Validation gate timed out: {gate.name}")
            self.gate_metrics["gates_failed"] += 1

            return False

        except Exception as e:
            gate.status = ValidationGateStatus.FAILED
            gate.closed_at = datetime.now()

            self.logger.error(f"Validation gate error: {gate.name} - {str(e)}")
            self.gate_metrics["gates_failed"] += 1

            return False

        finally:
            self.gate_metrics["total_gates_executed"] += 1

            # Update average execution time
            execution_time = time.time() - start_time
            total_gates = self.gate_metrics["total_gates_executed"]
            current_avg = self.gate_metrics["average_execution_time"]

            new_avg = ((current_avg * (total_gates - 1)) + execution_time) / total_gates
            self.gate_metrics["average_execution_time"] = new_avg

    @with_retry("standard", RetryStrategy.EXPONENTIAL_BACKOFF)
    async def _execute_automatic_gate(self,
                                    gate: ValidationGate,
                                    pipeline: ExecutionPipeline,
                                    execution_state: ExecutionState,
                                    context: Dict[str, Any]) -> bool:
        """Execute automatic validation gate with rules"""

        self.logger.debug(f"Executing automatic gate: {gate.name}")

        validation_results = []

        # Execute validation rules
        for rule_name in gate.validation_rules:
            try:
                result = await self._execute_validation_rule(
                    rule_name, gate, pipeline, execution_state, context
                )
                validation_results.append(result)

            except Exception as e:
                self.logger.error(f"Validation rule error: {rule_name} - {str(e)}")
                validation_results.append(
                    ValidationResult.failure(f"Rule execution error: {str(e)}")
                )

        # Execute custom validators
        for validator_name in gate.custom_validators:
            try:
                result = await self._execute_custom_validator(
                    validator_name, gate, pipeline, execution_state, context
                )
                validation_results.append(result)

            except Exception as e:
                self.logger.error(f"Custom validator error: {validator_name} - {str(e)}")
                validation_results.append(
                    ValidationResult.failure(f"Validator execution error: {str(e)}")
                )

        # Store results
        gate.validation_results = validation_results

        # Check if all validations passed
        return all(result.is_valid for result in validation_results)

    async def _execute_manual_gate(self,
                                 gate: ValidationGate,
                                 pipeline: ExecutionPipeline,
                                 execution_state: ExecutionState,
                                 context: Dict[str, Any]) -> bool:
        """Execute manual validation gate requiring human intervention"""

        self.logger.info(f"Manual gate requires human intervention: {gate.name}")

        # Create manual review request
        review_request = {
            "gate_id": gate.id,
            "gate_name": gate.name,
            "pipeline_id": pipeline.id,
            "pipeline_name": pipeline.name,
            "description": gate.description,
            "context": context,
            "requested_at": datetime.now(),
            "timeout_at": datetime.now() + timedelta(seconds=gate.timeout_seconds or 3600)
        }

        # Store review request (in real implementation, this would trigger notifications)
        self.pending_approvals[gate.id] = review_request

        # Wait for manual approval (simplified implementation)
        # In production, this would integrate with notification systems

        timeout_seconds = gate.timeout_seconds or 3600
        poll_interval = 30  # Check every 30 seconds

        elapsed = 0
        while elapsed < timeout_seconds:
            # Check if approval was provided (would be updated by external system)
            if gate.id in self._get_completed_manual_reviews():
                approval_result = self._get_manual_review_result(gate.id)
                self.logger.info(f"Manual gate decision received: {gate.name} - {approval_result}")
                return approval_result

            await asyncio.sleep(poll_interval)
            elapsed += poll_interval

        # Timeout reached
        self.logger.warning(f"Manual gate timed out: {gate.name}")
        return False

    async def _execute_approval_gate(self,
                                   gate: ValidationGate,
                                   pipeline: ExecutionPipeline,
                                   execution_state: ExecutionState,
                                   context: Dict[str, Any]) -> bool:
        """Execute approval gate with user approval workflow"""

        self.logger.info(f"Approval gate requires user approval: {gate.name}")

        # Create approval request
        approval_request = ApprovalRequest(
            user_id=pipeline.user_id,
            item_type="validation_gate",
            item_id=gate.id,
            title=f"Approval Required: {gate.name}",
            description=gate.description,
            timeout_at=datetime.now() + timedelta(seconds=gate.timeout_seconds or 3600),
            changes_summary=self._generate_changes_summary(pipeline, execution_state, context),
            risk_assessment=self._assess_gate_risk(gate, pipeline, context)
        )

        # Store approval request
        self.pending_approvals[approval_request.id] = approval_request

        # Wait for user approval
        return await self._wait_for_approval(approval_request)

    async def _execute_conditional_gate(self,
                                      gate: ValidationGate,
                                      pipeline: ExecutionPipeline,
                                      execution_state: ExecutionState,
                                      context: Dict[str, Any]) -> bool:
        """Execute conditional validation gate with custom logic"""

        self.logger.debug(f"Executing conditional gate: {gate.name}")

        # Execute conditions defined in gate metadata
        conditions = gate.metadata.get("conditions", [])

        for condition in conditions:
            condition_type = condition.get("type")
            condition_config = condition.get("config", {})

            if condition_type == "execution_time":
                # Check if execution time is within limits
                max_time = condition_config.get("max_seconds", 3600)
                if pipeline.started_at:
                    elapsed = (datetime.now() - pipeline.started_at).total_seconds()
                    if elapsed > max_time:
                        return False

            elif condition_type == "progress_threshold":
                # Check if progress meets threshold
                min_progress = condition_config.get("min_percentage", 0)
                if execution_state.progress_percentage < min_progress:
                    return False

            elif condition_type == "error_count":
                # Check error count in context
                max_errors = condition_config.get("max_errors", 5)
                error_count = context.get("error_count", 0)
                if error_count > max_errors:
                    return False

            elif condition_type == "custom_check":
                # Execute custom condition check
                check_name = condition_config.get("check_name")
                result = await self._execute_custom_condition_check(
                    check_name, gate, pipeline, execution_state, context
                )
                if not result:
                    return False

        return True

    async def _execute_hybrid_gate(self,
                                 gate: ValidationGate,
                                 pipeline: ExecutionPipeline,
                                 execution_state: ExecutionState,
                                 context: Dict[str, Any]) -> bool:
        """Execute hybrid gate combining automatic validation with approval"""

        self.logger.info(f"Executing hybrid gate: {gate.name}")

        # First, run automatic validations
        auto_result = await self._execute_automatic_gate(gate, pipeline, execution_state, context)

        if not auto_result:
            # If automatic validation fails, check if manual override is allowed
            allow_override = gate.metadata.get("allow_manual_override", False)

            if allow_override:
                self.logger.info(f"Automatic validation failed, requesting manual override: {gate.name}")
                return await self._execute_approval_gate(gate, pipeline, execution_state, context)
            else:
                return False

        # If automatic validation passes, check if approval is still required
        require_approval_on_success = gate.metadata.get("require_approval_on_success", False)

        if require_approval_on_success:
            return await self._execute_approval_gate(gate, pipeline, execution_state, context)

        return True

    async def _execute_validation_rule(self,
                                     rule_name: str,
                                     gate: ValidationGate,
                                     pipeline: ExecutionPipeline,
                                     execution_state: ExecutionState,
                                     context: Dict[str, Any]) -> ValidationResult:
        """Execute individual validation rule"""

        # Standard validation rules
        if rule_name == "phase_completion":
            return await self._validate_phase_completion(pipeline, execution_state, context)
        elif rule_name == "integration_check":
            return await self._validate_integration_status(pipeline, execution_state, context)
        elif rule_name == "roadmap_completion":
            return await self._validate_roadmap_completion(pipeline, execution_state, context)
        elif rule_name == "production_readiness":
            return await self._validate_production_readiness(pipeline, execution_state, context)
        elif rule_name == "security_check":
            return await self._validate_security_requirements(pipeline, execution_state, context)
        elif rule_name == "performance_check":
            return await self._validate_performance_requirements(pipeline, execution_state, context)
        else:
            # Unknown rule
            return ValidationResult.failure(f"Unknown validation rule: {rule_name}")

    async def _execute_custom_validator(self,
                                      validator_name: str,
                                      gate: ValidationGate,
                                      pipeline: ExecutionPipeline,
                                      execution_state: ExecutionState,
                                      context: Dict[str, Any]) -> ValidationResult:
        """Execute custom validator"""

        # Custom validators would be loaded dynamically
        # For now, return success
        return ValidationResult.success(f"Custom validator {validator_name} passed")

    # Validation rule implementations

    async def _validate_phase_completion(self,
                                       pipeline: ExecutionPipeline,
                                       execution_state: ExecutionState,
                                       context: Dict[str, Any]) -> ValidationResult:
        """Validate that current phase is properly completed"""

        # Check if current stage is completed
        current_stage_id = execution_state.current_stage_id
        if not current_stage_id:
            return ValidationResult.failure("No current stage identified")

        # Find current stage
        current_stage = next((s for s in pipeline.stages if s.id == current_stage_id), None)
        if not current_stage:
            return ValidationResult.failure(f"Current stage not found: {current_stage_id}")

        if current_stage.status != "COMPLETED":
            return ValidationResult.failure(f"Current stage not completed: {current_stage.name}")

        return ValidationResult.success("Phase completion validated")

    async def _validate_integration_status(self,
                                         pipeline: ExecutionPipeline,
                                         execution_state: ExecutionState,
                                         context: Dict[str, Any]) -> ValidationResult:
        """Validate integration status"""

        # Check integration points defined in context
        integration_checks = context.get("integration_checks", [])

        for check in integration_checks:
            # Perform integration validation
            # This would involve actual integration testing
            pass

        return ValidationResult.success("Integration status validated")

    async def _validate_roadmap_completion(self,
                                         pipeline: ExecutionPipeline,
                                         execution_state: ExecutionState,
                                         context: Dict[str, Any]) -> ValidationResult:
        """Validate complete roadmap execution"""

        # Check if all stages are completed
        completed_stages = sum(1 for stage in pipeline.stages if stage.status == "COMPLETED")
        total_stages = len(pipeline.stages)

        if completed_stages != total_stages:
            return ValidationResult.failure(
                f"Not all stages completed: {completed_stages}/{total_stages}"
            )

        return ValidationResult.success("Roadmap completion validated")

    async def _validate_production_readiness(self,
                                           pipeline: ExecutionPipeline,
                                           execution_state: ExecutionState,
                                           context: Dict[str, Any]) -> ValidationResult:
        """Validate production readiness"""

        # Check production readiness criteria
        readiness_checks = [
            "security_configuration",
            "performance_benchmarks",
            "error_handling",
            "monitoring_setup",
            "backup_procedures"
        ]

        # Simulate production readiness checks
        failed_checks = []

        for check in readiness_checks:
            # Perform actual readiness check
            check_result = context.get(f"{check}_status", "passed")
            if check_result != "passed":
                failed_checks.append(check)

        if failed_checks:
            return ValidationResult.failure(
                f"Production readiness checks failed: {', '.join(failed_checks)}"
            )

        return ValidationResult.success("Production readiness validated")

    async def _validate_security_requirements(self,
                                            pipeline: ExecutionPipeline,
                                            execution_state: ExecutionState,
                                            context: Dict[str, Any]) -> ValidationResult:
        """Validate security requirements"""

        # Perform security validation
        security_issues = context.get("security_issues", [])

        if security_issues:
            return ValidationResult.failure(
                f"Security issues found: {', '.join(security_issues)}"
            )

        return ValidationResult.success("Security requirements validated")

    async def _validate_performance_requirements(self,
                                               pipeline: ExecutionPipeline,
                                               execution_state: ExecutionState,
                                               context: Dict[str, Any]) -> ValidationResult:
        """Validate performance requirements"""

        # Check performance metrics
        performance_metrics = context.get("performance_metrics", {})

        # Define performance thresholds
        thresholds = {
            "response_time_ms": 500,
            "throughput_rps": 100,
            "memory_usage_mb": 512,
            "cpu_usage_percent": 80
        }

        failed_metrics = []

        for metric, threshold in thresholds.items():
            actual_value = performance_metrics.get(metric, 0)
            if actual_value > threshold:
                failed_metrics.append(f"{metric}: {actual_value} > {threshold}")

        if failed_metrics:
            return ValidationResult.failure(
                f"Performance thresholds exceeded: {', '.join(failed_metrics)}"
            )

        return ValidationResult.success("Performance requirements validated")

    # Helper methods

    def _generate_changes_summary(self,
                                pipeline: ExecutionPipeline,
                                execution_state: ExecutionState,
                                context: Dict[str, Any]) -> List[str]:
        """Generate summary of changes for approval"""

        changes = [
            f"Pipeline: {pipeline.name}",
            f"Progress: {execution_state.progress_percentage:.1f}%",
            f"Current phase: {execution_state.current_phase}"
        ]

        # Add context-specific changes
        files_modified = context.get("files_modified", [])
        if files_modified:
            changes.append(f"Files modified: {len(files_modified)}")

        return changes

    def _assess_gate_risk(self,
                        gate: ValidationGate,
                        pipeline: ExecutionPipeline,
                        context: Dict[str, Any]) -> str:
        """Assess risk level for gate"""

        # Simple risk assessment based on gate type and context
        if "production" in gate.name.lower() or "deploy" in gate.name.lower():
            return "high"
        elif "test" in gate.name.lower() or "validation" in gate.name.lower():
            return "low"
        else:
            return "medium"

    async def _wait_for_approval(self, approval_request: ApprovalRequest) -> bool:
        """Wait for user approval with timeout"""

        timeout = (approval_request.timeout_at - datetime.now()).total_seconds()
        poll_interval = 10  # Check every 10 seconds

        elapsed = 0
        while elapsed < timeout:
            # Check if approval was provided
            if approval_request.status != ApprovalStatus.PENDING:
                return approval_request.status == ApprovalStatus.APPROVED

            await asyncio.sleep(poll_interval)
            elapsed += poll_interval

        # Timeout reached
        approval_request.status = ApprovalStatus.TIMEOUT
        return False

    async def _execute_custom_condition_check(self,
                                            check_name: str,
                                            gate: ValidationGate,
                                            pipeline: ExecutionPipeline,
                                            execution_state: ExecutionState,
                                            context: Dict[str, Any]) -> bool:
        """Execute custom condition check"""

        # Custom condition checks would be loaded dynamically
        # For now, return True
        return True

    def _get_completed_manual_reviews(self) -> List[str]:
        """Get list of completed manual review gate IDs"""
        # This would be populated by external review system
        return []

    def _get_manual_review_result(self, gate_id: str) -> bool:
        """Get result of manual review"""
        # This would fetch the actual review result
        return True

    def _record_gate_execution(self,
                             gate: ValidationGate,
                             result: bool,
                             execution_time: float,
                             context: Optional[Dict[str, Any]]) -> None:
        """Record gate execution for analysis"""

        record = {
            "timestamp": datetime.now().isoformat(),
            "gate_id": gate.id,
            "gate_name": gate.name,
            "gate_type": gate.gate_type,
            "result": result,
            "execution_time": execution_time,
            "validation_results": len(gate.validation_results),
            "context_size": len(context) if context else 0
        }

        self.gate_execution_history.append(record)

        # Keep only recent history
        if len(self.gate_execution_history) > 100:
            self.gate_execution_history = self.gate_execution_history[-100:]

    def get_gate_metrics(self) -> Dict[str, Any]:
        """Get validation gate metrics"""

        total_gates = self.gate_metrics["total_gates_executed"]
        success_rate = (
            self.gate_metrics["gates_passed"] / max(1, total_gates)
        ) * 100

        return {
            **self.gate_metrics,
            "success_rate_percent": round(success_rate, 2),
            "pending_approvals": len(self.pending_approvals),
            "recent_executions": len(self.gate_execution_history)
        }

    def bypass_gate(self, gate_id: str, reason: str) -> bool:
        """Bypass a validation gate with reason"""

        gate = self.gates.get(gate_id)
        if not gate:
            return False

        gate.status = ValidationGateStatus.BYPASSED
        gate.metadata = gate.metadata or {}
        gate.metadata["bypass_reason"] = reason
        gate.metadata["bypassed_at"] = datetime.now().isoformat()

        self.gate_metrics["gates_bypassed"] += 1

        self.logger.warning(f"Gate bypassed: {gate.name} - Reason: {reason}")
        return True