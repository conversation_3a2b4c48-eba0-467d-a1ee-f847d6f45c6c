# AI Coding Agent Environment Configuration Example
# Copy this file to .env and fill in the actual values.
# Never commit .env to version control.
# Variables are namespaced using Pydantic Settings conventions (e.g., PARENT__CHILD).

# =============================================================================
# CORE DATABASE & CACHING
# =============================================================================
# PostgreSQL Configuration
# In production, these should be handled via Docker Secrets.
# DB__POSTGRES_PASSWORD=<set-via-secrets/postgres_password.txt>
DB__POSTGRES_DB=ai_coding_agent
DB__URL=postgresql://postgres:${DB__POSTGRES_PASSWORD}@postgresql:5432/ai_coding_agent

# Redis Configuration
REDIS__URL=redis://redis:6379/0

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# Supabase Configuration
# In production, these should be handled via Docker Secrets.
# AUTH__SUPABASE_URL=<set-via-secrets/supabase_url.txt>
# AUTH__SUPABASE_KEY=<set-via-secrets/supabase_key.txt>
# AUTH__SUPABASE_SERVICE_KEY=<set-via-secrets/supabase_service_key.txt>

# JWT Configuration
# In production, this should be handled via Docker Secrets.
# AUTH__JWT_SECRET=<set-via-secrets/jwt_secret.txt>

# Security Configuration
SECURITY__ALLOWED_HOSTS=localhost,127.0.0.1,ai-orchestrator
SECURITY__CORS_ORIGINS=http://localhost:3000,https://admin.your-domain.com

# =============================================================================
# AI/LLM SERVICE API KEYS & CONFIGURATION
# =============================================================================
# AI Service API Keys (set only the ones you use)
LLM__OPENROUTER_API_KEY=your_openrouter_api_key_here
LLM__OPENAI_API_KEY=your_openai_api_key_here
LLM__ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Local LLM Configuration
LLM__DEFAULT_LOCAL_PROVIDER=ollama
LLM__OLLAMA_BASE_URL=http://host.docker.internal:11434

# Cloud LLM Configuration
LLM__DEFAULT_CLOUD_PROVIDER=openrouter
LLM__ENABLE_CLOUD_FALLBACK=true

# =============================================================================
# DEVELOPMENT & DEPLOYMENT
# =============================================================================
# Deployment Environment
DEPLOYMENT__ENVIRONMENT=development
DEPLOYMENT__WORKERS=4
DEPLOYMENT__MAX_CONNECTIONS=100
DEPLOYMENT__RATE_LIMIT_ENABLED=true
DEPLOYMENT__RATE_LIMIT_REQUESTS_PER_MINUTE=60

# Development Ports (only used in dev mode)
DEV__API_PORT=8000
DEV__DASHBOARD_PORT=3000
DEV__CODE_SERVER_PORT=8080

# Code Server Configuration
# In production, this should be handled via Docker Secrets.
# DEV__CODE_SERVER_PASSWORD=<set-via-secrets/code_server_password.txt>

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
# Monitoring Ports
MONITORING__PROMETHEUS_PORT=9091
MONITORING__GRAFANA_PORT=3001
MONITORING__METRICS_PORT=9090

# Monitoring Credentials
MONITORING__GRAFANA_ADMIN_USER=admin
# MONITORING__GRAFANA_ADMIN_PASSWORD=<set-via-environment-specific-config>
# MONITORING__GRAFANA_SECRET_KEY=<set-via-environment-specific-config>

# =============================================================================
# DOCKER & VOLUME PATHS
# =============================================================================
# Container Resource Limits
DOCKER__RESOURCE_LIMITS=true
DOCKER__MAX_USER_CONTAINERS=10
DOCKER__CONTAINER_CPU_LIMIT=1.0
DOCKER__CONTAINER_MEMORY_LIMIT=2G

# Volume Paths (for production bind mounts)
VOLUMES__POSTGRES_DATA_PATH=./volumes/postgres-prod-data
VOLUMES__REDIS_DATA_PATH=./volumes/redis-prod-data

# =============================================================================
# EXTERNAL URLS (for production)
# =============================================================================
# Replace with your actual public-facing domains
URLS__NEXT_PUBLIC_API_BASE_URL=https://api.your-domain.com
URLS__NEXTAUTH_URL=https://admin.your-domain.com