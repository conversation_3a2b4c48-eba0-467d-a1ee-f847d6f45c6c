"""
Distributed Lock Manager using Redis Built-in Lock Implementation.

Enhanced version using redis.asyncio.lock.Lock for robust distributed locking
with automatic lock release, deadlock prevention, and performance metrics.

Features:
- Built-in Redis lock implementation for reliability
- Prometheus metrics for lock contention and performance monitoring
- Automatic lock cleanup and expiration handling
- Tenant-based key namespacing for multi-tenancy
- Comprehensive logging and error handling

Usage pattern (async):
    redis_client = await get_redis_client()
    lock_manager = LockManager(redis_client, tenant_id="tenant_1")
    acquired = await lock_manager.acquire_lock(project_id=1, agent_role="backend", task_id=42)
    if acquired:
        try:
            # Do work...
            pass
        finally:
            await lock_manager.release_lock(project_id=1)
"""
from __future__ import annotations

import json
import logging
import time
from datetime import datetime, timezone
from typing import Optional, Dict, Any

from redis.asyncio import Redis
from redis.asyncio.lock import Lock as RedisLock

# Prefer using the shared Redis client provided by redis_service
try:
    # FastAPI dependency-style import (optional for DI in routes/services)
    from src.services.redis_service import get_redis_client  # type: ignore
except Exception:  # pragma: no cover - fallback for non-FastAPI contexts
    get_redis_client = None  # type: ignore

# Prometheus metrics (optional - graceful fallback if not available)
try:
    from prometheus_client import Counter, Histogram, Gauge
    PROMETHEUS_AVAILABLE = True

    # Lock metrics
    lock_acquisitions_total = Counter(
        'lock_acquisitions_total',
        'Total number of lock acquisition attempts',
        ['tenant_id', 'project_id', 'agent_role', 'status']
    )

    lock_duration_seconds = Histogram(
        'lock_duration_seconds',
        'Duration of lock holding time',
        ['tenant_id', 'project_id', 'agent_role']
    )

    lock_contention_total = Counter(
        'lock_contention_total',
        'Total number of lock contentions (failed acquisitions)',
        ['tenant_id', 'project_id']
    )

    active_locks_gauge = Gauge(
        'active_locks_current',
        'Current number of active locks',
        ['tenant_id']
    )

except ImportError:
    PROMETHEUS_AVAILABLE = False
    lock_acquisitions_total = None
    lock_duration_seconds = None
    lock_contention_total = None
    active_locks_gauge = None

logger = logging.getLogger(__name__)


class LockManager:
    """
    Manage per-project distributed locks using Redis built-in lock implementation.

    Enhanced with Prometheus metrics, automatic cleanup, and robust error handling.

    Attributes:
        redis: Async Redis client instance
        tenant_id: Tenant identifier for key namespacing
        key_prefix: Prefix for lock keys
        active_locks: Dictionary tracking active locks and their metadata
    """

    def __init__(self, redis_client: Redis, tenant_id: str, key_prefix: str = "lock:project:") -> None:
        """
        Initialize the lock manager with Redis built-in locks.

        Args:
            redis_client: Async Redis client instance.
            tenant_id: Tenant identifier for namespacing lock keys.
            key_prefix: Suffix used for project lock keys.
        """
        from src.utils.redis_keys import tenant_key

        self.redis: Redis = redis_client
        self.tenant_id: str = tenant_id
        self._suffix_prefix: str = key_prefix
        self._tenant_key = tenant_key

        # Track active locks for metrics and cleanup
        self.active_locks: Dict[str, Dict[str, Any]] = {}

        # Performance tracking
        self._lock_start_times: Dict[str, float] = {}

        logger.info(f"LockManager initialized for tenant {tenant_id}")

    def _key(self, project_id: int) -> str:
        """Build the Redis key for a project lock (tenant-scoped)."""
        # key becomes: ai:tenant:{tenant_id}:lock:project:{project_id}
        return self._tenant_key(self.tenant_id, self._suffix_prefix.rstrip(':'), str(project_id))

    def _get_redis_lock(self, key: str, timeout: int) -> RedisLock:
        """
        Create a Redis built-in lock instance.

        Args:
            key: Lock key
            timeout: Lock timeout in seconds

        Returns:
            Redis lock instance
        """
        return RedisLock(
            redis=self.redis,
            name=key,
            timeout=timeout,
            blocking_timeout=1.0,  # Wait up to 1 second for lock acquisition
            thread_local=False      # Allow usage across async tasks
        )

    async def acquire_lock(
        self,
        project_id: int,
        agent_role: str,
        task_id: int,
        timeout_seconds: int = 600,
    ) -> bool:
        """
        Attempt to acquire the project lock using Redis built-in lock.

        Uses Redis built-in distributed lock for atomic lock acquisition
        with automatic expiration and deadlock prevention.

        Args:
            project_id: Target project identifier.
            agent_role: Agent role trying to acquire the lock (e.g., 'backend').
            task_id: Task identifier associated with this lock acquisition.
            timeout_seconds: Expiration in seconds to avoid stale locks (default: 10 minutes).

        Returns:
            True if the lock was acquired, False if already held by another actor.
        """
        key = self._key(project_id)
        start_time = time.time()

        # Record lock acquisition attempt
        if PROMETHEUS_AVAILABLE and lock_acquisitions_total:
            lock_acquisitions_total.labels(
                tenant_id=self.tenant_id,
                project_id=str(project_id),
                agent_role=agent_role,
                status='attempt'
            ).inc()

        try:
            # Create Redis built-in lock
            redis_lock = self._get_redis_lock(key, timeout_seconds)

            # Attempt to acquire the lock (non-blocking)
            acquired = await redis_lock.acquire(blocking=False)

            if acquired:
                # Store lock metadata
                lock_metadata = {
                    "agent_role": agent_role,
                    "task_id": task_id,
                    "acquired_at": datetime.now(timezone.utc).isoformat(),
                    "tenant_id": self.tenant_id,
                    "redis_lock": redis_lock
                }

                self.active_locks[key] = lock_metadata
                self._lock_start_times[key] = start_time

                # Update metrics
                if PROMETHEUS_AVAILABLE:
                    if lock_acquisitions_total:
                        lock_acquisitions_total.labels(
                            tenant_id=self.tenant_id,
                            project_id=str(project_id),
                            agent_role=agent_role,
                            status='success'
                        ).inc()

                    if active_locks_gauge:
                        active_locks_gauge.labels(tenant_id=self.tenant_id).inc()

                logger.info(
                    f"Lock acquired for project {project_id} by {agent_role} "
                    f"(task {task_id}) in tenant {self.tenant_id}"
                )
                return True
            else:
                # Lock contention detected
                if PROMETHEUS_AVAILABLE:
                    if lock_contention_total:
                        lock_contention_total.labels(
                            tenant_id=self.tenant_id,
                            project_id=str(project_id)
                        ).inc()

                    if lock_acquisitions_total:
                        lock_acquisitions_total.labels(
                            tenant_id=self.tenant_id,
                            project_id=str(project_id),
                            agent_role=agent_role,
                            status='contention'
                        ).inc()

                logger.warning(
                    f"Lock contention for project {project_id} by {agent_role} "
                    f"(task {task_id}) in tenant {self.tenant_id}"
                )
                return False

        except Exception as e:
            # Record failed acquisition
            if PROMETHEUS_AVAILABLE and lock_acquisitions_total:
                lock_acquisitions_total.labels(
                    tenant_id=self.tenant_id,
                    project_id=str(project_id),
                    agent_role=agent_role,
                    status='error'
                ).inc()

            logger.error(
                f"Failed to acquire lock for project {project_id}: {e}",
                exc_info=True
            )
            return False

    async def release_lock(self, project_id: int) -> bool:
        """
        Release the project lock using Redis built-in lock.

        Args:
            project_id: Target project identifier.

        Returns:
            True if the lock was released, False otherwise.
        """
        key = self._key(project_id)

        try:
            # Get lock metadata if available
            lock_metadata = self.active_locks.get(key)

            if not lock_metadata:
                logger.warning(f"No active lock found for project {project_id}")
                return False

            # Get the Redis lock instance
            redis_lock = lock_metadata.get("redis_lock")

            if redis_lock:
                # Release the Redis built-in lock
                await redis_lock.release()

                # Calculate lock duration for metrics
                start_time = self._lock_start_times.get(key)
                if start_time and PROMETHEUS_AVAILABLE and lock_duration_seconds:
                    duration = time.time() - start_time
                    lock_duration_seconds.labels(
                        tenant_id=self.tenant_id,
                        project_id=str(project_id),
                        agent_role=lock_metadata.get("agent_role", "unknown")
                    ).observe(duration)

                # Update active locks gauge
                if PROMETHEUS_AVAILABLE and active_locks_gauge:
                    active_locks_gauge.labels(tenant_id=self.tenant_id).dec()

                # Clean up tracking data
                del self.active_locks[key]
                if key in self._lock_start_times:
                    del self._lock_start_times[key]

                logger.info(f"Lock released for project {project_id} in tenant {self.tenant_id}")
                return True
            else:
                logger.error(f"Redis lock instance not found for project {project_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to release lock for project {project_id}: {e}", exc_info=True)
            return False

    async def is_locked(self, project_id: int) -> bool:
        """
        Check if a project is currently locked.

        Args:
            project_id: Target project identifier.

        Returns:
            True if the project is locked, False otherwise.
        """
        key = self._key(project_id)

        try:
            # Check if lock exists in Redis
            exists = await self.redis.exists(key)
            return bool(exists)
        except Exception as e:
            logger.error(f"Failed to check lock status for project {project_id}: {e}")
            return False

    async def get_lock_info(self, project_id: int) -> Optional[Dict[str, Any]]:
        """
        Get information about the current lock holder.

        Args:
            project_id: Target project identifier.

        Returns:
            Dictionary with lock information if locked, None otherwise.
        """
        key = self._key(project_id)

        try:
            # Check local active locks first
            if key in self.active_locks:
                lock_metadata = self.active_locks[key].copy()
                # Remove the Redis lock instance for serialization
                lock_metadata.pop("redis_lock", None)
                return lock_metadata

            # Check Redis for lock information
            lock_data = await self.redis.get(key)
            if lock_data:
                return json.loads(lock_data)

            return None

        except Exception as e:
            logger.error(f"Failed to get lock info for project {project_id}: {e}")
            return None

    async def cleanup_expired_locks(self) -> int:
        """
        Clean up any expired locks that weren't properly released.

        Returns:
            Number of locks cleaned up.
        """
        cleaned_count = 0

        try:
            # Get all lock keys for this tenant
            pattern = self._tenant_key(self.tenant_id, self._suffix_prefix.rstrip(':'), "*")
            keys = await self.redis.keys(pattern)

            for key in keys:
                try:
                    # Check if key still exists (Redis TTL handles expiration)
                    exists = await self.redis.exists(key)
                    if not exists:
                        # Remove from local tracking if it was there
                        if key in self.active_locks:
                            del self.active_locks[key]
                        if key in self._lock_start_times:
                            del self._lock_start_times[key]
                        cleaned_count += 1

                except Exception as e:
                    logger.error(f"Error checking lock key {key}: {e}")

            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} expired locks for tenant {self.tenant_id}")

        except Exception as e:
            logger.error(f"Failed to cleanup expired locks: {e}")

        return cleaned_count

    def get_metrics(self) -> Dict[str, Any]:
        """
        Get current lock manager metrics.

        Returns:
            Dictionary with current metrics.
        """
        return {
            "tenant_id": self.tenant_id,
            "active_locks_count": len(self.active_locks),
            "active_lock_keys": list(self.active_locks.keys()),
            "prometheus_available": PROMETHEUS_AVAILABLE
        }
