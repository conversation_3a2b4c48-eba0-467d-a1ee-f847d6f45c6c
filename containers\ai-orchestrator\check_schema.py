#!/usr/bin/env python3
"""
Check current database schema for projects table
"""
from sqlalchemy import create_engine, inspect
import os

def main():
    url = os.environ.get('DATABASE_URL')
    if not url:
        print("DATABASE_URL not found")
        return

    engine = create_engine(url)
    inspector = inspect(engine)

    print('Projects table columns:')
    try:
        columns = inspector.get_columns('projects')
        for col in columns:
            print(f'  {col["name"]}: {col["type"]}')
    except Exception as e:
        print(f'Error getting columns: {e}')

    print('\nForeign keys for projects table:')
    try:
        fks = inspector.get_foreign_keys('projects')
        for fk in fks:
            print(f'  {fk["constrained_columns"]} -> {fk["referred_table"]}.{fk["referred_columns"]}')
    except Exception as e:
        print(f'Error getting foreign keys: {e}')

    print('\nTables in database:')
    try:
        tables = inspector.get_table_names()
        for table in sorted(tables):
            print(f'  {table}')
    except Exception as e:
        print(f'Error getting tables: {e}')

if __name__ == '__main__':
    main()
