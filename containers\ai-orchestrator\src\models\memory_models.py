"""
SQLAlchemy models for the AI Memory System.

This module defines the persistent database model(s) used by the memory
service. The system uses a UUID-based ownership model that references
the `user_profiles.supabase_user_id` column (Postgres UUID compatibility
is preserved while supporting SQLite in tests).

Only SQLAlchemy models should live here. Pydantic request/response
schemas are moved to `src.schemas.memory_schemas`.
"""

from __future__ import annotations

from datetime import datetime
from typing import Optional
from uuid import UUID

from sqlalchemy import Column, String, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID as PGUUID

from src.models.database import Base
from src.models.custom_types import JsonVariant, UuidVariant


class Memory(Base):
    """Persistent memory entry stored in the database.

    Fields:
        id: Primary key UUID
        owner_id: UUID -> user_profiles.supabase_user_id (FK)
        memory_type: string enum stored as text
        content: text payload
        metadata: JSON/JSONB metadata (nullable)
        created_at: timestamp
        updated_at: timestamp
    """

    __tablename__ = "memories"

    id: UUID = Column(PGUUID(as_uuid=True), primary_key=True, default=func.uuid_generate_v4())
    owner_id: UUID = Column(UuidVariant, ForeignKey("user_profiles.supabase_user_id", ondelete="CASCADE"), nullable=False, index=True, comment="UUID owner reference to user_profiles.supabase_user_id")
    memory_type: str = Column(String(64), nullable=False, index=True, comment="Type of memory (user_preference, code_pattern, etc.)")
    content: str = Column(Text, nullable=False)
    memory_metadata: Optional[dict] = Column(JsonVariant, nullable=True, comment="Metadata JSON for the memory entry")
    created_at: datetime = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at: datetime = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationship convenience
    owner = relationship("UserProfile", backref="memories", lazy="selectin")

    def to_dict(self) -> dict:
        return {
            "id": str(self.id),
            "owner_id": str(self.owner_id),
            "memory_type": self.memory_type,
            "content": self.content,
            "metadata": self.memory_metadata or {},
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
