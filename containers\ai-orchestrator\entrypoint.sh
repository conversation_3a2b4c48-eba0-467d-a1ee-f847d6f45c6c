#!/bin/bash
set -e

echo "entrypoint: waiting for dependent services to be ready..."

# Detect Windows/Docker Desktop environment
WINDOWS_DETECTED=false
if mount | grep -q "type fuse.grpcfuse" || mount | grep -q "osxfs" || mount | grep -q "wslfs"; then
    WINDOWS_DETECTED=true
    echo "entrypoint: Windows/Docker Desktop detected - using compatibility mode"
else
    echo "entrypoint: Linux environment detected - using standard mode"
fi

# Ensure data directory exists and has proper permissions
echo "Ensuring data directory permissions..."
if [ -d "/app/data" ]; then
  # Try to set permissions, but don't fail on Windows bind mounts
  if [ "$WINDOWS_DETECTED" = "false" ]; then
    chmod 755 /app/data || echo "Warning: Could not set permissions on /app/data"
  else
    echo "Skipping chmod on /app/data (Windows bind mount compatibility)"
  fi
else
  echo "Creating data directory..."
  mkdir -p /app/data
  if [ "$WINDOWS_DETECTED" = "false" ]; then
    chmod 755 /app/data || echo "Warning: Could not set permissions on /app/data"
  else
    echo "Created /app/data (skipping chmod for Windows compatibility)"
  fi
fi

# Note: Vector storage now handled entirely by Supabase pgvector
echo "Vector storage is handled by Supabase pgvector (ChromaDB removed)"

# Set up signal handling for graceful shutdown
cleanup() {
    echo "Shutting down services..."
    # All vector operations now handled by Supabase, no cleanup needed
    exit 0
}
trap 'cleanup' TERM INT HUP

# Helper: wait for a TCP host:port using nc (netcat) instead of bash /dev/tcp
wait_for_tcp() {
  host=$1
  port=$2
  timeout=${3:-60}
  echo "Waiting for $host:$port (timeout ${timeout}s)"
  while ! nc -z "$host" "$port" 2>/dev/null; do
    timeout=$((timeout-1))
    if [ "$timeout" -le 0 ]; then
      echo "Timed out waiting for $host:$port"
      return 1
    fi
    sleep 1
  done
  echo "$host:$port is available"
}

# Wait for Redis (use service name and port inside compose network)
if [ "${REDIS_URL:-}" = "" ]; then
  # If REDIS_URL not set, rely on service name and default port
  echo "REDIS_URL not set, waiting for redis:6379"
  wait_for_tcp "redis" 6379 60 || echo "Warning: redis not reachable"
else
  echo "REDIS_URL provided"
fi

# Wait for Ollama if configured as local provider
if [ "${DEFAULT_LOCAL_PROVIDER:-}" = "ollama" ] || [ "${EMBEDDING_PROVIDER:-}" = "ollama" ]; then
  echo "Waiting for ollama:11434"
  wait_for_tcp "ollama" 11434 120 || echo "Warning: ollama not reachable"
fi

# Setup role configuration if it doesn't exist
echo "Setting up role configuration..."
if [ -f "/app/setup_config.py" ]; then
  python /app/setup_config.py || echo "Warning: Could not setup default configuration"
else
  echo "Warning: setup_config.py not found"
fi

echo "Dependency wait complete, executing as appuser: $@"
# Execute the main application directly as appuser
exec "$@"
