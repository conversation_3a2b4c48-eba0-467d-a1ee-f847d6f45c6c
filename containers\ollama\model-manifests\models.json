{"models": [{"name": "nomic-embed-text", "description": "Nomic text embedding model for dense vector embeddings (768 dimensions)", "source": "nomic"}, {"name": "mxbai-embed-large", "description": "Mixed Bread AI large embedding model for high-quality embeddings (1024 dimensions)", "source": "mixedbread"}, {"name": "all-minilm", "description": "Sentence Transformers all-MiniLM model for efficient embeddings (384 dimensions)", "source": "sentence-transformers"}, {"name": "bge-large-en-v1.5", "description": "BGE Large model optimized for retrieval tasks (1024 dimensions) - excellent for pgvector", "source": "BAAI"}]}