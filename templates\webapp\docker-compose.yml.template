version: '3.8'

services:
  __PROJECT_NAME__-app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BUILD_DATE: ${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        VERSION: ${VERSION:-1.0.0}
    container_name: __PROJECT_NAME__-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8000}:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENVIRONMENT=${ENVIRONMENT:-development}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - __PROJECT_NAME__-network
    depends_on:
      __PROJECT_NAME__-db:
        condition: service_healthy
      __PROJECT_NAME__-redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  __PROJECT_NAME__-db:
    image: postgres:15-alpine
    container_name: __PROJECT_NAME__-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-__PROJECT_NAME__}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - __PROJECT_NAME__-db-data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - __PROJECT_NAME__-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-__PROJECT_NAME__}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  __PROJECT_NAME__-redis:
    image: redis:7-alpine
    container_name: __PROJECT_NAME__-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - __PROJECT_NAME__-redis-data:/data
    networks:
      - __PROJECT_NAME__-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
      start_period: 30s

networks:
  __PROJECT_NAME__-network:
    driver: bridge
    name: __PROJECT_NAME__-network

volumes:
  __PROJECT_NAME__-db-data:
    name: __PROJECT_NAME__-db-data
  __PROJECT_NAME__-redis-data:
    name: __PROJECT_NAME__-redis-data
