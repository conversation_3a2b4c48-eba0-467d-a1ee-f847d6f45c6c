#!/usr/bin/env python3
"""
Check current alembic version
"""
from sqlalchemy import create_engine, text
import os

def main():
    url = os.environ.get('DATABASE_URL')
    if not url:
        print("DATABASE_URL not found")
        return

    engine = create_engine(url)
    with engine.connect() as conn:
        try:
            result = conn.execute(text('SELECT version_num FROM alembic_version'))
            version = result.fetchone()
            if version:
                print('Current Alembic version:', version[0])
            else:
                print('No alembic version found')
        except Exception as e:
            print(f'Error: {e}')

if __name__ == '__main__':
    main()
