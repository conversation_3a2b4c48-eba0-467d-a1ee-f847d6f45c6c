import * as vscode from 'vscode';
import * as path from 'path';
import axios from 'axios';
import { apiClient, CloneProjectRequest, UploadProjectRequest } from './api';
import { extractProjectName, isValidGitUrl, readFilesFromUris } from './utils';

// Configuration for API endpoints
const API_BASE_URL_CONFIG = process.env.NODE_ENV === 'production'
    ? 'http://ai-orchestrator:8000/api/v1'
    : 'http://localhost:8000/api/v1';

/**
 * Main extension activation function
 */
export function activate(context: vscode.ExtensionContext) {
    console.log('Project Importer Extension is now active');

    // Register GitHub authentication command
    const loginWithGithubCommand = vscode.commands.registerCommand(
        'projectImporter.loginWithGithub',
        async () => {
            try {
                // Get the auth URL from the AI orchestrator
                const response = await axios.get(`${API_BASE_URL_CONFIG}/auth/github/url`);
                const authUrl = response.data.url;

                // Open the GitHub auth URL in external browser
                vscode.env.openExternal(vscode.Uri.parse(authUrl));

                // Show information message
                vscode.window.showInformationMessage(
                    'GitHub authentication started in your browser. Please complete the process there.'
                );
            } catch (error) {
                vscode.window.showErrorMessage(`GitHub authentication failed: ${error}`);
            }
        }
    );

    // Register clone repository command
    const cloneRepoCommand = vscode.commands.registerCommand(
        'projectImporter.cloneRepository',
        async () => {
            // Get repository URL from user
            const repoUrl = await vscode.window.showInputBox({
                placeHolder: 'https://github.com/username/repository.git',
                prompt: 'Enter GitHub repository URL',
                validateInput: (value) => {
                    if (!value) return 'URL is required';
                    if (!isValidGitUrl(value)) return 'Please enter a valid Git repository URL';
                    return null;
                }
            });

            if (!repoUrl) return;

            // Extract project name from URL
            const projectName = extractProjectName(repoUrl);
            if (!projectName) {
                vscode.window.showErrorMessage('Could not extract project name from URL');
                return;
            }

            // Confirm project name with user
            const confirmedName = await vscode.window.showInputBox({
                value: projectName,
                prompt: 'Confirm project name',
                placeHolder: 'Enter project name'
            });

            if (!confirmedName) return;

            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: `Cloning ${repoUrl}`,
                cancellable: false
            }, async (progress: any) => {
                try {
                    progress.report({ increment: 10, message: 'Preparing clone request...' });

                    // Prepare API request
                    const request: CloneProjectRequest = {
                        url: repoUrl,
                        project_name: confirmedName
                    };

                    progress.report({ increment: 30, message: 'Sending request to server...' });

                    // Call the API
                    const response = await apiClient.cloneProject(request);

                    progress.report({ increment: 60, message: 'Processing response...' });

                    // Show success message
                    vscode.window.showInformationMessage(
                        `Successfully cloned ${response.name} (ID: ${response.id})`
                    );

                    progress.report({ increment: 100, message: 'Complete!' });

                } catch (error: any) {
                    console.error('Clone error:', error);
                    vscode.window.showErrorMessage(
                        `Failed to clone repository: ${error.message || 'Unknown error'}`
                    );
                }
            });
        }
    );

    // Register upload project command
    const uploadProjectCommand = vscode.commands.registerCommand(
        'projectImporter.uploadProject',
        async () => {
            try {
                // Prompt for project name
                const projectName = await vscode.window.showInputBox({
                    prompt: 'Enter project name',
                    placeHolder: 'my-project',
                    validateInput: (value) => {
                        if (!value || value.trim().length === 0) {
                            return 'Project name is required';
                        }
                        if (value.length > 100) {
                            return 'Project name must be less than 100 characters';
                        }
                        return null;
                    }
                });

                if (!projectName) return;

                // Prompt for project description (optional)
                const projectDescription = await vscode.window.showInputBox({
                    prompt: 'Enter project description (optional)',
                    placeHolder: 'A brief description of your project'
                });

                // Open file picker to select files/folders
                const fileUris = await vscode.window.showOpenDialog({
                    canSelectFiles: true,
                    canSelectFolders: true,
                    canSelectMany: true,
                    defaultUri: vscode.workspace.workspaceFolders?.[0]?.uri,
                    openLabel: 'Select Files/Folders to Upload'
                });

                if (!fileUris || fileUris.length === 0) return;

                // Show progress notification
                vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: `Uploading project: ${projectName}`,
                    cancellable: false
                }, async (progress: any) => {
                    try {
                        progress.report({ increment: 10, message: 'Reading files...' });

                        // Read file contents
                        const files = await readFilesFromUris(fileUris);

                        if (files.length === 0) {
                            throw new Error('No valid files found to upload');
                        }

                        progress.report({ increment: 30, message: `Found ${files.length} files, preparing upload...` });

                        // Prepare API request
                        const request: UploadProjectRequest = {
                            files: files,
                            project_name: projectName.trim(),
                            description: projectDescription?.trim() || undefined
                        };

                        progress.report({ increment: 50, message: 'Sending files to server...' });

                        // Call the API
                        const response = await apiClient.uploadProject(request);

                        progress.report({ increment: 80, message: 'Processing response...' });

                        // Open the project in workspace if successful
                        if (response.status === 'success' || response.status === 'completed') {
                            // Try to open the project workspace
                            try {
                                const workspaceUri = vscode.Uri.parse(`/home/<USER>/workspace/${response.name}`);
                                await vscode.commands.executeCommand('vscode.openFolder', workspaceUri, false);
                            } catch (workspaceError) {
                                console.warn('Could not open workspace:', workspaceError);
                                // Continue with success message even if workspace opening fails
                            }

                            progress.report({ increment: 100, message: 'Complete!' });

                            // Show success message
                            vscode.window.showInformationMessage(
                                `✅ Project "${response.name}" uploaded successfully! (ID: ${response.id})`
                            );
                        } else {
                            throw new Error(response.message || 'Upload failed');
                        }

                    } catch (error: any) {
                        console.error('Upload error:', error);
                        vscode.window.showErrorMessage(
                            `❌ Failed to upload project: ${error.message || 'Unknown error'}`
                        );
                        throw error; // Re-throw to mark progress as failed
                    }
                });

            } catch (error: any) {
                console.error('Upload command error:', error);
                vscode.window.showErrorMessage(
                    `❌ Upload cancelled: ${error.message || 'Unknown error'}`
                );
            }
        }
    );

    // Register welcome view
    const welcomeViewProvider = new ProjectImporterViewProvider(context.extensionUri);
    const welcomeView = vscode.window.registerWebviewViewProvider(
        'projectImporterWelcome',
        welcomeViewProvider
    );

    // Add all commands to subscriptions
    context.subscriptions.push(
        loginWithGithubCommand,
        cloneRepoCommand,
        uploadProjectCommand,
        welcomeView
    );
}

/**
 * Welcome view provider for project importer
 */
class ProjectImporterViewProvider implements vscode.WebviewViewProvider {
    constructor(private readonly extensionUri: vscode.Uri) {}

    resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        token: vscode.CancellationToken
    ) {
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.extensionUri]
        };

        webviewView.webview.html = this.getHtmlContent(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(message => {
            if (message.command === 'loginWithGithub') {
                vscode.commands.executeCommand('projectImporter.loginWithGithub');
            } else if (message.command === 'cloneRepository') {
                vscode.commands.executeCommand('projectImporter.cloneRepository');
            } else if (message.command === 'uploadProject') {
                vscode.commands.executeCommand('projectImporter.uploadProject');
            }
        });
    }

    private getHtmlContent(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Project Importer</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    padding: 10px;
                    color: var(--vscode-foreground);
                }
                .welcome-container {
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }
                .action-button {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                    text-align: left;
                }
                .action-button:hover {
                    background-color: var(--vscode-button-secondaryHoverBackground);
                }
                .icon {
                    margin-right: 10px;
                    font-size: 18px;
                }
            </style>
        </head>
        <body>
            <div class="welcome-container">
                <h2>Project Importer</h2>
                <p>Get started by importing a project:</p>

                <button class="action-button" id="github-login">
                    <span class="icon">🔑</span>
                    Login with GitHub
                </button>

                <button class="action-button" id="clone-repo">
                    <span class="icon">📋</span>
                    Clone GitHub Repository
                </button>

                <button class="action-button" id="upload-project">
                    <span class="icon">📤</span>
                    Upload Project from Computer
                </button>
            </div>

            <script>
                (function() {
                    const vscode = acquireVsCodeApi();

                    document.getElementById('github-login').addEventListener('click', () => {
                        vscode.postMessage({ command: 'loginWithGithub' });
                    });

                    document.getElementById('clone-repo').addEventListener('click', () => {
                        vscode.postMessage({ command: 'cloneRepository' });
                    });

                    document.getElementById('upload-project').addEventListener('click', () => {
                        vscode.postMessage({ command: 'uploadProject' });
                    });
                })();
            </script>
        </body>
        </html>`;
    }
}

export function deactivate() {}