# Quick local verification script for services (PowerShell)
# Replace environment variables/JWT keys as needed before running.

param(
  [string]$SUPABASE_URL = $env:SUPABASE_URL,
  [string]$SUPABASE_SERVICE_ROLE_KEY = $env:SUPABASE_SERVICE_ROLE_KEY,
  [switch]$VerboseCheck
)

Write-Host "Checking Docker containers (filter by name)..."
Write-Host "Ollama container status:"
docker ps --filter name=ollama

Write-Host "\nTail Ollama logs (press Ctrl+C to stop):"
Write-Host "docker logs -f ollama" -ForegroundColor DarkGray

Write-Host "\nSimple HTTP ping for Ollama (host):"
try {
  curl -v http://localhost:11434/ 2>&1 | Select-String "<html>|HTTP/"
}
catch {
  Write-Host "Ollama ping failed: $_"
}

Write-Host "\nChecking ai-orchestrator health:"
try {
  curl -v http://localhost:8000/health
}
catch {
  Write-Host "ai-orchestrator ping failed: $_"
}

Write-Host "\nTail ai-orchestrator logs (press Ctrl+C to stop):"
Write-Host "docker logs -f ai-coding-orchestrator-dev" -ForegroundColor DarkGray

Write-Host "\nChecking Redis connectivity from orchestrator container (if container exists):"
try {
  docker exec -it ai-coding-orchestrator-dev python -c "import redis,os; print('REDIS_URL', os.getenv('REDIS_URL')); r=redis.from_url(os.getenv('REDIS_URL')); print('PING', r.ping())"
}
catch {
  Write-Host "Could not exec into orchestrator container: $_"
}

if ($SUPABASE_URL -and $SUPABASE_SERVICE_ROLE_KEY) {
  Write-Host "\nFetching one row from Supabase embeddings table via REST (replace table if needed):"
  try {
    curl -s -H "apikey: $($SUPABASE_SERVICE_ROLE_KEY)" -H "Authorization: Bearer $($SUPABASE_SERVICE_ROLE_KEY)" "$($SUPABASE_URL)/rest/v1/embeddings?select=*&limit=1" | ConvertFrom-Json | Out-String | Write-Host
  }
  catch {
    Write-Host "Supabase REST query failed: $_"
  }
}
else {
  Write-Host "\nSupabase variables not set. Export SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY or pass as params."
}

Write-Host "\nDone. For more interactive checks, run the individual docker logs/exec/curl commands shown above."
