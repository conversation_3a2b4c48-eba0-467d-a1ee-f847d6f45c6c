"""
Dispatcher API Router

Provides endpoints to create tasks and trigger the dispatcher cycle.
"""
from __future__ import annotations

import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from src.models.database import get_db
from src.repository.task_repository import TaskRepository
from src.services import get_dispatcher
from src.services.dispatcher import Dispatcher
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

router = APIRouter(tags=["dispatcher"])  # Expose exact paths requested


class TaskCreateRequest(BaseModel):
    """Request schema for creating a new task."""
    project_id: int = Field(..., ge=1)
    agent_role: str = Field(..., min_length=1)
    input_data: Dict[str, Any] = Field(default_factory=dict)


@router.post("/tasks/", status_code=status.HTTP_201_CREATED)
async def create_task(
    payload: TaskCreateRequest,
    db: Session = Depends(get_db),
):
    """Create a new pending task for the specified project and role."""
    try:
        task = await TaskRepository.create_task(
            db,
            project_id=payload.project_id,
            agent_role=payload.agent_role,
            input_data=payload.input_data,
        )

        # Return a serializable representation of the task
        return {
            "id": task.id,
            "project_id": task.project_id,
            "status": task.status,
            "agent_role": task.agent_role,
            "input_data": task.input_data,
            "output_data": task.output_data,
            "created_at": task.created_at,
            "updated_at": task.updated_at,
        }
    except Exception as e:
        logger.error(f"Failed to create task: {e}")
        raise HTTPException(status_code=500, detail="Failed to create task")


@router.post("/dispatch/run-once")
async def dispatch_run_once(dispatcher: Dispatcher = Depends(get_dispatcher)):
    """Trigger a single dispatcher cycle."""
    try:
        await dispatcher.run_once()
        return {"status": "dispatcher cycle complete"}
    except Exception as e:
        logger.error(f"Dispatcher run_once failed: {e}")
        raise HTTPException(status_code=500, detail="Dispatcher cycle failed")