# Pre-commit hooks for AI Coding Agent Project
repos:
  # General hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        exclude: \.md$
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: detect-private-key
      - id: mixed-line-ending
        args: [--fix=lf]

  # Python hooks
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]
        exclude: (migrations/|venv/|\.venv/)

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.12
    hooks:
      - id: ruff
        args: [--fix]
        exclude: (migrations/|venv/|\.venv/)
      - id: ruff-format
        exclude: (migrations/|venv/|\.venv/)

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        args: [--ignore-missing-imports]
        exclude: (migrations/|venv/|\.venv/|tests/)

  # File naming and structure validation
  - repo: local
    hooks:
      - id: check-file-naming
        name: check file naming conventions
        entry: python scripts/validate_naming.py
        language: python
        types: [file]
        pass_filenames: false
        always_run: true

      - id: validate-directory-structure
        name: validate directory structure
        entry: python scripts/validate_structure.py
        language: python
        types: [file]
        pass_filenames: false
        always_run: true

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint
        name: lint Dockerfiles
        description: Runs hadolint Dockerfile linter
        entry: hadolint
        language: system
        types: [dockerfile]
        args: [--ignore, DL3008, --ignore, DL3013]
