#!/usr/bin/env python3
"""
Comprehensive test environment setup script for Windows.

This script addresses the fundamental Python path issues that prevent tests from finding
the src.models module and other imports.

Usage:
    python setup_test_environment.py
    python -m pytest tests/
"""

import sys
import os
from pathlib import Path

def setup_python_paths():
    """Set up Python paths to resolve import issues."""
    print("Setting up Python paths...")

    # Get the project root (where this script is located)
    project_root = Path(__file__).parent.absolute()
    print(f"Project root: {project_root}")

    # Define the ai-orchestrator paths
    ai_orchestrator_path = project_root / "containers" / "ai-orchestrator"
    src_path = ai_orchestrator_path / "src"

    print(f"AI Orchestrator path: {ai_orchestrator_path}")
    print(f"Source path: {src_path}")

    # Check if paths exist
    if not ai_orchestrator_path.exists():
        raise FileNotFoundError(f"AI Orchestrator directory not found: {ai_orchestrator_path}")

    if not src_path.exists():
        raise FileNotFoundError(f"Source directory not found: {src_path}")

    # Add paths to Python path (in correct order)
    paths_to_add = [
        str(src_path),              # This allows 'from src.models import ...'
        str(ai_orchestrator_path),  # This allows 'from containers.ai-orchestrator.src import ...'
        str(project_root)           # This allows 'from tests import ...'
    ]

    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)
            print(f"Added to Python path: {path}")

    return ai_orchestrator_path, src_path

def setup_environment_variables():
    """Set up environment variables for testing."""
    print("Setting up environment variables...")

    # Test environment configuration
    test_env_vars = {
        'ENVIRONMENT': 'testing',
        'USE_SUPABASE': 'false',
        'ENABLE_ROLE_CONFIG_INIT': 'false',
        'ENABLE_WEBSOCKET_CHAT': 'false',
        'DATABASE_URL': 'postgresql://test:test@localhost:5432/test_db',
        'REDIS_URL': 'redis://localhost:6379',
        'OLLAMA_BASE_URL': 'http://localhost:11434',
        'CORS_ORIGINS': '["http://localhost:3000"]',
        'CORS_ALLOW_CREDENTIALS': 'true',
        'CORS_ALLOW_METHODS': '["*"]',
        'CORS_ALLOW_HEADERS': '["*"]',

        # Dummy secrets for testing (JWT_SECRET must be at least 32 characters)
        'POSTGRES_PASSWORD': 'test_password',
        'JWT_SECRET': 'test_jwt_secret_key_for_testing_only_32_chars_minimum',
        'SUPABASE_URL': 'https://test.supabase.co',
        'SUPABASE_KEY': 'test_supabase_key',
        'SUPABASE_ANON_KEY': 'test_supabase_anon_key',
        'SUPABASE_SERVICE_KEY': 'test_supabase_service_key',

        # Windows compatibility
        'PYTHONIOENCODING': 'utf-8',
        'PYTHONLEGACYWINDOWSSTDIO': '1',
        'PYTHONPATH': '',  # Will be set below
    }

    for key, value in test_env_vars.items():
        os.environ[key] = value
        print(f"Set {key} = {value}")

    # Set PYTHONPATH to include our source directories
    ai_orchestrator_path, src_path = setup_python_paths()
    pythonpath_dirs = [
        str(src_path),
        str(ai_orchestrator_path),
        str(Path(__file__).parent)
    ]
    os.environ['PYTHONPATH'] = os.pathsep.join(pythonpath_dirs)
    print(f"Set PYTHONPATH = {os.environ['PYTHONPATH']}")

def verify_imports():
    """Verify that critical imports work after setup."""
    print("\nVerifying imports...")

    test_imports = [
        ('src.models', 'Basic models import'),
        ('src.services', 'Services import'),
        ('src.agents', 'Agents import'),
    ]

    for module_name, description in test_imports:
        try:
            __import__(module_name)
            print(f"✅ {description}: {module_name}")
        except ImportError as e:
            print(f"❌ {description}: {module_name} - {e}")

    # Try to import specific modules that tests use
    specific_imports = [
        ('src.models.validation_models', 'ValidationModels'),
        ('src.services.task_validator', 'TaskValidator'),
    ('src.agents.architect_agent', 'ArchitectAgent'),
    ]

    print("\nVerifying specific test imports...")
    for module_name, description in specific_imports:
        try:
            __import__(module_name)
            print(f"✅ {description}: {module_name}")
        except ImportError as e:
            print(f"❌ {description}: {module_name} - {e}")

def main():
    """Main setup function."""
    print("=== Python Test Environment Setup ===")

    try:
        setup_environment_variables()
        verify_imports()

        print("\n=== Setup Complete ===")
        print("You can now run tests with:")
        print("  python -m pytest tests/")
        print("  python -m pytest tests/unit/")
        print("  python -m pytest tests/unit/test_architect_agent.py -v")

        return True

    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
