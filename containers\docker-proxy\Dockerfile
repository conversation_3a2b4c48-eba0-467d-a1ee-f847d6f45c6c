FROM haproxy:2.8-alpine

# Switch to root to install packages (HAProxy Alpine defaults to non-root)
USER root

# Install curl for health checks (Context7 recommended approach)
RUN apk add --no-cache curl

# Create non-root user with UID/GID 1000
RUN addgroup -g 1000 haproxy_user && \
  adduser -D -u 1000 -G haproxy_user haproxy_user

# Copy HAProxy configuration
COPY haproxy.cfg /usr/local/etc/haproxy/haproxy.cfg

# Health check using curl (Context7 best practice)
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:2377/health || exit 1

# Expose Docker API port and health check port
EXPOSE 2375 2377

# Switch to non-root user
USER 1000:1000