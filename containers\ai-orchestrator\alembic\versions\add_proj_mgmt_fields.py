"""add project management fields

Revision ID: add_proj_mgmt_fields
Revises: 4dd8ad7e7723
Create Date: 2025-01-27 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'add_proj_mgmt_fields'
down_revision: Union[str, None] = '4dd8ad7e7723'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Add new fields to projects table
    op.add_column('projects', sa.Column('user_id', sa.Integer(), nullable=True))
    op.add_column('projects', sa.Column('status', sa.String(length=50), server_default='active', nullable=False))
    op.add_column('projects', sa.Column('progress_percentage', sa.Float(), server_default='0.0', nullable=False))
    op.add_column('projects', sa.Column('last_activity_at', sa.DateTime(timezone=True), nullable=True))

    # Create foreign key constraint for user_id
    op.create_foreign_key(
        'fk_projects_user_id_users',
        'projects', 'users',
        ['user_id'], ['id'],
        ondelete='CASCADE'
    )

    # Add new fields to roadmap_items table
    op.add_column('roadmap_items', sa.Column('started_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('roadmap_items', sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('roadmap_items', sa.Column('assigned_to', sa.String(length=255), nullable=True))
    op.add_column('roadmap_items', sa.Column('priority', sa.String(length=20), server_default='medium', nullable=False))

    # Create indexes for new fields
    op.create_index('ix_projects_user_id', 'projects', ['user_id'], unique=False)
    op.create_index('ix_projects_status', 'projects', ['status'], unique=False)
    op.create_index('ix_roadmap_items_started_at', 'roadmap_items', ['started_at'], unique=False)
    op.create_index('ix_roadmap_items_completed_at', 'roadmap_items', ['completed_at'], unique=False)
    op.create_index('ix_roadmap_items_assigned_to', 'roadmap_items', ['assigned_to'], unique=False)
    op.create_index('ix_roadmap_items_priority', 'roadmap_items', ['priority'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Drop indexes
    op.drop_index('ix_roadmap_items_priority', table_name='roadmap_items')
    op.drop_index('ix_roadmap_items_assigned_to', table_name='roadmap_items')
    op.drop_index('ix_roadmap_items_completed_at', table_name='roadmap_items')
    op.drop_index('ix_roadmap_items_started_at', table_name='roadmap_items')
    op.drop_index('ix_projects_status', table_name='projects')
    op.drop_index('ix_projects_user_id', table_name='projects')

    # Drop foreign key constraint
    op.drop_constraint('fk_projects_user_id_users', 'projects', type_='foreignkey')

    # Drop columns from roadmap_items
    op.drop_column('roadmap_items', 'priority')
    op.drop_column('roadmap_items', 'assigned_to')
    op.drop_column('roadmap_items', 'completed_at')
    op.drop_column('roadmap_items', 'started_at')

    # Drop columns from projects
    op.drop_column('projects', 'last_activity_at')
    op.drop_column('projects', 'progress_percentage')
    op.drop_column('projects', 'status')
    op.drop_column('projects', 'user_id')

    # ### end Alembic commands ###
