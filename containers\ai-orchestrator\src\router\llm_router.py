# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: FastAPI router for LLM service endpoints

from typing import Optional
from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, BackgroundTasks, Request
from fastapi.responses import JSONResponse
import logging

try:
    from src.utils.auth import get_current_user
    from src.services.enhanced_llm_service import EnhancedLLMService
    from src.models.llm_models import (
        GenerateRequest, LLMProvider, ModelPullRequest,
        RateLimitExceededError, ProviderUnavailableError, GenerationError
    )
except ImportError:
    # Fallback to absolute imports for direct module execution
    from src.utils.auth import get_current_user
    from src.services.enhanced_llm_service import EnhancedLLMService
    from src.models.llm_models import (
        GenerateRequest, LLMProvider, ModelPullRequest,
        RateLimitExceededError, ProviderUnavailableError, GenerationError
    )

logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter(prefix="/api/llm", tags=["LLM"])


# Dependency to get the LLM service instance from the app state
def get_llm_service(request: Request) -> EnhancedLLMService:
    """Get the LLM service instance from the app state."""
    return request.app.state.llm_service


@router.get("/health")
async def health_check(llm_service: EnhancedLLMService = Depends(get_llm_service)):
    """Comprehensive health check for all LLM providers."""
    try:
        health = await llm_service.health_check()

        # Convert to dict for JSON serialization
        return {
            "status": health.status,
            "providers": [
                {
                    "provider": status.provider.value,
                    "available": status.available,
                    "api_key_configured": status.api_key_configured,
                    "error_message": status.error_message,
                    "response_time_ms": status.response_time_ms,
                    "last_checked": status.last_checked
                }
                for status in health.providers
            ],
            "total_requests": health.total_requests,
            "failed_requests": health.failed_requests,
            "average_response_time_ms": health.average_response_time_ms
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": str(e)}
        )


@router.get("/providers")
async def list_providers(
    llm_service: EnhancedLLMService = Depends(get_llm_service),
    current_user=Depends(get_current_user)
):
    """List all available LLM providers and their status."""
    try:
        providers_info = []

        for provider in LLMProvider:
            status = await llm_service.test_provider_connection(provider)
            config = llm_service.providers.get(provider, {})

            providers_info.append({
                "provider": provider.value,
                "enabled": config.get("enabled", False),
                "available": status.available,
                "api_key_configured": status.api_key_configured,
                "default_model": config.get("default_model"),
                "base_url": config.get("base_url"),
                "timeout": config.get("timeout"),
                "response_time_ms": status.response_time_ms,
                "error_message": status.error_message
            })

        return {"providers": providers_info}
    except Exception as e:
        logger.error(f"Failed to list providers: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/models")
async def list_models(
    provider: Optional[LLMProvider] = None,
    current_user=Depends(get_current_user),
    llm_service: EnhancedLLMService = Depends(get_llm_service)
):
    """List available models from all or specific providers."""
    try:
        models = await llm_service.list_available_models(provider)

        # Convert to dict for JSON serialization
        models_data = []
        for model in models:
            models_data.append({
                "name": model.name,
                "provider": model.provider.value,
                "status": model.status.value,
                "size": model.size,
                "modified_at": model.modified_at,
                "parameters": model.parameters,
                "context_length": model.context_length,
                "cost_per_token": model.cost_per_token
            })

        return {"models": models_data}
    except Exception as e:
        logger.error(f"Failed to list models: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate")
async def generate_text(
    request: GenerateRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user),
    llm_service: EnhancedLLMService = Depends(get_llm_service)
):
    """Generate text using LLM with comprehensive error handling and monitoring."""
    try:
        # Extract user ID from current_user (adjust based on your auth implementation)
        user_id = getattr(current_user, 'id', 'default')

        response = await llm_service.generate(request, user_id=user_id)

        # Log successful generation in background
        background_tasks.add_task(
            _log_generation_success,
            user_id, request.provider or "auto", request.model or "auto",
            len(request.prompt), response.usage.completion_tokens
        )

        return {
            "content": response.content,
            "model": response.model,
            "provider": response.provider.value,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens,
                "cost": response.usage.cost,
                "duration_ms": response.usage.duration_ms
            },
            "request_id": response.request_id,
            "finish_reason": response.finish_reason,
            "created_at": response.created_at
        }

    except RateLimitExceededError as e:
        logger.warning(f"Rate limit exceeded for user {current_user}: {e}", exc_info=True)
        raise HTTPException(status_code=429, detail="Rate limit exceeded for this provider.")
    except ProviderUnavailableError as e:
        logger.error(f"Provider unavailable: {e}", exc_info=True)
        raise HTTPException(status_code=503, detail="The selected provider is currently unavailable.")
    except GenerationError as e:
        logger.error(f"Generation failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Text generation failed.")
    except Exception as e:
        logger.error(f"Unexpected error in text generation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="An unexpected error occurred during text generation.")


@router.post("/embeddings")
async def generate_embeddings(
    texts: list[str],
    current_user=Depends(get_current_user)
):
    """Generate embeddings for a list of texts using the configured embedding provider."""
    try:
        # Get the vector service
        from src.services.vector_service import get_vector_service
        vector_service = await get_vector_service()

        # Generate embeddings for each text
        embeddings = []
        for text in texts:
            if not text or not text.strip():
                embeddings.append(None)  # Empty text gets None embedding
                continue

            try:
                embedding = await vector_service.generate_embedding(text.strip())
                embeddings.append(embedding)
            except Exception as e:
                logger.error(f"Failed to generate embedding for text: {str(e)}")
                embeddings.append(None)  # Failed embedding gets None

        return {
            "embeddings": embeddings,
            "provider": vector_service.config.embedding_provider.value,
            "model": vector_service.config.embedding_model,
            "dimension": len(embeddings[0]) if embeddings and embeddings[0] else 0,
            "count": len([e for e in embeddings if e is not None])
        }

    except Exception as e:
        logger.error(f"Embeddings generation failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during embeddings generation."
        )


@router.get("/rate-limits/{provider}")
async def get_rate_limits(
    provider: LLMProvider,
    current_user=Depends(get_current_user),
    llm_service: EnhancedLLMService = Depends(get_llm_service)
):
    """Get current rate limit status for a provider."""
    try:
        user_id = getattr(current_user, 'id', 'default')

        rate_limit = await llm_service.check_rate_limit(provider, user_id)

        return {
            "provider": provider.value,
            "requests_per_minute": rate_limit.requests_per_minute,
            "requests_remaining": rate_limit.requests_remaining,
            "reset_time": rate_limit.reset_time,
            "cost_limit_usd": rate_limit.cost_limit_usd,
            "cost_used_usd": rate_limit.cost_used_usd
        }
    except Exception as e:
        logger.error(f"Failed to get rate limits for provider {provider.value}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve rate limit information.")


@router.post("/models/pull")
async def pull_model(
    request: ModelPullRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user),
    llm_service: EnhancedLLMService = Depends(get_llm_service)
):
    """Pull a model from the provider (currently only supports Ollama)."""
    try:
        result = await llm_service.pull_model(request)

        if result.success:
            # Log successful pull in background
            background_tasks.add_task(
                _log_model_pull,
                getattr(current_user, 'id', 'default'),
                request.model_name,
                request.provider.value,
                "success"
            )

            return {
                "success": True,
                "message": result.message,
                "model_name": result.model_name,
                "provider": result.provider.value
            }
        else:
            # Log failed pull in background
            background_tasks.add_task(
                _log_model_pull,
                getattr(current_user, 'id', 'default'),
                request.model_name,
                request.provider.value,
                "failed"
            )

            logger.error(f"Model pull failed for {request.model_name}: {result.message}")
            raise HTTPException(
                status_code=500,
                detail="Model pull failed."
            )

    except ProviderUnavailableError as e:
        logger.error(f"Model pull failed for {request.model_name}: {e}", exc_info=True)
        raise HTTPException(
            status_code=400,
            detail="Provider not supported for model pulling."
        )
    except Exception as e:
        logger.error(f"Failed to pull model {request.model_name}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="An unexpected error occurred during model pull.")


@router.get("/statistics")
async def get_usage_statistics(
    time_window: str = "1h",
    current_user=Depends(get_current_user),
    llm_service: EnhancedLLMService = Depends(get_llm_service)
):
    """Get usage statistics for monitoring and analytics."""
    try:
        stats = await llm_service.get_usage_statistics(time_window)

        return stats
    except Exception as e:
        logger.error(f"Failed to get statistics: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve usage statistics.")


@router.post("/validate-keys")
async def validate_api_keys(
    current_user=Depends(get_current_user),
    llm_service: EnhancedLLMService = Depends(get_llm_service)
):
    """Validate API keys for all configured providers."""
    try:
        results = {}

        for provider in LLMProvider:
            try:
                is_valid = await llm_service.validate_api_key(provider)
                results[provider.value] = {
                    "valid": is_valid,
                    "configured": bool(llm_service.providers.get(provider, {}).get('api_key')) or provider == LLMProvider.OLLAMA
                }
            except Exception as e:
                results[provider.value] = {
                    "valid": False,
                    "configured": False,
                    "error": str(e)
                }

        return {"api_keys": results}
    except Exception as e:
        logger.error(f"Failed to validate API keys: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="An unexpected error occurred during API key validation.")


# Background task functions
async def _log_generation_success(user_id: str, provider: str, model: str,
                                prompt_length: int, completion_tokens: int):
    """Log successful generation for analytics."""
    logger.info(
        f"Generation success - User: {user_id}, Provider: {provider}, "
        f"Model: {model}, Prompt length: {prompt_length}, "
        f"Completion tokens: {completion_tokens}"
    )


async def _log_model_pull(user_id: str, model_name: str, provider: str, status: str):
    """Log model pull operation."""
    logger.info(
        f"Model pull {status} - User: {user_id}, Model: {model_name}, "
        f"Provider: {provider}"
    )

