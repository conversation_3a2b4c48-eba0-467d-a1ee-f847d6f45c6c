import pytest
import asyncio
from typing import AsyncGenerator
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from src.main import app
from src.models.database import Base
from src.models.database_async import get_async_db as get_db # Use the async session getter
from src.core.config import settings

# Create a new async engine for a separate test database
# Note: DATABASE_URL_TEST must be set in your test environment
test_engine = create_async_engine("sqlite+aiosqlite:///./test.db", echo=True)
TestingSessionLocal = sessionmaker(
    autocommit=False, autoflush=False, bind=test_engine, class_=AsyncSession
)

@pytest.fixture(scope="function")
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """
    A fixture that provides a configured test client and an isolated
    database transaction for each test.
    """
    # Create all tables for the test database
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    # Create a new database session and begin a transaction
    async with TestingSessionLocal() as session:
        # Start a nested transaction so we can roll it back
        await session.begin_nested()

        # Override the get_db dependency to use our test session
        def override_get_db() -> AsyncGenerator[AsyncSession, None]:
            yield session

        app.dependency_overrides[get_db] = override_get_db

        # Yield the test client to the test function
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client

        # After the test is done, roll back the transaction
        await session.rollback()

    # Clean up the app dependency override
    del app.dependency_overrides[get_db]

    # Drop all tables after the test
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
