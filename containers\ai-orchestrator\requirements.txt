--extra-index-url https://download.pytorch.org/whl/cpu

# Base requirements
fastapi>=0.109.1
uvicorn>=0.24.0
orjson>=3.9.0
python-multipart>=0.0.7
sqlalchemy>=2.0.0
alembic>=1.13.0
psycopg2-binary>=2.9.7
redis>=5.0.0
redis[hiredis]>=5.0.0
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.2.1
email-validator>=2.0.0
aiofiles>=23.2.0
requests>=2.31.0
aiohttp>=3.9.0
PyYAML>=6.0.1

# Docker container management
docker>=6.1.0

# Supabase and Authentication
supabase>=2.0.0
supabase-auth>=2.0.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
PyJWT>=2.8.0

# Database and Vector Operations
asyncpg>=0.29.0
pgvector>=0.2.4
numpy>=1.24.0

# AI/ML components (CPU-only versions to avoid CUDA downloads)
langchain>=0.1.0
langgraph>=0.2.0
langchain-community>=0.0.10
langchain-openai>=0.0.5
langchain-core>=0.1.0

# CUDA and GPU acceleration
torch>=2.8.0
torchvision>=0.23.0
torchaudio>=2.8.0
cupy-cuda12x>=12.0.0
faiss-cpu>=1.7.0
sentence-transformers>=3.0.1
openai>=1.3.0
anthropic>=0.7.0

# Embedding and Vector Search (CPU-optimized)
torch==2.8.0
torchvision==0.23.0
torchaudio==2.8.0
sentence-transformers==3.0.1
transformers>=4.35.0
scikit-learn>=1.3.0

# Additional ML/AI packages
tokenizers>=0.15.0
huggingface-hub>=0.17.0
accelerate>=0.24.0

# Monitoring and rate limiting
prometheus-client>=0.19.0
slowapi>=0.1.9
psutil>=5.9.0

# File filtering and path matching
pathspec>=0.11.0

# Development and debugging
debugpy>=1.8.0
watchdog>=3.0.0

# Testing dependencies
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-asyncio>=0.23.0
httpx>=0.25.0
pytest-mock>=3.12.0
PyJWT>=2.8.0

# Additional utilities
python-dateutil>=2.8.0
pytz>=2023.3
cryptography>=41.0.0
bcrypt>=4.0.0
websockets>=12.0
gunicorn>=21.2.0
PyYAML>=6.0.0

# Vector database and search
# NOTE: Consolidated to use only Supabase pgvector for all vector operations
# ChromaDB removed in favor of unified vector storage

# Code analysis and formatting
isort>=5.13.2
mypy>=1.8.0
ruff>=0.1.0

# Security and Hashing
argon2-cffi>=23.1.0

# Type Checking
types-PyYAML>=6.0.12
types-docker>=6.1.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.4.0
