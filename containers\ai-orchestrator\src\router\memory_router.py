"""
Memory Management Router for AI Coding Agent.

This module provides REST API endpoints for memory management operations,
including storing, retrieving, and learning from memories.

Author: AI Coding Agent
Version: 1.0.0
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Body, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from src.core.config import settings
from src.schemas.memory_schemas import (
    ContextResponse,
    MemoryCleanupRequest,
    MemoryCleanupResponse,
    MemorySearchRequest,
    MemorySearchResponse,
    MemoryStatistics,
    MemoryStoreRequest,
    PatternStoreRequest,
    ValidationResult,
)
from src.services.auth_service import get_current_user
from src.services.memory_management_service import (
    CodePattern,
    LearningOutcome,
    MemoryEntry,
    MemoryManagementService,
    MemoryPriority,
    MemoryType,
    get_memory_service,
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/api/memory", tags=["memory"], responses={404: {"description": "Not found"}}
)


@router.post("/store", response_model=MemoryEntry)
async def store_memory(
    request: MemoryStoreRequest,
    current_user=Depends(get_current_user),
    memory_service: MemoryManagementService = Depends(get_memory_service),
):
    """
    Store a new memory entry.

    This endpoint allows storing various types of memories including
    user preferences, code patterns, project context, and learning outcomes.
    """
    try:
        # Derive owner_id from authenticated user
        owner_id = getattr(current_user, "supabase_user_id", None)

        if not owner_id:
            raise HTTPException(status_code=401, detail="Unauthenticated")

        memory = await memory_service.store_memory(
            content=request.content,
            memory_type=request.memory_type,
            tags=request.tags,
            metadata=None,  # Will be created with defaults
            owner_id=owner_id,
            use_database=True,  # Enable database storage
        )

        # Update metadata if additional fields provided
        if request.priority != MemoryPriority.MEDIUM:
            memory.metadata.priority = request.priority
        if request.expires_at:
            memory.metadata.expires_at = request.expires_at
        if request.source != "ai_agent":
            memory.metadata.source = request.source

        await memory_service._save_memories()  # Save updated metadata

        logger.info(f"Stored memory via API: {memory.id}")
        return memory

    except Exception as e:
        logger.error(f"Failed to store memory: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to store memory: {str(e)}")


@router.post("/search", response_model=MemorySearchResponse)
async def search_memories(
    request: MemorySearchRequest,
    current_user=Depends(get_current_user),
    memory_service: MemoryManagementService = Depends(get_memory_service),
):
    """
    Search for memories based on various criteria.

    Supports text search, filtering by memory type, tags, and result limiting.
    """
    try:
        start_time = datetime.now()

        owner_id = getattr(current_user, "supabase_user_id", None)
        if not owner_id:
            raise HTTPException(status_code=401, detail="Unauthenticated")

        memories = await memory_service.search_memories(
            query=request.query,
            owner_id=owner_id,
            memory_type=request.memory_type,
            limit=request.limit,
            use_semantic=request.use_semantic if hasattr(request, "use_semantic") else True,
            use_database=True,
        )

        execution_time = (datetime.now() - start_time).total_seconds()

        # Convert search results to MemoryEntry objects for response
        memory_entries = []
        for result in memories:
            if isinstance(result, dict):
                # Handle new search format with scores
                metadata = result.get("metadata", {})
                memory_entry = MemoryEntry(
                    id=result["id"],
                    content=result["content"],
                    memory_type=MemoryType(result["memory_type"]),
                    metadata=metadata,
                )
                memory_entries.append(memory_entry)
            else:
                # Handle legacy format
                memory_entries.append(result)

        search_criteria = {
            "query": request.query,
            "memory_type": request.memory_type.value if request.memory_type else None,
            "tags": request.tags,
            "limit": request.limit,
            "use_semantic": getattr(request, "use_semantic", True),
        }

        response = MemorySearchResponse(
            memories=memory_entries,
            total_count=len(memory_entries),
            search_criteria=search_criteria,
            execution_time=execution_time,
        )

        logger.info(f"Memory search completed: {len(memories)} results in {execution_time:.2f}s")
        return response

    except Exception as e:
        logger.error(f"Failed to search memories: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to search memories: {str(e)}")


@router.get("/stats", response_model=Dict[str, Any])
async def get_memory_statistics(
    owner_id: Optional[str] = Query(None, description="Owner's Supabase user ID"),
    memory_service: MemoryManagementService = Depends(get_memory_service),
    current_user=Depends(get_current_user),
):
    """
    Get comprehensive memory statistics.

    Returns statistics about stored memories, patterns, and learning outcomes.
    """
    try:
        # Owner ID must come from authenticated user; ignore query param
        owner = getattr(current_user, "supabase_user_id", None)
        if not owner:
            raise HTTPException(status_code=401, detail="Unauthenticated")
        stats = await memory_service.get_memory_stats(owner_id=owner, use_database=True)

        logger.info(f"Retrieved memory statistics for user {owner_id}")
        return stats

    except Exception as e:
        logger.error(f"Failed to get memory statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get memory statistics: {str(e)}")


@router.post("/patterns", response_model=CodePattern)
async def store_code_pattern(
    request: PatternStoreRequest,
    current_user=Depends(get_current_user),
    memory_service: MemoryManagementService = Depends(get_memory_service),
):
    """
    Store a reusable code pattern.

    This endpoint allows storing code patterns that can be reused
    for consistent code generation across the project.
    """
    try:
        # Patterns are associated with an authenticated owner implicitly
        pattern = CodePattern(**request.dict())
        stored_pattern = await memory_service.store_code_pattern(pattern)

        logger.info(f"Stored code pattern via API: {pattern.name}")
        return stored_pattern

    except Exception as e:
        logger.error(f"Failed to store code pattern: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to store code pattern: {str(e)}")


@router.get("/patterns", response_model=List[CodePattern])
async def get_code_patterns(
    language: Optional[str] = Query(None, description="Filter by programming language"),
    framework: Optional[str] = Query(None, description="Filter by framework"),
    pattern_type: Optional[str] = Query(None, description="Filter by pattern type"),
    memory_service: MemoryManagementService = Depends(get_memory_service),
):
    """
    Retrieve code patterns based on criteria.

    Supports filtering by language, framework, and pattern type.
    """
    try:
        patterns = await memory_service.get_code_patterns(
            language=language, framework=framework, pattern_type=pattern_type
        )

        logger.info(f"Retrieved {len(patterns)} code patterns")
        return patterns

    except Exception as e:
        logger.error(f"Failed to retrieve code patterns: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve code patterns: {str(e)}")


@router.post("/learn", response_model=LearningOutcome)
async def learn_from_interaction(
    request: Dict[str, Any],
    current_user=Depends(get_current_user),
    memory_service: MemoryManagementService = Depends(get_memory_service),
):
    """
    Learn from an interaction and store the outcome.

    This endpoint allows the AI agent to learn from interactions
    and improve future responses based on outcomes.
    """
    try:
        learning = await memory_service.learn_from_interaction(
            interaction_type=request.get("interaction_type"),
            content=request.get("content"),
            outcome=request.get("outcome"),
            context=request.get("context", {}),
        )

        logger.info(f"Learned from interaction: {learning.topic}")
        return learning

    except Exception as e:
        logger.error(f"Failed to learn from interaction: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to learn from interaction: {str(e)}")


@router.post("/context", response_model=ContextResponse)
async def get_relevant_context(
    request: Dict[str, Any],
    current_user=Depends(get_current_user),
    memory_service: MemoryManagementService = Depends(get_memory_service),
):
    """
    Get relevant context for a coding task.

    This endpoint provides comprehensive context including memories,
    code patterns, and learning outcomes relevant to a specific task.
    """
    try:
        context = await memory_service.get_relevant_context(
            task=request.get("task"),
            language=request.get("language"),
            framework=request.get("framework"),
        )

        # Convert dict response to ContextResponse model
        response = ContextResponse(
            memories=[MemoryEntry(**m) for m in context["memories"]],
            patterns=[CodePattern(**p) for p in context["patterns"]],
            learnings=[LearningOutcome(**l) for l in context["learnings"]],
            context_summary=context["context_summary"],
        )

        logger.info(f"Retrieved context for task: {request.task}")
        return response

    except Exception as e:
        logger.error(f"Failed to get relevant context: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get relevant context: {str(e)}")


@router.get("/stats", response_model=MemoryStatistics)
async def get_memory_statistics(
    memory_service: MemoryManagementService = Depends(get_memory_service),
    current_user=Depends(get_current_user),
):
    """
    Get comprehensive statistics about the memory system.

    Provides insights into memory usage, patterns, and system health.
    """
    try:
        stats = await memory_service.get_statistics()

        statistics = MemoryStatistics(**stats)
        logger.info("Retrieved memory statistics")
        return statistics

    except Exception as e:
        logger.error(f"Failed to get memory statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get memory statistics: {str(e)}")


@router.post("/validate")
async def validate_memory_content(
    content: str = Body(..., description="Content to validate"),
    memory_type: MemoryType = Body(..., description="Type of memory"),
):
    """
    Validate memory content before storing.

    This endpoint performs validation checks on memory content
    to ensure quality and consistency.
    """
    try:
        errors = []
        warnings = []
        suggestions = []

        # Basic validation
        if len(content.strip()) < 10:
            errors.append("Content is too short (minimum 10 characters)")

        if (
            len(content.strip()) > settings.MAX_MEMORY_ENTRIES * 10
        ):  # Rough estimate based on max entries
            warnings.append("Content is very long, consider breaking it into smaller memories")

        # Type-specific validation
        if memory_type == MemoryType.CODE_PATTERN:
            if "```" not in content:
                suggestions.append(
                    "Consider including code examples with proper markdown formatting"
                )

        if memory_type == MemoryType.LEARNING_OUTCOME:
            if not any(
                word in content.lower() for word in ["learned", "lesson", "outcome", "improved"]
            ):
                suggestions.append("Learning outcomes should describe what was learned or improved")

        is_valid = len(errors) == 0

        result = ValidationResult(
            is_valid=is_valid, errors=errors, warnings=warnings, suggestions=suggestions
        )

        logger.info(f"Validated memory content: valid={is_valid}")
        return result

    except Exception as e:
        logger.error(f"Failed to validate memory content: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to validate memory content: {str(e)}")


@router.post("/cleanup", response_model=MemoryCleanupResponse)
async def cleanup_memories(
    request: MemoryCleanupRequest,
    current_user=Depends(get_current_user),
    memory_service: MemoryManagementService = Depends(get_memory_service),
):
    """
    Clean up memories based on criteria.

    This endpoint allows cleaning up expired memories, old entries,
    or memories with specific tags. Supports dry-run mode.
    """
    try:
        # For now, just clean up expired memories for the authenticated user
        owner = getattr(current_user, "supabase_user_id", None)
        if not owner:
            raise HTTPException(status_code=401, detail="Unauthenticated")
        # The service currently handles local cleanup; database-scoped cleanup will use owner
        await memory_service.cleanup_expired_memories()

        # In a full implementation, this would handle all the cleanup criteria
        response = MemoryCleanupResponse(
            memories_removed=0,  # Would be calculated based on actual cleanup
            patterns_removed=0,
            learnings_removed=0,
            errors=[],
            dry_run=request.dry_run,
        )

        logger.info(f"Memory cleanup completed: dry_run={request.dry_run}")
        return response

    except Exception as e:
        logger.error(f"Failed to cleanup memories: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cleanup memories: {str(e)}")


@router.get("/health")
async def memory_health_check(
    memory_service: MemoryManagementService = Depends(get_memory_service),
):
    """
    Health check for the memory system.

    Returns the status of the memory service and basic statistics.
    """
    try:
        stats = await memory_service.get_statistics()

        return {"status": "healthy", "timestamp": datetime.now().isoformat(), "statistics": stats}

    except Exception as e:
        logger.error(f"Memory health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
            },
        )
