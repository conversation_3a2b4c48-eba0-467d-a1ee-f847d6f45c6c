name: Publish Images to GHCR

on:
  push:
    branches:
      - main

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: jlambey/codingagenttwo

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        include:
          - service: ai-orchestrator
            dockerfile: ./containers/ai-orchestrator/Dockerfile
            context: ./containers/ai-orchestrator
            target: production
          - service: user-portal
            dockerfile: ./containers/user-portal/Dockerfile.dev
            context: ./containers/user-portal
            target: production
          - service: code-server
            dockerfile: ./containers/code-server/Dockerfile
            context: ./containers/code-server
            target: production
          - service: ollama
            dockerfile: ./containers/ollama/Dockerfile
            context: ./containers/ollama
            target: production

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/jlambey/codingagenttwo/${{ matrix.service }}
          tags: |
            type=sha,prefix=sha-
            type=raw,value=latest

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ${{ matrix.context }}
          file: ${{ matrix.dockerfile }}
          target: ${{ matrix.target }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
