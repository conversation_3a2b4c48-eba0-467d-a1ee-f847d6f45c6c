#!/bin/bash

# AI Coding Agent Development Startup Script
# This script starts the development environment with hot-reloading and debugging features
# Usage: ./scripts/start-dev.sh

set -e

echo " AI Coding Agent - Development Setup"
echo "======================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo " Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if required files exist
if [ ! -f "docker-compose.yml" ]; then
    echo " docker-compose.yml not found. Please run from project root."
    exit 1
fi

if [ ! -f "docker-compose.dev.yml" ]; then
    echo " docker-compose.dev.yml not found. Please run from project root."
    exit 1
fi

echo " Found Docker Compose configuration files"
echo " Using multi-file configuration:"
echo "   • docker-compose.yml (base services)"
echo "   • docker-compose.dev.yml (development overrides)"
echo "   → This ensures code-server is included for development!"
echo ""

# Start the development environment
# -f docker-compose.yml: Base production configuration
# -f docker-compose.dev.yml: Development overrides (ports, volumes, hot-reload)
# up -d: Start in detached mode (background)
# --build: Rebuild images if needed
# --profile cpu: Explicitly enable the 'cpu' profile for Ollama
# --watch: Enable file watching for hot-reloading
docker compose -f docker-compose.yml -f docker-compose.dev.yml --profile cpu up --build --watch

echo ""
echo " Development environment started successfully!"
echo ""
echo " Access points:"
echo "   API:              http://api.localhost"
echo "   User Portal:      http://portal.localhost"
echo "   Traefik Dashboard: http://traefik.localhost"
echo "   Code Server:      http://localhost:9999"
echo "   Supabase Studio:  http://127.0.0.1:54323"
echo "   Redis:            localhost:6379"
echo ""
echo " Note: Make sure Supabase is running (supabase start) before accessing the portal"
echo ""
echo " To stop: docker compose -f docker-compose.yml -f docker-compose.dev.yml down"
