name: Docker Build and Publish

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'containers/**/Dockerfile'
      - '.github/workflows/docker-lint.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'containers/**/Dockerfile'

jobs:
  hadolint:
    name: Run Hadolint
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Hadolint
      uses: hadolint/hadolint-action@v3.1.0
      with:
        dockerfile: "containers/**/Dockerfile"
        # Configuration options for hadolint
        # Ignore certain rules that might not apply to our use case
        ignored-rules: |
          DL3008 # Pin versions in apt-get install (we want latest for security updates)
          DL3018 # Pin versions in apk add (not using alpine)
          DL4006 # Set the SHELL option (we use bash explicitly)
        # Treat warnings as errors for production readiness
        failure-threshold: warning

    - name: Upload Hadolint results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: hadolint-results
        path: |
          hadolint-output.json
          hadolint-output.sarif

  docker-build-verify:
    name: Verify Docker Build
    runs-on: ubuntu-latest
    needs: hadolint
    if: success() || failure() # Run even if hadolint fails to catch build issues

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Ollama container (dry-run)
      run: |
        cd containers/ollama
        docker build --pull --no-cache --progress=plain --target builder -t ollama-builder .
        echo "Builder stage completed successfully"

    - name: Build final image (dry-run)
      run: |
        cd containers/ollama
        docker build --pull --no-cache --progress=plain -t ollama-final .
        echo "Final image build completed successfully"

  publish-image:
    name: Publish to Container Registry
    runs-on: ubuntu-latest
    needs: [hadolint, docker-build-verify]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata for Docker
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: |
          ghcr.io/${{ github.repository_owner }}/ollama-service
        tags: |
          type=sha
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}

    - name: Build and push Ollama image
      uses: docker/build-push-action@v5
      with:
        context: containers/ollama
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Image digest
      run: echo ${{ steps.build-and-push.outputs.digest }}
