#!/bin/bash
#
# Reliable runner for Alembic commands
#
# This script ensures that <PERSON><PERSON><PERSON> is executed in the correct environment,
# with the virtual environment activated and the PYTHONPATH set correctly.
# It should be run from within the 'containers/ai-orchestrator' directory.
#
# Usage:
# ./run_alembic.sh <alembic_command>
#
# Example:
# ./run_alembic.sh current
# ./run_alembic.sh upgrade head
# ./run_alembic.sh revision --autogenerate -m "Your message"
#

set -e

# Get the directory of this script, which is /app/containers/ai-orchestrator
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
# The project root is two levels up from the script's directory
PROJECT_ROOT_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"
VENV_PATH="$PROJECT_ROOT_DIR/.venv/bin/activate"

# Check if we are in the correct directory
if [ "$PWD" != "$SCRIPT_DIR" ]; then
  echo "Error: This script must be run from the 'containers/ai-orchestrator' directory."
  echo "Please 'cd $SCRIPT_DIR' and try again."
  exit 1
fi

# Check if the virtual environment exists
if [ ! -f "$VENV_PATH" ]; then
    echo "Error: Virtual environment not found at $VENV_PATH"
    echo "Please run the setup script in /.jules/setup.sh first."
    exit 1
fi

# Activate the virtual environment
# shellcheck disable=SC1090
source "$VENV_PATH"
echo "Virtual environment activated."

# Set the PYTHONPATH to the ai-orchestrator directory, so that `from src...` imports work
export PYTHONPATH=$SCRIPT_DIR

echo "PYTHONPATH set to: $PYTHONPATH"
echo "Running command: alembic $@"
echo "-------------------------------------"

# Execute the alembic command with all passed arguments
alembic "$@"

echo "-------------------------------------"
echo "Alembic command finished."
