import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ConfigManager from '../ConfigManager';
import { useRoleManager } from '../../hooks/useRoleManager';
import { mockRoles } from './mockData';

// Mock the useRoleManager hook
jest.mock('../../hooks/useRoleManager');
const mockedUseRoleManager = useRoleManager as jest.Mock;

// Mock the RoleConfigPanel component
jest.mock('../RoleConfigPanel', () => {
  return function MockRoleConfigPanel({ roleName, onSave, isLoading }: {
    roleName: string;
    onSave: (config: Record<string, unknown>) => void;
    isLoading: boolean;
  }) {
    return (
      <div data-testid={`role-config-${roleName}`}>
        <h3>Config for {roleName}</h3>
        <button
          onClick={() => onSave({})}
          disabled={isLoading}
        >
          {isLoading ? 'Saving...' : 'Save'}
        </button>
      </div>
    );
  };
});

describe('ConfigManager Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock implementation
    mockedUseRoleManager.mockReturnValue({
      roles: null,
      activeRole: null,
      loading: { global: 'loading' },
      errors: {},
      showCreateForm: false,
      showDeleteModal: null,
      newRoleName: '',
      setActiveRole: jest.fn(),
      refreshRoles: jest.fn().mockResolvedValue(undefined),
      createRole: jest.fn().mockResolvedValue(true),
      updateRole: jest.fn().mockResolvedValue(true),
      deleteRole: jest.fn().mockResolvedValue(true),
      saveRole: jest.fn().mockResolvedValue(true),
      clearError: jest.fn(),
      openCreateForm: jest.fn(),
      closeCreateForm: jest.fn(),
      openDeleteModal: jest.fn(),
      closeDeleteModal: jest.fn(),
      setNewRoleName: jest.fn(),
      getRoleNames: jest.fn().mockReturnValue([]),
      getRoleByName: jest.fn(),
      isRoleEnabled: jest.fn().mockReturnValue(true),
      getRoleDisplayName: jest.fn((roleName) => roleName.charAt(0).toUpperCase() + roleName.slice(1)),
      canDeleteRole: jest.fn().mockReturnValue(true),
    });
  });

  const setupMockWithData = () => {
    mockedUseRoleManager.mockReturnValue({
      roles: mockRoles,
      activeRole: 'architect',
      loading: { global: 'idle' },
      errors: {},
      showCreateForm: false,
      showDeleteModal: null,
      newRoleName: '',
      setActiveRole: jest.fn(),
      refreshRoles: jest.fn().mockResolvedValue(undefined),
      createRole: jest.fn().mockResolvedValue(true),
      updateRole: jest.fn().mockResolvedValue(true),
      deleteRole: jest.fn().mockResolvedValue(true),
      saveRole: jest.fn().mockResolvedValue(true),
      clearError: jest.fn(),
      openCreateForm: jest.fn(),
      closeCreateForm: jest.fn(),
      openDeleteModal: jest.fn(),
      closeDeleteModal: jest.fn(),
      setNewRoleName: jest.fn(),
      getRoleNames: jest.fn().mockReturnValue(Object.keys(mockRoles)),
      getRoleByName: jest.fn((name) => mockRoles[name]),
      isRoleEnabled: jest.fn((name) => mockRoles[name]?.enabled),
      getRoleDisplayName: jest.fn((roleName) => roleName.charAt(0).toUpperCase() + roleName.slice(1)),
      canDeleteRole: jest.fn().mockReturnValue(Object.keys(mockRoles).length > 1),
    });
  };

  it('renders the component with initial loading state', async () => {
    render(<ConfigManager />);
    expect(await screen.findByText('Loading role configurations...')).toBeInTheDocument();
  });

  it('displays role tabs after loading', async () => {
    setupMockWithData();
    render(<ConfigManager />);

  expect(await screen.findByText('Architect')).toBeInTheDocument();
  expect(await screen.findByText('Backend')).toBeInTheDocument();
  expect(await screen.findByText('Frontend')).toBeInTheDocument();
  await waitFor(() => expect(screen.queryByText('Loading role configurations...')).not.toBeInTheDocument());
  });

  it('switches between role configurations', async () => {
    setupMockWithData();
    const setActiveRole = jest.fn();
    mockedUseRoleManager.mockReturnValue({
      ...mockedUseRoleManager(),
      roles: mockRoles,
      loading: { global: 'idle' },
      activeRole: 'architect',
      setActiveRole,
      getRoleNames: () => Object.keys(mockRoles),
    });

    render(<ConfigManager />);

  const backendTab = await screen.findByText('Backend');
  await userEvent.click(backendTab);

    expect(setActiveRole).toHaveBeenCalledWith('backend');
  });

  it('handles role creation workflow', async () => {
    setupMockWithData();
    const openCreateForm = jest.fn();
    const closeCreateForm = jest.fn();
    const createRole = jest.fn().mockResolvedValue(true);
    const setNewRoleName = jest.fn();

    // Initial render state
    mockedUseRoleManager.mockImplementation(() => ({
      ...mockedUseRoleManager(),
      roles: mockRoles,
      loading: { global: 'idle' },
      activeRole: 'architect',
      showCreateForm: false,
      openCreateForm,
      getRoleNames: () => Object.keys(mockRoles),
    }));

    const { rerender } = render(<ConfigManager />);

  const addRoleButton = await screen.findByText('Add Role');
  await userEvent.click(addRoleButton);
    expect(openCreateForm).toHaveBeenCalled();

    // Mock state change for showing the form
    mockedUseRoleManager.mockImplementation(() => ({
      ...mockedUseRoleManager(),
      roles: mockRoles,
      loading: { global: 'idle' },
      activeRole: 'architect',
      showCreateForm: true,
      newRoleName: 'Test Role',
      closeCreateForm,
      createRole,
      setNewRoleName,
      getRoleNames: () => Object.keys(mockRoles),
    }));

    rerender(<ConfigManager />);

  const nameInput = await screen.findByPlaceholderText('e.g., Code Reviewer');
  const createButton = await screen.findByText('Create Role');

  await userEvent.type(nameInput, 'Test Role');
  await waitFor(() => expect(setNewRoleName).toHaveBeenCalledWith('Test Role'));

  await userEvent.click(createButton);
  await waitFor(() => expect(createRole).toHaveBeenCalled());
  });

  it('handles role deletion workflow', async () => {
    setupMockWithData();
    const openDeleteModal = jest.fn();
    const closeDeleteModal = jest.fn();
    const deleteRole = jest.fn().mockResolvedValue(true);

    mockedUseRoleManager.mockImplementation(() => ({
      ...mockedUseRoleManager(),
      roles: mockRoles,
      loading: { global: 'idle' },
      activeRole: 'frontend',
      showDeleteModal: null,
      openDeleteModal,
      getRoleNames: () => Object.keys(mockRoles),
  canDeleteRole: (name: string) => name !== 'architect',
    }));

    const { rerender } = render(<ConfigManager />);

  const deleteButton = (await screen.findAllByLabelText(/delete role/i))[1]; // Get delete button for 'Frontend'
  await userEvent.click(deleteButton);
    expect(openDeleteModal).toHaveBeenCalledWith('frontend');

    // Mock state change for showing the modal
    mockedUseRoleManager.mockImplementation(() => ({
      ...mockedUseRoleManager(),
      roles: mockRoles,
      loading: { global: 'idle' },
      activeRole: 'frontend',
      showDeleteModal: 'frontend',
      closeDeleteModal,
      deleteRole,
      getRoleNames: () => Object.keys(mockRoles),
  canDeleteRole: (name: string) => name !== 'architect',
    }));

    rerender(<ConfigManager />);

  const confirmButton = await screen.findByText('Delete Role');
  await userEvent.click(confirmButton);
  await waitFor(() => expect(deleteRole).toHaveBeenCalledWith('frontend'));
  });

  it('handles configuration saving with loading states', async () => {
    setupMockWithData();
    const saveRole = jest.fn();
    mockedUseRoleManager.mockReturnValue({
      ...mockedUseRoleManager(),
      roles: mockRoles,
      loading: { global: 'idle', save: {} },
      activeRole: 'architect',
      saveRole,
      getRoleNames: () => Object.keys(mockRoles),
    });

    const { rerender } = render(<ConfigManager />);

  const saveButton = (await screen.findByTestId('role-config-architect')).querySelector('button');
    expect(saveButton).not.toBeNull();

  await userEvent.click(saveButton!);
  await waitFor(() => expect(saveRole).toHaveBeenCalledWith('architect'));

    // Mock saving state
    mockedUseRoleManager.mockReturnValue({
      ...mockedUseRoleManager(),
      roles: mockRoles,
      loading: { global: 'idle', save: { architect: true } },
      activeRole: 'architect',
      saveRole,
      getRoleNames: () => Object.keys(mockRoles),
    });
    rerender(<ConfigManager />);

  expect(await screen.findByText('Saving...')).toBeInTheDocument();
  });

  it('validates role name during creation', async () => {
    setupMockWithData();
    const createRole = jest.fn();
    const openCreateForm = jest.fn();

    mockedUseRoleManager.mockImplementation(() => ({
      ...mockedUseRoleManager(),
      roles: mockRoles,
      loading: { global: 'idle' },
      activeRole: 'architect',
      showCreateForm: true,
      newRoleName: 'architect', // Existing name
      errors: { create: 'Role name already exists' },
      createRole,
      openCreateForm,
      getRoleNames: () => Object.keys(mockRoles),
    }));

    render(<ConfigManager />);

  expect(await screen.findByText('Role name already exists')).toBeInTheDocument();
  });

  it('displays role status indicators', async () => {
    setupMockWithData();
    render(<ConfigManager />);

  const frontendTab = await screen.findByText('Frontend');
  const indicator = frontendTab.querySelector('span');
  expect(indicator).toHaveClass('bg-gray-400'); // For disabled role
  });

  it('handles empty state gracefully', async () => {
    mockedUseRoleManager.mockReturnValue({
      ...mockedUseRoleManager(),
      roles: {},
      loading: { global: 'idle' },
      getRoleNames: () => [],
    });

    render(<ConfigManager />);

  expect(await screen.findByText('No roles configured.')).toBeInTheDocument();
  expect(await screen.findByText('Add Role')).toBeInTheDocument();
  });
});