{"python.pythonPath": "/usr/bin/python3", "python.linting.enabled": true, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "editor.formatOnSave": true, "files.associations": {"*.py": "python", "*.js": "javascript", "*.jsx": "javascriptreact", "*.ts": "typescript", "*.tsx": "typescriptreact", "*.json": "json", "*.yml": "yaml", "*.yaml": "yaml", "Dockerfile*": "dockerfile"}, "terminal.integrated.shell.linux": "/bin/bash", "gitlens.advanced.blame.customArguments": [], "tailwindCSS.includeLanguages": {"plaintext": "html", "javascript": "javascript", "typescript": "typescript", "javascriptreact": "javascriptreact", "typescriptreact": "typescriptreact"}, "context7.enabled": true, "context7.apiKey": "${OPENROUTER_API_KEY}", "context7.model": "deepseek/deepseek-chat", "context7.maxTokens": 4096, "context7.temperature": 0.7}