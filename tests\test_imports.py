#!/usr/bin/env python3
"""
Test the Python path setup to verify imports work correctly.

This script tests whether the environment is properly configured
to run the AI Coding Agent tests.

Usage:
    python test_imports.py
"""

import sys
import os
from pathlib import Path

def test_python_path_setup():
    """Test that Python paths are set up correctly."""
    print("=== Testing Python Path Setup ===")

    # Get current directory
    current_dir = Path(__file__).parent.absolute()
    print(f"Current directory: {current_dir}")

    # Check if ai-orchestrator directory exists
    ai_orchestrator_dir = current_dir / "containers" / "ai-orchestrator"
    src_dir = ai_orchestrator_dir / "src"

    print(f"AI Orchestrator directory: {ai_orchestrator_dir}")
    print(f"Source directory: {src_dir}")

    if not ai_orchestrator_dir.exists():
        print("❌ ERROR: AI Orchestrator directory not found!")
        return False

    if not src_dir.exists():
        print("❌ ERROR: Source directory not found!")
        return False

    print("✅ Required directories found")

    # Add to Python path
    sys.path.insert(0, str(src_dir))
    sys.path.insert(0, str(ai_orchestrator_dir))

    print("✅ Added directories to Python path")
    return True

def test_basic_imports():
    """Test basic imports that tests require."""
    print("\n=== Testing Basic Imports ===")

    import_tests = [
        ("src", "Root src module"),
        ("src.models", "Models module"),
        ("src.services", "Services module"),
        ("src.agents", "Agents module"),
    ]

    success_count = 0

    for module_name, description in import_tests:
        try:
            __import__(module_name)
            print(f"✅ {description}: {module_name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {description}: {module_name} - {str(e)}")

    print(f"\nImport Results: {success_count}/{len(import_tests)} successful")
    return success_count == len(import_tests)

def test_specific_imports():
    """Test specific imports that failing tests use."""
    print("\n=== Testing Specific Test Imports ===")

    specific_tests = [
        ("src.models.validation_models", "Validation models"),
        ("src.services.task_validator", "Task validator"),
    ("src.agents.architect_agent", "Architect agent"),
        ("src.models.llm_models", "LLM models"),
    ]

    success_count = 0

    for module_name, description in specific_tests:
        try:
            module = __import__(module_name, fromlist=[''])
            print(f"✅ {description}: {module_name}")

            # Try to access some common attributes
            if hasattr(module, '__file__'):
                print(f"   Located at: {module.__file__}")

            success_count += 1
        except ImportError as e:
            print(f"❌ {description}: {module_name} - {str(e)}")
        except Exception as e:
            print(f"⚠️  {description}: {module_name} - Import succeeded but error occurred: {str(e)}")
            success_count += 1  # Count as success since import worked

    print(f"\nSpecific Import Results: {success_count}/{len(specific_tests)} successful")
    return success_count == len(specific_tests)

def test_environment_variables():
    """Test that environment variables are set correctly."""
    print("\n=== Testing Environment Variables ===")

    required_env_vars = [
        'ENVIRONMENT',
        'USE_SUPABASE',
        'DATABASE_URL',
        'REDIS_URL',
        'JWT_SECRET'
    ]

    # Set test environment variables
    test_env = {
        'ENVIRONMENT': 'testing',
        'USE_SUPABASE': 'false',
        'DATABASE_URL': 'postgresql://test:test@localhost:5432/test_db',
        'REDIS_URL': 'redis://localhost:6379',
        'JWT_SECRET': 'test_jwt_secret_key_for_testing_only_32_chars_minimum'
    }

    for key, value in test_env.items():
        os.environ[key] = value

    success_count = 0
    for var in required_env_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var} = {value}")
            success_count += 1
        else:
            print(f"❌ {var} not set")

    print(f"\nEnvironment Results: {success_count}/{len(required_env_vars)} set")
    return success_count == len(required_env_vars)

def main():
    """Run all tests."""
    print("🧪 AI Coding Agent Import Test Suite")
    print("=" * 50)

    tests = [
        ("Python Path Setup", test_python_path_setup),
        ("Basic Imports", test_basic_imports),
        ("Specific Imports", test_specific_imports),
        ("Environment Variables", test_environment_variables),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("🏁 Test Summary:")

    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if not passed:
            all_passed = False

    if all_passed:
        print("\n🎉 All tests passed! You should be able to run pytest now:")
        print("   python -m pytest tests/ -v")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
        print("   Try running setup_test_environment.py first:")
        print("   python setup_test_environment.py")

    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
