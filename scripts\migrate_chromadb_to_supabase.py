#!/usr/bin/env python3
"""
ChromaDB to Supabase pgvector Migration Script

This script migrates data from ChromaDB backup files to the unified
Supabase pgvector knowledge tier system.

Usage:
    python migrate_chromadb_to_supabase.py --backup-dir ./backup/chromadb_migration

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import argparse
import json
import logging
import os
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import dataclass
import hashlib

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class MigrationStats:
    """Migration statistics tracking."""
    total_processed: int = 0
    public_migrated: int = 0
    private_migrated: int = 0
    errors: int = 0
    skipped: int = 0

    def to_dict(self) -> Dict[str, int]:
        """Convert to dictionary for reporting."""
        return {
            'total_processed': self.total_processed,
            'public_migrated': self.public_migrated,
            'private_migrated': self.private_migrated,
            'errors': self.errors,
            'skipped': self.skipped
        }


class ChromaDBMigrator:
    """Migrates ChromaDB data to Supabase pgvector with knowledge tiers."""

    def __init__(self, supabase_url: str, supabase_key: str):
        """Initialize migrator with Supabase connection."""
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        self.stats = MigrationStats()

        # Collection to knowledge tier mapping
        self.collection_tier_mapping = {
            'public_docs': 'public',
            'public_documentation': 'public',
            'code_snippets': 'public',
            'tutorials': 'public',
            'api_references': 'public',
            'examples': 'public',
            'guides': 'public',
            # All other collections default to private
        }

    async def migrate_backup_directory(self, backup_dir: Path) -> MigrationStats:
        """
        Migrate all ChromaDB backup files in directory.

        Args:
            backup_dir: Directory containing ChromaDB backup files

        Returns:
            Migration statistics
        """
        try:
            logger.info(f"Starting migration from backup directory: {backup_dir}")

            if not backup_dir.exists():
                raise FileNotFoundError(f"Backup directory not found: {backup_dir}")

            # Look for ChromaDB backup files (JSON format expected)
            backup_files = list(backup_dir.glob("*.json"))

            if not backup_files:
                logger.warning(f"No JSON backup files found in {backup_dir}")
                return self.stats

            logger.info(f"Found {len(backup_files)} backup files to process")

            for backup_file in backup_files:
                try:
                    await self._migrate_backup_file(backup_file)
                except Exception as e:
                    logger.error(f"Failed to migrate {backup_file}: {str(e)}")
                    self.stats.errors += 1

            logger.info(f"Migration completed: {self.stats.to_dict()}")
            return self.stats

        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            raise

    async def _migrate_backup_file(self, backup_file: Path) -> None:
        """Migrate a single ChromaDB backup file."""
        logger.info(f"Processing backup file: {backup_file}")

        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            # Extract collection name from filename or data
            collection_name = backup_file.stem

            # Determine knowledge tier
            knowledge_tier = self.collection_tier_mapping.get(
                collection_name, 'private'
            )

            logger.info(f"Migrating collection '{collection_name}' to tier '{knowledge_tier}'")

            # Process documents in the backup
            if 'documents' in backup_data:
                await self._migrate_documents(
                    backup_data['documents'],
                    collection_name,
                    knowledge_tier
                )
            elif 'embeddings' in backup_data and 'metadatas' in backup_data:
                # Handle different backup format
                await self._migrate_embeddings_format(
                    backup_data,
                    collection_name,
                    knowledge_tier
                )
            else:
                logger.warning(f"Unknown backup format in {backup_file}")
                self.stats.skipped += 1

        except Exception as e:
            logger.error(f"Error processing {backup_file}: {str(e)}")
            self.stats.errors += 1

    async def _migrate_documents(
        self,
        documents: List[Dict[str, Any]],
        collection_name: str,
        knowledge_tier: str
    ) -> None:
        """Migrate documents from ChromaDB format."""
        for i, doc in enumerate(documents):
            try:
                self.stats.total_processed += 1

                # Extract document data
                content = doc.get('content', '')
                metadata = doc.get('metadata', {})
                embedding = doc.get('embedding', [])
                document_id = doc.get('id', f"{collection_name}_{i}")

                if not content:
                    logger.warning(f"Skipping document with no content: {document_id}")
                    self.stats.skipped += 1
                    continue

                # Generate content hash for deduplication
                content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()

                # Enhance metadata
                enhanced_metadata = {
                    **metadata,
                    'collection': collection_name,
                    'migrated_from': 'chromadb',
                    'original_id': document_id
                }

                # Store in Supabase with knowledge tier
                await self._store_in_supabase(
                    document_id=document_id,
                    content=content,
                    embedding=embedding,
                    knowledge_tier=knowledge_tier,
                    metadata=enhanced_metadata,
                    content_hash=content_hash
                )

                if knowledge_tier == 'public':
                    self.stats.public_migrated += 1
                else:
                    self.stats.private_migrated += 1

                # Log progress every 100 documents
                if self.stats.total_processed % 100 == 0:
                    logger.info(f"Processed {self.stats.total_processed} documents...")

            except Exception as e:
                logger.error(f"Failed to migrate document {i}: {str(e)}")
                self.stats.errors += 1

    async def _migrate_embeddings_format(
        self,
        backup_data: Dict[str, Any],
        collection_name: str,
        knowledge_tier: str
    ) -> None:
        """Migrate from embeddings + metadatas format."""
        embeddings = backup_data.get('embeddings', [])
        metadatas = backup_data.get('metadatas', [])
        documents = backup_data.get('documents', [])
        ids = backup_data.get('ids', [])

        # Ensure all arrays have the same length
        max_len = max(len(embeddings), len(metadatas), len(documents), len(ids))

        for i in range(max_len):
            try:
                self.stats.total_processed += 1

                # Extract data with bounds checking
                content = documents[i] if i < len(documents) else f"Document {i}"
                metadata = metadatas[i] if i < len(metadatas) else {}
                embedding = embeddings[i] if i < len(embeddings) else []
                document_id = ids[i] if i < len(ids) else f"{collection_name}_{i}"

                if not content:
                    self.stats.skipped += 1
                    continue

                # Generate content hash
                content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()

                # Enhance metadata
                enhanced_metadata = {
                    **metadata,
                    'collection': collection_name,
                    'migrated_from': 'chromadb',
                    'original_id': document_id
                }

                # Store in Supabase
                await self._store_in_supabase(
                    document_id=document_id,
                    content=content,
                    embedding=embedding,
                    knowledge_tier=knowledge_tier,
                    metadata=enhanced_metadata,
                    content_hash=content_hash
                )

                if knowledge_tier == 'public':
                    self.stats.public_migrated += 1
                else:
                    self.stats.private_migrated += 1

            except Exception as e:
                logger.error(f"Failed to migrate embedding {i}: {str(e)}")
                self.stats.errors += 1

    async def _store_in_supabase(
        self,
        document_id: str,
        content: str,
        embedding: List[float],
        knowledge_tier: str,
        metadata: Dict[str, Any],
        content_hash: str
    ) -> None:
        """Store document in Supabase with knowledge tier."""
        try:
            # Import here to avoid circular imports
            from src.services.supabase_service import get_supabase_service

            supabase_service = await get_supabase_service()

            # Check if document already exists (deduplication)
            existing_query = """
                SELECT id FROM document_embeddings
                WHERE content_hash = $1
            """

            existing = await supabase_service.execute_query(
                existing_query,
                content_hash,
                fetch_type='one'
            )

            if existing:
                logger.debug(f"Document with hash {content_hash} already exists, skipping")
                self.stats.skipped += 1
                return

            # Insert new document embedding
            insert_query = """
                INSERT INTO document_embeddings (
                    document_id, content, embedding, chunk_index,
                    knowledge_tier, metadata, content_hash,
                    user_id, project_id
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9
                ) RETURNING id
            """

            # For public knowledge, user_id and project_id are NULL
            user_id = None if knowledge_tier == 'public' else metadata.get('user_id')
            project_id = None if knowledge_tier == 'public' else metadata.get('project_id')

            result = await supabase_service.execute_query(
                insert_query,
                document_id,
                content,
                embedding,
                0,  # chunk_index
                knowledge_tier,
                json.dumps(metadata),
                content_hash,
                user_id,
                project_id,
                fetch_type='one'
            )

            if result and 'id' in result:
                logger.debug(f"Stored document {document_id} with ID {result['id']}")
            else:
                raise Exception("Failed to store document in Supabase")

        except Exception as e:
            logger.error(f"Failed to store in Supabase: {str(e)}")
            raise

    def generate_migration_report(self) -> str:
        """Generate a detailed migration report."""
        report = f"""

ChromaDB to Supabase Migration Report
=====================================

Total Documents Processed: {self.stats.total_processed}
Successfully Migrated:     {self.stats.public_migrated + self.stats.private_migrated}
  - Public Knowledge:       {self.stats.public_migrated}
  - Private Knowledge:      {self.stats.private_migrated}
Skipped (Duplicates/Empty): {self.stats.skipped}
Errors:                     {self.stats.errors}

Success Rate: {((self.stats.public_migrated + self.stats.private_migrated) / max(self.stats.total_processed, 1)) * 100:.1f}%

Collection Tier Mapping:
{json.dumps(self.collection_tier_mapping, indent=2)}

Migration completed at: {import_time().strftime('%Y-%m-%d %H:%M:%S')}
        """
        return report.strip()


async def main():
    """Main migration function."""
    parser = argparse.ArgumentParser(
        description="Migrate ChromaDB data to Supabase pgvector"
    )
    parser.add_argument(
        "--backup-dir",
        type=Path,
        default=Path("./backup/chromadb_migration"),
        help="Directory containing ChromaDB backup files"
    )
    parser.add_argument(
        "--supabase-url",
        type=str,
        help="Supabase URL (if not in environment)"
    )
    parser.add_argument(
        "--supabase-key",
        type=str,
        help="Supabase service key (if not in environment)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Perform a dry run without actually migrating data"
    )

    args = parser.parse_args()

    # Get Supabase credentials
    supabase_url = args.supabase_url or os.getenv('SUPABASE_URL')
    supabase_key = args.supabase_key or os.getenv('SUPABASE_SERVICE_KEY')

    if not supabase_url or not supabase_key:
        logger.error("Supabase URL and service key are required")
        logger.error("Set SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables")
        logger.error("Or use --supabase-url and --supabase-key arguments")
        return 1

    if args.dry_run:
        logger.info("DRY RUN MODE - No data will be migrated")

    try:
        # Initialize migrator
        migrator = ChromaDBMigrator(supabase_url, supabase_key)

        # Perform migration
        if not args.dry_run:
            stats = await migrator.migrate_backup_directory(args.backup_dir)
        else:
            # Dry run - just analyze backup files
            logger.info(f"Analyzing backup directory: {args.backup_dir}")
            backup_files = list(args.backup_dir.glob("*.json")) if args.backup_dir.exists() else []
            logger.info(f"Found {len(backup_files)} backup files to migrate")
            stats = MigrationStats()

        # Generate and display report
        print(migrator.generate_migration_report())

        if stats.errors > 0:
            logger.warning(f"Migration completed with {stats.errors} errors")
            return 1
        else:
            logger.info("Migration completed successfully!")
            return 0

    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        return 1


def import_time():
    """Import time module for timestamp generation."""
    from datetime import datetime
    return datetime.now()


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
