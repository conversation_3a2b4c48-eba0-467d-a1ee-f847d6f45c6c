# Port Allocation Strategy

## Current Conflicts Analysis
- **Supabase CLI**: 54321-54327 (external service)
- **Docker Backend**: Holding previous ports from failed starts
- **Windows Services**: 80, 443

## New Port Allocation Plan

### Production (docker-compose.yml)
- **No host port bindings** - Use Traefik routing only
- Access via: `http://portal.localhost`, `http://api.localhost`

### Development (docker-compose.dev.yml)
| Service | Host Port | Container Port | Purpose |
|---------|-----------|----------------|---------|
| ollama-dev | 9434 | 11434 | Ollama API |
| docker-proxy | 9375 | 2375 | Docker proxy |
| traefik | 9080, 9443, 9091 | 80, 443, 8080 | Web proxy & dashboard |
| ai-orchestrator | 9001, 9002 | 8000, 8000 | FastAPI & Vector Storage |
| redis-dev | 9379 | 6379 | Redis admin |
| code-server | 9999 | 8080 | VS Code |
| prometheus | 9090 | 9090 | Metrics |
| loki | 9100 | 3100 | Logs |
| grafana | 9000 | 3000 | Monitoring |

### Debugger Ports (Optional)
| Service | Host Port | Container Port | Purpose |
|---------|-----------|----------------|---------|
| ai-orchestrator | 9678 | 5678 | Python debugger |

## Access URLs
- **Portal**: http://portal.localhost (via Traefik)
- **API**: http://api.localhost (via Traefik)
- **Traefik Dashboard**: http://localhost:9091
- **Grafana**: http://localhost:9000
- **Prometheus**: http://localhost:9090
