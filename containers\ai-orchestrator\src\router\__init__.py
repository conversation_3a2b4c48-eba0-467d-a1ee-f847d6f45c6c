# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: Router package initialization with comprehensive exports

"""
Router Package for AI Orchestrator

This package contains all FastAPI routers for the AI Orchestrator service.
"""

import logging

logger = logging.getLogger(__name__)

# Import routers with error handling
try:
    from src.router.auth_router import router as auth_router
except ImportError as e:
    try:
        from router.auth_router import router as auth_router
    except ImportError:
        logger.error(f"Failed to import auth_router: {e}")
        auth_router = None

try:
    from src.router.llm_router import (
        router as llm_router,
        get_llm_service
    )
except ImportError as e:
    try:
        from router.llm_router import (
            router as llm_router,
            get_llm_service
        )
    except ImportError:
        logger.error(f"Failed to import llm_router components: {e}")
        llm_router = None
        get_llm_service = None

try:
    from src.router.role_management import (
        router as role_management_router,
        initialize_role_configuration,
        RoleConfigurationManager
    )
except ImportError as e:
    logger.error(f"Failed to import role_management components: {e}")
    role_management_router = None
    initialize_role_configuration = None
    RoleConfigurationManager = None

try:
    from src.router.routers import router as main_router
except ImportError as e:
    try:
        from router.routers import router as main_router
    except ImportError:
        logger.error(f"Failed to import main_router: {e}")
        main_router = None

# Define comprehensive exports for clean imports and proper IDE support
__all__ = [
    # Main routers
    "auth_router",
    "llm_router",
    "role_management_router",
    "main_router",

    # Initialization functions
    "initialize_role_configuration",

    # Service management functions
    "get_llm_service",
    "RoleConfigurationManager",
]