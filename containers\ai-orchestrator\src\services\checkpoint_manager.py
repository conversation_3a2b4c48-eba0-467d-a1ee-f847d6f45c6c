# Project: AI Coding Agent
# Purpose: Enhanced checkpoint management system with async operations and no shell_agent dependency

import asyncio
import hashlib
import json
import logging
import os
import shutil
import time
import platform
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Any

from src.models.validation_models import (
    Checkpoint, StateSnapshot, RollbackPlan, ExecutionState
)
from src.utils.secrets import get_database_url


class CheckpointManager:
    """
    Enhanced checkpoint management system with async operations and state serialization.

    Provides functionality for:
    - Creating project state snapshots with comprehensive serialization
    - Backing up file system and database state with compression
    - Rolling back to previous checkpoints with verification
    - Managing checkpoint storage and cleanup with retention policies
    - State diff analysis and rollback planning
    - Transaction-like rollback operations with safety checks

    Key improvements:
    - Async file operations using asyncio.create_subprocess_exec
    - No dependency on shell_agent (replaced with direct subprocess calls)
    - Enhanced error handling and logging
    - Proper resource cleanup and timeout handling
    """

    def __init__(self, project_root: str = "/workspace", backup_root: str = "/workspace/.checkpoints"):
        self.project_root = Path(project_root)
        self.backup_root = Path(backup_root)
        self.logger = logging.getLogger("checkpoint_manager")

        # Checkpoint storage
        self._checkpoints: Dict[str, Checkpoint] = {}
        self._checkpoint_metadata_file = self.backup_root / "checkpoints.json"

        # State snapshots and rollback plans storage
        self._state_snapshots: Dict[str, StateSnapshot] = {}
        self._rollback_plans: Dict[str, RollbackPlan] = {}

        # Configuration
        self.max_checkpoints = 20  # Maximum number of checkpoints to keep
        self.compression_enabled = True  # Enable compression for snapshots
        self.excluded_patterns = [
            '*.pyc', '__pycache__', '.git', '.checkpoints',
            'node_modules', '.next', 'venv', 'env',
            '*.log', 'logs', 'tmp', 'temp'
        ]

        # Metrics tracking
        self.checkpoint_metrics = {
            "successful_rollbacks": 0,
            "failed_rollbacks": 0,
            "average_rollback_time": 0.0,
            "total_checkpoints_created": 0,
            "total_storage_used_mb": 0.0
        }

        # Initialize backup directory
        self._initialize_backup_directory()

        self.logger.info(f"Checkpoint Manager initialized - Project: {self.project_root}, Backups: {self.backup_root}")

    async def initialize_async(self):
        """Initialize async components like loading metadata"""
        await self._load_checkpoint_metadata()

    def _get_system_root_path(self) -> str:
        """Get system root path handling both Windows (C:\\) and Unix (/) systems."""
        if platform.system() == "Windows":
            return "C:\\"
        else:
            return "/"

    def _initialize_backup_directory(self):
        """Initialize the backup directory structure"""
        try:
            self.backup_root.mkdir(parents=True, exist_ok=True)

            # Create subdirectories
            (self.backup_root / "files").mkdir(exist_ok=True)
            (self.backup_root / "database").mkdir(exist_ok=True)
            (self.backup_root / "metadata").mkdir(exist_ok=True)

            self.logger.info("Backup directory structure initialized")

        except Exception as e:
            self.logger.error(f"Failed to initialize backup directory: {str(e)}")
            raise

    async def _load_checkpoint_metadata(self) -> None:
        """Load checkpoint metadata from disk asynchronously.

        Raises:
            RuntimeError: If metadata loading fails critically
        """
        try:
            if not await asyncio.to_thread(self._checkpoint_metadata_file.exists):
                self.logger.info("No existing checkpoint metadata found - starting fresh")
                return

            # Use asyncio.to_thread for file I/O operations with proper error handling
            def _read_metadata() -> dict:
                try:
                    with open(self._checkpoint_metadata_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                except (json.JSONDecodeError, UnicodeDecodeError) as e:
                    self.logger.error(f"Corrupted metadata file: {e}")
                    # Backup corrupted file and return empty dict
                    backup_path = self._checkpoint_metadata_file.with_suffix('.json.corrupted')
                    shutil.move(str(self._checkpoint_metadata_file), str(backup_path))
                    return {}

            metadata = await asyncio.to_thread(_read_metadata)

            # Convert to Checkpoint objects with validation
            loaded_count = 0
            for checkpoint_id, data in metadata.items():
                try:
                    # Validate required fields before creating Checkpoint
                    required_fields = ['id', 'name', 'created_at']
                    for field in required_fields:
                        if field not in data:
                            self.logger.warning(f"Skipping invalid checkpoint {checkpoint_id}: missing {field}")
                            continue

                    # Parse ISO timestamp
                    if isinstance(data.get('created_at'), str):
                        data['created_at'] = datetime.fromisoformat(data['created_at'].replace('Z', '+00:00'))

                    self._checkpoints[checkpoint_id] = Checkpoint(**data)
                    loaded_count += 1
                except (TypeError, ValueError) as e:
                    self.logger.warning(f"Skipping invalid checkpoint {checkpoint_id}: {e}")
                    continue

            self.logger.info(f"Loaded {loaded_count} valid checkpoints from metadata")

        except Exception as e:
            self.logger.error(f"Failed to load checkpoint metadata: {str(e)}")
            # Don't raise exception - allow service to start with empty checkpoints

    async def _execute_database_command(self, command: str, database_url: Optional[str] = None) -> dict[str, Any]:
        """
        Execute database command using async subprocess instead of shell_agent.

        Args:
            command: SQL command to execute
            database_url: Database connection URL

        Returns:
            Dictionary with execution result
        """
        try:
            if not database_url:
                database_url = get_database_url()

            # Use asyncio.create_subprocess_exec for non-blocking execution
            if platform.system() == "Windows":
                # For Windows, use create_subprocess_shell for better psql compatibility
                command_str = f'psql "{database_url}" -c "{command}"'
                process = await asyncio.create_subprocess_shell(
                    command_str,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
            else:
                process = await asyncio.create_subprocess_exec(
                    "psql", database_url, "-c", command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )

            stdout, stderr = await process.communicate()

            return {
                "success": process.returncode == 0,
                "returncode": process.returncode,
                "stdout": stdout.decode('utf-8') if stdout else "",
                "stderr": stderr.decode('utf-8') if stderr else "",
                "command": command
            }

        except Exception as e:
            self.logger.error(f"Database command execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "command": command
            }

    async def _execute_file_operation(self, operation: str, *args) -> dict[str, Any]:
        """
        Execute file system operations using async subprocess.

        Args:
            operation: Type of operation (copy, move, delete, etc.)
            *args: Arguments for the operation

        Returns:
            Dictionary with execution result
        """
        try:
            if operation == "copy":
                source, destination = args
                if platform.system() == "Windows":
                    # For Windows, use create_subprocess_shell for xcopy compatibility
                    command_str = f'xcopy "{source}" "{destination}" /E /I /Y'
                    process = await asyncio.create_subprocess_shell(
                        command_str,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                else:
                    process = await asyncio.create_subprocess_exec(
                        "cp", "-r", str(source), str(destination),
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
            elif operation == "compress":
                source, archive_path = args
                process = await asyncio.create_subprocess_exec(
                    "tar", "-czf", str(archive_path), "-C", str(source.parent), source.name,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
            elif operation == "extract":
                archive_path, destination = args
                process = await asyncio.create_subprocess_exec(
                    "tar", "-xzf", str(archive_path), "-C", str(destination),
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
            else:
                raise ValueError(f"Unsupported operation: {operation}")

            stdout, stderr = await process.communicate()

            return {
                "success": process.returncode == 0,
                "returncode": process.returncode,
                "stdout": stdout.decode('utf-8') if stdout else "",
                "stderr": stderr.decode('utf-8') if stderr else "",
                "operation": operation
            }

        except Exception as e:
            self.logger.error(f"File operation {operation} failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "operation": operation
            }

    async def create_checkpoint(
        self,
        name: str,
        description: str = "",
        include_database: bool = True,
        include_files: bool = True,
        tags: Optional[List[str]] = None
    ) -> str:
        """
        Create a comprehensive checkpoint with state serialization.

        Args:
            name: Human-readable name for the checkpoint
            description: Optional description of the checkpoint
            include_database: Whether to include database backup
            include_files: Whether to include file system backup
            tags: Optional tags for categorizing checkpoints

        Returns:
            Checkpoint ID for later reference

        Raises:
            RuntimeError: If checkpoint creation fails
        """
        start_time = time.time()
        checkpoint_id = f"cp_{int(start_time)}_{hashlib.md5(name.encode()).hexdigest()[:8]}"

        try:
            self.logger.info(f"Creating checkpoint '{name}' with ID: {checkpoint_id}")

            checkpoint_dir = self.backup_root / checkpoint_id
            checkpoint_dir.mkdir(parents=True, exist_ok=True)

            # Create state snapshot
            state_snapshot = await self._create_state_snapshot(checkpoint_id)

            backup_paths = {}

            # Backup file system if requested
            if include_files:
                files_backup_path = await self._backup_files(checkpoint_id, checkpoint_dir)
                backup_paths["files"] = str(files_backup_path)

            # Backup database if requested
            if include_database:
                db_backup_path = await self._backup_database(checkpoint_id, checkpoint_dir)
                backup_paths["database"] = str(db_backup_path)

            # Create checkpoint metadata with correct model fields
            checkpoint = Checkpoint(
                id=checkpoint_id,
                roadmap_id="checkpoint_roadmap",
                type="manual",
                timestamp=datetime.fromtimestamp(start_time),
                project_hash=hashlib.md5(name.encode()).hexdigest()[:16],
                database_backup_path=backup_paths.get("database"),
                files_backup_path=backup_paths.get("files"),
                metadata={
                    "name": name,
                    "description": description,
                    "tags": tags or [],
                    "size_bytes": await self._calculate_checkpoint_size(checkpoint_dir),
                    "compression_enabled": self.compression_enabled,
                    "state_snapshot_id": state_snapshot.id if state_snapshot else None
                }
            )

            # Store checkpoint
            self._checkpoints[checkpoint_id] = checkpoint
            await self._save_checkpoint_metadata()

            # Update metrics
            self.checkpoint_metrics["total_checkpoints_created"] += 1
            checkpoint_size_mb = checkpoint.metadata.get("size_bytes", 0) / (1024 * 1024)
            self.checkpoint_metrics["total_storage_used_mb"] += checkpoint_size_mb

            # Cleanup old checkpoints if needed
            await self._cleanup_old_checkpoints()

            elapsed_time = time.time() - start_time
            self.logger.info(f"Checkpoint '{name}' created successfully in {elapsed_time:.2f}s")

            return checkpoint_id

        except Exception as e:
            self.logger.error(f"Failed to create checkpoint '{name}': {str(e)}")
            # Cleanup partial checkpoint
            checkpoint_dir = self.backup_root / checkpoint_id
            if checkpoint_dir.exists():
                await asyncio.to_thread(shutil.rmtree, checkpoint_dir)
            raise RuntimeError(f"Checkpoint creation failed: {str(e)}")

    async def _create_state_snapshot(self, checkpoint_id: str) -> StateSnapshot:
        """Create a comprehensive state snapshot for the checkpoint."""
        try:
            # Capture current execution state
            execution_state = ExecutionState(
                pipeline_id="checkpoint_pipeline",
                current_stage_id=None,
                current_phase="checkpoint_creation",
                progress_percentage=0.0,
                variables={"current_directory": str(self.project_root)},
                artifacts={"environment_variables": str(dict(os.environ))},
                serialized_state={
                    "active_processes": [],  # Would require process enumeration
                    "open_files": [],       # Would require file handle enumeration
                    "system_metrics": {}    # Would require system monitoring
                }
            )

            # Create state snapshot
            state_snapshot = StateSnapshot(
                checkpoint_id=checkpoint_id,
                execution_state=execution_state,
                pipeline_state={},
                file_checksums={},
                directory_structure=await self._generate_file_tree(),
                database_schema_hash=None,
                database_backup_path=None,
                created_at=datetime.now(),
                size_bytes=0,
                compression_used=self.compression_enabled
            )

            self._state_snapshots[checkpoint_id] = state_snapshot
            return state_snapshot

        except Exception as e:
            self.logger.error(f"Failed to create state snapshot: {str(e)}")
            # Return minimal state snapshot on error
            return StateSnapshot(
                checkpoint_id=checkpoint_id,
                execution_state=ExecutionState(
                    pipeline_id="error_pipeline",
                    current_phase="error_recovery",
                    variables={"current_directory": str(self.project_root)},
                    artifacts={},
                    serialized_state={}
                ),
                pipeline_state={},
                file_checksums={},
                directory_structure={},
                created_at=datetime.now(),
                size_bytes=0,
                compression_used=False
            )

    async def _generate_file_tree(self) -> Dict[str, Any]:
        """Generate a file tree representation of the project with size limits.

        Returns:
            Dict containing file tree structure with metadata
        """
        try:
            def _build_tree(path: Path, max_depth: int = 5, current_depth: int = 0) -> Dict[str, Any]:
                tree = {"type": "directory", "children": {}, "items_count": 0}

                if current_depth >= max_depth:
                    tree["truncated"] = True
                    return tree

                try:
                    items = list(path.iterdir())
                    # Limit number of items processed to prevent memory issues
                    if len(items) > 1000:
                        self.logger.warning(f"Directory {path} has {len(items)} items, truncating to 1000")
                        items = items[:1000]
                        tree["truncated"] = True

                    for item in items:
                        # Skip items matching exclusion patterns
                        if any(pattern in item.name.lower() for pattern in self.excluded_patterns):
                            continue

                        try:
                            if item.is_file():
                                stat = item.stat()
                                tree["children"][item.name] = {
                                    "type": "file",
                                    "size": stat.st_size,
                                    "modified": stat.st_mtime,
                                    "mode": oct(stat.st_mode)[-3:]
                                }
                                tree["items_count"] += 1
                            elif item.is_dir():
                                subtree = _build_tree(item, max_depth, current_depth + 1)
                                tree["children"][item.name] = subtree
                                tree["items_count"] += subtree.get("items_count", 0)
                        except (PermissionError, OSError) as e:
                            # Log but don't fail on individual file/dir access issues
                            self.logger.debug(f"Cannot access {item}: {e}")
                            continue

                except (PermissionError, OSError) as e:
                    self.logger.debug(f"Cannot read directory {path}: {e}")
                    tree["error"] = str(e)

                return tree

            return await asyncio.to_thread(_build_tree, self.project_root)

        except Exception as e:
            self.logger.error(f"Failed to generate file tree: {str(e)}")
            return {"type": "directory", "children": {}, "error": str(e)}

    async def _capture_dependencies(self) -> Dict[str, Any]:
        """Capture project dependencies with size limits and error handling.

        Returns:
            Dict containing dependency file contents and metadata
        """
        dependencies = {}
        max_file_size = 10 * 1024 * 1024  # 10MB limit for dependency files

        try:
            # Check for common dependency files
            dependency_files = [
                "package.json", "yarn.lock", "package-lock.json",
                "requirements.txt", "Pipfile", "pyproject.toml",
                "Dockerfile", "docker-compose.yml", "Cargo.toml",
                "go.mod", "pom.xml", "build.gradle", "composer.json"
            ]

            async def _read_dependency_file(dep_file: str) -> None:
                """Read a single dependency file safely."""
                try:
                    file_path = self.project_root / dep_file
                    if not await asyncio.to_thread(file_path.exists):
                        return

                    stat = await asyncio.to_thread(file_path.stat)

                    # Skip very large files
                    if stat.st_size > max_file_size:
                        dependencies[dep_file] = {
                            "error": f"File too large ({stat.st_size} bytes)",
                            "size": stat.st_size,
                            "modified": stat.st_mtime
                        }
                        return

                    content = await asyncio.to_thread(
                        file_path.read_text,
                        encoding='utf-8',
                        errors='replace'  # Replace invalid characters
                    )

                    dependencies[dep_file] = {
                        "content": content,
                        "size": len(content),
                        "modified": stat.st_mtime,
                        "encoding": "utf-8"
                    }

                except (UnicodeDecodeError, OSError) as e:
                    self.logger.debug(f"Cannot read {dep_file}: {e}")
                    dependencies[dep_file] = {
                        "error": str(e),
                        "size": 0
                    }

            # Process files concurrently with limited concurrency
            semaphore = asyncio.Semaphore(5)  # Limit concurrent file operations

            async def _limited_read(dep_file: str):
                async with semaphore:
                    await _read_dependency_file(dep_file)

            await asyncio.gather(
                *[_limited_read(dep_file) for dep_file in dependency_files],
                return_exceptions=True
            )

        except Exception as e:
            self.logger.error(f"Failed to capture dependencies: {str(e)}")
            dependencies["_error"] = str(e)

        return dependencies

    async def _capture_configuration(self) -> Dict[str, Any]:
        """Capture project configuration files with sensitive data redaction.

        Returns:
            Dict containing configuration file contents with secrets redacted
        """
        configuration = {}
        max_config_size = 5 * 1024 * 1024  # 5MB limit for config files

        # Patterns for sensitive data that should be redacted
        sensitive_patterns = [
            r'(password|passwd|secret|key|token|api_key)([\s\="\']*)([^\s\n\r"\'\']+)',
            r'(DB_PASSWORD|DATABASE_PASSWORD)([\s\="\']*)([^\s\n\r"\'\']+)',
            r'(JWT_SECRET|JWT_KEY)([\s\="\']*)([^\s\n\r"\'\']+)'
        ]

        try:
            # Check for common config files
            config_files = [
                ".env", ".env.local", ".env.production", ".env.development",
                "config.json", "settings.json", ".gitignore", "config.yaml",
                "tsconfig.json", "next.config.js", "vite.config.js",
                "tailwind.config.js", "webpack.config.js", ".eslintrc.json"
            ]

            def _redact_sensitive_data(content: str, filename: str) -> str:
                """Redact sensitive information from configuration content."""
                import re

                # Only redact .env files and config files that might contain secrets
                if not any(pattern in filename.lower() for pattern in ['.env', 'config', 'secret']):
                    return content

                redacted = content
                for pattern in sensitive_patterns:
                    redacted = re.sub(
                        pattern,
                        r'\1\2[REDACTED]',
                        redacted,
                        flags=re.IGNORECASE
                    )
                return redacted

            async def _read_config_file(config_file: str) -> None:
                """Read a single configuration file safely."""
                try:
                    file_path = self.project_root / config_file
                    if not await asyncio.to_thread(file_path.exists):
                        return

                    stat = await asyncio.to_thread(file_path.stat)

                    # Skip very large files
                    if stat.st_size > max_config_size:
                        configuration[config_file] = {
                            "error": f"Config file too large ({stat.st_size} bytes)",
                            "size": stat.st_size,
                            "modified": stat.st_mtime
                        }
                        return

                    content = await asyncio.to_thread(
                        file_path.read_text,
                        encoding='utf-8',
                        errors='replace'
                    )

                    # Redact sensitive information
                    safe_content = await asyncio.to_thread(
                        _redact_sensitive_data, content, config_file
                    )

                    configuration[config_file] = {
                        "content": safe_content,
                        "size": len(content),  # Original size
                        "modified": stat.st_mtime,
                        "redacted": safe_content != content
                    }

                except (UnicodeDecodeError, OSError) as e:
                    self.logger.debug(f"Cannot read config file {config_file}: {e}")
                    configuration[config_file] = {
                        "error": str(e),
                        "size": 0
                    }

            # Process config files concurrently
            semaphore = asyncio.Semaphore(3)

            async def _limited_read(config_file: str):
                async with semaphore:
                    await _read_config_file(config_file)

            await asyncio.gather(
                *[_limited_read(config_file) for config_file in config_files],
                return_exceptions=True
            )

        except Exception as e:
            self.logger.error(f"Failed to capture configuration: {str(e)}")
            configuration["_error"] = str(e)

        return configuration

    async def _backup_files(self, checkpoint_id: str, checkpoint_dir: Path) -> Path:
        """Backup project files with compression."""
        try:
            files_dir = checkpoint_dir / "files"
            files_dir.mkdir(exist_ok=True)

            if self.compression_enabled:
                archive_path = files_dir / "project_files.tar.gz"
                result = await self._execute_file_operation("compress", self.project_root, archive_path)

                if not result.get("success"):
                    raise RuntimeError(f"File compression failed: {result.get('error', 'Unknown error')}")

                return archive_path
            else:
                # Direct copy without compression
                backup_path = files_dir / "project_files"
                result = await self._execute_file_operation("copy", self.project_root, backup_path)

                if not result.get("success"):
                    raise RuntimeError(f"File copy failed: {result.get('error', 'Unknown error')}")

                return backup_path

        except Exception as e:
            self.logger.error(f"File backup failed: {str(e)}")
            raise

    async def _backup_database(self, checkpoint_id: str, checkpoint_dir: Path) -> Path:
        """Backup database using pg_dump."""
        try:
            db_dir = checkpoint_dir / "database"
            db_dir.mkdir(exist_ok=True)
            backup_file = db_dir / f"database_backup_{checkpoint_id}.sql"

            # Get database URL
            database_url = get_database_url()

            # Execute pg_dump command
            command = f"pg_dump '{database_url}' > '{backup_file}'"
            result = await self._execute_database_command(command)

            if not result.get("success"):
                raise RuntimeError(f"Database backup failed: {result.get('error', 'Unknown error')}")

            self.logger.info(f"Database backup completed: {backup_file}")
            return backup_file

        except Exception as e:
            self.logger.error(f"Database backup failed: {str(e)}")
            raise

    async def _calculate_checkpoint_size(self, checkpoint_dir: Path) -> int:
        """Calculate total size of checkpoint directory with timeout protection.

        Args:
            checkpoint_dir: Path to checkpoint directory

        Returns:
            Total size in bytes, 0 if calculation fails
        """
        try:
            def _get_size(path: Path) -> int:
                """Calculate size with early termination for large directories."""
                total = 0
                item_count = 0
                max_items = 50000  # Prevent runaway calculations

                try:
                    if path.is_file():
                        return path.stat().st_size
                    elif path.is_dir():
                        for item in path.rglob('*'):
                            item_count += 1
                            if item_count > max_items:
                                self.logger.warning(
                                    f"Size calculation truncated at {max_items} items for {path}"
                                )
                                break

                            try:
                                if item.is_file():
                                    total += item.stat().st_size
                            except (PermissionError, OSError):
                                continue  # Skip inaccessible files
                except (PermissionError, OSError):
                    pass  # Return partial size if directory becomes inaccessible

                return total

            # Use timeout to prevent hanging on large directories
            return await asyncio.wait_for(
                asyncio.to_thread(_get_size, checkpoint_dir),
                timeout=30.0  # 30 second timeout
            )

        except asyncio.TimeoutError:
            self.logger.warning(f"Size calculation timeout for {checkpoint_dir}")
            return 0
        except Exception as e:
            self.logger.error(f"Failed to calculate checkpoint size: {str(e)}")
            return 0

    async def _save_checkpoint_metadata(self) -> None:
        """Save checkpoint metadata to disk asynchronously with atomic writes.

        Raises:
            RuntimeError: If metadata saving fails
        """
        try:
            def _write_metadata():
                metadata = {
                    checkpoint_id: {
                        "id": cp.id,
                        "roadmap_id": cp.roadmap_id,
                        "type": cp.type,
                        "timestamp": cp.timestamp.isoformat(),
                        "project_hash": cp.project_hash,
                        "database_backup_path": cp.database_backup_path,
                        "files_backup_path": cp.files_backup_path,
                        "metadata": cp.metadata,
                        "rollback_instructions": cp.rollback_instructions
                    }
                    for checkpoint_id, cp in self._checkpoints.items()
                }

                # Atomic write using temporary file
                temp_file = self._checkpoint_metadata_file.with_suffix('.tmp')
                try:
                    with open(temp_file, 'w', encoding='utf-8') as f:
                        json.dump(metadata, f, indent=2, ensure_ascii=False)
                    # Atomic move to final location
                    temp_file.replace(self._checkpoint_metadata_file)
                except Exception:
                    # Cleanup temp file on error
                    if temp_file.exists():
                        temp_file.unlink()
                    raise

            await asyncio.to_thread(_write_metadata)
            self.logger.debug("Checkpoint metadata saved successfully")

        except Exception as e:
            self.logger.error(f"Failed to save checkpoint metadata: {str(e)}")
            raise RuntimeError(f"Checkpoint metadata save failed: {str(e)}")

    async def _cleanup_old_checkpoints(self) -> None:
        """Remove old checkpoints if exceeding maximum limit with safe cleanup.

        Uses atomic operations and proper error handling to ensure cleanup
        doesn't corrupt the checkpoint system.
        """
        if len(self._checkpoints) <= self.max_checkpoints:
            return

        try:
            # Sort checkpoints by creation time (oldest first)
            sorted_checkpoints = sorted(
                self._checkpoints.items(),
                key=lambda x: x[1].timestamp  # Use correct field name
            )

            # Determine how many to remove
            checkpoints_to_remove = sorted_checkpoints[:len(self._checkpoints) - self.max_checkpoints]

            self.logger.info(
                f"Cleaning up {len(checkpoints_to_remove)} old checkpoints "
                f"(keeping {self.max_checkpoints} of {len(self._checkpoints)})"
            )

            # Track cleanup results
            cleanup_results = {
                "successful_removals": 0,
                "failed_removals": 0,
                "errors": []
            }

            for checkpoint_id, checkpoint_data in checkpoints_to_remove:
                try:
                    self.logger.info(f"Removing old checkpoint: {checkpoint_data.metadata.get('name', checkpoint_id)} ({checkpoint_id})")

                    # Remove checkpoint directory with retry logic
                    checkpoint_dir = self.backup_root / checkpoint_id
                    if await asyncio.to_thread(checkpoint_dir.exists):
                        # Use timeout for directory removal
                        await asyncio.wait_for(
                            asyncio.to_thread(shutil.rmtree, checkpoint_dir),
                            timeout=60.0  # 60 second timeout for removal
                        )

                    # Remove from memory only after successful filesystem cleanup
                    del self._checkpoints[checkpoint_id]
                    if checkpoint_id in self._state_snapshots:
                        del self._state_snapshots[checkpoint_id]
                    if checkpoint_id in self._rollback_plans:
                        del self._rollback_plans[checkpoint_id]

                    cleanup_results["successful_removals"] += 1

                except asyncio.TimeoutError:
                    error_msg = f"Timeout removing checkpoint {checkpoint_id}"
                    self.logger.error(error_msg)
                    cleanup_results["failed_removals"] += 1
                    cleanup_results["errors"].append(error_msg)
                except Exception as e:
                    error_msg = f"Failed to remove checkpoint {checkpoint_id}: {str(e)}"
                    self.logger.error(error_msg)
                    cleanup_results["failed_removals"] += 1
                    cleanup_results["errors"].append(error_msg)

            # Save updated metadata only if we had some successful removals
            if cleanup_results["successful_removals"] > 0:
                await self._save_checkpoint_metadata()

            # Log cleanup summary
            self.logger.info(
                f"Cleanup completed: {cleanup_results['successful_removals']} removed, "
                f"{cleanup_results['failed_removals']} failed"
            )

            if cleanup_results["errors"]:
                self.logger.warning(f"Cleanup errors encountered: {cleanup_results['errors'][:3]}")

        except Exception as e:
            self.logger.error(f"Critical error during checkpoint cleanup: {str(e)}")
            # Don't re-raise - cleanup failures shouldn't break the system

    def list_checkpoints(self) -> List[Checkpoint]:
        """
        List all available checkpoints.

        Returns:
            List of Checkpoint objects sorted by creation time (newest first)
        """
        return sorted(
            self._checkpoints.values(),
            key=lambda cp: cp.timestamp,  # Use correct field name
            reverse=True
        )

    async def get_checkpoint(self, checkpoint_id: str) -> Optional[Checkpoint]:
        """
        Get specific checkpoint by ID.

        Args:
            checkpoint_id: Unique identifier for the checkpoint

        Returns:
            Checkpoint object if found, None otherwise
        """
        return self._checkpoints.get(checkpoint_id)

    def get_checkpoint_metrics(self) -> Dict[str, Any]:
        """Get comprehensive checkpoint management metrics.

        Returns:
            Dictionary containing detailed metrics about checkpoint operations
        """
        try:
            # Calculate current storage usage
            current_storage_mb = sum(
                cp.metadata.get("size_bytes", 0) for cp in self._checkpoints.values()
            ) / (1024 * 1024)

            # Get checkpoint age statistics
            checkpoint_ages = []
            now = datetime.now()

            for cp in self._checkpoints.values():
                # Handle timezone-aware datetime comparison
                created_at = cp.timestamp  # Use correct field name
                if created_at.tzinfo is None:
                    created_at = created_at.replace(tzinfo=timezone.utc)
                if now.tzinfo is None:
                    now = now.replace(tzinfo=timezone.utc)

                age_days = (now - created_at).total_seconds() / 86400
                checkpoint_ages.append(age_days)

            # Calculate statistics
            avg_age = sum(checkpoint_ages) / len(checkpoint_ages) if checkpoint_ages else 0
            oldest_age = max(checkpoint_ages) if checkpoint_ages else 0
            newest_age = min(checkpoint_ages) if checkpoint_ages else 0

            # Update metrics with current data
            self.checkpoint_metrics.update({
                "total_storage_used_mb": current_storage_mb,
                "active_checkpoints": len(self._checkpoints),
                "active_state_snapshots": len(self._state_snapshots),
                "active_rollback_plans": len(self._rollback_plans),
                "average_checkpoint_age_days": round(avg_age, 2),
                "oldest_checkpoint_age_days": round(oldest_age, 2),
                "newest_checkpoint_age_days": round(newest_age, 2),
                "storage_limit_reached": current_storage_mb > 1000,  # 1GB warning
                "last_updated": datetime.now(timezone.utc).isoformat()
            })

            return self.checkpoint_metrics.copy()

        except Exception as e:
            self.logger.error(f"Failed to calculate checkpoint metrics: {e}")
            return {
                "error": str(e),
                "last_updated": datetime.now(timezone.utc).isoformat()
            }
