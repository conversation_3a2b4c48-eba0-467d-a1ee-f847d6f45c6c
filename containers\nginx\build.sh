#!/bin/bash
# Optimized Build and Deployment Script for Nginx Containers
# This script demonstrates best practices for building, testing, and deploying
# the optimized nginx containers with security hardening and performance tuning

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.yml"
NGINX_COMPOSE_FILE="$SCRIPT_DIR/docker-compose.nginx.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running or not accessible"
        exit 1
    fi
}

# Function to validate nginx configuration
validate_nginx_config() {
    local container_name=$1
    log_info "Validating nginx configuration in $container_name..."

    if docker exec "$container_name" nginx -t >/dev/null 2>&1; then
        log_success "Nginx configuration is valid"
        return 0
    else
        log_error "Nginx configuration validation failed"
        docker exec "$container_name" nginx -t
        return 1
    fi
}

# Function to build optimized nginx image
build_nginx_image() {
    local image_name=${1:-"ai-coding-agent/nginx:latest"}
    local build_context="$SCRIPT_DIR"

    log_info "Building optimized nginx image: $image_name"

    # Build with optimizations
    docker build \
        --target production \
        --build-arg NGINX_VERSION=1.25-alpine \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --cache-from "$image_name" \
        --tag "$image_name" \
        --label "org.opencontainers.image.created=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
        --label "org.opencontainers.image.revision=$(git rev-parse HEAD 2>/dev/null || echo 'unknown')" \
        "$build_context"

    log_success "Built nginx image: $image_name"
}

# Function to build optimized hosting-server image
build_hosting_server_image() {
    local image_name=${1:-"ai-coding-agent/hosting-server:latest"}
    local build_context="$PROJECT_ROOT/containers/hosting-server"

    log_info "Building optimized hosting-server image: $image_name"

    docker build \
        --target production \
        --build-arg NGINX_VERSION=1.25-alpine \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --cache-from "$image_name" \
        --tag "$image_name" \
        --label "org.opencontainers.image.created=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
        --label "org.opencontainers.image.revision=$(git rev-parse HEAD 2>/dev/null || echo 'unknown')" \
        "$build_context"

    log_success "Built hosting-server image: $image_name"
}

# Function to run security scan
security_scan() {
    local image_name=$1

    log_info "Running security scan on $image_name..."

    if command -v trivy >/dev/null 2>&1; then
        trivy image --exit-code 1 --no-progress --format table "$image_name"
    elif command -v docker >/dev/null 2>&1 && docker run --rm -v /var/run/docker.sock:/var/run/docker.sock -v "$HOME/.docker:/.docker" goodwithtech/dockle:latest image "$image_name" >/dev/null 2>&1; then
        log_warning "Trivy not found, using Dockle for basic security scan"
        docker run --rm \
            -v /var/run/docker.sock:/var/run/docker.sock \
            -v "$HOME/.docker:/.docker" \
            goodwithtech/dockle:latest image "$image_name"
    else
        log_warning "No security scanning tools found. Install Trivy or Dockle for security scanning."
    fi
}

# Function to test container health
test_container_health() {
    local container_name=$1
    local max_attempts=${2:-30}
    local attempt=1

    log_info "Testing health of container: $container_name"

    while [ $attempt -le $max_attempts ]; do
        if docker exec "$container_name" curl -f http://localhost/health >/dev/null 2>&1; then
            log_success "Container $container_name is healthy"
            return 0
        fi

        log_info "Waiting for $container_name to be healthy (attempt $attempt/$max_attempts)..."
        sleep 2
        ((attempt++))
    done

    log_error "Container $container_name failed health check after $max_attempts attempts"
    return 1
}

# Function to deploy with zero downtime
deploy_zero_downtime() {
    log_info "Starting zero-downtime deployment..."

    # Scale up new containers
    docker-compose -f "$COMPOSE_FILE" -f "$NGINX_COMPOSE_FILE" up -d --scale nginx=2 --scale hosting-server=2

    # Wait for new containers to be healthy
    test_container_health "nginx" || return 1
    test_container_health "hosting-server" || return 1

    # Update routing (this would be handled by your load balancer)
    log_info "Updating load balancer configuration..."

    # Scale down old containers
    docker-compose -f "$COMPOSE_FILE" -f "$NGINX_COMPOSE_FILE" up -d --scale nginx=1 --scale hosting-server=1

    log_success "Zero-downtime deployment completed"
}

# Function to cleanup old images
cleanup_images() {
    log_info "Cleaning up dangling images..."

    # Remove dangling images
    docker image prune -f

    # Remove images older than 30 days (optional)
    # docker image prune -a --filter "until=720h" -f

    log_success "Image cleanup completed"
}

# Function to show usage
usage() {
    cat << EOF
Optimized Nginx Container Build and Deployment Script

USAGE:
    $0 [COMMAND] [OPTIONS]

COMMANDS:
    build           Build optimized nginx images
    deploy          Deploy containers with zero downtime
    test            Run tests and health checks
    security        Run security scan on images
    cleanup         Clean up old images and containers
    validate        Validate nginx configurations
    all             Run full build, test, and deploy cycle

OPTIONS:
    --nginx-image IMAGE       Custom nginx image name/tag
    --hosting-image IMAGE     Custom hosting-server image name/tag
    --no-cache               Disable build cache
    --verbose               Enable verbose output
    --help                  Show this help message

EXAMPLES:
    $0 build
    $0 build --nginx-image myorg/nginx:latest
    $0 deploy
    $0 test
    $0 all

ENVIRONMENT VARIABLES:
    NGINX_IMAGE              Default nginx image name
    HOSTING_SERVER_IMAGE     Default hosting-server image name
    COMPOSE_PROJECT_NAME     Docker compose project name

EOF
}

# Main script logic
main() {
    local command=${1:-"help"}
    local nginx_image=${NGINX_IMAGE:-"ai-coding-agent/nginx:latest"}
    local hosting_image=${HOSTING_SERVER_IMAGE:-"ai-coding-agent/hosting-server:latest"}
    local no_cache=false
    local verbose=false

    # Parse command line arguments
    shift
    while [[ $# -gt 0 ]]; do
        case $1 in
            --nginx-image)
                nginx_image="$2"
                shift 2
                ;;
            --hosting-image)
                hosting_image="$2"
                shift 2
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done

    # Enable verbose mode
    if [[ "$verbose" == "true" ]]; then
        set -x
    fi

    # Check prerequisites
    check_docker

    case $command in
        build)
            log_info "Starting build process..."

            # Build nginx image
            if [[ "$no_cache" == "true" ]]; then
                export DOCKER_BUILDKIT=0
            else
                export DOCKER_BUILDKIT=1
            fi

            build_nginx_image "$nginx_image"
            build_hosting_server_image "$hosting_image"

            log_success "Build process completed"
            ;;

        deploy)
            log_info "Starting deployment process..."
            deploy_zero_downtime
            ;;

        test)
            log_info "Running tests..."

            # Start containers for testing
            docker-compose -f "$COMPOSE_FILE" -f "$NGINX_COMPOSE_FILE" up -d

            # Test nginx configuration
            validate_nginx_config "nginx"

            # Test container health
            test_container_health "nginx"
            test_container_health "hosting-server"

            log_success "All tests passed"
            ;;

        security)
            log_info "Running security scans..."
            security_scan "$nginx_image"
            security_scan "$hosting_image"
            ;;

        cleanup)
            cleanup_images
            ;;

        validate)
            log_info "Validating configurations..."
            # This would validate configurations before deployment
            validate_nginx_config "nginx"
            ;;

        all)
            log_info "Running full build, test, and deploy cycle..."

            # Build
            $0 build --nginx-image "$nginx_image" --hosting-image "$hosting_image"

            # Test
            $0 test

            # Security scan
            $0 security

            # Deploy
            $0 deploy

            log_success "Full cycle completed successfully"
            ;;

        help|*)
            usage
            ;;
    esac
}

# Run main function with all arguments
main "$@"
