#!/usr/bin/env python3
"""
Check user_profiles table structure
"""
from sqlalchemy import create_engine, inspect
import os

def main():
    url = os.environ.get('DATABASE_URL')
    if not url:
        print("DATABASE_URL not found")
        return

    engine = create_engine(url)
    inspector = inspect(engine)

    print('user_profiles table columns:')
    try:
        columns = inspector.get_columns('user_profiles')
        for col in columns:
            print(f'  {col["name"]}: {col["type"]}')
    except Exception as e:
        print(f'Error: {e}')

if __name__ == '__main__':
    main()
