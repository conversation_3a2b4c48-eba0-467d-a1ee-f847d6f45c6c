# Entry point for ai-orchestrator
import logging
import uuid
from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import Depends, FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, ORJSONResponse
from slowapi import _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded

# Internal imports
from src.approval.approval_manager import ApprovalManager
from src.core.config import LL<PERSON><PERSON>ider, settings
from src.core.rate_limiting import SLOWAPI_AVAILABLE, apply_rate_limit, limiter

# Router imports
from src.router.deployment_router import router as deployment_router
from src.router.github_auth_router import router as github_auth_router
from src.router.health_router import router as health_router
from src.router.llm_router import router as llm_router
from src.router.memory_router import router as memory_router
from src.router.project_router import router as project_router
from src.router.redis_router import router as redis_router
from src.router.roadmap_router import router as roadmap_router
from src.router.role_management import router as role_router
from src.router.security_router import router as security_router
from src.router.supabase_router import router as supabase_router
from src.router.user_router import router as user_router
from src.router.websocket_router import router as websocket_router
from src.router.workspace_router import router as workspace_router
from src.services.enhanced_llm_service import EnhancedLLMService
from src.services.memory_management_service import MemoryManagementService
from src.services.rag_service import get_rag_service
from src.services.redis_service import get_redis_manager
from src.services.supabase_service import cleanup_supabase_service, get_supabase_service
from src.services.vector_service import get_vector_service
from src.utils.auth import get_current_user, init_supabase

# Global app instance for accessing services
app_instance = None


def get_llm_service(request: Request) -> EnhancedLLMService:
    """Get the LLM service instance from the app state."""
    global app_instance
    if app_instance and hasattr(app_instance.state, "llm_service"):
        return app_instance.state.llm_service
    # Fallback: create a new instance if not available
    return EnhancedLLMService()


# Configure logging
logging.basicConfig(
    level=settings.LOG_LEVEL.upper(), format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management for startup and shutdown events."""
    logger.info("Starting AI Orchestrator service...")

    # Initialize services
    try:
        redis_manager = get_redis_manager()
        await redis_manager.initialize()
        logger.info("Redis services initialized successfully.")
    except Exception as e:
        logger.warning(
            f"Redis initialization failed: {e}. Caching and rate limiting may be affected."
        )

    if settings.USE_SUPABASE:
        logger.info("Initializing Supabase-related services...")
        try:
            app.state.supabase_service = await get_supabase_service()
            app.state.vector_service = await get_vector_service()
            app.state.rag_service = await get_rag_service()
            logger.info("Supabase, Vector, and RAG services initialized.")
        except Exception as e:
            logger.error(f"Supabase service initialization failed: {e}", exc_info=True)
            app.state.supabase_service = None
            app.state.vector_service = None
            app.state.rag_service = None
    else:
        init_supabase()
        logger.info("Legacy Supabase auth client initialized.")
        app.state.supabase_service = None

    try:
        redis_client = (
            await get_redis_manager().get_client() if get_redis_manager().is_available() else None
        )
        app.state.llm_service = EnhancedLLMService(redis_client=redis_client)
        logger.info("Enhanced LLM service initialized.")
    except Exception as e:
        logger.error(f"Failed to initialize LLM service: {e}", exc_info=True)
        app.state.llm_service = EnhancedLLMService(redis_client=None)
        logger.warning("LLM service initialized in a degraded state.")

    try:
        if (
            settings.USE_SUPABASE
            and hasattr(app.state, "supabase_service")
            and app.state.supabase_service
        ):
            app.state.memory_service = MemoryManagementService(
                supabase_service=app.state.supabase_service
            )
            await app.state.memory_service.initialize()
            logger.info("MemoryManagementService initialized.")
        else:
            app.state.memory_service = None
            logger.warning("Supabase not configured, MemoryManagementService will be unavailable.")
    except Exception as e:
        logger.error(f"Failed to initialize MemoryManagementService: {e}", exc_info=True)
        app.state.memory_service = None
        logger.warning("MemoryManagementService initialization failed.")

    if settings.ENABLE_ROLE_CONFIG_INIT:
        from src.router.role_management import initialize_role_configuration

        await initialize_role_configuration()
        logger.info("Role configuration initialized.")

    if settings.ENABLE_WEBSOCKET_CHAT:
        from src.router.websocket_router import initialize_websocket_chat

        await initialize_websocket_chat()
        logger.info("WebSocket chat services initialized.")

    app.state.approval_manager = ApprovalManager()
    await app.state.approval_manager.initialize()
    logger.info("ApprovalManager initialized.")

    logger.info("AI Orchestrator service startup completed.")

    yield

    logger.info("Shutting down AI Orchestrator service...")
    await app.state.approval_manager.shutdown()
    if get_redis_manager().is_available():
        await get_redis_manager().close()
    if settings.USE_SUPABASE:
        await cleanup_supabase_service()
    logger.info("AI Orchestrator service shutdown completed.")


app = FastAPI(
    title="AI Orchestrator",
    description="AI Coding Agent Orchestration Service with Enhanced LLM Integration",
    version="2.0.0",
    lifespan=lifespan,
    default_response_class=ORJSONResponse,
)

# Set global app instance for service access
app_instance = app

# =====================================================================================
# Middleware & Exception Handlers
# =====================================================================================


@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    """Applies security headers to all outgoing responses."""
    response = await call_next(request)
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    return response


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Global exception handler for all unhandled exceptions."""
    if isinstance(exc, HTTPException):
        # Re-raise HTTPException to let FastAPI's default handler manage it
        raise exc

    error_id = str(uuid.uuid4())[:8]
    client_host = getattr(request.client, "host", "unknown") if request.client else "unknown"
    logger.exception(
        f"Unhandled exception: {exc!r} [Error ID: {error_id}] from {client_host} on {request.method} {request.url}",
        extra={"error_id": error_id},
    )
    return JSONResponse(
        status_code=500,
        content={"detail": "An internal server error occurred.", "error_id": error_id},
        headers={"X-Error-ID": error_id},
    )


if SLOWAPI_AVAILABLE:
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)

app.include_router(llm_router)
app.include_router(role_router)
app.include_router(redis_router)
app.include_router(websocket_router, prefix="/ws")
app.include_router(user_router, prefix="/api/v2")
app.include_router(workspace_router)
app.include_router(health_router)
app.include_router(memory_router)
app.include_router(security_router)
app.include_router(roadmap_router)
app.include_router(deployment_router)


# Include optional routers conditionally
if settings.ENABLE_GITHUB_AUTH:
    app.include_router(github_auth_router, prefix="/auth/github")
    logger.info("GitHub authentication router included")

if settings.ENABLE_PROJECT_MANAGEMENT:
    app.include_router(project_router, prefix="/api/projects")
    logger.info("Project management router included")

# Include Supabase router if enabled
if settings.USE_SUPABASE:
    app.include_router(supabase_router)
    logger.info("Supabase router included")


@app.get("/health")
async def health_check():
    """Basic health check endpoint - no rate limiting for Docker health checks."""
    try:
        # Simple health check without complex dependencies
        return {
            "status": "ok",
            "service": "ai-orchestrator",
            "version": "2.0.0",
            "timestamp": datetime.utcnow().isoformat(),
            "comprehensive_health": "/api/v1/health/detailed",
            "readiness_probe": "/api/v1/health/ready",
            "liveness_probe": "/api/v1/health/live",
            "metrics": "/api/v1/health/metrics",
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "error",
            "service": "ai-orchestrator",
            "version": "2.0.0",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat(),
        }


@app.get("/")
async def read_root():
    """Root endpoint with service information."""
    use_supabase = settings.USE_SUPABASE

    features = [
        "Enhanced LLM integration",
        "Multi-provider support",
        "Rate limiting and cost control",
        "Comprehensive monitoring",
        "Fallback mechanisms",
        "WebSocket real-time chat with AI agents",
    ]

    endpoints = {
        "health": "/health",
        "websocket_chat": "/ws (WebSocket for real-time chat)",
        "llm_health": "/api/llm/health",
        "providers": "/api/llm/providers",
        "models": "/api/llm/models",
        "generate": "/api/llm/generate",
        "agents": "/api/agents",
        "roles": "/api/roles",
        "role_config": "/api/roles/{role_name}",
        "provider_models": "/api/roles/providers/models",
        "redis_health": "/api/v1/redis/health",
        "redis_stats": "/api/v1/redis/stats",
        "cache_management": "/api/v1/redis/cache/*",
    }

    if use_supabase:
        features.extend(
            [
                "Supabase authentication",
                "Vector storage and search",
                "RAG (Retrieval-Augmented Generation)",
                "Row Level Security (RLS)",
                "Permission-aware operations",
            ]
        )

        endpoints.update(
            {
                "supabase_health": "/api/v1/supabase/health",
                "auth_register": "/api/v1/supabase/auth/register",
                "auth_login": "/api/v1/supabase/auth/login",
                "user_profile": "/api/v1/supabase/user/profile",
                "projects": "/api/v1/supabase/projects",
                "search": "/api/v1/supabase/search",
                "rag_generate": "/api/v1/supabase/rag/generate",
                "user_stats": "/api/v1/supabase/stats/user",
            }
        )

    # Always add Redis features
    features.extend(
        [
            "Redis caching and session management",
            "Real-time messaging via pub/sub",
            "Background task processing",
            "Rate limiting and API protection",
        ]
    )

    return {
        "message": "AI Coding Agent Orchestrator",
        "version": "2.0.0",
        "status": "running",
        "supabase_enabled": use_supabase,
        "features": features,
        "endpoints": endpoints,
    }


@app.get("/api/agents")
@apply_rate_limit("60/minute")
async def list_agents(request: Request, current_user=Depends(get_current_user)):
    """List available AI agents."""
    return {
        "agents": [
            {
                "name": "architect",
                "status": "ready",
                "description": "System architecture and design agent",
                "capabilities": ["system_design", "architecture_review", "documentation"],
            },
            {
                "name": "backend",
                "status": "ready",
                "description": "Backend development agent",
                "capabilities": ["api_development", "database_design", "server_optimization"],
            },
            {
                "name": "frontend",
                "status": "ready",
                "description": "Frontend development agent",
                "capabilities": ["ui_development", "responsive_design", "user_experience"],
            },
            {
                "name": "issue_fix",
                "status": "ready",
                "description": "Bug fixing and debugging agent",
                "capabilities": ["bug_analysis", "code_debugging", "performance_optimization"],
            },
            {
                "name": "shell",
                "status": "ready",
                "description": "Command line and system operations agent",
                "capabilities": ["command_execution", "system_administration", "automation"],
            },
        ]
    }


@app.get("/api/system/info")
@apply_rate_limit("30/minute")
async def system_info(request: Request):
    """Get system information and configuration."""
    try:
        return {
            "service": "ai-orchestrator",
            "version": "2.0.0",
            "environment": settings.ENVIRONMENT.value,
            "features": {
                "enhanced_llm": True,
                "multi_provider": True,
                "rate_limiting": True,
                "cost_control": True,
                "monitoring": True,
                "fallback_support": True,
            },
            "providers": {
                "ollama": {"enabled": True, "base_url": str(settings.OLLAMA_BASE_URL)},
                "openrouter": {
                    "enabled": settings.openrouter_available,
                    "configured": settings.openrouter_available,
                },
                "openai": {
                    "enabled": settings.openai_available,
                    "configured": settings.openai_available,
                },
                "anthropic": {
                    "enabled": settings.anthropic_available,
                    "configured": settings.anthropic_available,
                },
            },
            "configuration": {
                "default_local_provider": settings.DEFAULT_LOCAL_PROVIDER.value,
                "default_cloud_provider": settings.DEFAULT_CLOUD_PROVIDER.value,
                "cloud_fallback_enabled": settings.ENABLE_CLOUD_FALLBACK,
                "redis_available": settings.redis_available,
            },
        }
    except Exception as e:
        logger.error(f"Failed to get system info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve system information")


# Legacy endpoints for backward compatibility
@app.get("/api/models")
@apply_rate_limit("60/minute")
async def list_models_legacy(request: Request, current_user=Depends(get_current_user)):
    """Legacy endpoint - redirects to new LLM models endpoint."""
    # This redirects to the new enhanced endpoint

    try:
        llm_service = get_llm_service(request)
        models = await llm_service.list_available_models()

        # Convert to legacy format for compatibility
        models_legacy = []
        for model in models:
            models_legacy.append(
                {
                    "name": model.name,
                    "provider": model.provider.value,
                    "status": model.status.value,
                    "size": model.size,
                    "modified_at": model.modified_at,
                }
            )

        return {"models": models_legacy}
    except Exception as e:
        logger.error(f"Failed to list models (legacy): {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/ollama/status")
@apply_rate_limit("60/minute")
async def ollama_status_legacy(request: Request):
    """Legacy Ollama status endpoint."""

    try:
        llm_service = get_llm_service(request)
        status = await llm_service.test_provider_connection(LLMProvider.OLLAMA)

        return {
            "connected": status.available,
            "base_url": llm_service.providers[LLMProvider.OLLAMA]["base_url"],
            "message": "Connected to Ollama on host"
            if status.available
            else f"Cannot connect to Ollama: {status.error_message}",
            "response_time_ms": status.response_time_ms,
        }
    except Exception as e:
        logger.error(f"Failed to check Ollama status: {str(e)}")
        return {"connected": False, "message": f"Error checking Ollama status: {str(e)}"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
