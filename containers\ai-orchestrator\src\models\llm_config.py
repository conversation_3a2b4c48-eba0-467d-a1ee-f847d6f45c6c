"""
SQLAlchemy models for LLM Providers, Models, and Agent Model Assignments.

Conventions:
- Uses project's shared Base (naming convention in database.Base)
- Timestamps via server defaults and onupdate hooks
- Provider/API key management stores only a reference + last4 for display
- Agent assignment is unique per agent_role (one active assignment per role)
"""
from __future__ import annotations

from datetime import datetime
from typing import Optional

from sqlalchemy import (
    Column,
    Integer,
    String,
    Boolean,
    DateTime,
    ForeignKey,
    UniqueConstraint,
    Float,
    Numeric,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from src.models.database import Base


class LLMProvider(Base):
    """LLM Provider configuration and metadata.

    Example providers: ollama, openrouter, openai, anthropic
    We store only a reference to the API key (e.g., secret name/path) and last4 for display.
    """

    __tablename__ = "llm_providers"

    id: int = Column(Integer, primary_key=True, index=True, autoincrement=True)

    # Unique provider key (e.g., "ollama", "openrouter", "openai", "anthropic")
    name: str = Column(String(64), nullable=False, unique=True, index=True)

    # Optional display name (e.g., "OpenRouter", "OpenAI")
    display_name: Optional[str] = Column(String(128), nullable=True)

    # Reference to secret store entry (e.g., Docker secret, env var key, vault path)
    api_key_reference: Optional[str] = Column(String(255), nullable=True)

    # Last 4 chars of the API key for safe UI display
    api_key_last4: Optional[str] = Column(String(4), nullable=True)

    # Optional base URL (e.g., for OpenRouter, custom gateways, or local proxy)
    base_url: Optional[str] = Column(String(255), nullable=True)

    enabled: bool = Column(Boolean, nullable=False, server_default="true")

    # Optional guardrails and limits
    rate_limit_rpm: Optional[int] = Column(Integer, nullable=True)
    cost_limit_usd: Optional[Numeric] = Column(Numeric(10, 2), nullable=True)

    created_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    models = relationship("LLMModel", back_populates="provider", cascade="all, delete-orphan")
    assignments = relationship("AgentModelAssignment", back_populates="provider")

    def __repr__(self) -> str:  # pragma: no cover
        return f"<LLMProvider id={self.id} name={self.name} enabled={self.enabled}>"


class LLMModel(Base):
    """LLM Model catalog entry belonging to a provider.

    Examples: name="gpt-4o", provider=OpenAI; name="llama3:8b", provider=Ollama
    """

    __tablename__ = "llm_models"
    __table_args__ = (
        UniqueConstraint("provider_id", "name", name="uq_llm_models_provider_id_name"),
    )

    id: int = Column(Integer, primary_key=True, index=True, autoincrement=True)

    provider_id: int = Column(
        Integer,
        ForeignKey("llm_providers.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    # Model identifier as used by provider APIs (e.g., "gpt-4o", "llama3:8b")
    name: str = Column(String(128), nullable=False)

    # Optional friendly label for UI
    display_name: Optional[str] = Column(String(128), nullable=True)

    # Availability/status hint ("available", "unavailable", "pulling", "error")
    status: str = Column(String(32), nullable=False, server_default="available")

    # Optional metadata
    context_length: Optional[int] = Column(Integer, nullable=True)
    cost_per_token: Optional[Numeric] = Column(Numeric(10, 5), nullable=True)
    parameters: Optional[str] = Column(String(64), nullable=True)  # e.g., "7B", "13B"
    is_default: bool = Column(Boolean, nullable=False, server_default="false")

    created_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    provider = relationship("LLMProvider", back_populates="models", lazy="selectin")

    def __repr__(self) -> str:  # pragma: no cover
        return f"<LLMModel id={self.id} provider_id={self.provider_id} name={self.name}>"


class AgentModelAssignment(Base):
    """Mapping of agent_role to its selected provider and models.

    One active assignment per agent role (unique on agent_role).
    Supports an optional fallback model as per roadmap's fallback mechanism.
    """

    __tablename__ = "agent_model_assignments"

    id: int = Column(Integer, primary_key=True, index=True, autoincrement=True)

    # e.g., "architect", "frontend", "backend", "shell", "issue_fix"
    agent_role: str = Column(String(64), nullable=False, unique=True, index=True)

    provider_id: int = Column(
        Integer,
        ForeignKey("llm_providers.id", ondelete="RESTRICT"),
        nullable=False,
        index=True,
    )

    primary_model_id: int = Column(
        Integer,
        ForeignKey("llm_models.id", ondelete="RESTRICT"),
        nullable=False,
        index=True,
    )

    fallback_model_id: Optional[int] = Column(
        Integer,
        ForeignKey("llm_models.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
    )

    # Optional per-role generation controls
    temperature: float = Column(Float, nullable=False, server_default="0.7")
    max_tokens: Optional[int] = Column(Integer, nullable=True)

    enabled: bool = Column(Boolean, nullable=False, server_default="true")

    created_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    provider = relationship("LLMProvider", back_populates="assignments", lazy="selectin")
    primary_model = relationship(
        "LLMModel",
        foreign_keys=[primary_model_id],
        lazy="selectin",
    )
    fallback_model = relationship(
        "LLMModel",
        foreign_keys=[fallback_model_id],
        lazy="selectin",
    )

    def __repr__(self) -> str:  # pragma: no cover
        return (
            f"<AgentModelAssignment id={self.id} role={self.agent_role} "
            f"provider_id={self.provider_id} primary_model_id={self.primary_model_id} "
            f"fallback_model_id={self.fallback_model_id}>"
        )