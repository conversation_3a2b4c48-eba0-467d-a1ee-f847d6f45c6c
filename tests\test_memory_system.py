"""
Memory Management System Tests

Tests for the comprehensive memory management system including configuration,
storage, retrieval, and learning capabilities.
"""

import pytest
import async<PERSON>
import json
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

from src.services.memory_management_service import MemoryManagementService
from src.schemas.memory_schemas import (
    MemoryType,
    MemoryPriority,
    MemoryEntry,
    MemoryMetadata,
    CodePattern,
    LearningOutcome,
)
from src.core.config import settings


class TestMemoryManagementService:
    """Test suite for MemoryManagementService."""

    @pytest.fixture
    def mock_supabase_service(self):
        """Create a mock Supabase service."""
        return Mock()

    @pytest.fixture
    def memory_service(self, mock_supabase_service):
        """Create a memory service instance for testing."""
        service = MemoryManagementService(mock_supabase_service)
        return service

    @pytest.fixture
    def sample_memory_entry(self):
        """Create a sample memory entry for testing."""
        return MemoryEntry(
            content="Test memory content",
            memory_type=MemoryType.USER_PREFERENCE,
            metadata=MemoryMetadata(
                tags=["test", "sample"],
                priority=MemoryPriority.HIGH,
                confidence=0.9
            )
        )

    @pytest.fixture
    def sample_code_pattern(self):
        """Create a sample code pattern for testing."""
        return CodePattern(
            name="test_pattern",
            description="A test code pattern",
            language="python",
            pattern_type="function",
            template="def {function_name}({parameters}):\n    {body}",
            variables={"function_name": "str", "parameters": "str", "body": "str"},
            examples=["def hello(name):\n    return f'Hello {name}'"],
            best_practices=["Use descriptive function names", "Add docstrings"]
        )

    def test_initialization_with_config(self, memory_service):
        """Test that service initializes with correct configuration values."""
        assert memory_service.max_memory_entries == settings.MAX_MEMORY_ENTRIES
        assert memory_service.memory_search_similarity_threshold == settings.MEMORY_SEARCH_SIMILARITY_THRESHOLD
        assert memory_service.enable_learning_loops == settings.ENABLE_LEARNING_LOOPS
        assert memory_service.memory_auto_save == settings.MEMORY_AUTO_SAVE

    def test_memory_file_paths(self, memory_service):
        """Test that memory file paths are set correctly from config."""
        assert memory_service.memory_file == Path(settings.MEMORY_FILE_PATH)
        assert memory_service.backup_file == Path(settings.MEMORY_BACKUP_PATH)

    @pytest.mark.asyncio
    async def test_store_memory(self, memory_service, sample_memory_entry):
        """Test storing a memory entry."""
        # Store the memory
        result = await memory_service.store_memory(sample_memory_entry)

        # Verify the result
        assert result.success is True
        assert result.memory_id is not None
        assert result.memory_id in memory_service.memories

        # Verify the stored memory
        stored = memory_service.memories[result.memory_id]
        assert stored.content == sample_memory_entry.content
        assert stored.memory_type == sample_memory_entry.memory_type
        assert stored.metadata.tags == sample_memory_entry.metadata.tags

    @pytest.mark.asyncio
    async def test_retrieve_memories(self, memory_service, sample_memory_entry):
        """Test retrieving memories."""
        # Store a memory first
        await memory_service.store_memory(sample_memory_entry)

        # Retrieve memories
        memories = await memory_service.retrieve_memories(
            memory_type=MemoryType.USER_PREFERENCE,
            limit=10
        )

        assert len(memories) == 1
        assert memories[0].content == sample_memory_entry.content

    @pytest.mark.asyncio
    async def test_search_memories(self, memory_service, sample_memory_entry):
        """Test searching memories."""
        # Store a memory
        await memory_service.store_memory(sample_memory_entry)

        # Search for memories
        results = await memory_service.search_memories(
            query="test memory",
            limit=5
        )

        assert len(results) >= 1
        found_memory = next((r for r in results if r.content == sample_memory_entry.content), None)
        assert found_memory is not None

    @pytest.mark.asyncio
    async def test_store_code_pattern(self, memory_service, sample_code_pattern):
        """Test storing a code pattern."""
        result = await memory_service.store_code_pattern(sample_code_pattern)

        assert result.success is True
        assert result.pattern_name == sample_code_pattern.name
        assert sample_code_pattern.name in memory_service.code_patterns

    @pytest.mark.asyncio
    async def test_get_code_patterns(self, memory_service, sample_code_pattern):
        """Test retrieving code patterns."""
        # Store a pattern first
        await memory_service.store_code_pattern(sample_code_pattern)

        # Retrieve patterns
        patterns = await memory_service.get_code_patterns(language="python")

        assert len(patterns) == 1
        assert patterns[0].name == sample_code_pattern.name

    @pytest.mark.asyncio
    async def test_learn_from_interaction(self, memory_service):
        """Test learning from an interaction."""
        interaction_data = {
            "user_query": "How do I create a FastAPI route?",
            "agent_response": "To create a FastAPI route, use the @app.get decorator...",
            "outcome": "successful",
            "context": {"language": "python", "framework": "fastapi"}
        }

        result = await memory_service.learn_from_interaction(interaction_data)

        assert result.success is True
        assert len(memory_service.learning_outcomes) >= 1

    @pytest.mark.asyncio
    async def test_memory_cleanup(self, memory_service, sample_memory_entry):
        """Test memory cleanup functionality."""
        # Store a memory with expiration
        expired_memory = sample_memory_entry.model_copy()
        expired_memory.metadata.expires_at = datetime.now() - timedelta(days=1)

        await memory_service.store_memory(expired_memory)

        # Run cleanup
        cleaned_count = await memory_service.cleanup_expired_memories()

        assert cleaned_count >= 1

    @pytest.mark.asyncio
    async def test_memory_persistence(self, memory_service, sample_memory_entry, tmp_path):
        """Test memory persistence to file."""
        # Create a temporary file for testing
        test_file = tmp_path / "test_memories.json"
        memory_service.memory_file = test_file

        # Store a memory
        await memory_service.store_memory(sample_memory_entry)

        # Save to file
        await memory_service._save_memories()

        # Verify file exists and contains data
        assert test_file.exists()

        # Load from file
        await memory_service._load_memories()

        # Verify memory was loaded
        assert len(memory_service.memories) >= 1

    def test_memory_statistics(self, memory_service, sample_memory_entry):
        """Test memory statistics generation."""
        stats = memory_service.get_memory_statistics()

        assert "total_memories" in stats
        assert "memory_types" in stats
        assert "total_patterns" in stats
        assert "total_learning_outcomes" in stats
        assert isinstance(stats["total_memories"], int)
        assert isinstance(stats["total_patterns"], int)


class TestMemoryConfiguration:
    """Test memory configuration values."""

    def test_memory_config_values(self):
        """Test that memory configuration values are properly set."""
        assert settings.MAX_MEMORY_ENTRIES > 0
        assert 0.0 <= settings.MEMORY_SEARCH_SIMILARITY_THRESHOLD <= 1.0
        assert settings.MEMORY_MAX_SEARCH_RESULTS > 0
        assert settings.MEMORY_SEARCH_TIMEOUT > 0
        assert isinstance(settings.ENABLE_LEARNING_LOOPS, bool)
        assert 0.0 <= settings.LEARNING_CONFIDENCE_THRESHOLD <= 1.0
        assert settings.LEARNING_PATTERN_MIN_OCCURRENCES > 0

    def test_memory_file_paths_config(self):
        """Test that memory file paths are properly configured."""
        assert settings.MEMORY_FILE_PATH is not None
        assert settings.MEMORY_BACKUP_PATH is not None
        assert str(settings.MEMORY_FILE_PATH).endswith('.json')
        assert str(settings.MEMORY_BACKUP_PATH).endswith('.json')


if __name__ == "__main__":
    pytest.main([__file__])
