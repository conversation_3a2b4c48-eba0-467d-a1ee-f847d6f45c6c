from fastapi import APIRouter, HTTPException, status, Request
from fastapi.responses import RedirectResponse
from pydantic import BaseModel
import logging
import secrets
import json
from src.utils.auth import get_supabase, create_access_token, Token
from src.core.config import settings
from src.services.redis_service import get_redis_manager
from datetime import timedelta, datetime

logger = logging.getLogger(__name__)

# GitHub OAuth configuration from centralized settings

router = APIRouter(prefix="/auth/github", tags=["github-authentication"])

class GitHubAuthResponse(BaseModel):
    url: str

class GitHubAuthStatus(BaseModel):
    configured: bool
    client_id_set: bool
    redirect_uri: str

@router.get("/status", response_model=GitHubAuthStatus)
async def github_auth_status():
    """Check GitHub OAuth configuration status"""
    return GitHubAuthStatus(
        configured=settings.github_oauth_available,
        client_id_set=bool(settings.GITHUB_CLIENT_ID),
        redirect_uri=settings.GITHUB_REDIRECT_URI
    )

@router.get("/url", response_model=GitHubAuthResponse)
async def github_auth_url():
    """Get GitHub OAuth URL"""
    if not settings.GITHUB_CLIENT_ID:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="GitHub OAuth not configured. Please set GITHUB_CLIENT_ID environment variable."
        )

    # Get Supabase client
    supabase = get_supabase()
    if not supabase:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service not available"
        )

    try:
        # Generate GitHub OAuth URL via Supabase
        data = supabase.auth.sign_in_with_oauth({
            "provider": "github",
            "options": {
                "redirect_to": settings.GITHUB_REDIRECT_URI
            }
        })

        # Extract URL from response
        auth_url = data.url

        return {"url": auth_url}
    except Exception as e:
        logger.error(f"Failed to generate GitHub auth URL: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate GitHub authentication URL."
        )

@router.get("/callback")
async def github_callback(code: str, request: Request):
    """Handle GitHub OAuth callback"""
    supabase = get_supabase()
    if not supabase:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service not available"
        )

    try:
        # Exchange code for session via Supabase
        session_response = supabase.auth.exchange_code_for_session({
            "auth_code": code
        })

        if not session_response or not hasattr(session_response, 'user') or not session_response.user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to authenticate with GitHub"
            )

        user = session_response.user

        # Generate a short-lived code
        auth_code = secrets.token_urlsafe(32)

        # Store user session data in Redis, expiring in 5 minutes
        redis_manager = get_redis_manager()
        user_data = {
            "id": user.id,
            "email": user.email,
            "provider": "github",
            "github_username": user.user_metadata.get("user_name") if user.user_metadata else None,
            "session_created_at": datetime.utcnow().isoformat()
        }
        await redis_manager.setex(f"github_auth_code:{auth_code}", 300, json.dumps(user_data))

        # Redirect to code-server with the short-lived code
        redirect_url = f"{settings.CODE_SERVER_URL}/?github_auth_code={auth_code}"
        return RedirectResponse(url=redirect_url)

    except Exception as e:
        logger.error(f"GitHub callback failed: {e}", exc_info=True)
        # Redirect to code-server with a generic error
        error_url = f"{settings.CODE_SERVER_URL}/?auth_error=github_authentication_failed"
        return RedirectResponse(url=error_url)

class ExchangeCodeRequest(BaseModel):
    code: str

@router.post("/exchange-code", response_model=Token)
async def exchange_code_for_token(
    request: ExchangeCodeRequest
):
    """Exchange a short-lived auth code for a JWT."""
    redis_manager = get_redis_manager()
    code_key = f"github_auth_code:{request.code}"

    # Check if code exists in Redis
    user_data_json = await redis_manager.get(code_key)
    if not user_data_json:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invalid or expired authorization code."
        )

    # Immediately delete the code to make it single-use
    await redis_manager.delete(code_key)

    try:
        user_data = json.loads(user_data_json)

        # Create a new access token
        access_token_expires = timedelta(hours=24)
        access_token = create_access_token(
            data={
                "sub": user_data["id"],
                "email": user_data["email"],
                "provider": user_data["provider"],
                "github_username": user_data.get("github_username")
            },
            expires_delta=access_token_expires
        )

        return Token(access_token=access_token, token_type="bearer")

    except (json.JSONDecodeError, KeyError) as e:
        logger.error(f"Failed to process user data from Redis for code {request.code}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process authentication data."
        )


@router.post("/logout")
async def github_logout():
    """Handle GitHub logout"""
    try:
        supabase = get_supabase()
        if supabase:
            supabase.auth.sign_out()

        return {"message": "Successfully logged out"}
    except Exception as e:
        logger.error(f"GitHub logout failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed."
        )