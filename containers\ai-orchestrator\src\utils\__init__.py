# Project: AI Coding Agent
# Purpose: Utils package exports with comprehensive initialization

"""
Utils Package for AI Orchestrator

This package contains utility modules for authentication, LLM services,
and common helper functions used throughout the AI Orchestrator.
"""

import logging
import sys
from typing import Any, Optional, Dict

logger = logging.getLogger(__name__)

# Import utility modules with error handling for optional dependencies
try:
    from src.utils.auth import (
        init_supabase, get_supabase, verify_token, create_access_token,
        get_current_user, hash_password, verify_password, get_auth_status,
        is_supabase_available, TokenData  # type: ignore[assignment]
    )
    AUTH_AVAILABLE = True
except ImportError as e:
    logger.error(f"Failed to import auth utilities: {e}")
    AUTH_AVAILABLE = False

    # Create minimal fallback functions with proper type matching
    def init_supabase() -> None:
        """Fallback initialization function."""
        logger.warning("Auth module not available - authentication disabled")

    def get_supabase() -> Optional[Any]:
        """Fallback Supabase client function."""
        return None

    async def verify_token(*args, **kwargs) -> Any:
        """Fallback token verification function."""
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service not available"
        )

    def create_access_token(*args, **kwargs) -> str:
        """Fallback token creation function."""
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Token creation service not available"
        )

    async def get_current_user(*args, **kwargs) -> Any:
        """Fallback current user function."""
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="User authentication service not available"
        )

    def hash_password(password: str) -> str:
        """Fallback password hashing function using basic SHA-256."""
        import hashlib
        return hashlib.sha256(password.encode()).hexdigest()

    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Fallback password verification function."""
        return hash_password(plain_password) == hashed_password

    def get_auth_status() -> Dict[str, Any]:
        """Fallback auth status function."""
        return {"available": False, "error": "Auth module import failed"}

    def is_supabase_available() -> bool:
        """Fallback Supabase availability check."""
        return False

    # Define a simple fallback TokenData class that matches the interface
    class TokenData:
        """Fallback TokenData class with minimal interface."""
        def __init__(self, username: Optional[str] = None, user_id: Optional[str] = None,
                     email: Optional[str] = None, is_local: bool = False, **kwargs):
            self.username = username
            self.user_id = user_id
            self.email = email
            self.is_local = is_local
            # Set any additional kwargs as attributes
            for key, value in kwargs.items():
                setattr(self, key, value)

try:
    from src.utils.llm_service import (
        UniversalLLMService, LLMService, LLMProvider, LLMResponse, llm_service
    )
    LLM_AVAILABLE = True
except ImportError as e:
    logger.error(f"Failed to import LLM service utilities: {e}")
    LLM_AVAILABLE = False
    UniversalLLMService = None
    LLMService = None
    LLMProvider = None
    LLMResponse = None
    llm_service = None

try:
    from src.utils.utils import (
        generate_request_id, sanitize_filename, ensure_directory,
        safe_json_loads, safe_json_dumps, get_environment_info,
        format_bytes, is_valid_email, truncate_string, merge_dicts
    )
    UTILS_AVAILABLE = True
except ImportError as e:
    logger.error(f"Failed to import utility functions: {e}")
    UTILS_AVAILABLE = False

    # Create enhanced fallback functions with actual functionality
    import uuid
    import json
    import re
    import os
    from pathlib import Path

    def generate_request_id(*args, **kwargs):
        """Generate a unique request ID."""
        return f"req-{uuid.uuid4().hex[:12]}"

    def sanitize_filename(filename, max_length=255, replacement_char='_'):
        """Remove invalid characters for filenames."""
        return re.sub(r'[<>:"/\\|?*]', replacement_char, str(filename))

    def ensure_directory(path):
        """Ensure directory exists, creating if necessary."""
        Path(path).mkdir(parents=True, exist_ok=True)
        return path

    def safe_json_loads(json_str, default=None, max_size=10000000):
        """Safely parse JSON string with error handling."""
        try:
            return json.loads(json_str) if json_str else default
        except (json.JSONDecodeError, TypeError):
            return default

    def safe_json_dumps(data, default="{}"):
        """Safely serialize data to JSON with error handling."""
        try:
            return json.dumps(data) if data is not None else default
        except (TypeError, ValueError):
            return default

    def get_environment_info():
        """Get basic environment information."""
        return {
            "python_version": sys.version,
            "platform": os.name,
            "cwd": os.getcwd()
        }

    def format_bytes(bytes_count):
        """Format byte count as human-readable string."""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_count < 1024.0:
                return f"{bytes_count:.1f} {unit}"
            bytes_count /= 1024.0
        return f"{bytes_count:.1f} PB"

    def is_valid_email(email):
        """Validate email address format."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, str(email))) if email else False

    def truncate_string(text, max_length=100, suffix="...", word_boundary=True):
        """Truncate string to maximum length."""
        text = str(text) if text else ""
        return text[:max_length] + suffix if len(text) > max_length else text

    def merge_dicts(*dicts):
        """Merge multiple dictionaries into one."""
        result = {}
        for d in dicts:
            if isinstance(d, dict):
                result.update(d)
        return result

# Define comprehensive exports for clean imports and proper IDE support
__all__ = [
    # Authentication utilities
    "init_supabase",
    "get_supabase",
    "verify_token",
    "create_access_token",
    "get_current_user",
    "hash_password",
    "verify_password",
    "get_auth_status",
    "is_supabase_available",
    "TokenData",

    # LLM service utilities
    "UniversalLLMService",
    "LLMService",
    "LLMProvider",
    "LLMResponse",
    "llm_service",

    # Common utilities
    "generate_request_id",
    "sanitize_filename",
    "ensure_directory",
    "safe_json_loads",
    "safe_json_dumps",
    "get_environment_info",
    "format_bytes",
    "is_valid_email",
    "truncate_string",
    "merge_dicts",

    # Availability flags
    "AUTH_AVAILABLE",
    "LLM_AVAILABLE",
    "UTILS_AVAILABLE",
]