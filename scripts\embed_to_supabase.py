"""Small example: call Ollama embedding endpoint and upsert to Supabase via REST.
Set environment variables: OLLAMA_BASE_URL, SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY
"""
import os
import requests
import json
import sys

OLLAMA_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
TABLE = "embeddings"
VECTOR_DIM = int(os.getenv("VECTOR_DIM", "1536"))  # set to your model dim

if not SUPABASE_URL or not SUPABASE_KEY:
    print("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in the environment")
    sys.exit(1)


def get_embedding(text: str):
    """Call Ollama embedding endpoint. Adjust path/response parsing to your Ollama image."""
    resp = requests.post(
        f"{OLLAMA_URL}/v1/embeddings",
        json={"model": "bge-large-en-v1.5", "input": text},
        timeout=30,
    )
    resp.raise_for_status()
    data = resp.json()
    # adjust this to the real response shape: assume data["embedding"] or data[0]["embedding"]
    if isinstance(data, dict) and "embedding" in data:
        return data["embedding"]
    if isinstance(data, list) and len(data) and "embedding" in data[0]:
        return data[0]["embedding"]
    raise RuntimeError("Unexpected embedding response: %s" % data)


def upsert_embedding(tenant_id, project_id, doc_id, content, embedding, metadata=None):
    payload = {
        "tenant_id": tenant_id,
        "project_id": project_id,
        "doc_id": doc_id,
        "content": content,
        "embedding": embedding,
        "metadata": metadata or {}
    }
    headers = {
        "apikey": SUPABASE_KEY,
        "Authorization": f"Bearer {SUPABASE_KEY}",
        "Content-Type": "application/json",
        "Prefer": "resolution=merge-duplicates"
    }
    r = requests.post(
        f"{SUPABASE_URL}/rest/v1/{TABLE}",
        headers=headers,
        data=json.dumps(payload),
        params={"on_conflict": "id"},
        timeout=30,
    )
    r.raise_for_status()
    return r.json()


if __name__ == "__main__":
    sample_text = "Important part of user roadmap or doc"
    emb = get_embedding(sample_text)
    print("Embedding length:", len(emb))
    res = upsert_embedding("00000000-0000-0000-0000-000000000000", None, None, sample_text, emb, {"source": "roadmap"})
    print("Upsert result:", res)
