# Project: AI Coding Agent
# Purpose: API router for approval system endpoints

from fastapi import APIRouter, HTTPException, Depends, Query, Request
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime

from src.approval.approval_manager import ApprovalManager
from src.approval.approval_models import (
    ApprovalStatus, ApprovalType
)
from src.models.validation_models import ValidationResult
from src.utils.auth import get_current_user, TokenData, get_current_admin_user

# Initialize router and logger
router = APIRouter(prefix="/api/v1", tags=["approvals"])
logger = logging.getLogger("approval_router")

# Dependency to get the approval manager instance from the app state
def get_approval_manager(request: Request) -> ApprovalManager:
    return request.app.state.approval_manager

# API Models for request/response
from pydantic import BaseModel, Field

class CreateApprovalRequest(BaseModel):
    """Request model for creating new approval requests"""
    approval_type: ApprovalType
    title: str
    description: str
    item_type: str
    item_id: str
    roadmap_id: Optional[str] = None

    # Optional configuration
    timeout_minutes: Optional[int] = 60
    files_affected: List[str] = Field(default_factory=list)
    services_affected: List[str] = Field(default_factory=list)
    changes_summary: List[str] = Field(default_factory=list)
    impact_summary: List[str] = Field(default_factory=list)
    rollback_plan: Optional[str] = None
    estimated_duration: Optional[int] = None


class ApprovalDecisionRequest(BaseModel):
    """Request model for approval decisions"""
    comments: Optional[str] = None
    conditions: Optional[List[str]] = None
    response_metadata: Optional[Dict[str, Any]] = None


class ApprovalCancellationRequest(BaseModel):
    """Request model for cancelling approval requests"""
    reason: str

# Output models (Schemas) for API responses
class ApprovalRequestOut(BaseModel):
    id: str
    user_id: str
    status: ApprovalStatus
    approval_type: ApprovalType
    title: str
    description: str
    item_type: str
    item_id: str
    created_at: datetime
    responded_at: Optional[datetime] = None
    risk_level: str
    risk_assessment: str

    class Config:
        from_attributes = True

class ApprovalMetricsOut(BaseModel):
    total_requests: int
    pending_requests: int
    approved_count: int
    rejected_count: int
    timeout_count: int
    cancelled_count: int
    auto_approved_count: int
    average_response_time_minutes: float

    class Config:
        from_attributes = True

class ApprovalAuditEntryOut(BaseModel):
    id: str
    approval_id: str
    user_id: Optional[str] = None
    action: str
    details: str
    timestamp: datetime

    class Config:
        from_attributes = True


# Approval management endpoints

@router.post("/approvals/create", response_model=ApprovalRequestOut)
async def create_approval_request(
    request: CreateApprovalRequest,
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Create a new approval request.

    This endpoint creates a new approval request with automatic risk assessment
    and notification handling. The request will be queued for user approval
    unless it qualifies for auto-approval based on risk level.
    """
    if not current_user.user_id:
        raise HTTPException(status_code=401, detail="User ID is required")

    try:
        approval_request = await manager.create_approval_request(
            user_id=current_user.user_id,
            approval_type=request.approval_type,
            title=request.title,
            description=request.description,
            item_type=request.item_type,
            item_id=request.item_id,
            roadmap_id=request.roadmap_id,
            timeout_minutes=request.timeout_minutes or 60,
            files_affected=request.files_affected,
            services_affected=request.services_affected,
            changes_summary=request.changes_summary,
            impact_summary=request.impact_summary,
            rollback_plan=request.rollback_plan,
            estimated_duration=request.estimated_duration
        )

        logger.info(f"Created approval request {approval_request.id} for user {current_user.user_id}")
        return approval_request

    except Exception as e:
        logger.error(f"Failed to create approval request: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create approval request: {str(e)}")


@router.get("/approvals/pending", response_model=List[ApprovalRequestOut])
async def get_pending_approvals(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Get all pending approval requests.

    Returns a list of all pending approval requests, optionally filtered by user.
    Admins can view approvals for any user, while regular users can only view their own.
    Results are sorted by priority (high risk first, then by creation time).
    """
    user_to_filter = current_user.user_id
    if user_id:
        if user_id != current_user.user_id and current_user.role != "admin":
            raise HTTPException(status_code=403, detail="Not authorized to view approvals for this user")
        user_to_filter = user_id

    try:
        pending_approvals = await manager.get_pending_approvals(user_to_filter)
        logger.info(f"Retrieved {len(pending_approvals)} pending approvals for user {user_to_filter or 'all'}")
        return pending_approvals

    except Exception as e:
        logger.error(f"Failed to get pending approvals: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve approvals: {str(e)}")


@router.get("/approvals/{approval_id}", response_model=ApprovalRequestOut)
async def get_approval_request(
    approval_id: str,
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Get a specific approval request by ID.

    Returns the full details of an approval request including status,
    risk assessment, and all associated metadata.
    Admins can view any approval, while regular users can only view their own.
    """
    try:
        approval = await manager.get_approval_request(approval_id)
        if not approval:
            raise HTTPException(status_code=404, detail="Approval request not found")

        if approval.user_id != current_user.user_id and current_user.role != "admin":
            raise HTTPException(status_code=403, detail="Not authorized to view this approval")

        logger.info(f"Retrieved approval request {approval_id}")
        return approval

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get approval request {approval_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve approval: {str(e)}")


@router.post("/approvals/{approval_id}/approve", response_model=dict)
async def approve_request(
    approval_id: str,
    request: ApprovalDecisionRequest,
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Approve an approval request.

    Records the user's approval decision with optional comments and conditions.
    Triggers any registered event handlers and sends confirmation notifications.
    """
    if not current_user.user_id:
        raise HTTPException(status_code=401, detail="User ID is required")

    try:
        success = await manager.respond_to_approval(
            approval_id=approval_id,
            user_id=current_user.user_id,
            decision=ApprovalStatus.APPROVED,
            comments=request.comments,
            response_metadata=request.response_metadata or {}
        )

        if success:
            logger.info(f"Approval {approval_id} approved by user {current_user.user_id}")
            return {"status": "approved", "approval_id": approval_id}
        else:
            raise HTTPException(status_code=400, detail="Failed to approve request")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to approve request {approval_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to approve request: {str(e)}")


@router.post("/approvals/{approval_id}/reject", response_model=dict)
async def reject_request(
    approval_id: str,
    request: ApprovalDecisionRequest,
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Reject an approval request.

    Records the user's rejection decision with optional comments explaining
    the reasons for rejection. Triggers event handlers and notifications.
    """
    if not current_user.user_id:
        raise HTTPException(status_code=401, detail="User ID is required")

    try:
        success = await manager.respond_to_approval(
            approval_id=approval_id,
            user_id=current_user.user_id,
            decision=ApprovalStatus.REJECTED,
            comments=request.comments,
            response_metadata=request.response_metadata or {}
        )

        if success:
            logger.info(f"Approval {approval_id} rejected by user {current_user.user_id}")
            return {"status": "rejected", "approval_id": approval_id}
        else:
            raise HTTPException(status_code=400, detail="Failed to reject request")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reject request {approval_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reject request: {str(e)}")


@router.post("/approvals/{approval_id}/cancel", response_model=dict)
async def cancel_approval_request(
    approval_id: str,
    request: ApprovalCancellationRequest,
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Cancel a pending approval request.

    Allows the request creator or system admin to cancel a pending approval.
    Records the cancellation reason and triggers appropriate event handlers.
    """
    if not current_user.user_id:
        raise HTTPException(status_code=401, detail="User ID is required")

    try:
        success = await manager.cancel_approval_request(
            approval_id=approval_id,
            user_id=current_user.user_id,
            reason=request.reason
        )

        if success:
            logger.info(f"Approval {approval_id} cancelled by user {current_user.user_id}")
            return {"status": "cancelled", "approval_id": approval_id, "reason": request.reason}
        else:
            raise HTTPException(status_code=400, detail="Failed to cancel request")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel request {approval_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel request: {str(e)}")


# System monitoring and metrics endpoints

@router.get("/approvals/metrics", response_model=ApprovalMetricsOut)
async def get_approval_metrics(
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_admin_user)
):
    """
    Get approval system metrics and statistics.

    Returns comprehensive metrics about approval system performance,
    including response times, approval rates, and system health.
    Requires admin privileges.
    """
    try:
        metrics = await manager.get_approval_metrics()
        logger.info("Retrieved approval system metrics")
        return metrics

    except Exception as e:
        logger.error(f"Failed to get approval metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve metrics: {str(e)}")


@router.get("/approvals/audit", response_model=List[ApprovalAuditEntryOut])
async def get_approval_audit_trail(
    approval_id: Optional[str] = Query(None, description="Filter by approval ID"),
    limit: int = Query(100, description="Maximum number of entries to return"),
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_admin_user)
):
    """
    Get audit trail entries for approval activities.

    Returns a chronological log of all approval-related activities,
    optionally filtered by specific approval ID. Useful for compliance
    and debugging purposes. Requires admin privileges.
    """
    try:
        audit_entries = await manager.get_audit_trail(approval_id)

        # Apply limit
        if limit < len(audit_entries):
            audit_entries = audit_entries[-limit:]  # Get most recent entries

        logger.info(f"Retrieved {len(audit_entries)} audit entries")
        return audit_entries

    except Exception as e:
        logger.error(f"Failed to get audit trail: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve audit trail: {str(e)}")


# Integration endpoints for validation system

from src.services.auth_dependencies import require_service_account

@router.post("/approvals/validation/{task_id}", response_model=ApprovalRequestOut, dependencies=[Depends(require_service_account)])
async def request_task_validation_approval(
    task_id: str,
    user_id: str,
    validation_result: ValidationResult,
    manager: ApprovalManager = Depends(get_approval_manager)
):
    """
    Request approval for a task validation result.

    This endpoint is called by the validation system when a task validation
    requires user approval before proceeding. Creates an approval request
    with validation context and task details.
    """
    try:
        # Determine approval type and risk based on validation result
        approval_type = ApprovalType.TASK_EXECUTION
        risk_factors = []

        if not validation_result.is_valid:
            approval_type = ApprovalType.DESTRUCTIVE_OPERATION
            risk_factors.append("Validation failed")

        if validation_result.warnings:
            risk_factors.extend(validation_result.warnings)

        # Create approval request
        approval_request = await manager.create_approval_request(
            user_id=user_id,
            approval_type=approval_type,
            title=f"Task Validation Approval: {task_id}",
            description=f"Task validation {'failed' if not validation_result.is_valid else 'completed with warnings'} and requires approval to proceed",
            item_type="task",
            item_id=task_id,
            changes_summary=[
                f"Validation status: {'Failed' if not validation_result.is_valid else 'Passed with warnings'}",
                f"Details: {validation_result.details or 'No details provided'}"
            ],
            impact_summary=risk_factors,
            rollback_plan="Task execution can be cancelled without system impact"
        )

        logger.info(f"Created validation approval request {approval_request.id} for task {task_id}")
        return approval_request

    except Exception as e:
        logger.error(f"Failed to create validation approval for task {task_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create validation approval: {str(e)}")


@router.post("/approvals/phase/{phase_id}", response_model=ApprovalRequestOut, dependencies=[Depends(require_service_account)])
async def request_phase_completion_approval(
    phase_id: str,
    user_id: str,
    phase_data: Dict[str, Any],
    manager: ApprovalManager = Depends(get_approval_manager)
):
    """
    Request approval for phase completion.

    Creates an approval request when a phase completes and requires user
    review before proceeding to the next phase. Includes summary of
    changes and impact assessment.
    """
    try:
        # Extract phase information
        phase_title = phase_data.get('title', f'Phase {phase_id}')
        completed_tasks = phase_data.get('completed_tasks', [])
        files_created = phase_data.get('files_created', [])
        files_modified = phase_data.get('files_modified', [])

        # Create approval request
        approval_request = await manager.create_approval_request(
            user_id=user_id,
            approval_type=ApprovalType.PHASE_COMPLETION,
            title=f"Phase Completion: {phase_title}",
            description=f"Phase '{phase_title}' has completed successfully and is ready for your review",
            item_type="phase",
            item_id=phase_id,
            roadmap_id=phase_data.get('roadmap_id'),
            changes_summary=[
                f"Completed {len(completed_tasks)} tasks",
                f"Created {len(files_created)} new files",
                f"Modified {len(files_modified)} existing files"
            ],
            files_affected=files_created + files_modified,
            services_affected=phase_data.get('services_affected', []),
            rollback_plan="Phase can be rolled back to previous checkpoint if needed"
        )

        logger.info(f"Created phase completion approval {approval_request.id} for phase {phase_id}")
        return approval_request

    except Exception as e:
        logger.error(f"Failed to create phase approval for {phase_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create phase approval: {str(e)}")


# WebSocket connection management (for real-time notifications)

@router.post("/approvals/websocket/register")
async def register_websocket_connection(
    user_id: str,
    connection_id: str,
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Register a WebSocket connection for real-time approval notifications.

    This endpoint is called when a user establishes a WebSocket connection
    to receive real-time updates about approval requests.
    """
    if user_id != current_user.user_id:
        raise HTTPException(status_code=403, detail="Not authorized to register a websocket for this user")
    try:
        # In a full implementation, you would store the actual WebSocket connection
        # For now, we'll just register the connection ID
        manager.register_websocket_connection(user_id, connection_id)

        logger.info(f"Registered WebSocket connection {connection_id} for user {user_id}")
        return {"status": "registered", "user_id": user_id, "connection_id": connection_id}

    except Exception as e:
        logger.error(f"Failed to register WebSocket connection: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to register connection: {str(e)}")


@router.post("/approvals/websocket/unregister")
async def unregister_websocket_connection(
    user_id: str,
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Unregister a WebSocket connection for a user.

    Called when a user disconnects to clean up connection tracking.
    """
    if user_id != current_user.user_id:
        raise HTTPException(status_code=403, detail="Not authorized to unregister a websocket for this user")
    try:
        manager.unregister_websocket_connection(user_id)

        logger.info(f"Unregistered WebSocket connection for user {user_id}")
        return {"status": "unregistered", "user_id": user_id}

    except Exception as e:
        logger.error(f"Failed to unregister WebSocket connection: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to unregister connection: {str(e)}")


# Health check endpoint

@router.get("/approvals/health")
async def approval_system_health(
    manager: ApprovalManager = Depends(get_approval_manager),
    current_user: TokenData = Depends(get_current_user)
):
    """
    Check the health of the approval system.

    Returns system status and key metrics for monitoring purposes.
    """
    try:
        metrics = await manager.get_approval_metrics()
        pending_approvals = await manager.get_pending_approvals()

        # Determine system health based on metrics
        is_healthy = True
        health_issues = []

        # Check for high timeout rates
        total_requests = metrics.total_requests
        if total_requests > 0:
            timeout_rate = metrics.timeout_count / total_requests
            if timeout_rate > 0.1:  # More than 10% timeouts
                is_healthy = False
                health_issues.append(f"High timeout rate: {timeout_rate:.1%}")

        # Check average response time
        if metrics.average_response_time_minutes > 120:  # More than 2 hours average
            is_healthy = False
            health_issues.append(f"High average response time: {metrics.average_response_time_minutes:.1f} minutes")

        return {
            "status": "healthy" if is_healthy else "degraded",
            "issues": health_issues,
            "metrics": {
                "total_requests": metrics.total_requests,
                "pending_requests": len(pending_approvals),
                "average_response_time_minutes": metrics.average_response_time_minutes,
                "approval_rate": (metrics.approved_count / max(1, total_requests)) if total_requests > 0 else 0
            },
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to check approval system health: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to check system health: {str(e)}")

# Export router for inclusion in main FastAPI app
__all__ = ["router"]