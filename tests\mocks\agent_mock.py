# tests/mocks/agent_mock.py
from typing import Any, Dict
from src.agents.architect_agent import ArchitectAgent
from src.agents.base_agent import ValidationResult

class TestArchitectAgent(ArchitectAgent):
    """A test-only implementation of ArchitectAgent that stubs out abstract methods."""

    async def _execute_core(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """A no-op implementation of the abstract _execute_core method."""
        return {"success": True, "message": "This is a mock response from TestArchitectAgent."}

    async def _validate_agent_specific_prerequisites(self, task_input: Dict[str, Any]) -> "ValidationResult":
        """Overrides validation for testing purposes."""
        return ValidationResult.success("Mock validation passed")

    async def _validate_agent_specific_completion(self, task_input: Dict[str, Any], result: Dict[str, Any]) -> "ValidationResult":
        """Overrides validation for testing purposes."""
        return ValidationResult.success("Mock validation passed")
