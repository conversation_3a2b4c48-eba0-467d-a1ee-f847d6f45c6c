import { NextRequest, NextResponse } from "next/server";
import axios from "axios";
import { getServerSession } from "next-auth";
import { authOptions } from "../../../auth/[...nextauth]/route";
import { logger } from "@/lib/logger";

const API_BASE_URL = process.env.API_BASE_URL ||
  process.env.NEXT_PUBLIC_API_BASE_URL ||
  "http://ai-orchestrator:8000";

export async function POST(
  _request: NextRequest,
  { params }: { params: { projectId: string } },
) {
  const { projectId } = params;

  if (!projectId) {
    return NextResponse.json({ message: "Project ID is required" }, {
      status: 400,
    });
  }

  try {
    const session = await getServerSession(authOptions);
    const accessToken = (session as unknown as Record<string, unknown>)
      ?.accessToken as string | undefined;
    const userId = session?.user?.id ?? "user-123";

    // Forward request to ai-orchestrator
    const response = await axios.post(
      `${API_BASE_URL}/projects/${projectId}/scan`,
      {},
      {
        headers: {
          ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
          "X-User-ID": userId,
        },
      },
    );

    return NextResponse.json(response.data);
  } catch (error) {
    logger.error("Failed to start scan:", error);
    const errorMessage = error instanceof Error
      ? error.message
      : "Unknown error";
    return NextResponse.json({
      message: "Failed to start scan",
      error: errorMessage,
    }, { status: 500 });
  }
}
