#!/usr/bin/env pwsh
# Unicode Detection Script for AI Coding Agent
# Scans codebase for Unicode characters and suggests ASCII alternatives

param(
  [switch]$Fix,           # Automatically fix common Unicode issues
  [switch]$ShowContext,   # Show lines around Unicode occurrences
  [string]$Path = "."     # Path to scan (defaults to current directory)
)

Write-Host "[SCAN] Unicode Character Detection Tool" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

# Define file patterns to check
$filePatterns = @("*.py", "*.ps1", "*.yml", "*.yaml", "*.sh", "*.js", "*.ts", "*.dockerfile")

# Define common Unicode replacements
$unicodeReplacements = @{
  ""   = "[OK]"
  ""   = "[FAIL]"
  ""  = "[WARN]"
  ""  = "[LOCK]"
  ""  = "[BLOCK]"
  ""  = "[SUCCESS]"
  "ℹ"  = "[INFO]"
  ""  = "[SEARCH]"
  ""  = "[STATS]"
  "" = "[SECURITY]"
  ""  = "[NETWORK]"
  ""  = "[WEB]"
  ""  = "[INTERNAL]"
  ""  = "[TARGET]"
  ""  = "[TOOLS]"
  "→"   = "->"
  "←"   = "<-"
  "↑"   = "^"
  "↓"   = "v"
}

# Get all files to check
$filesToCheck = @()
foreach ($pattern in $filePatterns) {
  $files = Get-ChildItem -Path $Path -Recurse -Include $pattern |
  Where-Object {
    $_.FullName -notmatch "\.git|node_modules|__pycache__|\.venv|venv|\.pytest_cache"
  }
  $filesToCheck += $files
}

Write-Host "[INFO] Scanning $($filesToCheck.Count) files..." -ForegroundColor Yellow

$foundUnicode = $false
$totalIssues = 0
$fixedIssues = 0

foreach ($file in $filesToCheck) {
  try {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $lines = Get-Content $file.FullName -Encoding UTF8

    # Check if file contains non-ASCII characters
    if ($content -match '[^\x00-\x7F]') {
      $foundUnicode = $true
      Write-Host ""
      Write-Host "[UNICODE] Found in: $($file.FullName)" -ForegroundColor Red

      # Find specific Unicode characters
      for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        if ($line -match '[^\x00-\x7F]') {
          $totalIssues++

          # Show context if requested
          if ($ShowContext) {
            $startLine = [Math]::Max(0, $i - 1)
            $endLine = [Math]::Min($lines.Count - 1, $i + 1)

            Write-Host "  Context (lines $($startLine + 1)-$($endLine + 1)):" -ForegroundColor Yellow
            for ($j = $startLine; $j -le $endLine; $j++) {
              $prefix = if ($j -eq $i) { ">>> " } else { "    " }
              Write-Host "  $prefix$($j + 1): $($lines[$j])" -ForegroundColor Gray
            }
          }
          else {
            Write-Host "  Line $($i + 1): $line" -ForegroundColor Gray
          }

          # Check for known replacements
          $suggestions = @()
          foreach ($unicode in $unicodeReplacements.Keys) {
            if ($line -match [regex]::Escape($unicode)) {
              $suggestions += "$unicode -> $($unicodeReplacements[$unicode])"
            }
          }

          if ($suggestions.Count -gt 0) {
            Write-Host "  [SUGGEST] $($suggestions -join ', ')" -ForegroundColor Cyan

            # Auto-fix if requested
            if ($Fix) {
              $originalLine = $line
              foreach ($unicode in $unicodeReplacements.Keys) {
                $line = $line -replace [regex]::Escape($unicode), $unicodeReplacements[$unicode]
              }

              if ($line -ne $originalLine) {
                $lines[$i] = $line
                $fixedIssues++
                Write-Host "  [FIXED] Applied automatic fix" -ForegroundColor Green
              }
            }
          }
        }
      }

      # Write back fixed content if fixes were applied
      if ($Fix -and $fixedIssues -gt 0) {
        $lines | Set-Content $file.FullName -Encoding UTF8
        Write-Host "  [SAVED] Applied $fixedIssues fixes to file" -ForegroundColor Green
      }
    }
  }
  catch {
    Write-Host "[ERROR] Failed to process $($file.FullName): $($_.Exception.Message)" -ForegroundColor Red
  }
}

Write-Host ""
Write-Host "[RESULTS] Scan Complete" -ForegroundColor Magenta
Write-Host "========================" -ForegroundColor Magenta

if ($foundUnicode) {
  Write-Host "[FOUND] Unicode characters detected in $totalIssues locations" -ForegroundColor Red

  if ($Fix) {
    Write-Host "[FIXED] Automatically fixed $fixedIssues issues" -ForegroundColor Green
    if ($fixedIssues -lt $totalIssues) {
      Write-Host "[MANUAL] $($totalIssues - $fixedIssues) issues require manual fixing" -ForegroundColor Yellow
    }
  }
  else {
    Write-Host "[ACTION] Run with -Fix to automatically replace common Unicode characters" -ForegroundColor Yellow
  }

  Write-Host ""
  Write-Host "[HELP] Common ASCII alternatives:" -ForegroundColor Cyan
  foreach ($pair in $unicodeReplacements.GetEnumerator() | Sort-Object Key) {
    Write-Host "  $($pair.Key) -> $($pair.Value)" -ForegroundColor White
  }

  exit 1
}
else {
  Write-Host "[SUCCESS] No Unicode characters found in code files!" -ForegroundColor Green
  Write-Host "[OK] Codebase is compatible with all terminal environments" -ForegroundColor Green
  exit 0
}

# Usage examples:
# .\scripts\check-unicode.ps1                    # Scan for Unicode
# .\scripts\check-unicode.ps1 -Fix               # Auto-fix common issues
# .\scripts\check-unicode.ps1 -ShowContext       # Show context around issues
# .\scripts\check-unicode.ps1 -Path ".\src"      # Scan specific directory
