#!/usr/bin/env pwsh
# Network Security Model Verification Script
# Tests the two-network setup for proper isolation and connectivity

Write-Host " AI Coding Agent - Two-Network Security Verification" -ForegroundColor Cyan
Write-Host "=================================================================" -ForegroundColor Cyan

# Function to test network connectivity
function Test-NetworkConnectivity {
  param(
    [string]$FromContainer,
    [string]$ToContainer,
    [string]$Port,
    [bool]$ShouldSucceed = $true
  )

  $testName = if ($ShouldSucceed) { " ALLOW" } else { " BLOCK" }
  Write-Host "$testName Testing: $FromContainer -> $ToContainer`:$Port" -ForegroundColor Yellow

  docker-compose exec -T $FromContainer sh -c "timeout 3 nc -z $ToContainer $Port" 2>$null
  $exitCode = $LASTEXITCODE

  if ($ShouldSucceed) {
    if ($exitCode -eq 0) {
      Write-Host "   PASS: Connection successful (expected)" -ForegroundColor Green
      return $true
    }
    else {
      Write-Host "   FAIL: Connection failed (unexpected)" -ForegroundColor Red
      return $false
    }
  }
  else {
    if ($exitCode -ne 0) {
      Write-Host "   PASS: Connection blocked (expected)" -ForegroundColor Green
      return $true
    }
    else {
      Write-Host "   FAIL: Connection succeeded (security breach!)" -ForegroundColor Red
      return $false
    }
  }
}

# Function to check if container is running
function Test-ContainerRunning {
  param([string]$ContainerName)

  $status = docker-compose ps -q $ContainerName 2>$null
  if ($status) {
    $health = docker inspect --format='{{.State.Health.Status}}' $status 2>$null
    return ($health -eq "healthy" -or $health -eq "")
  }
  return $false
}

Write-Host ""
Write-Host " Step 1: Verifying Container Status" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Magenta

$containers = @("traefik", "ai-orchestrator", "user-portal", "redis", "ollama")
$runningContainers = @()

foreach ($container in $containers) {
  if (Test-ContainerRunning $container) {
    Write-Host "   $container is running" -ForegroundColor Green
    $runningContainers += $container
  }
  else {
    Write-Host "    $container is not running or not healthy" -ForegroundColor Yellow
  }
}

if ($runningContainers.Count -eq 0) {
  Write-Host " No containers are running. Please start the stack first:" -ForegroundColor Red
  Write-Host "   docker-compose up -d" -ForegroundColor White
  exit 1
}

Write-Host ""
Write-Host " Step 2: Network Connectivity Tests" -ForegroundColor Magenta
Write-Host "=====================================" -ForegroundColor Magenta

$allPassed = $true

# Web Network (DMZ) Tests - These should succeed
Write-Host ""
Write-Host " Web Network (DMZ) Connectivity Tests:" -ForegroundColor Blue

if ("traefik" -in $runningContainers -and "ai-orchestrator" -in $runningContainers) {
  $allPassed = (Test-NetworkConnectivity "traefik" "ai-orchestrator" "8000" $true) -and $allPassed
}

if ("traefik" -in $runningContainers -and "user-portal" -in $runningContainers) {
  $allPassed = (Test-NetworkConnectivity "traefik" "user-portal" "3000" $true) -and $allPassed
}

# Internal Network Tests - These should succeed
Write-Host ""
Write-Host " Internal Network Connectivity Tests:" -ForegroundColor Blue

if ("ai-orchestrator" -in $runningContainers -and "redis" -in $runningContainers) {
  $allPassed = (Test-NetworkConnectivity "ai-orchestrator" "redis" "6379" $true) -and $allPassed
}

if ("ai-orchestrator" -in $runningContainers -and "ollama" -in $runningContainers) {
  $allPassed = (Test-NetworkConnectivity "ai-orchestrator" "ollama" "11434" $true) -and $allPassed
}

# Network Isolation Tests - These should fail (security working correctly)
Write-Host ""
Write-Host "  Network Isolation Tests (should fail):" -ForegroundColor Blue

if ("user-portal" -in $runningContainers -and "redis" -in $runningContainers) {
  $allPassed = (Test-NetworkConnectivity "user-portal" "redis" "6379" $false) -and $allPassed
}

if ("user-portal" -in $runningContainers -and "ollama" -in $runningContainers) {
  $allPassed = (Test-NetworkConnectivity "user-portal" "ollama" "11434" $false) -and $allPassed
}

if ("traefik" -in $runningContainers -and "redis" -in $runningContainers) {
  $allPassed = (Test-NetworkConnectivity "traefik" "redis" "6379" $false) -and $allPassed
}

Write-Host ""
Write-Host " Step 3: Network Configuration Verification" -ForegroundColor Magenta
Write-Host "=============================================" -ForegroundColor Magenta

# Check network existence
$webNetwork = docker network ls --filter name=ai-coding-agent_web_network -q
$internalNetwork = docker network ls --filter name=ai-coding-agent_internal_network -q

if ($webNetwork) {
  Write-Host "   Web network exists: ai-coding-agent_web_network" -ForegroundColor Green
}
else {
  Write-Host "   Web network missing: ai-coding-agent_web_network" -ForegroundColor Red
  $allPassed = $false
}

if ($internalNetwork) {
  Write-Host "   Internal network exists: ai-coding-agent_internal_network" -ForegroundColor Green
}
else {
  Write-Host "   Internal network missing: ai-coding-agent_internal_network" -ForegroundColor Red
  $allPassed = $false
}

Write-Host ""
Write-Host " Step 4: Service Health Checks" -ForegroundColor Magenta
Write-Host "================================" -ForegroundColor Magenta

foreach ($container in $runningContainers) {
  $health = docker inspect --format='{{.State.Health.Status}}' $(docker-compose ps -q $container) 2>$null

  switch ($health) {
    "healthy" {
      Write-Host "   $container is healthy" -ForegroundColor Green
    }
    "starting" {
      Write-Host "  ⏳ $container is starting" -ForegroundColor Yellow
    }
    "unhealthy" {
      Write-Host "   $container is unhealthy" -ForegroundColor Red
      $allPassed = $false
    }
    default {
      Write-Host "  ℹ  $container has no health check defined" -ForegroundColor Cyan
    }
  }
}

Write-Host ""
Write-Host " Final Results" -ForegroundColor Magenta
Write-Host "================" -ForegroundColor Magenta

if ($allPassed) {
  Write-Host " ALL TESTS PASSED!" -ForegroundColor Green
  Write-Host "    Network segmentation is working correctly" -ForegroundColor Green
  Write-Host "    DMZ services can communicate via web network" -ForegroundColor Green
  Write-Host "    Backend services are isolated on internal network" -ForegroundColor Green
  Write-Host "    AI Orchestrator bridges both networks securely" -ForegroundColor Green
  Write-Host ""
  Write-Host " Your two-network security model is properly configured!" -ForegroundColor Cyan
  exit 0
}
else {
  Write-Host " SOME TESTS FAILED!" -ForegroundColor Red
  Write-Host "   Please check the configuration and logs above." -ForegroundColor Yellow
  Write-Host ""
  Write-Host " Troubleshooting steps:" -ForegroundColor Cyan
  Write-Host "   1. Ensure all containers are running: docker-compose ps" -ForegroundColor White
  Write-Host "   2. Check container logs: docker-compose logs <service>" -ForegroundColor White
  Write-Host "   3. Verify network configuration: docker network ls" -ForegroundColor White
  Write-Host "   4. Restart the stack: docker-compose down && docker-compose up -d" -ForegroundColor White
  exit 1
}
