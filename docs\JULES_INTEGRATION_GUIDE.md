# Jules Integration Guide for AI Coding Agent

## Overview

This repository contains two comprehensive prompts designed to help <PERSON> systematically review and repair the AI Coding Agent codebase.

## Available Prompts

### 🌐 **Option A: Full Codebase Review (Recommended)**

**File**: `JULES_COMPREHENSIVE_CODEBASE_REVIEW.md`

**Scope**: Complete end-to-end project analysis and repair

**What it covers**:
- All Python source code across all directories
- Container configurations and Docker setup
- Infrastructure code and deployment scripts
- Documentation and README files
- Testing infrastructure and coverage
- Security configurations and best practices
- Performance optimizations throughout

**Use when**: You want <PERSON> to review and fix everything from top to bottom

**Timeline**: 8-12 hours over 1-2 days

### 🔧 **Option B: Targeted Services Repair**

**File**: `JULES_SERVICES_REPAIR_PROMPT.md`

**Scope**: Focused repair of the critical services directory

**What it covers**:
- `containers/ai-orchestrator/src/services/` directory only
- 24+ service files with identified issues
- Compilation errors, import problems, type safety
- Async patterns and error handling

**Use when**: You want to focus specifically on the services layer issues

**Timeline**: 2-4 hours

## How to Use with Jules

### Setup Steps

1. **Connect Jules to your repository**:
   - Visit [jules.google.com](https://jules.google.com/)
   - Sign in and connect your GitHub account
   - Select the `codingagenttwo` repository

2. **Choose your approach**:
   - For comprehensive review: Use the full codebase prompt
   - For focused work: Use the services repair prompt

3. **Provide the prompt**:
   Copy the contents of your chosen markdown file and paste it as your Jules prompt

### Recommended Approach

**Start with the Comprehensive Review** - Jules will:
- Analyze the entire project structure
- Identify and prioritize all issues
- Work systematically from critical to minor issues
- Ensure everything is production-ready

**Jules will automatically**:
- Read the AGENTS.md file for project context
- Follow the Copilot Instructions for coding standards
- Set up the environment properly
- Validate changes continuously
- Create proper GitHub branches and PRs

## Expected Outcomes

### After Comprehensive Review

- **Zero compilation errors** across all Python files
- **Complete type safety** with mypy compliance
- **All containers build and run** successfully
- **Full documentation** accuracy and completeness
- **Security vulnerabilities** addressed
- **Performance optimizations** implemented
- **Test coverage** improved and all tests passing

### Quality Improvements

- Professional-grade error handling throughout
- Comprehensive logging and monitoring
- Optimized async/await patterns
- Secure configuration management
- Clean, maintainable code architecture

### 🛡️ **Security Assessment Reports**

Jules will provide comprehensive security analysis including:

- **Vulnerability Assessment**: Complete catalog of security issues with risk ratings
- **Compliance Analysis**: OWASP Top 10, container security, and best practices review
- **Remediation Roadmap**: Prioritized security fixes with implementation guidance
- **Security Monitoring**: Recommendations for ongoing security monitoring

### 🚀 **Enhancement & Improvement Analysis**

Jules will identify strategic improvement opportunities:

- **Performance Optimization**: Database, caching, and resource utilization improvements
- **Architecture Enhancements**: Scalability, maintainability, and design pattern opportunities
- **DevOps Improvements**: CI/CD, monitoring, and infrastructure optimization
- **AI/ML Specific**: Model deployment, monitoring, and performance enhancements

### 📊 **Detailed Assessment Reports**

**Security Reports:**
1. Executive security posture summary
2. Detailed vulnerability catalog with CVSS scores
3. Compliance gap analysis
4. Security remediation timeline and priorities

**Performance & Architecture Reports:**
1. Performance bottleneck analysis with metrics
2. Scalability assessment and recommendations
3. Technical debt quantification and remediation plan
4. Code quality metrics and improvement opportunities

**Strategic Recommendations:**
1. Technology stack modernization opportunities
2. Infrastructure and deployment optimization
3. Development workflow improvements
4. Long-term architectural evolution roadmap

## Project Context for Jules

The AI Coding Agent is a sophisticated multi-agent system with:
- **Container-first architecture** using Docker and docker-compose
- **Async-first Python** with FastAPI and SQLAlchemy
- **Multi-provider LLM integration** (Ollama, OpenRouter, OpenAI, Anthropic)
- **Unified knowledge management** (Supabase pgvector)
- **Sequential agent workflows** with proper resource management

Jules will understand this architecture through the AGENTS.md file and apply appropriate patterns consistently across the codebase.

## Getting Started

Choose your prompt file and start your Jules task. The system is designed to work autonomously while providing you with progress updates and the ability to provide feedback during execution.

**Remember**: Jules can work for hours unattended, so you can start the task and come back to review the results when complete.
