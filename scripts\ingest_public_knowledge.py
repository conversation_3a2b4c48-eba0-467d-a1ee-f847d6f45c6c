#!/usr/bin/env python3
"""
Public Knowledge Ingestion Script

This script ingests public knowledge documents into ChromaDB for the Two-Tiered Knowledge System.
It processes Markdown and text files, chunks them, generates embeddings, and stores them in ChromaDB.

ChromaDB is now embedded in the ai-orchestrator-chroma container.

Usage:
    python scripts/ingest_public_knowledge.py --source-dir ./knowledge_base/fastapi --collection-name public_knowledge

    # Connect to development container
    python scripts/ingest_public_knowledge.py --source-dir ./docs --collection-name public_docs --chromadb-port 8002

    # Connect to production container
    python scripts/ingest_public_knowledge.py --source-dir ./docs --collection-name public_docs --chromadb-port 8003

Author: AI Coding Agent
Version: 1.0.0
"""

import argparse
import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "containers" / "ai-orchestrator" / "src"))

# Import required modules
try:
    import chromadb

    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False

try:
    from langchain_community.text_splitter import RecursiveCharacterTextSplitter

    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False

# Import our internal services (only if needed)
VectorStorageService = None

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class PublicKnowledgeIngester:
    """
    Ingester for public knowledge documents into ChromaDB.
    """

    def __init__(
        self,
        source_dir: str,
        collection_name: str,
        chromadb_host: str = "localhost",
        chromadb_port: int = 8000,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        dry_run: bool = False,
        max_files: Optional[int] = None,
    ):
        """
        Initialize the ingester.

        Args:
            source_dir: Directory containing knowledge base files
            collection_name: ChromaDB collection name
            chromadb_host: ChromaDB server host
            chromadb_port: ChromaDB server port
            chunk_size: Size of text chunks
            chunk_overlap: Overlap between chunks
            dry_run: If True, don't store in ChromaDB
            max_files: Maximum number of files to process
        """
        self.source_dir = Path(source_dir)
        self.collection_name = collection_name
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.dry_run = dry_run
        self.max_files = max_files

        # Validate source directory
        if not self.source_dir.exists():
            raise ValueError(f"Source directory does not exist: {self.source_dir}")

        if not self.source_dir.is_dir():
            raise ValueError(f"Source path is not a directory: {self.source_dir}")

        # Initialize ChromaDB client (only if not dry run)
        if not dry_run:
            try:
                # Connect to ChromaDB embedded in ai-orchestrator-chroma container
                self.chromadb_client = chromadb.HttpClient(host=chromadb_host, port=chromadb_port)
                logger.info(
                    f"Connected to ChromaDB (ai-orchestrator-chroma) at {chromadb_host}:{chromadb_port}"
                )
            except Exception as e:
                logger.error(f"Failed to connect to ChromaDB: {e}")
                logger.error("Make sure ai-orchestrator-chroma container is running")
                raise
        else:
            self.chromadb_client = None
            logger.info("Dry run mode - ChromaDB connection skipped")

        # Initialize text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=["\n## ", "\n### ", "\n#### ", "\n", ". ", " ", ""],
        )

        # Initialize vector service (we'll need to mock or configure this)
        # For now, we'll create a placeholder
        self.vector_service = None

        # Statistics
        self.stats = {
            "files_processed": 0,
            "chunks_created": 0,
            "embeddings_generated": 0,
            "start_time": time.time(),
        }

    def setup_vector_service(self):
        """Setup the vector storage service for embeddings."""

        # For the ingestion script, we need a minimal setup
        # We'll create a mock SupabaseService since we only need embedding generation
        class MockSupabaseService:
            def __init__(self):
                pass

        from src.services.vector_service import VectorConfig, VectorStorageService

        try:
            # Create minimal config for embedding generation only
            config = VectorConfig(
                embedding_provider="sentence_transformers",  # Use local embeddings
                embedding_model="all-MiniLM-L6-v2",  # Small, fast model
                embedding_dimension=384,
                max_chunk_size=1000,
                chunk_overlap=200,
            )

            mock_supabase = MockSupabaseService()
            self.vector_service = VectorStorageService(mock_supabase, config)

            # Initialize the service
            asyncio.run(self.vector_service.initialize())

            logger.info("Vector service initialized for ingestion")
        except Exception as e:
            logger.error(f"Failed to initialize vector service: {e}")
            raise

    def get_supported_files(self) -> List[Path]:
        """Get all supported files from the source directory."""
        supported_extensions = {".md", ".txt", ".markdown"}

        files = []
        for ext in supported_extensions:
            files.extend(self.source_dir.rglob(f"*{ext}"))

        return sorted(files)

    def read_file_content(self, file_path: Path) -> str:
        """Read content from a file."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            return content
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            return ""

    def chunk_text(self, text: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Chunk text into smaller pieces with metadata."""
        try:
            chunks = self.text_splitter.split_text(text)

            chunked_docs = []
            for i, chunk in enumerate(chunks):
                chunk_metadata = metadata.copy()
                chunk_metadata.update(
                    {"chunk_id": i, "total_chunks": len(chunks), "chunk_size": len(chunk)}
                )

                chunked_docs.append(
                    {
                        "content": chunk,
                        "metadata": chunk_metadata,
                        "id": f"{metadata['source_file']}_chunk_{i}",
                    }
                )

            return chunked_docs

        except Exception as e:
            logger.error(f"Failed to chunk text: {e}")
            return []

    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using vector service."""
        if not self.vector_service:
            raise ValueError("Vector service not initialized")

        try:
            # Use the vector service's embedding generation method
            # This assumes the method exists and is async
            if hasattr(self.vector_service, "generate_embedding"):
                embedding = await self.vector_service.generate_embedding(text)
                return embedding
            else:
                # Fallback: try to access the embedding model directly
                if self.vector_service._embedding_model:
                    # For sentence transformers
                    embedding = self.vector_service._embedding_model.encode(text).tolist()
                    return embedding
                else:
                    raise ValueError("No embedding model available")
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise

    def create_collection_if_not_exists(self):
        """Create ChromaDB collection if it doesn't exist."""
        if self.dry_run:
            logger.info(f"Dry run: Would create/use collection '{self.collection_name}'")
            self.collection = None
            return

        try:
            # Try to get existing collection
            self.collection = self.chromadb_client.get_collection(self.collection_name)
            logger.info(f"Using existing collection: {self.collection_name}")
        except Exception:
            # Collection doesn't exist, create it
            self.collection = self.chromadb_client.create_collection(
                name=self.collection_name,
                metadata={"description": "Public knowledge base for AI coding agent"},
            )
            logger.info(f"Created new collection: {self.collection_name}")

    async def process_file(self, file_path: Path) -> int:
        """Process a single file and return number of chunks created."""
        logger.info(f"Processing file: {file_path}")

        # Read file content
        content = self.read_file_content(file_path)
        if not content:
            return 0

        # Create metadata
        metadata = {
            "source_file": str(file_path.relative_to(self.source_dir)),
            "file_path": str(file_path),
            "file_size": len(content),
            "file_extension": file_path.suffix,
            "ingestion_time": time.time(),
        }

        # Chunk the text
        chunks = self.chunk_text(content, metadata)
        if not chunks:
            logger.warning(f"No chunks created for file: {file_path}")
            return 0

        logger.info(f"Created {len(chunks)} chunks for file: {file_path}")

        # Generate embeddings and prepare data for ChromaDB
        documents = []
        embeddings = []
        metadatas = []
        ids = []

        for chunk in chunks:
            try:
                # Generate embedding
                embedding = await self.generate_embedding(chunk["content"])

                documents.append(chunk["content"])
                embeddings.append(embedding)
                metadatas.append(chunk["metadata"])
                ids.append(chunk["id"])

                self.stats["embeddings_generated"] += 1

            except Exception as e:
                logger.error(f"Failed to process chunk {chunk['id']}: {e}")
                continue

        # Store in ChromaDB
        if documents:
            if self.dry_run:
                logger.info(f"Dry run: Would store {len(documents)} chunks in ChromaDB")
            else:
                try:
                    self.collection.add(
                        documents=documents, embeddings=embeddings, metadatas=metadatas, ids=ids
                    )
                    logger.info(f"Stored {len(documents)} chunks in ChromaDB")
                except Exception as e:
                    logger.error(f"Failed to store chunks in ChromaDB: {e}")
                    return 0

        return len(chunks)

    async def ingest_all_files(self):
        """Process all files in the source directory."""
        # Setup
        self.setup_vector_service()
        self.create_collection_if_not_exists()

        # Get all supported files
        files = self.get_supported_files()
        if self.max_files:
            files = files[: self.max_files]
            logger.info(f"Limited to first {self.max_files} files for testing")

        logger.info(f"Found {len(files)} supported files to process")

        if not files:
            logger.warning("No supported files found in source directory")
            return

        # Process each file
        total_chunks = 0
        for i, file_path in enumerate(files, 1):
            logger.info(f"Processing file {i}/{len(files)}: {file_path}")
            chunks_created = await self.process_file(file_path)
            total_chunks += chunks_created
            self.stats["files_processed"] += 1

        self.stats["chunks_created"] = total_chunks

    def print_summary(self):
        """Print ingestion summary."""
        duration = time.time() - self.stats["start_time"]

        print("\n" + "=" * 60)
        if self.dry_run:
            print(" DRY RUN - PUBLIC KNOWLEDGE INGESTION SUMMARY")
        else:
            print(" PUBLIC KNOWLEDGE INGESTION SUMMARY")
        print("=" * 60)
        print(f" Files Processed: {self.stats['files_processed']}")
        print(f" Chunks Created: {self.stats['chunks_created']}")
        print(f" Embeddings Generated: {self.stats['embeddings_generated']}")
        print(f" Duration: {duration:.2f} seconds")
        print(f" Collection: {self.collection_name}")
        if self.dry_run:
            print(" Mode: Dry Run (no data stored)")
        else:
            print(" Mode: Live (data stored in ChromaDB)")
        print("=" * 60)

        if self.stats["files_processed"] > 0:
            if self.dry_run:
                print(" Dry run completed successfully!")
            else:
                print(" Ingestion completed successfully!")
        else:
            print("  No files were processed. Check your source directory.")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Ingest public knowledge documents into ChromaDB",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/ingest_public_knowledge.py --source-dir ./knowledge_base/fastapi --collection-name public_knowledge
  python scripts/ingest_public_knowledge.py --source-dir ./docs --collection-name documentation --chunk-size 800
        """,
    )

    parser.add_argument(
        "--source-dir", required=True, help="Directory containing knowledge base files"
    )

    parser.add_argument(
        "--collection-name", required=True, help="ChromaDB collection name for ingestion"
    )

    parser.add_argument(
        "--chromadb-host", default="localhost", help="ChromaDB server host (default: localhost)"
    )

    parser.add_argument(
        "--chromadb-port", type=int, default=8000, help="ChromaDB server port (default: 8000)"
    )

    parser.add_argument(
        "--chunk-size", type=int, default=1000, help="Size of text chunks (default: 1000)"
    )

    parser.add_argument(
        "--chunk-overlap", type=int, default=200, help="Overlap between chunks (default: 200)"
    )

    parser.add_argument(
        "--dry-run", action="store_true", help="Perform dry run without storing in ChromaDB"
    )

    parser.add_argument(
        "--max-files", type=int, help="Limit number of files to process (for testing)"
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Create ingester
        ingester = PublicKnowledgeIngester(
            source_dir=args.source_dir,
            collection_name=args.collection_name,
            chromadb_host=args.chromadb_host,
            chromadb_port=args.chromadb_port,
            chunk_size=args.chunk_size,
            chunk_overlap=args.chunk_overlap,
            dry_run=args.dry_run,
            max_files=args.max_files,
        )

        # Run ingestion
        await ingester.ingest_all_files()

        # Print summary
        ingester.print_summary()

    except KeyboardInterrupt:
        print("\n Ingestion interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Ingestion failed: {e}")
        print(f"\n Ingestion failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
