"""
Port Management Service for AI Coding Agent

Manages port allocation for isolated project environments to prevent conflicts.
Enhanced with distributed locking for multi-instance safety.
"""

import logging
import socket
from typing import Dict, Optional, List, Any
from dataclasses import dataclass
from pathlib import Path
import json

# Import the enhanced LockManager for distributed locking
from services.lock_manager import LockManager
from services.redis_service import get_redis_client

logger = logging.getLogger(__name__)


@dataclass
class PortAllocation:
    """Port allocation information."""
    project_name: str
    user_id: str
    port: int
    service_type: str
    allocated_at: str
    status: str  # 'active', 'reserved', 'released'


class PortManager:
    """Manages port allocation for project isolation with distributed locking."""

    def __init__(self, port_range_start: int = 8000, port_range_end: int = 9000, tenant_id: str = "default"):
        self.port_range_start = port_range_start
        self.port_range_end = port_range_end
        self.tenant_id = tenant_id
        self.allocated_ports: Dict[int, PortAllocation] = {}
        self.user_ports: Dict[str, Dict[str, int]] = {}  # user_id -> {project_name: port}

        # Use distributed locking for concurrency safety
        self._lock_manager: Optional[LockManager] = None
        self._redis_client = None

        # Load existing allocations
        self._load_port_allocations()

    async def _get_lock_manager(self) -> LockManager:
        """Get or create the LockManager instance."""
        if self._lock_manager is None:
            if self._redis_client is None:
                self._redis_client = await get_redis_client()
            self._lock_manager = LockManager(self._redis_client, self.tenant_id)
        return self._lock_manager

    def _get_allocations_file(self) -> Path:
        """Get path to port allocations file."""
        return Path(__file__).parent.parent.parent / "data" / "port_allocations.json"

    def _load_port_allocations(self):
        """Load existing port allocations from disk."""
        try:
            allocations_file = self._get_allocations_file()
            if allocations_file.exists():
                with open(allocations_file, 'r') as f:
                    data = json.load(f)

                for port_str, allocation_data in data.get('allocated_ports', {}).items():
                    port = int(port_str)
                    allocation = PortAllocation(**allocation_data)
                    self.allocated_ports[port] = allocation

                    # Rebuild user_ports index
                    user_id = allocation.user_id
                    project_name = allocation.project_name
                    if user_id not in self.user_ports:
                        self.user_ports[user_id] = {}
                    self.user_ports[user_id][project_name] = port

                logger.info(f"Loaded {len(self.allocated_ports)} port allocations")
        except Exception as e:
            logger.warning(f"Failed to load port allocations: {e}")

    def _save_port_allocations(self):
        """Save port allocations to disk."""
        try:
            allocations_file = self._get_allocations_file()
            allocations_file.parent.mkdir(parents=True, exist_ok=True)

            data = {
                'allocated_ports': {
                    str(port): {
                        'project_name': alloc.project_name,
                        'user_id': alloc.user_id,
                        'port': alloc.port,
                        'service_type': alloc.service_type,
                        'allocated_at': alloc.allocated_at,
                        'status': alloc.status
                    }
                    for port, alloc in self.allocated_ports.items()
                }
            }

            with open(allocations_file, 'w') as f:
                json.dump(data, f, indent=2)

        except Exception as e:
            logger.error(f"Failed to save port allocations: {e}")

    def _is_port_available(self, port: int) -> bool:
        """Check if a port is available on the system."""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result != 0  # Port is available if connection fails
        except Exception:
            return False

    async def allocate_port(self, user_id: str, project_name: str, service_type: str = "webapp") -> Optional[int]:
        """Allocate a port for a project using distributed locking."""
        lock_manager = await self._get_lock_manager()

        # Use a synthetic project_id for port allocation (hash of user_id + project_name)
        lock_project_id = hash(f"{user_id}:{project_name}") % 1000000

        acquired = await lock_manager.acquire_lock(
            project_id=lock_project_id,
            agent_role="port_manager",
            task_id=hash(f"allocate_port:{user_id}:{project_name}") % 1000000,
            timeout_seconds=60
        )

        if not acquired:
            logger.error(f"Failed to acquire lock for port allocation: {user_id}:{project_name}")
            return None

        try:
            # Check if project already has a port
            if user_id in self.user_ports and project_name in self.user_ports[user_id]:
                existing_port = self.user_ports[user_id][project_name]
                if existing_port in self.allocated_ports:
                    logger.info(f"Project {project_name} already has port {existing_port}")
                    return existing_port

            # Find available port
            for port in range(self.port_range_start, self.port_range_end + 1):
                if port not in self.allocated_ports and self._is_port_available(port):
                    # Allocate the port
                    from datetime import datetime
                    allocation = PortAllocation(
                        project_name=project_name,
                        user_id=user_id,
                        port=port,
                        service_type=service_type,
                        allocated_at=datetime.utcnow().isoformat(),
                        status="active"
                    )

                    self.allocated_ports[port] = allocation

                    if user_id not in self.user_ports:
                        self.user_ports[user_id] = {}
                    self.user_ports[user_id][project_name] = port

                    self._save_port_allocations()

                    logger.info(f"Allocated port {port} for project {project_name} (user: {user_id})")
                    return port

            logger.error(f"No available ports in range {self.port_range_start}-{self.port_range_end}")
            return None

        finally:
            # Always release the lock
            await lock_manager.release_lock(lock_project_id)

    async def release_port(self, user_id: str, project_name: str) -> bool:
        """Release a port allocation using distributed locking."""
        lock_manager = await self._get_lock_manager()

        # Use the same project_id as in allocate_port
        lock_project_id = hash(f"{user_id}:{project_name}") % 1000000

        acquired = await lock_manager.acquire_lock(
            project_id=lock_project_id,
            agent_role="port_manager",
            task_id=hash(f"release_port:{user_id}:{project_name}") % 1000000,
            timeout_seconds=60
        )

        if not acquired:
            logger.error(f"Failed to acquire lock for port release: {user_id}:{project_name}")
            return False

        try:
            if user_id not in self.user_ports or project_name not in self.user_ports[user_id]:
                logger.warning(f"No port allocation found for project {project_name}")
                return False

            port = self.user_ports[user_id][project_name]

            if port in self.allocated_ports:
                self.allocated_ports[port].status = "released"
                del self.allocated_ports[port]
                del self.user_ports[user_id][project_name]

                if not self.user_ports[user_id]:
                    del self.user_ports[user_id]

                self._save_port_allocations()

                logger.info(f"Released port {port} for project {project_name}")
                return True

            return False

        finally:
            # Always release the lock
            await lock_manager.release_lock(lock_project_id)

    async def get_project_port(self, user_id: str, project_name: str) -> Optional[int]:
        """Get the allocated port for a project."""
        if user_id in self.user_ports and project_name in self.user_ports[user_id]:
            return self.user_ports[user_id][project_name]
        return None

    async def get_user_allocations(self, user_id: str) -> List[PortAllocation]:
        """Get all port allocations for a user."""
        allocations = []
        if user_id in self.user_ports:
            for project_name, port in self.user_ports[user_id].items():
                if port in self.allocated_ports:
                    allocations.append(self.allocated_ports[port])
        return allocations

    async def cleanup_inactive_ports(self) -> int:
        """Clean up ports that are no longer in use using distributed locking."""
        lock_manager = await self._get_lock_manager()

        # Use a special project_id for cleanup operations
        cleanup_project_id = 999999

        acquired = await lock_manager.acquire_lock(
            project_id=cleanup_project_id,
            agent_role="port_manager",
            task_id=hash("cleanup_inactive_ports") % 1000000,
            timeout_seconds=120  # Longer timeout for cleanup
        )

        if not acquired:
            logger.error("Failed to acquire lock for port cleanup")
            return 0

        try:
            cleaned_count = 0
            ports_to_remove = []

            for port, allocation in self.allocated_ports.items():
                if not self._is_port_available(port):
                    # Port is still in use, keep it
                    continue

                # Check if the project still exists (this would need integration with ProjectRepository)
                # For now, we'll mark old allocations for cleanup
                from datetime import datetime, timedelta
                try:
                    allocated_time = datetime.fromisoformat(allocation.allocated_at)
                    if datetime.utcnow() - allocated_time > timedelta(days=7):  # 7 days old
                        ports_to_remove.append(port)
                except Exception:
                    continue

            for port in ports_to_remove:
                allocation = self.allocated_ports[port]
                user_id = allocation.user_id
                project_name = allocation.project_name

                del self.allocated_ports[port]
                if user_id in self.user_ports and project_name in self.user_ports[user_id]:
                    del self.user_ports[user_id][project_name]
                    if not self.user_ports[user_id]:
                        del self.user_ports[user_id]

                cleaned_count += 1
                logger.info(f"Cleaned up inactive port {port} for project {project_name}")

            if cleaned_count > 0:
                self._save_port_allocations()

            return cleaned_count

        finally:
            # Always release the lock
            await lock_manager.release_lock(cleanup_project_id)

    def get_allocation_stats(self) -> Dict[str, Any]:
        """Get port allocation statistics."""
        total_range = self.port_range_end - self.port_range_start + 1
        allocated = len(self.allocated_ports)
        available = total_range - allocated

        return {
            "total_range": total_range,
            "allocated": allocated,
            "available": available,
            "utilization_percent": round((allocated / total_range) * 100, 2)
        }


# Global port manager instance
port_manager = PortManager()


def get_port_manager() -> PortManager:
    """Get the global port manager instance."""
    return port_manager
