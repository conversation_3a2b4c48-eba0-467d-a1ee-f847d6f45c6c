# Code Style and Character Encoding Guidelines

## Overview

This document establishes strict guidelines for character usage in the AI Coding Agent project to ensure compatibility across all platforms and terminal environments.

## ❌ FORBIDDEN: Emojis and Unicode Symbols

### Why No Emojis?
- **Encoding Issues**: Different terminals, shells, and systems handle Unicode differently
- **Compatibility**: Not all environments support Unicode rendering
- **Accessibility**: Screen readers and assistive technologies may struggle
- **Professionalism**: ASCII alternatives are more universally accepted

### Examples of What NOT to Use:
```
❌ Don't use: ✅ ❌ 🔒 🚫 🎉 ⚠️ 🔍 📊 🛡️ 🌐
✅ Use instead: [OK] [FAIL] [LOCK] [BLOCK] [SUCCESS] [WARN] [INFO] [RESULTS] [SECURITY] [NETWORK]
```

## ✅ APPROVED: ASCII Alternatives

### Status Indicators
```
[OK]       - Success/healthy state
[FAIL]     - Failure/error state
[WARN]     - Warning state
[ERROR]    - Critical error
[INFO]     - Informational
[DEBUG]    - Debug information
[PASS]     - Test passed
[SKIP]     - Test skipped
```

### Action Indicators
```
[START]    - Process starting
[STOP]     - Process stopping
[RUNNING]  - Process running
[PENDING]  - Process pending
[COMPLETE] - Process completed
[TIMEOUT]  - Process timed out
```

### Security Indicators
```
[SECURE]   - Security feature active
[BLOCK]    - Access blocked
[ALLOW]    - Access allowed
[AUDIT]    - Security audit event
[BREACH]   - Security breach detected
```

### Network Indicators
```
[WEB]      - Web network/DMZ
[INTERNAL] - Internal network
[BRIDGE]   - Bridge service
[ISOLATED] - Network isolated
[CONNECT]  - Connection established
[DISCONNECT] - Connection lost
```

## Implementation Guidelines

### 1. PowerShell Scripts
```powershell
# ✅ Good
Write-Host "[OK] Service is running" -ForegroundColor Green
Write-Host "[FAIL] Service failed to start" -ForegroundColor Red

# ❌ Bad
Write-Host "✅ Service is running" -ForegroundColor Green
Write-Host "❌ Service failed to start" -ForegroundColor Red
```

### 2. Python Code
```python
# ✅ Good
print("[INFO] Starting AI orchestrator...")
logger.info("[ERROR] Database connection failed")

# ❌ Bad
print("ℹ️ Starting AI orchestrator...")
logger.info("❌ Database connection failed")
```

### 3. Docker Compose Labels
```yaml
# ✅ Good
labels:
  - "status=healthy"
  - "security.level=secure"

# ❌ Bad (if using in descriptions)
labels:
  - "status=✅healthy"
  - "security.level=🔒secure"
```

### 4. Documentation
```markdown
<!-- ✅ Good -->
## [SUCCESS] Installation Complete
- [OK] Database connected
- [OK] Services running

<!-- ❌ Bad -->
## 🎉 Installation Complete
- ✅ Database connected
- ✅ Services running
```

## Tool Configuration

### 1. Git Hooks (Pre-commit)
Create `.git/hooks/pre-commit`:
```bash
#!/bin/bash
# Check for emoji usage in code files
if git diff --cached --name-only | grep -E '\.(py|ps1|yml|yaml|sh|js|ts)$' | xargs grep -l '[^\x00-\x7F]'; then
    echo "[ERROR] Emoji or Unicode characters detected in code files!"
    echo "Please use ASCII alternatives like [OK], [FAIL], [WARN], etc."
    exit 1
fi
```

### 2. VS Code Settings
Add to `.vscode/settings.json`:
```json
{
    "files.encoding": "utf8",
    "terminal.integrated.unicodeVersion": "6",
    "editor.renderControlCharacters": true,
    "problems.decorations.enabled": true,
    "emmet.showExpandedAbbreviation": "never"
}
```

### 3. Linting Rules
For Python (`.flake8`):
```ini
[flake8]
exclude = .git,__pycache__,docs/source/conf.py,old,build,dist
max-line-length = 88
extend-ignore = E203, W503
# Add custom rule to flag unicode characters in strings
```

### 4. Editor Configuration
Create `.editorconfig`:
```ini
root = true

[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

[*.{py,ps1,sh,yml,yaml}]
charset = utf-8
# Enforce ASCII-only in code files
```

## PowerShell Specific Guidelines

### String Interpolation
```powershell
# ✅ Good - Proper escaping
Write-Host "$testName Testing: $FromContainer -> $ToContainer`:$Port"

# ❌ Bad - Can cause parsing issues
Write-Host "$testName Testing: $FromContainer → ${ToContainer}:${Port}"
```

### Error Handling
```powershell
# ✅ Good
try {
    # code here
} catch {
    Write-Host "[ERROR] Operation failed: $($_.Exception.Message)" -ForegroundColor Red
}

# ❌ Bad
try {
    # code here
} catch {
    Write-Host "❌ Operation failed: $($_.Exception.Message)" -ForegroundColor Red
}
```

## Testing and Validation

### Automated Checks
1. **CI/CD Pipeline**: Add Unicode detection to build process
2. **Pre-commit Hooks**: Block commits with Unicode in code
3. **Linting**: Configure linters to flag Unicode usage
4. **Code Review**: Manual review checklist

### Test Script
Create `scripts/check-unicode.ps1`:
```powershell
#!/usr/bin/env pwsh
# Unicode Detection Script

$files = Get-ChildItem -Recurse -Include "*.py", "*.ps1", "*.yml", "*.yaml", "*.sh" |
    Where-Object { $_.FullName -notmatch "\.git|node_modules|__pycache__" }

$foundUnicode = $false

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    if ($content -match '[^\x00-\x7F]') {
        Write-Host "[WARN] Unicode characters found in: $($file.FullName)" -ForegroundColor Yellow
        $foundUnicode = $true
    }
}

if ($foundUnicode) {
    Write-Host "[FAIL] Unicode characters detected in code files" -ForegroundColor Red
    exit 1
} else {
    Write-Host "[OK] No Unicode characters found in code files" -ForegroundColor Green
    exit 0
}
```

## Team Guidelines

### Code Reviews
- **Mandatory Check**: Review for Unicode usage
- **Automated Tools**: Use linters and hooks
- **Documentation**: Update this guide as needed

### New Team Members
- **Onboarding**: Include this guide in setup process
- **Training**: Explain encoding issues and solutions
- **Tools**: Ensure proper editor configuration

### Emergency Fixes
If Unicode is accidentally introduced:
1. Identify affected files
2. Replace with ASCII alternatives
3. Test in multiple terminal environments
4. Update documentation if needed

## Exceptions

### When Unicode Might Be Acceptable
1. **User-facing content** (not code output)
2. **Documentation examples** (clearly marked)
3. **Test data** (when testing Unicode handling)
4. **Configuration files** for user customization

### How to Handle Exceptions
```python
# If Unicode is absolutely necessary, document it clearly
def display_user_content(content: str):
    """
    Display user-provided content which may contain Unicode.

    Note: This function handles user input and may display Unicode
    characters. For system output, use ASCII alternatives.
    """
    print(f"[USER] {content}")  # User content may have Unicode
```

## Migration Strategy

### Existing Code
1. **Audit**: Use detection script to find Unicode usage
2. **Prioritize**: Fix critical/frequently used files first
3. **Replace**: Use ASCII alternatives systematically
4. **Test**: Verify functionality after changes

### New Code
1. **Prevention**: Use hooks and linters
2. **Guidelines**: Follow this style guide
3. **Review**: Include Unicode check in PR template
4. **Automation**: Add to CI/CD pipeline

This approach ensures your codebase remains compatible across all environments while maintaining clear, professional output.
