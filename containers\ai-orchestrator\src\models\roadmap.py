# Project: AI Coding Agent
# Purpose: RoadmapItem model for hierarchical project roadmaps

"""
SQLAlchemy RoadmapItem model for the Architect Roadmap Generation System.

Provides hierarchical structure for project roadmaps with phases, steps, and tasks.
"""

from __future__ import annotations

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import (
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import backref, relationship
from sqlalchemy.sql import func
from src.models.custom_types import JsonVariant
from src.models.database import Base


class Roadmap(Base):
    """Database model representing a versioned roadmap.

    Attributes:
        id: Primary key UUID.
        project_id: FK to projects.id indicating the project this roadmap belongs to.
        title: Title of the roadmap.
        content: JSONB content of the roadmap.
        owner_id: UUID of the user who owns this roadmap.
        version: Version number of this roadmap.
        parent_id: FK to roadmaps.id for versioning (self-referencing).
        status: Status of this roadmap version ('draft', 'active', 'archived').
        created_at: Creation timestamp.
        updated_at: Last update timestamp.
    """

    __tablename__ = "roadmaps"

    id: UUID = Column(PGUUID(as_uuid=True), primary_key=True, default=func.uuid_generate_v4())
    project_id: int = Column(
        Integer, ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True
    )
    title: str = Column(Text, nullable=False)
    content: dict = Column(JSONB, nullable=False, default=dict)
    owner_id: UUID = Column(
        PGUUID(as_uuid=True),
        ForeignKey("user_profiles.supabase_user_id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    version: int = Column(Integer, nullable=False, default=1, index=True)
    parent_id: Optional[UUID] = Column(
        PGUUID(as_uuid=True), ForeignKey("roadmaps.id"), nullable=True, index=True
    )
    status: str = Column(String(50), nullable=False, default="draft", index=True)
    created_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    project = relationship("Project", backref="roadmaps")
    parent = relationship("Roadmap", remote_side=[id], backref="versions")
    roadmap_summaries = relationship(
        "RoadmapSummary", back_populates="roadmap", cascade="all, delete-orphan"
    )
    roadmap_source_references = relationship(
        "RoadmapSourceReference", back_populates="roadmap", cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return (
            f"<Roadmap(id={self.id}, "
            f"project_id={self.project_id}, "
            f"title={self.title[:50]}..., "
            f"version={self.version}, "
            f"status={self.status})>"
        )


class RoadmapSummary(Base):
    """Database model for AI-generated roadmap summaries with embeddings."""

    __tablename__ = "roadmap_summaries"

    id: UUID = Column(PGUUID(as_uuid=True), primary_key=True, default=func.uuid_generate_v4())
    roadmap_id: UUID = Column(
        PGUUID(as_uuid=True),
        ForeignKey("roadmaps.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    phase_title: str = Column(Text, nullable=False)
    summary_text: str = Column(Text, nullable=False)
    embedding: Optional[List[float]] = Column(JsonVariant, nullable=True)  # For vector embeddings
    owner_id: UUID = Column(
        PGUUID(as_uuid=True),
        ForeignKey("user_profiles.supabase_user_id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    created_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )

    # Relationships
    roadmap = relationship("Roadmap", back_populates="roadmap_summaries")

    def __repr__(self) -> str:
        return (
            f"<RoadmapSummary(id={self.id}, "
            f"roadmap_id={self.roadmap_id}, "
            f"phase_title={self.phase_title[:30]}...)>"
        )


class RoadmapSourceReference(Base):
    """Database model for tracking source documents used in roadmap generation."""

    __tablename__ = "roadmap_source_references"

    id: UUID = Column(PGUUID(as_uuid=True), primary_key=True, default=func.uuid_generate_v4())
    roadmap_id: UUID = Column(
        PGUUID(as_uuid=True),
        ForeignKey("roadmaps.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    source_document_id: UUID = Column(PGUUID(as_uuid=True), nullable=False, index=True)
    excerpt: str = Column(Text, nullable=False)
    retrieval_score: Optional[float] = Column(JsonVariant, nullable=True)
    owner_id: UUID = Column(
        PGUUID(as_uuid=True),
        ForeignKey("user_profiles.supabase_user_id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    created_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )

    # Relationships
    roadmap = relationship("Roadmap", back_populates="roadmap_source_references")

    def __repr__(self) -> str:
        return (
            f"<RoadmapSourceReference(id={self.id}, "
            f"roadmap_id={self.roadmap_id}, "
            f"source_document_id={self.source_document_id})>"
        )


class RoadmapItem(Base):
    """Database model representing an item in a hierarchical project roadmap.

    Attributes:
        id: Primary key.
        project_id: FK to projects.id indicating the project this roadmap belongs to.
        parent_id: FK to roadmap_items.id for hierarchical relationships (self-referencing).
        level: Hierarchy level (1=Phase, 2=Step, 3=Task).
        sequence_order: Order within the same level and parent.
        title: Title of the roadmap item.
        description: Detailed description of the roadmap item.
        item_type: Type of item ('phase', 'step', 'task').
        status: Current status ('pending', 'in_progress', 'completed', 'blocked').
        agent_role: For task-level items, the agent role responsible ('backend', 'frontend', 'shell').
        estimated_effort: Estimated effort level ('small', 'medium', 'large').
        dependencies: List of roadmap item IDs this item depends on.
        created_at: Creation timestamp.
    """

    __tablename__ = "roadmap_items"

    id: int = Column(Integer, primary_key=True, index=True, autoincrement=True)

    project_id: int = Column(
        Integer,
        ForeignKey("projects.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    # Hierarchical structure
    parent_id: Optional[int] = Column(
        Integer,
        ForeignKey("roadmap_items.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
    )
    level: int = Column(Integer, nullable=False, index=True)  # 1=Phase, 2=Step, 3=Task
    sequence_order: int = Column(Integer, nullable=False)

    # Content
    title: str = Column(String(500), nullable=False)
    description: Optional[str] = Column(Text, nullable=True)
    item_type: str = Column(String(50), nullable=False, index=True)  # 'phase', 'step', 'task'

    # Status and assignment
    status: str = Column(String(50), nullable=False, default="pending", index=True)
    agent_role: Optional[str] = Column(String(50), nullable=True, index=True)

    # Estimation and dependencies
    estimated_effort: Optional[str] = Column(String(50), nullable=True)
    dependencies: List[int] = Column(JsonVariant, default=list)

    # Progress tracking
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    assigned_to = Column(String(255), nullable=True)  # User assignment

    # Priority levels
    priority = Column(String(20), default="medium")  # low, medium, high, critical

    # Timestamps
    created_at: datetime = Column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )

    # Relationships
    project = relationship("Project", backref="roadmap_items")

    # Self-referencing relationship for hierarchy
    children = relationship(
        "RoadmapItem",
        backref=backref("parent", remote_side=[id]),
        cascade="all, delete-orphan",
        lazy="selectin",
    )

    def __repr__(self) -> str:
        return (
            f"<RoadmapItem(id={self.id}, "
            f"project_id={self.project_id}, "
            f"level={self.level}, "
            f"item_type={self.item_type}, "
            f"title={self.title[:50]}...)>"
        )

    @property
    def is_phase(self) -> bool:
        """Check if this item is a phase."""
        return self.level == 1

    @property
    def is_step(self) -> bool:
        """Check if this item is a step."""
        return self.level == 2

    @property
    def is_task(self) -> bool:
        """Check if this item is a task."""
        return self.level == 3

    def get_full_path(self) -> str:
        """Get the full hierarchical path of this item."""
        if self.parent:
            parent_path = self.parent.get_full_path()
            return f"{parent_path} > {self.title}"
        return self.title
