import { useCallback, useEffect, useRef, useState } from "react";
import { EmbeddingProgressUpdate } from "../types/project";

interface UseEmbeddingStatusReturn {
  isConnected: boolean;
  connectionStatus: "connecting" | "connected" | "disconnected" | "error";
  lastUpdate?: EmbeddingProgressUpdate;
}

export function useEmbeddingStatus(
  userId: string,
  onProgressUpdate?: (update: EmbeddingProgressUpdate) => void,
): UseEmbeddingStatusReturn {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<
    "connecting" | "connected" | "disconnected" | "error"
  >("disconnected");
  const [lastUpdate, setLastUpdate] = useState<
    EmbeddingProgressUpdate | undefined
  >();
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setConnectionStatus("connecting");

    const wsUrl = `${
      process.env.NEXT_PUBLIC_WEBSOCKET_URL || "ws://localhost:8000"
    }/ws/chat/${userId}`;

    try {
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        setIsConnected(true);
        setConnectionStatus("connected");
        reconnectAttempts.current = 0;
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.event === "embedding_progress") {
            const update: EmbeddingProgressUpdate = {
              event: "embedding_progress",
              projectId: data.projectId,
              status: data.status,
              progress: data.progress,
              message: data.message,
              timestamp: data.timestamp,
            };

            setLastUpdate(update);

            if (onProgressUpdate) {
              onProgressUpdate(update);
            }
          }
        } catch (error) {
          // use logger to avoid console usage in production
          // eslint-disable-next-line @typescript-eslint/no-var-requires
          const { logger } = require("@/lib/logger");
          logger.error("Failed to parse WebSocket message:", error);
        }
      };

      ws.onclose = () => {
        setIsConnected(false);
        setConnectionStatus("disconnected");
        wsRef.current = null;

        // Attempt to reconnect if not at max attempts
        if (reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, 1000 * reconnectAttempts.current); // Exponential backoff
        }
      };

      ws.onerror = (error) => {
        const { logger } = require("@/lib/logger");
        logger.error("WebSocket error:", error);
        setConnectionStatus("error");
      };
    } catch (error) {
      const { logger } = require("@/lib/logger");
      logger.error("Failed to create WebSocket connection:", error);
      setConnectionStatus("error");
    }
  }, [userId, onProgressUpdate]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionStatus("disconnected");
  }, []);

  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    isConnected,
    connectionStatus,
    lastUpdate,
  };
}
