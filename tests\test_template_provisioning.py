"""
Test suite for the template-based provisioning system.

This test validates the complete provisioning workflow:
1. Template creation and copying
2. Placeholder replacement
3. Project directory structure
4. Integration with ArchitectAgent
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import MagicMock, patch

# Test imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'containers', 'ai-orchestrator'))

from src.repository.project_repository import ProjectRepository
from src.agents.architect_agent import ArchitectAgent


class TestTemplateProvisioning:
    """Test the template-based provisioning system."""

    @pytest.fixture
    def temp_workspace(self):
        """Create a temporary workspace for testing."""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = MagicMock()
        session.add = MagicMock()
        session.commit = MagicMock()
        session.refresh = MagicMock()
        session.execute = MagicMock()
        session.rollback = MagicMock()
        return session

    @pytest.fixture
    def mock_user(self):
        """Mock user object."""
        user = MagicMock()
        user.id = "test-user-123"
        return user

    @pytest.fixture
    def project_repository(self, temp_workspace):
        """Create ProjectRepository with mocked workspace."""
        repo = ProjectRepository()

        # Mock the get_user_workspace_path method
        async def mock_get_workspace(user_id):
            return temp_workspace / "users" / user_id

        repo.get_user_workspace_path = mock_get_workspace
        return repo

    @pytest.mark.asyncio
    async def test_template_directory_exists(self):
        """Test that template directory exists with required files."""
        template_dir = Path(__file__).parent.parent / "templates" / "webapp"

        assert template_dir.exists(), "Template directory should exist"

        required_files = [
            "Dockerfile.template",
            "docker-compose.yml.template",
            ".env.example.template",
            "src/main.py.template",
            "requirements.txt.template",
            "init-db.sql.template",
            "README.md.template"
        ]

        for file_name in required_files:
            file_path = template_dir / file_name
            assert file_path.exists(), f"Template file {file_name} should exist"
            assert file_path.is_file(), f"{file_name} should be a file"

    @pytest.mark.asyncio
    async def test_template_placeholder_replacement(self):
        """Test that placeholders are correctly replaced in template files."""
        template_dir = Path(__file__).parent.parent / "templates" / "webapp"
        dockerfile_template = template_dir / "Dockerfile.template"

        if dockerfile_template.exists():
            content = dockerfile_template.read_text()
            assert "__PROJECT_NAME__" in content, "Template should contain placeholder"

    @pytest.mark.asyncio
    async def test_provision_project_from_template(self, project_repository, temp_workspace):
        """Test the template provisioning functionality."""
        project_name = "test-project"
        project_path = temp_workspace / "users" / "test-user-123" / project_name

        # Create the user directory
        project_path.parent.mkdir(parents=True, exist_ok=True)
        project_path.mkdir(exist_ok=True)

        # Test the provisioning method
        await project_repository._provision_project_from_template(project_path, project_name)

        # Verify files were created
        expected_files = [
            "Dockerfile",
            "docker-compose.yml",
            ".env.example",
            "src/main.py",
            "requirements.txt",
            "init-db.sql",
            "README.md"
        ]

        for file_name in expected_files:
            file_path = project_path / file_name
            assert file_path.exists(), f"File {file_name} should be created"

            # Verify placeholder replacement
            if file_path.suffix in ['.py', '.yml', '.yaml', '.md', '.txt', '.sql', '']:
                content = file_path.read_text()
                assert "__PROJECT_NAME__" not in content, f"Placeholder should be replaced in {file_name}"
                assert project_name in content, f"Project name should appear in {file_name}"

    @pytest.mark.asyncio
    async def test_create_project_with_template(self, project_repository, mock_db_session, mock_user):
        """Test the enhanced create_project method."""
        project_name = "my-awesome-project"

        with patch('src.models.project.Project') as MockProject, \
             patch('src.models.project.user_project_association') as mock_association:

            # Mock the Project model
            mock_project = MagicMock()
            mock_project.id = "project-123"
            MockProject.return_value = mock_project

            # Test project creation
            result = await project_repository.create_project(
                db=mock_db_session,
                user=mock_user,
                project_name=project_name,
                description="Test project"
            )

            # Verify database operations
            mock_db_session.add.assert_called_once()
            assert mock_db_session.commit.call_count >= 2  # Once for project, once for association

            # Verify project object
            assert result == mock_project

    @pytest.mark.asyncio
    async def test_architect_agent_provisioning_detection(self):
        """Test ArchitectAgent's new project detection."""
        architect = ArchitectAgent()

        # Test roadmap with new project indicators
        new_project_roadmap = MagicMock()
        new_project_roadmap.title = "Create new project for e-commerce"
        new_project_roadmap.description = "Setup a new project"
        new_project_roadmap.metadata = {"is_new_project": True}

        is_new = await architect._is_new_project_roadmap(new_project_roadmap)
        assert is_new, "Should detect new project from metadata"

        # Test roadmap with title indicators
        title_roadmap = MagicMock()
        title_roadmap.title = "Initialize project for blog platform"
        title_roadmap.description = None
        title_roadmap.metadata = None

        is_new_title = await architect._is_new_project_roadmap(title_roadmap)
        assert is_new_title, "Should detect new project from title"

        # Test existing project roadmap
        existing_roadmap = MagicMock()
        existing_roadmap.title = "Add user authentication feature"
        existing_roadmap.description = "Enhance existing app"
        existing_roadmap.metadata = None

        is_existing = await architect._is_new_project_roadmap(existing_roadmap)
        assert not is_existing, "Should not detect existing project as new"

    @pytest.mark.asyncio
    async def test_project_name_extraction(self):
        """Test project name extraction from roadmap."""
        architect = ArchitectAgent()

        # Test with metadata
        roadmap_with_metadata = MagicMock()
        roadmap_with_metadata.metadata = {"project_name": "my-custom-project"}
        roadmap_with_metadata.title = "Create new project"

        name = architect._extract_project_name_from_roadmap(roadmap_with_metadata)
        assert name == "my-custom-project"

        # Test with title extraction
        roadmap_with_title = MagicMock()
        roadmap_with_title.metadata = None
        roadmap_with_title.title = "Create new E-Commerce Platform!"

        name_from_title = architect._extract_project_name_from_roadmap(roadmap_with_title)
        assert name_from_title == "e-commerce-platform"

    def test_container_health_parsing(self):
        """Test container health status parsing."""
        architect = ArchitectAgent()

        # Test healthy containers JSON output
        healthy_json = '''[
            {"Name": "test-app", "State": "running", "Health": "healthy"},
            {"Name": "test-db", "State": "running", "Health": ""}
        ]'''

        is_healthy = architect._parse_container_health_status(healthy_json)
        assert is_healthy, "Should parse healthy containers correctly"

        # Test unhealthy containers
        unhealthy_json = '''[
            {"Name": "test-app", "State": "running", "Health": "unhealthy"},
            {"Name": "test-db", "State": "running", "Health": ""}
        ]'''

        is_unhealthy = architect._parse_container_health_status(unhealthy_json)
        assert not is_unhealthy, "Should detect unhealthy containers"

        # Test text format
        healthy_text = """
        NAME        STATE    STATUS
        test-app    Up       healthy
        test-db     Up
        """

        is_healthy_text = architect._parse_container_health_status(healthy_text)
        assert is_healthy_text, "Should parse healthy text format"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
