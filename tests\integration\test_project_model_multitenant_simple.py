"""
Tests for Multi-Tenant Project Model Updates.

This module tests the updated Project model with multi-tenant database
schema fields (db_schema_name and db_connection_url).

Tests cover:
- Model field validation
- Schema name uniqueness
- Connection URL storage
- Integration with existing functionality

Author: AI Coding Agent
Version: 1.0.0
"""


from src.models.project import Project


class TestProjectModelMultiTenant:
    """Test suite for multi-tenant Project model functionality."""

    def test_project_model_has_schema_fields(self):
        """Test that Project model includes the new multi-tenant fields."""
        # Check that the model has the required fields
        assert hasattr(Project, 'db_schema_name')
        assert hasattr(Project, 'db_connection_url')

        # Check field properties
        schema_field = Project.__table__.columns['db_schema_name']
        url_field = Project.__table__.columns['db_connection_url']

        # Verify field types
        assert str(schema_field.type) == 'VARCHAR(255)'
        assert str(url_field.type) == 'VARCHAR(500)'

        # Verify constraints
        assert schema_field.unique is True  # Schema names must be unique
        assert schema_field.nullable is True  # Allow null for backward compatibility
        assert url_field.nullable is True  # Allow null for backward compatibility

    def test_project_model_instantiation(self):
        """Test that Project model can be instantiated with schema fields."""
        # Create a project instance with schema information
        project = Project(
            name="Test Project",
            description="A test project for multi-tenant testing",
            db_schema_name="project_123",
            db_connection_url="postgresql://user:pass@localhost:5432/db?options=-csearch_path%3Dproject_123"
        )

        # Verify the project was created with correct schema info
        assert project.name == "Test Project"
        assert project.description == "A test project for multi-tenant testing"
        assert project.db_schema_name == "project_123"
        assert project.db_connection_url == "postgresql://user:pass@localhost:5432/db?options=-csearch_path%3Dproject_123"

    def test_project_model_without_schema_info(self):
        """Test creating a project without schema information (backward compatibility)."""
        # Create a project without schema details (should be allowed)
        project = Project(
            name="Legacy Project",
            description="A project created without schema info"
        )

        # Verify the project was created successfully
        assert project.name == "Legacy Project"
        assert project.db_schema_name is None
        assert project.db_connection_url is None

    def test_connection_url_encoding(self):
        """Test that connection URLs properly encode the search_path parameter."""
        test_url = "postgresql://user:pass@localhost:5432/testdb?options=-csearch_path%3Dproject_123"

        project = Project(
            name="URL Test",
            db_schema_name="project_123",
            db_connection_url=test_url
        )

        # Verify the encoding is correct
        assert "%3D" in project.db_connection_url  # URL encoded equals sign
        assert "search_path" in project.db_connection_url
        assert "project_123" in project.db_connection_url

    def test_schema_name_format(self):
        """Test that schema names follow the expected format."""
        test_cases = [
            "project_1",
            "project_123",
            "project_999999",
            "project_0"
        ]

        for schema_name in test_cases:
            project = Project(
                name=f"Test {schema_name}",
                db_schema_name=schema_name
            )

            assert project.db_schema_name == schema_name
            assert schema_name.startswith("project_")
            assert schema_name[len("project_"):].isdigit()  # Should be followed by digits

    def test_long_connection_url(self):
        """Test that connection URLs can handle long URLs."""
        long_url = "**************************************************************************************/very_long_database_name?options=-csearch_path%3Dproject_123&sslmode=require&connect_timeout=30&application_name=my_app"

        project = Project(
            name="Long URL Test",
            db_schema_name="project_123",
            db_connection_url=long_url
        )

        # Verify the long URL was stored correctly
        assert project.db_connection_url == long_url
        assert len(project.db_connection_url) > 255  # Should handle URLs longer than typical VARCHAR limits

    def test_schema_field_uniqueness_constraint(self):
        """Test that the schema name field has a uniqueness constraint."""
        # This test verifies the database constraint is properly defined
        # The actual constraint enforcement would be tested in integration tests
        schema_field = Project.__table__.columns['db_schema_name']
        assert schema_field.unique is True

    def test_url_field_length_constraint(self):
        """Test that the connection URL field has appropriate length."""
        # This test verifies the field length is sufficient for complex URLs
        url_field = Project.__table__.columns['db_connection_url']
        # VARCHAR(500) should be sufficient for most database URLs
        assert str(url_field.type) == 'VARCHAR(500)'
