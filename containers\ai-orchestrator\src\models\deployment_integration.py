"""
Deployment Integration Model for storing encrypted deployment credentials.

This module defines the SQLAlchemy model for securely storing deployment
integration credentials (like Vercel API keys) with encryption at rest.
"""

from typing import List
from enum import Enum as PyEnum
from sqlalchemy import Column, String, DateTime, ForeignKey, Enum, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from src.models.database import Base
from src.models.custom_types import UuidVariant

import uuid


class DeploymentProvider(PyEnum):
    """Enumeration of supported deployment providers."""
    VERCEL = "vercel"
    NETLIFY = "netlify"
    RAILWAY = "railway"
    # Future providers can be added here:
    # FLY_IO = "fly_io"
    # RENDER = "render"


class DeploymentIntegration(Base):
    """
    Model for storing encrypted deployment integration credentials.

    This model securely stores API keys and credentials for external deployment
    platforms like Vercel, with encryption at rest and proper user association.

    Attributes:
        id: Primary key, UUID
        user_id: Foreign key to users table
        provider: Deployment provider (e.g., VERCEL)
        encrypted_api_key: Encrypted API key (never stored in plaintext)
        team_id: Optional team/organization identifier
        created_at: Timestamp when integration was created
        updated_at: Timestamp when integration was last modified
        user: Relationship to User model
    """

    __tablename__ = "deployment_integrations"

    # Primary key - UUID for better security and global uniqueness
    id = Column(
        UuidVariant,
        primary_key=True,
        default=lambda: str(uuid.uuid4()),
        index=True,
        comment="Unique deployment integration identifier"
    )

    # User association
    user_id = Column(
        UuidVariant,
        ForeignKey("user_profiles.supabase_user_id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="UUID foreign key to user_profiles.supabase_user_id"
    )

    # Deployment provider
    provider = Column(
        Enum(DeploymentProvider),
        nullable=False,
        index=True,
        comment="Deployment provider (e.g., VERCEL)"
    )

    # Encrypted API key - never stored in plaintext
    encrypted_api_key = Column(
        Text,
        nullable=False,
        comment="Encrypted API key using application encryption"
    )

    # Optional team/organization identifier
    team_id = Column(
        String(255),
        nullable=True,
        comment="Team or organization identifier (provider-specific)"
    )

    # Audit timestamps
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Integration creation timestamp"
    )

    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Last modification timestamp"
    )

    # Relationships
    user = relationship("UserProfile", back_populates="deployment_integrations")

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"<DeploymentIntegration(id={self.id}, provider='{self.provider}', user_id={self.user_id})>"

    def __str__(self) -> str:
        """Human-readable string representation."""
        return f"{self.provider.value} integration for user {self.user_id}"

    @property
    def provider_display_name(self) -> str:
        """Get human-readable provider name."""
        return self.provider.value.upper()

    @classmethod
    def get_provider_choices(cls) -> List[dict]:
        """Get available provider choices for API responses."""
        return [
            {"value": provider.value, "display_name": provider.value.upper()}
            for provider in DeploymentProvider
        ]


# Add relationship to User model (back-population)
# This should be added to the User model in user.py:
# deployment_integrations = relationship("DeploymentIntegration", back_populates="user", cascade="all, delete-orphan")
