import pytest
from unittest.mock import AsyncMock
from src.utils.llm_service import UniversalLLMService

@pytest.fixture
def llm_service():
    return UniversalLLMService()

@pytest.mark.asyncio
async def test_generate_ollama_without_temperature(llm_service, mocker):
    """
    Test that generate_ollama does not include 'temperature' in the payload
    when the temperature argument is not provided.
    """
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.json.return_value = {"message": {"content": "test response"}}

    mock_cm = AsyncMock()
    mock_cm.__aenter__.return_value = mock_response

    mock_post = mocker.patch("aiohttp.ClientSession.post", return_value=mock_cm)

    await llm_service.generate_ollama(
        model="test_model",
        prompt="test_prompt"
    )

    mock_post.assert_called_once()
    called_json = mock_post.call_args.kwargs['json']
    assert "temperature" not in called_json["options"]

@pytest.mark.asyncio
async def test_generate_ollama_with_temperature(llm_service, mocker):
    """
    Test that generate_ollama includes 'temperature' in the payload
    when the temperature argument is provided.
    """
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.json.return_value = {"message": {"content": "test response"}}

    mock_cm = AsyncMock()
    mock_cm.__aenter__.return_value = mock_response

    mock_post = mocker.patch("aiohttp.ClientSession.post", return_value=mock_cm)

    await llm_service.generate_ollama(
        model="test_model",
        prompt="test_prompt",
        temperature=0.5
    )

    mock_post.assert_called_once()
    called_json = mock_post.call_args.kwargs['json']
    assert "temperature" in called_json["options"]
    assert called_json["options"]["temperature"] == 0.5
