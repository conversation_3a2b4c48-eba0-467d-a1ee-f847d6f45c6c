#!/usr/bin/env python3
"""
Enhanced Template Provisioning Integration Test

Tests all four enhancement areas:
1. Template extensibility with multiple template types
2. Environment isolation with port management
3. Resource management with limits and monitoring
4. Rollback capability with failure scenarios

Usage:
    python scripts/test-enhanced-provisioning.py
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add the source directory to the path for imports
sys.path.append(str(Path(__file__).parent.parent / "containers" / "ai-orchestrator" / "src"))

try:
    from src.services.rollback_manager import RollbackStage
except ImportError:
    # Fallback for when running outside container
    RollbackStage = None

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the AI orchestrator to Python path and set environment
orchestrator_path = Path(__file__).parent.parent / "containers" / "ai-orchestrator"
sys.path.append(str(orchestrator_path))

# Set environment variables for testing
import os
os.environ.setdefault("DATABASE_URL", "postgresql://postgres:testpassword@localhost:5432/ai_coding_agent")
os.environ.setdefault("REDIS_URL", "redis://localhost:6379/0")
os.environ.setdefault("JWT_SECRET", "test-jwt-secret-key-for-development-only-change-in-production")
os.environ.setdefault("ENVIRONMENT", "testing")
os.environ.setdefault("LOG_LEVEL", "INFO")

# Change to orchestrator directory so .env file can be found
original_cwd = os.getcwd()
os.chdir(orchestrator_path)


class EnhancedProvisioningTester:
    """Tests the enhanced template provisioning system."""

    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_results: List[Dict[str, Any]] = []

    def log_test_result(self, test_name: str, success: bool, message: str, details: Optional[Dict[str, Any]] = None):
        """Log test result."""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "details": details or {}
        }
        self.test_results.append(result)

        level = logging.INFO if success else logging.ERROR
        status = " PASS" if success else " FAIL"
        logger.log(level, f"{test_name}: {status} - {message}")

    async def test_template_extensibility(self) -> bool:
        """Test 1: Template extensibility with multiple template types."""
        logger.info(" Testing Template Extensibility...")

        try:
            from src.services.template_registry import get_template_registry, TemplateType

            registry = get_template_registry()

            # Test 1.1: List available templates
            templates = registry.list_available_templates()
            if len(templates) < 3:
                self.log_test_result(
                    "template_registry_populated",
                    False,
                    f"Expected at least 3 templates, found {len(templates)}"
                )
                return False

            self.log_test_result(
                "template_registry_populated",
                True,
                f"Found {len(templates)} template types",
                {"template_count": len(templates)}
            )

            # Test 1.2: Template metadata validation
            webapp_metadata = registry.get_template_metadata(TemplateType.WEBAPP)
            if not webapp_metadata or not webapp_metadata.required_placeholders:
                self.log_test_result(
                    "template_metadata_validation",
                    False,
                    "WebApp template metadata missing or invalid"
                )
                return False

            self.log_test_result(
                "template_metadata_validation",
                True,
                f"WebApp template has {len(webapp_metadata.required_placeholders)} placeholders"
            )

            # Test 1.3: Intelligent template selection
            react_selection = registry.get_template_for_project_type("React frontend application")
            if react_selection != TemplateType.REACT_FRONTEND:
                self.log_test_result(
                    "intelligent_template_selection",
                    False,
                    f"Expected REACT_FRONTEND, got {react_selection}"
                )
                return False

            self.log_test_result(
                "intelligent_template_selection",
                True,
                "Correctly selected React template for frontend project"
            )

            return True

        except Exception as e:
            self.log_test_result(
                "template_extensibility",
                False,
                f"Template extensibility test failed: {e}"
            )
            return False

    async def test_environment_isolation(self) -> bool:
        """Test 2: Environment isolation with port management."""
        logger.info(" Testing Environment Isolation...")

        try:
            from src.services.port_manager import get_port_manager

            port_manager = get_port_manager()

            # Test 2.1: Port allocation
            port1 = await port_manager.allocate_port("user1", "project1", "webapp")
            port2 = await port_manager.allocate_port("user1", "project2", "webapp")

            if not port1 or not port2 or port1 == port2:
                self.log_test_result(
                    "port_allocation_isolation",
                    False,
                    f"Port allocation failed: port1={port1}, port2={port2}"
                )
                return False

            self.log_test_result(
                "port_allocation_isolation",
                True,
                f"Allocated unique ports: {port1}, {port2}"
            )

            # Test 2.2: Port persistence
            retrieved_port = await port_manager.get_project_port("user1", "project1")
            if retrieved_port != port1:
                self.log_test_result(
                    "port_persistence",
                    False,
                    f"Port persistence failed: expected {port1}, got {retrieved_port}"
                )
                return False

            self.log_test_result(
                "port_persistence",
                True,
                f"Port persistence working: {retrieved_port}"
            )

            # Test 2.3: Port release
            released = await port_manager.release_port("user1", "project1")
            if not released:
                self.log_test_result(
                    "port_release",
                    False,
                    "Failed to release allocated port"
                )
                return False

            self.log_test_result(
                "port_release",
                True,
                "Successfully released port"
            )

            # Cleanup
            await port_manager.release_port("user1", "project2")

            return True

        except Exception as e:
            self.log_test_result(
                "environment_isolation",
                False,
                f"Environment isolation test failed: {e}"
            )
            return False

    async def test_resource_management(self) -> bool:
        """Test 3: Resource management with limits and monitoring."""
        logger.info(" Testing Resource Management...")

        try:
            from src.services.resource_manager import get_resource_manager

            resource_manager = get_resource_manager()

            # Test 3.1: Resource limits by template type
            webapp_limits = resource_manager.get_default_resource_limits("webapp")
            microservice_limits = resource_manager.get_default_resource_limits("microservice")

            if webapp_limits.memory_limit == microservice_limits.memory_limit:
                self.log_test_result(
                    "template_specific_limits",
                    False,
                    "Template-specific resource limits not working"
                )
                return False

            self.log_test_result(
                "template_specific_limits",
                True,
                f"WebApp: {webapp_limits.memory_limit}, Microservice: {microservice_limits.memory_limit}"
            )

            # Test 3.2: Docker Compose resource generation
            compose_resources = resource_manager.generate_docker_compose_resources(webapp_limits)
            if "deploy" not in compose_resources or "resources" not in compose_resources["deploy"]:
                self.log_test_result(
                    "compose_resource_generation",
                    False,
                    "Docker Compose resource generation failed"
                )
                return False

            self.log_test_result(
                "compose_resource_generation",
                True,
                "Docker Compose resources generated successfully"
            )

            # Test 3.3: System resource stats
            stats = resource_manager.get_system_resource_stats()
            required_keys = ["cpu_percent", "memory", "disk", "active_projects"]
            if not all(key in stats for key in required_keys):
                self.log_test_result(
                    "system_resource_stats",
                    False,
                    f"Missing system stats keys: {set(required_keys) - set(stats.keys())}"
                )
                return False

            self.log_test_result(
                "system_resource_stats",
                True,
                f"System stats: CPU {stats['cpu_percent']}%, Memory {stats['memory']['percent']}%"
            )

            return True

        except Exception as e:
            self.log_test_result(
                "resource_management",
                False,
                f"Resource management test failed: {e}"
            )
            return False

    async def test_rollback_capability(self) -> bool:
        """Test 4: Rollback capability with failure scenarios."""
        logger.info(" Testing Rollback Capability...")

        try:
            from src.services.rollback_manager import get_rollback_manager

            rollback_manager = get_rollback_manager()

            # Test 4.1: Checkpoint creation
            checkpoint_id = await rollback_manager.create_checkpoint(
                "test-project",
                "test-user",
                RollbackStage.DIRECTORY_CREATION,
                {"project_path": "/tmp/test-project"}
            )

            if not checkpoint_id or checkpoint_id not in rollback_manager.checkpoints:
                self.log_test_result(
                    "checkpoint_creation",
                    False,
                    "Failed to create rollback checkpoint"
                )
                return False

            self.log_test_result(
                "checkpoint_creation",
                True,
                f"Created checkpoint: {checkpoint_id}"
            )

            # Test 4.2: Rollback actions generation
            checkpoint = rollback_manager.checkpoints[checkpoint_id]
            if not checkpoint.rollback_actions:
                self.log_test_result(
                    "rollback_actions_generation",
                    False,
                    "No rollback actions generated"
                )
                return False

            self.log_test_result(
                "rollback_actions_generation",
                True,
                f"Generated {len(checkpoint.rollback_actions)} rollback actions"
            )

            # Test 4.3: Checkpoint status management
            await rollback_manager.commit_checkpoint(checkpoint_id)
            if rollback_manager.checkpoints[checkpoint_id].status != "committed":
                self.log_test_result(
                    "checkpoint_status_management",
                    False,
                    "Checkpoint status not updated correctly"
                )
                return False

            self.log_test_result(
                "checkpoint_status_management",
                True,
                "Checkpoint status management working"
            )

            return True

        except Exception as e:
            self.log_test_result(
                "rollback_capability",
                False,
                f"Rollback capability test failed: {e}"
            )
            return False

    async def test_integration_workflow(self) -> bool:
        """Test 5: Complete integration workflow."""
        logger.info(" Testing Integration Workflow...")

        try:
            # This would test the complete enhanced ProjectRepository workflow
            # For now, we'll test the service integrations

            from src.services.template_registry import get_template_registry
            from src.services.port_manager import get_port_manager
            from src.services.resource_manager import get_resource_manager
            from src.services.rollback_manager import get_rollback_manager

            # Test service availability
            services = {
                "template_registry": get_template_registry(),
                "port_manager": get_port_manager(),
                "resource_manager": get_resource_manager(),
                "rollback_manager": get_rollback_manager()
            }

            for service_name, service in services.items():
                if not service:
                    self.log_test_result(
                        "service_integration",
                        False,
                        f"Service {service_name} not available"
                    )
                    return False

            self.log_test_result(
                "service_integration",
                True,
                f"All {len(services)} services integrated successfully"
            )

            # Test workflow simulation
            # 1. Select template
            registry = services["template_registry"]
            template_type = registry.get_template_for_project_type("web application")

            # 2. Allocate port
            port_manager = services["port_manager"]
            port = await port_manager.allocate_port("test-user", "test-workflow-project")

            # 3. Get resource limits
            resource_manager = services["resource_manager"]
            limits = resource_manager.get_default_resource_limits(template_type.value)
            if not limits:
                self.log_test_result(
                    "resource_limits",
                    False,
                    "Failed to get resource limits"
                )
                return False

            # 4. Create checkpoint
            rollback_manager = services["rollback_manager"]
            checkpoint = await rollback_manager.create_checkpoint(
                "test-workflow-project",
                "test-user",
                RollbackStage.DIRECTORY_CREATION,
                {"project_path": "/tmp/test", "port": port}
            )

            # Cleanup
            await port_manager.release_port("test-user", "test-workflow-project")
            await rollback_manager.commit_checkpoint(checkpoint)

            self.log_test_result(
                "integration_workflow",
                True,
                "Complete integration workflow successful"
            )

            return True

        except Exception as e:
            self.log_test_result(
                "integration_workflow",
                False,
                f"Integration workflow test failed: {e}"
            )
            return False

    def generate_test_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report."""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests

        return {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": f"{(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%"
            },
            "results": self.test_results,
            "overall_status": "PASS" if failed_tests == 0 else "FAIL"
        }

    async def run_all_tests(self) -> bool:
        """Run all enhancement tests."""
        logger.info(" Starting Enhanced Template Provisioning Tests")
        logger.info("=" * 60)

        tests = [
            self.test_template_extensibility,
            self.test_environment_isolation,
            self.test_resource_management,
            self.test_rollback_capability,
            self.test_integration_workflow
        ]

        all_passed = True
        for test in tests:
            try:
                result = await test()
                if not result:
                    all_passed = False
            except Exception as e:
                logger.error(f"Test failed with exception: {e}")
                all_passed = False

        # Generate and display report
        report = self.generate_test_report()

        logger.info("=" * 60)
        logger.info(" ENHANCED PROVISIONING TEST REPORT")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {report['summary']['total_tests']}")
        logger.info(f"Passed: {report['summary']['passed']}")
        logger.info(f"Failed: {report['summary']['failed']}")
        logger.info(f"Success Rate: {report['summary']['success_rate']}")
        logger.info(f"Overall Status: {report['overall_status']}")

        if report['summary']['failed'] > 0:
            logger.info("\n FAILED TESTS:")
            for result in report['results']:
                if not result['success']:
                    logger.info(f"  - {result['test']}: {result['message']}")

        return all_passed


async def main():
    """Main test function."""
    tester = EnhancedProvisioningTester()
    success = await tester.run_all_tests()

    if success:
        logger.info("\n All enhanced provisioning tests passed!")
        logger.info(" Template extensibility working")
        logger.info(" Environment isolation working")
        logger.info(" Resource management working")
        logger.info(" Rollback capability working")
        logger.info(" Integration workflow working")
        return 0
    else:
        logger.error("\n Some enhanced provisioning tests failed.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
