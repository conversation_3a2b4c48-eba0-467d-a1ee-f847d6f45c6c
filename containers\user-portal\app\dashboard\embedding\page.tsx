'use client'

import { useState, useEffect } from 'react'
import { ProjectCard } from '../../../components/ProjectCard'
import { useEmbeddingStatus } from '../../../hooks/useEmbeddingStatus'
import { EmbeddingProgressUpdate, Project } from '../../../types/project'
import useProjects from '../../../hooks/useProjects'
import { useSession } from 'next-auth/react'
import CreateProjectModal from '../../../components/CreateProjectModal'

interface Environment {
  project_id: string;
  status: 'Running' | 'Stopped';
  url?: string;
}

export default function EmbeddingDashboard() {
  const { projects, loading: projectsLoading } = useProjects()
  const { data: session } = useSession()
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [mergedProjects, setMergedProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setLoading(projectsLoading)
  }, [projectsLoading])

  useEffect(() => {
    if (projects && projects.length > 0) {
      const fetchEnvironments = async () => {
        try {
          const response = await fetch('/api/projects/environments');
          if (!response.ok) {
            throw new Error('Failed to fetch environments');
          }
          const environments: Environment[] = await response.json();

          const projectsWithEnvironments = projects.map(p => {
            const env = environments.find(e => e.project_id === p.id);
            return {
              ...p,
              environment_status: env?.status ?? 'Stopped',
              environment_url: env?.url,
            };
          });
          setMergedProjects(projectsWithEnvironments);
        } catch (error) {
          console.error("Error fetching environments:", error);
          // If fetching fails, show projects with a default 'Stopped' status
          setMergedProjects(projects.map(p => ({ ...p, environment_status: 'Stopped' })));
        } finally {
          setLoading(false);
        }
      };
      fetchEnvironments();
    } else if (!projectsLoading) {
      setLoading(false);
      setMergedProjects([]);
    }
  }, [projects, projectsLoading]);

  const userId = session?.user?.id ?? session?.user?.email ?? 'user-123'

  const { isConnected } = useEmbeddingStatus(userId, (update: EmbeddingProgressUpdate) => {
    void update
  })

  const handleProjectCreated = () => {
    window.location.reload()
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="px-4 py-6 sm:px-0">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Projects</h1>
          <p className="mt-2 text-sm text-gray-600">
            Manage projects, environments, and embeddings.
            {isConnected && (
              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Live Updates
              </span>
            )}
          </p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          Create Project
        </button>
      </div>

      {(!mergedProjects || mergedProjects.length === 0) ? (
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No projects found</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating your first project.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mergedProjects.map((project: Project) => (
            <ProjectCard
              key={project.id}
              project={project}
              onScan={() => fetch(`/api/projects/${project.id}/scan`, { method: 'POST' })}
            />
          ))}
        </div>
      )}

      <CreateProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onProjectCreated={handleProjectCreated}
      />
    </div>
  )
}
