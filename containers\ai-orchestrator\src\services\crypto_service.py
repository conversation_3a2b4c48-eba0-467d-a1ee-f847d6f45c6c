"""
Cryptography Service for secure encryption and decryption of sensitive data.

This service provides secure encryption and decryption capabilities for
sensitive data like API keys, using industry-standard cryptographic algorithms.
Uses Argon2id for secure password hashing as recommended by OWASP.
"""

import base64
import logging
from typing import Optional
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Import Argon2 for secure password hashing
try:
    from argon2 import PasswordHasher
    from argon2.exceptions import VerifyMismatchError, HashingError
    ARGON2_AVAILABLE = True
except ImportError:
    ARGON2_AVAILABLE = False
    PasswordHasher = None
    VerifyMismatchError = Exception
    HashingError = Exception

from src.core.config import settings

logger = logging.getLogger(__name__)


class CryptoService:
    """
    Service for encrypting and decrypting sensitive data using Fernet symmetric encryption.

    Fernet provides authenticated encryption using AES-128 in CBC mode with HMAC-SHA256
    for authentication. This ensures both confidentiality and integrity of encrypted data.
    """

    def __init__(self):
        """Initialize the cryptography service with configuration."""
        self.fernet: Optional[Fernet] = None
        self._initialize_fernet()

    def _initialize_fernet(self) -> None:
        """
        Initialize Fernet encryption with key derived from application secret.

        The encryption key is derived from the application's JWT secret using PBKDF2HMAC
        with 100,000 iterations and a fixed salt for consistent key derivation.
        """
        if not settings.JWT_SECRET:
            logger.warning("No JWT secret configured - encryption/decryption will be disabled")
            return

        try:
            # Use a fixed salt for consistent key derivation (can be configurable if needed)
            salt = b"ai_coding_agent_crypto_salt"  # 32-byte salt

            # Derive key using PBKDF2HMAC with 100,000 iterations
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )

            # Derive encryption key from application JWT secret
            key = base64.urlsafe_b64encode(kdf.derive(settings.JWT_SECRET.encode()))
            self.fernet = Fernet(key)
            logger.info("Cryptography service initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize cryptography service: {e}")
            self.fernet = None

    def encrypt(self, plaintext: str) -> Optional[str]:
        """
        Encrypt plaintext data using Fernet symmetric encryption.

        Args:
            plaintext: The plaintext string to encrypt

        Returns:
            Optional[str]: Base64-encoded encrypted data, or None if encryption fails
        """
        if not self.fernet:
            logger.error("Cryptography service not initialized - cannot encrypt")
            return None

        if not plaintext:
            logger.warning("Attempted to encrypt empty plaintext")
            return None

        try:
            encrypted_data = self.fernet.encrypt(plaintext.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            return None

    def decrypt(self, encrypted_data: str) -> Optional[str]:
        """
        Decrypt encrypted data using Fernet symmetric encryption.

        Args:
            encrypted_data: Base64-encoded encrypted data

        Returns:
            Optional[str]: Decrypted plaintext, or None if decryption fails
        """
        if not self.fernet:
            logger.error("Cryptography service not initialized - cannot decrypt")
            return None

        if not encrypted_data:
            logger.warning("Attempted to decrypt empty data")
            return None

        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except InvalidToken:
            logger.error("Decryption failed: Invalid token (tampered or incorrect key)")
            return None
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            return None

    def is_initialized(self) -> bool:
        """
        Check if the cryptography service is properly initialized.

        Returns:
            bool: True if service is ready for encryption/decryption operations
        """
        return self.fernet is not None

    def health_check(self) -> dict:
        """
        Perform a health check of the cryptography service.

        Returns:
            dict: Health status information
        """
        status = self.is_initialized()
        return {
            "status": "healthy" if status else "unhealthy",
            "initialized": status,
            "encryption_available": status,
            "error": "Service not initialized" if not status else None
        }

    def hash_password(self, password: str) -> str:
        """
        Hash a password using Argon2id algorithm.

        Args:
            password: Plain text password to hash

        Returns:
            str: Argon2id hash of the password

        Raises:
            RuntimeError: If Argon2 is not available
            HashingError: If hashing fails
        """
        if not ARGON2_AVAILABLE or PasswordHasher is None:
            logger.error("Argon2 not available, falling back to PBKDF2 (not recommended)")
            # Fallback to PBKDF2 for backwards compatibility
            import hashlib
            import secrets
            salt = secrets.token_bytes(32)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = kdf.derive(password.encode())
            # Use hashlib for additional verification
            verification_hash = hashlib.sha256(password.encode() + salt).hexdigest()
            return base64.b64encode(salt + key).decode() + ":" + verification_hash

        try:
            ph = PasswordHasher(
                time_cost=2,      # Number of iterations
                memory_cost=65536, # Memory usage in KiB (64 MB)
                parallelism=1,    # Number of parallel threads
                hash_len=32,      # Hash output length
                salt_len=16,      # Salt length
            )
            return ph.hash(password)
        except (HashingError, Exception) as e:
            logger.error(f"Failed to hash password: {str(e)}")
            raise

    def verify_password(self, password: str, hash_: str) -> bool:
        """
        Verify a password against its Argon2id hash.

        Args:
            password: Plain text password to verify
            hash_: Argon2id hash to verify against

        Returns:
            bool: True if password matches hash, False otherwise
        """
        if not ARGON2_AVAILABLE or PasswordHasher is None:
            logger.warning("Argon2 not available, attempting PBKDF2 verification")
            # Fallback verification for PBKDF2
            try:
                # Handle both legacy and new PBKDF2 formats
                if ":" in hash_:
                    encoded_part, _verification_hash = hash_.split(":", 1)
                    decoded = base64.b64decode(encoded_part.encode())
                else:
                    decoded = base64.b64decode(hash_.encode())

                salt = decoded[:32]
                stored_key = decoded[32:]
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                key = kdf.derive(password.encode())
                return key == stored_key
            except Exception:
                return False

        try:
            ph = PasswordHasher()
            ph.verify(hash_, password)
            return True
        except VerifyMismatchError:
            return False
        except Exception as e:
            logger.error(f"Password verification error: {str(e)}")
            return False


# Global instance for singleton pattern
crypto_service = CryptoService()


def get_crypto_service() -> CryptoService:
    """
    Get the global crypto service instance.

    Returns:
        CryptoService: The global crypto service instance
    """
    return crypto_service
