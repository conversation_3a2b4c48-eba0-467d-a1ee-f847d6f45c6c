import pytest
from src.utils.auth import hash_password, verify_password
from src.core.config import reload_settings
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from src.models.database import Base
from src.services.project_service import ProjectService
from src.schemas.project_schemas import ProjectCreateRequest

# =====================================================================================
# Password Hashing Tests
# =====================================================================================

def test_password_hashing():
    """
    Tests that password hashing and verification work correctly.
    """
    password = "mysecretpassword"

    hashed_password = hash_password(password)

    assert password != hashed_password
    assert verify_password(password, hashed_password) is True
    assert verify_password("wrongpassword", hashed_password) is False

def test_hashed_password_is_random():
    """
    Tests that hashing the same password twice results in different hashes.
    """
    password = "anotherpassword"

    hash1 = hash_password(password)
    hash2 = hash_password(password)

    assert hash1 != hash2
    assert verify_password(password, hash1) is True
    assert verify_password(password, hash2) is True

# =====================================================================================
# JWT Secret Handling Tests
# =====================================================================================

def test_production_fails_with_no_secret(monkeypatch):
    """
    Tests that the app fails to start in production without a JWT secret.
    """
    monkeypatch.setenv("ENVIRONMENT", "production")
    monkeypatch.setenv("JWT_SECRET", "", raising=False)  # Use empty string to be explicit
    monkeypatch.setenv("DATABASE_URL", "postgresql://user:pass@localhost/db")
    monkeypatch.setenv("REDIS_URL", "redis://localhost:6379")


    with pytest.raises(ValueError, match="FATAL: JWT_SECRET is not configured for the production environment."):
        reload_settings()

def test_production_fails_with_short_secret(monkeypatch):
    """
    Tests that the app fails to start in production with a short JWT secret.
    """
    monkeypatch.setenv("ENVIRONMENT", "production")
    monkeypatch.setenv("JWT_SECRET", "shortsecret")
    monkeypatch.setenv("DATABASE_URL", "postgresql://user:pass@localhost/db")
    monkeypatch.setenv("REDIS_URL", "redis://localhost:6379")


    with pytest.raises(ValueError, match="FATAL: JWT_SECRET is too short for the production environment."):
        reload_settings()

def test_production_succeeds_with_long_secret(monkeypatch):
    """
    Tests that the app starts successfully in production with a secure JWT secret.
    """
    monkeypatch.setenv("ENVIRONMENT", "production")
    monkeypatch.setenv("JWT_SECRET", "a-very-long-and-secure-secret-key-that-is-definitely-32-characters")
    monkeypatch.setenv("DATABASE_URL", "postgresql://user:pass@localhost/db")
    monkeypatch.setenv("REDIS_URL", "redis://localhost:6379")
    monkeypatch.setenv("DOCKER_PROXY_URL", "http://localhost:2375")

    try:
        settings = reload_settings()
        assert settings.JWT_SECRET == "a-very-long-and-secure-secret-key-that-is-definitely-32-characters"
    except ValueError as e:
        pytest.fail(f"Settings validation failed unexpectedly: {e}")

def test_dev_fails_with_no_secret(monkeypatch):
    """
    Tests that the app fails to start in development without a JWT secret.
    """
    monkeypatch.setenv("ENVIRONMENT", "development")
    monkeypatch.setenv("JWT_SECRET", "", raising=False)
    monkeypatch.setenv("DATABASE_URL", "postgresql://user:pass@localhost/db")
    monkeypatch.setenv("REDIS_URL", "redis://localhost:6379")
    monkeypatch.setenv("DOCKER_PROXY_URL", "http://localhost:2375")

    with pytest.raises(ValueError, match="JWT_SECRET is not configured for the development environment."):
        reload_settings()

# =====================================================================================
# Authorization and Ownership Tests for ProjectService
# =====================================================================================

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="function")
def db_session(monkeypatch):
    """Create a test database session for the module."""
    monkeypatch.setenv("DOCKER_PROXY_URL", "http://localhost:2375")
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="module")
def project_service(db_session, monkeypatch):
    """Create a ProjectService instance for the module."""
    monkeypatch.setenv("DOCKER_PROXY_URL", "http://localhost:2375")
    return ProjectService(db=db_session)

@pytest.mark.asyncio
async def test_project_ownership_isolation(project_service: ProjectService, db_session):
    """
    Tests that users can only access projects they own.
    """
    user_1_id = 1
    user_2_id = 2

    # Create projects for user 1
    project_data_1a = ProjectCreateRequest(name="user_1_project_a", description="Project A for user 1")
    project_1a = await project_service.create_project(project_data_1a, user_1_id)
    project_data_1b = ProjectCreateRequest(name="user_1_project_b", description="Project B for user 1")
    project_1b = await project_service.create_project(project_data_1b, user_1_id)

    # Create a project for user 2
    project_data_2 = ProjectCreateRequest(name="user_2_project", description="Project for user 2")
    project_2 = await project_service.create_project(project_data_2, user_2_id)

    # Test get_project
    assert await project_service.get_project(project_1a.id, user_1_id) is not None
    assert await project_service.get_project(project_1a.id, user_2_id) is None

    # Test list_user_projects
    user_1_projects = await project_service.list_user_projects(user_1_id)
    assert len(user_1_projects) == 2
    assert {p.id for p in user_1_projects} == {project_1a.id, project_1b.id}

    user_2_projects = await project_service.list_user_projects(user_2_id)
    assert len(user_2_projects) == 1
    assert user_2_projects[0].id == project_2.id

    # Test delete_project
    with pytest.raises(ValueError, match="Project not found or user does not have permission to delete this project"):
        await project_service.delete_project(project_1a.id, user_2_id)

    try:
        await project_service.delete_project(project_1a.id, user_1_id)
    except ValueError:
        pytest.fail("User 1 should be able to delete their own project.")

    assert await project_service.get_project(project_1a.id, user_1_id) is None


# =====================================================================================
# JWT Implementation Tests
# =====================================================================================

from src.utils.auth import create_access_token
from jose import jwt

def test_create_and_verify_jwt(monkeypatch):
    """
    Tests that a JWT can be created and verified successfully using the jose library.
    """
    # Mock settings for the test
    monkeypatch.setenv("JWT_SECRET", "a-test-secret-that-is-at-least-32-characters-long")
    monkeypatch.setenv("JWT_ALGORITHM", "HS256")
    monkeypatch.setenv("DATABASE_URL", "postgresql://user:pass@localhost/db")
    monkeypatch.setenv("REDIS_URL", "redis://localhost:6379")
    reload_settings()

    user_id = "test_user_123"
    token_data = {"sub": user_id, "email": "<EMAIL>"}

    # Create a token
    token = create_access_token(data=token_data)

    assert isinstance(token, str)

    # Verify the token using the jose library directly
    decoded_payload = jwt.decode(token, "a-test-secret-that-is-at-least-32-characters-long", algorithms=["HS256"])

    assert decoded_payload["sub"] == user_id
    assert decoded_payload["email"] == "<EMAIL>"
