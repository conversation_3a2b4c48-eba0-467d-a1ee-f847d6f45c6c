"""
Sequential Agents - FrontendAgent (Next.js/React Specialist)

Implements autonomous frontend component generation using Next.js, React, TypeScript,
Tailwind CSS, and project npm tooling. The agent follows a sequential plan:

1) Analyze request (natural language feature description)
2) Design component via LLM (TSX using Tailwind)
3) Write component file under user-portal/src/components
4) Run npm validation (lint by default, optional tests)
5) Return structured result
"""

from __future__ import annotations

import asyncio
import logging
import os
import re
import textwrap
from pathlib import Path
from typing import Any, Dict, List, Tuple

from src.agents.base_agent import BaseAgent
from src.models.llm_models import GenerateRequest, LLMResponse
from src.services.enhanced_llm_service import get_llm_service

logger = logging.getLogger(__name__)


class FrontendAgent(BaseAgent):
    """Frontend agent specialized in Next.js/React component automation."""

    def __init__(self) -> None:
        super().__init__()
        # Paths
        # This file: containers/ai-orchestrator/src/agents/frontend.py
        # repo root = parents[4]
        here = Path(__file__).resolve()
        self._src_dir: Path = here.parents[1]  # ai-orchestrator/src
        self._project_root: Path = here.parents[2]  # containers/ai-orchestrator
        self._repo_root: Path = here.parents[4]
        self._frontend_root: Path = self._repo_root / "containers" / "user-portal"
        self._frontend_src: Path = self._frontend_root / "src"
        self._components_dir: Path = self._frontend_src / "components"
        # Lazy LLM handle
        self._llm = None

    async def _get_llm(self):
        if self._llm is None:
            self._llm = await get_llm_service()
        return self._llm

    async def _read_file(self, path: str) -> str:
        """Read a text file asynchronously (absolute or repo-relative)."""
        p = Path(path)
        if not p.is_absolute():
            p = self._repo_root / p
        return await asyncio.to_thread(p.read_text, encoding="utf-8")

    async def _write_file(self, path: str, content: str) -> None:
        """Write text to file, creating directories if needed (absolute or repo-relative)."""
        p = Path(path)
        if not p.is_absolute():
            p = self._repo_root / p
        await asyncio.to_thread(p.parent.mkdir, parents=True, exist_ok=True)
        await asyncio.to_thread(p.write_text, content, "utf-8")

    async def _run_npm_command(self, args: List[str]) -> Tuple[int, str, str]:
        """Run an npm command in the user-portal project directory.

        Example: ["run", "lint"] or ["run", "test", "--", "--watch=false"]
        Returns (returncode, stdout, stderr)
        """
        cmd = ["npm", *args]
        proc = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=str(self._frontend_root),
            env=os.environ.copy(),
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        out_b, err_b = await proc.communicate()
        return proc.returncode, out_b.decode(), err_b.decode()

    @staticmethod
    def _slugify(name: str) -> str:
        s = re.sub(r"[^a-zA-Z0-9_]+", "-", name.strip().lower()).strip("-")
        return s or "feature"

    @staticmethod
    def _to_pascal_case(slug: str) -> str:
        return "".join(part.capitalize() for part in re.split(r"[-_\s]+", slug) if part)

    @staticmethod
    def _extract_code(text: str) -> str:
        """Extract code from fenced blocks if present."""
        fence = re.findall(r"```[a-zA-Z0-9_\-]*\n(.*?)```", text, flags=re.DOTALL)
        if fence:
            return fence[0].strip()
        return text.strip()

    def _component_prompt(self, feature: str, description: str, component_name: str) -> str:
        """Prompt for a complete, production-ready TSX component using Tailwind."""
        return textwrap.dedent(
            f"""
            You are a senior frontend engineer. Generate a complete, production-ready Next.js/React component
            in a single .tsx file using TypeScript and Tailwind CSS for the feature: "{feature}".

            Description (natural language requirements):
            {description}

            Hard requirements:
            - Use React with TypeScript (.tsx) and export default function {component_name}(props: Props) {{ ... }}
            - Define a Props interface at the top with sensible, typed props
            - Use Tailwind CSS classes for styling (no external UI libraries)
            - Self-contained: include necessary imports (React, useState/useEffect if needed, types)
            - No external runtime dependencies beyond React/Next.js and Tailwind
            - Accessible: label inputs, provide alt text for images, keyboard-friendly
            - Add basic JSDoc comments for the component and key props
            - Include simple internal state handling where appropriate
            - Do not include tests or additional files; output only a complete .tsx module, no explanations

            Conventions:
            - Use functional component style
            - Use CSS classes like "bg-gray-50", "text-sm", "rounded-md", "hover:bg-gray-100" etc.
            - Use semantic HTML elements
            - Keep the component generic and reusable

            Example minimal header (for style only; customize fully):
            import React, {{ useState, useEffect }} from 'react';

            interface Props {{
              title?: string;
              onAction?: () => void;
            }}

            export default function {component_name}(props: Props) {{
              // component implementation...
              return (
                <div className="p-4">Hello</div>
              );
            }}
            """
        ).strip()

    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a TSX component and validate with npm tooling."""
        # 1) Analyze request
        self.logger.info("FrontendAgent received task input for component generation")
        feature_desc: str = str(
            task_input.get("feature")
            or task_input.get("title")
            or task_input.get("description")
            or "New component"
        )
        feature_slug = self._slugify(feature_desc.split("\n")[0])
        component_name = self._to_pascal_case(feature_slug)

        llm = await self._get_llm()

        created_files: List[str] = []
        npm_logs: List[Dict[str, Any]] = []

        # Paths
        component_path = self._components_dir / f"{component_name}.tsx"

        # 2) Design component via LLM
        prompt = self._component_prompt(feature_slug, feature_desc, component_name)
        req = GenerateRequest(prompt=prompt)
        resp: LLMResponse = await llm.generate(req, user_id="frontend-agent")
        code = self._extract_code(resp.content)

        # 3) Write component file
        await self._write_file(str(component_path), code)
        created_files.append(str(component_path.relative_to(self._repo_root)))

        # 4) Run npm validation
        # Prefer lint; run tests if requested via task_input
        lint_rc, lint_out, lint_err = await self._run_npm_command(["run", "lint"])
        npm_logs.append(
            {
                "step": "lint",
                "returncode": lint_rc,
                "stdout": lint_out,
                "stderr": lint_err,
            }
        )

        tests_requested = bool(task_input.get("run_tests", False))
        test_rc, test_out, test_err = 0, "", ""
        if tests_requested:
            test_rc, test_out, test_err = await self._run_npm_command(
                ["run", "test", "--", "--watch=false"]
            )
            npm_logs.append(
                {
                    "step": "test",
                    "returncode": test_rc,
                    "stdout": test_out,
                    "stderr": test_err,
                }
            )

        status = (
            "success"
            if lint_rc == 0 and (not tests_requested or test_rc == 0)
            else ("lint_failed" if lint_rc != 0 else "tests_failed")
        )

        # 5) Return structured result
        return {
            "agent": "frontend",
            "status": status,
            "feature": feature_slug,
            "component": {
                "name": component_name,
                "file": str(component_path.relative_to(self._repo_root)),
            },
            "files": created_files,
            "npm": npm_logs,
            "llm": {
                "model": resp.model,
                "provider": resp.provider.value,
            },
        }
