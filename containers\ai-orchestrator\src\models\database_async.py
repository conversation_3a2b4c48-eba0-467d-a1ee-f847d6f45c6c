import os
import logging
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy import text
from fastapi import Depends

# Import secrets utility for secure configuration
from src.utils.secrets import get_database_url

logger = logging.getLogger(__name__)

# Database URL configuration with secrets support
def get_async_database_url() -> str:
    """Get async database URL with proper test environment handling."""
    try:
        # Get the regular database URL and convert to async
        sync_url = get_database_url()
        if sync_url.startswith('postgresql://'):
            return sync_url.replace('postgresql://', 'postgresql+asyncpg://', 1)
        elif sync_url.startswith('postgres://'):
            return sync_url.replace('postgres://', 'postgresql+asyncpg://', 1)
        elif 'sqlite' in sync_url:
            return sync_url.replace('sqlite://', 'sqlite+aiosqlite://', 1)
        return sync_url
    except ValueError as e:
        logger.error(f"Database configuration error: {e}")
        # Fallback to environment variable for development
        return os.getenv(
            "DATABASE_URL",
            "postgresql+asyncpg://postgres:postgres@supabase_db_codingagenttwo:5432/postgres"
        )

if os.getenv('TESTING') == '1':
    ASYNC_DATABASE_URL = 'sqlite+aiosqlite:///./test.db'
else:
    ASYNC_DATABASE_URL = get_async_database_url()

    # Override for Supabase container connection
    if 'gateway.docker.internal' in ASYNC_DATABASE_URL:
        ASYNC_DATABASE_URL = ASYNC_DATABASE_URL.replace('gateway.docker.internal:54322', 'supabase_db_codingagenttwo:5432')

    # Fix postgres:// to postgresql+asyncpg:// for SQLAlchemy compatibility
    if ASYNC_DATABASE_URL and ASYNC_DATABASE_URL.startswith('postgres://'):
        ASYNC_DATABASE_URL = ASYNC_DATABASE_URL.replace('postgres://', 'postgresql+asyncpg://', 1)

# SQLAlchemy async engine configuration
engine_args = {
    "echo": os.getenv("SQL_ECHO", "false").lower() == "true",
    "pool_pre_ping": True,
}
if "sqlite" not in ASYNC_DATABASE_URL:
    engine_args.update({
        "pool_size": 10,
        "max_overflow": 20,
        "pool_recycle": 3600,
    })

async_engine = create_async_engine(ASYNC_DATABASE_URL, **engine_args)

# AsyncSessionLocal class for creating database sessions
AsyncSessionLocal = async_sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=async_engine,
    class_=AsyncSession
)

# Base class for all SQLAlchemy models (shared with sync version)
# Import the naming convention and Base from the sync database to avoid conflicts
from src.models.database import Base as SyncBase

# Use the same Base class as the sync version to avoid table conflicts
AsyncBase = SyncBase


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency that provides an async database session.

    This function creates a new SQLAlchemy AsyncSession for each request,
    ensures proper cleanup, and handles exceptions gracefully.

    Yields:
        AsyncSession: SQLAlchemy async database session

    Usage:
        @app.get("/users/")
        async def read_users(db: AsyncSession = Depends(get_async_db)):
            return await user_repository.get_users_async(db)
    """
    async with AsyncSessionLocal() as session:
        try:
            logger.debug("Async database session created")
            yield session
        except Exception as e:
            logger.error(f"Async database session error: {str(e)}")
            await session.rollback()
            raise
        finally:
            logger.debug("Async database session closed")
            await session.close()


async def get_async_database_info() -> dict:
    """
    Get async database connection information for health checks.

    Returns:
        dict: Database connection details (without sensitive info)
    """
    try:
        async with async_engine.connect() as conn:
            result = await conn.execute(text("SELECT version()"))
            version = result.fetchone()[0] if result else "Unknown"  # type: ignore

        # Get pool info safely
        pool_size = getattr(async_engine.pool, 'size', lambda: 0)()
        checked_out = getattr(async_engine.pool, 'checkedout', lambda: 0)()

        return {
            "status": "connected",
            "database_url": ASYNC_DATABASE_URL.split("@")[-1] if "@" in ASYNC_DATABASE_URL else "localhost",
            "version": version,
            "pool_size": pool_size,
            "checked_out_connections": checked_out,
        }
    except Exception as e:
        logger.error(f"Async database health check failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e)
        }


# Async database dependency for type hints
AsyncDatabaseDep = Depends(get_async_db)


async def create_async_tables():
    """
    Create all database tables using async engine.

    This function creates all tables defined in SQLAlchemy models.
    In test environments, it also creates a mock auth.users table
    to satisfy foreign key constraints.
    """
    global async_engine, ASYNC_DATABASE_URL

    logger.info("Creating database tables with async engine...")

    # Reconfigure for testing if needed
    if os.getenv('TESTING') == '1':
        test_db_url = os.getenv('DATABASE_URL', 'sqlite+aiosqlite:///./test.db')
        if ASYNC_DATABASE_URL != test_db_url:
            logger.info(f"Switching to test database: {test_db_url}")
            ASYNC_DATABASE_URL = test_db_url
            # Recreate engine with test database
            async_engine = create_async_engine(
                ASYNC_DATABASE_URL,
                echo=os.getenv("SQL_ECHO", "false").lower() == "true",
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
            )

    # Create mock auth.users table for testing BEFORE creating other tables
    if os.getenv('TESTING') == '1':
        logger.info("Creating mock auth.users table for testing...")
        try:
            async with async_engine.begin() as conn:
                # Enable foreign key constraints for SQLite
                await conn.execute(text("PRAGMA foreign_keys = ON"))
                # Create mock auth.users table (SQLite compatible)
                await conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS auth_users (
                        id TEXT PRIMARY KEY,
                        email TEXT UNIQUE NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        email_confirmed_at TEXT,
                        last_sign_in_at TEXT
                    )
                """))
                # Insert a test user for foreign key references
                await conn.execute(text("""
                    INSERT OR IGNORE INTO auth_users (id, email)
                    VALUES ('test-user-id', '<EMAIL>')
                """))
                await conn.commit()
                logger.info("Mock auth.users table created successfully")
        except Exception as e:
            logger.warning(f"Failed to create mock auth.users table: {e}")

    # Create all tables defined in SQLAlchemy models
    try:
        # Use the same Base metadata as the sync version
        async with async_engine.begin() as conn:
            await conn.run_sync(SyncBase.metadata.create_all)
        logger.info("All database tables created successfully with async engine")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


async def drop_async_tables():
    """
    Drop all database tables using async engine.

    This function drops all tables defined in SQLAlchemy models.
    """
    logger.info("Dropping database tables with async engine...")
    try:
        async with async_engine.begin() as conn:
            await conn.run_sync(SyncBase.metadata.drop_all)
        logger.info("All database tables dropped successfully with async engine")
    except Exception as e:
        logger.error(f"Failed to drop database tables: {e}")
        raise
