# Supabase Google OAuth Setup Guide

## Step 1: Configure Google OAuth Provider in Supabase Dashboard

### 1.1 Access Supabase Authentication Settings
1. Go to your Supabase project dashboard
2. Navigate to **Authentication** → **Providers**
3. Find **Google** in the list of providers

### 1.2 Enable Google Provider
1. Toggle **Enable sign in with Google** to ON
2. You'll see fields for:
   - **Client ID (for OAuth)**
   - **Client Secret (for OAuth)**

### 1.3 Configure Callback URL
The redirect URL will be automatically set to:
```
https://[your-project-ref].supabase.co/auth/v1/callback
```

## Step 2: Create Google OAuth Credentials

### 2.1 Google Cloud Console Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the **Google+ API** (if not already enabled)

### 2.2 Create OAuth 2.0 Credentials
1. Navigate to **APIs & Services** → **Credentials**
2. Click **+ CREATE CREDENTIALS** → **OAuth client ID**
3. Select **Web application**
4. Configure:
   - **Name**: "AI Coding Agent - Supabase"
   - **Authorized JavaScript origins**:
     ```
     http://localhost:9000
     http://portal.localhost
     https://your-production-domain.com
     ```
   - **Authorized redirect URIs**:
     ```
     https://[your-project-ref].supabase.co/auth/v1/callback
     ```

### 2.3 Copy Credentials
1. Copy the **Client ID** and **Client Secret**
2. Paste them into your Supabase Authentication settings
3. Click **Save**

## Step 3: Environment Variables

Add these to your `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://[your-project-ref].supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Google OAuth (for NextAuth fallback if needed)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# NextAuth Configuration
NEXTAUTH_URL=http://portal.localhost
NEXTAUTH_SECRET=your-secure-random-secret
```

## Step 4: Test Configuration

1. Save all settings in Supabase Dashboard
2. Test the authentication flow using the Supabase Auth widget
3. Verify JWT tokens are being generated correctly

## Security Notes

- Always use HTTPS in production
- Keep your client secret secure and never expose it in frontend code
- Regularly rotate your secrets
- Configure proper CORS settings in Supabase
