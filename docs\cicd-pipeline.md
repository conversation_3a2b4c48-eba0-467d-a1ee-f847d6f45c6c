# CI/CD Pipeline Implementation Guide

This guide outlines the CI/CD pipeline setup for the AI Coding Agent project using GitHub Actions.

## Pipeline Overview

The CI/CD pipeline automates the following processes:
- Code linting and formatting
- Unit and integration testing
- Docker image building and security scanning
- Deployment to different environments (dev, staging, production)

## GitHub Actions Workflows

### 1. Continuous Integration (CI)

Located at `.github/workflows/ci.yml`

```yaml
name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint:
    name: Code Linting
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        pip install -r requirements-dev.txt
        pip install -r containers/ai-orchestrator/requirements.txt

    - name: Lint Python code
      run: |
        flake8 .
        pylint containers/ai-orchestrator/src/

    - name: <PERSON>t Dockerfiles
      run: |
        ./scripts/lint_dockerfiles.sh

  test:
    name: Run Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: pgvector/pgvector:pg15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:alpine
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        pip install -r requirements-dev.txt
        pip install -r containers/ai-orchestrator/requirements.txt

    - name: Run unit tests
      run: pytest tests/unit/ -v
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379

    - name: Run integration tests
      run: pytest tests/integration/ -v
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379

  security:
    name: Security Scanning
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Run security scan
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        ignore-unfixed: true
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
```

### 2. Docker Build and Push

Located at `.github/workflows/docker-build.yml`

```yaml
name: Docker Build and Push

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata for Docker
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Build and push code-server image
      uses: docker/build-push-action@v4
      with:
        context: ./containers/code-server
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}-code-server
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push ai-orchestrator image
      uses: docker/build-push-action@v4
      with:
        context: ./containers/ai-orchestrator
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}-ai-orchestrator
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push user-portal image
      uses: docker/build-push-action@v4
      with:
        context: ./containers/user-portal
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}-user-portal
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
```

### 3. Deployment Pipeline

Located at `.github/workflows/deploy.yml`

```yaml
name: Deploy to Environments

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production

jobs:
  deploy-staging:
    name: Deploy to Staging
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    runs-on: ubuntu-latest
    environment: staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Deploy to Staging
      run: |
        echo "Deploying to staging environment..."
        # Add deployment commands here
        # Example: docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d

    - name: Run health checks
      run: |
        # Add health check commands
        curl -f http://staging.ai-coding-agent.com/health || exit 1

  deploy-production:
    name: Deploy to Production
    if: github.event.inputs.environment == 'production'
    runs-on: ubuntu-latest
    environment: production
    needs: deploy-staging

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Deploy to Production
      run: |
        echo "Deploying to production environment..."
        # Add deployment commands here
        # Example: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

    - name: Run health checks
      run: |
        # Add health check commands
        curl -f http://ai-coding-agent.com/health || exit 1
```

## Environment Configuration

### GitHub Secrets Required

Set the following secrets in your GitHub repository settings:

- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_KEY` - Your Supabase anon key
- `SUPABASE_SERVICE_KEY` - Your Supabase service key
- `DOCKER_REGISTRY_TOKEN` - Token for container registry (if using external registry)
- `DEPLOYMENT_SSH_KEY` - SSH key for deployment servers (if applicable)

### Environment Variables

Each environment should have its own `.env` file:

**.env.staging**
```bash
# Staging environment variables
SUPABASE_URL=your-staging-supabase-url
SUPABASE_KEY=your-staging-supabase-key
SUPABASE_SERVICE_KEY=your-staging-supabase-service-key
JWT_SECRET=staging-jwt-secret
NODE_ENV=staging
```

**.env.production**
```bash
# Production environment variables
SUPABASE_URL=your-production-supabase-url
SUPABASE_KEY=your-production-supabase-key
SUPABASE_SERVICE_KEY=your-production-supabase-service-key
JWT_SECRET=production-jwt-secret-change-this
NODE_ENV=production
```

## Testing Automation

### Unit Tests

Located in `tests/unit/` directory:

```python
# tests/unit/test_auth.py
import pytest
from fastapi.testclient import TestClient
from containers.ai-orchestrator.src.main import app

client = TestClient(app)

def test_health_endpoint():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}

def test_register_user():
    user_data = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "username": "testuser"
    }
    response = client.post("/auth/register", json=user_data)
    assert response.status_code == 200
    assert "access_token" in response.json()
```

### Integration Tests

Located in `tests/integration/` directory:

```python
# tests/integration/test_docker_compose.py
import subprocess
import time
import requests

def test_docker_compose_startup():
    # Start services
    subprocess.run(["docker-compose", "up", "-d"])

    # Wait for services to start
    time.sleep(30)

    # Test health endpoints
    services = [
        "http://localhost:8080/healthz",  # code-server
        "http://localhost:8000/health",   # ai-orchestrator
        "http://localhost:3000/health"    # admin-dashboard
    ]

    for service_url in services:
        response = requests.get(service_url)
        assert response.status_code == 200

    # Stop services
    subprocess.run(["docker-compose", "down"])
```

## Security Scanning

### Docker Image Scanning

The pipeline includes Trivy for container security scanning:

```yaml
- name: Run security scan
  uses: aquasecurity/trivy-action@master
  with:
    scan-type: 'image'
    image-ref: 'ghcr.io/your-org/ai-coding-agent:latest'
    format: 'sarif'
    output: 'trivy-results.sarif'
```

### Dependency Scanning

Add dependency vulnerability scanning:

```yaml
- name: Scan Python dependencies
  run: |
    pip install safety
    safety check -r containers/ai-orchestrator/requirements.txt
```

## Monitoring and Notifications

### Slack Notifications

Add Slack webhook notifications for pipeline status:

```yaml
- name: Notify Slack on failure
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: ${{ job.status }}
    channel: '#ci-cd-notifications'
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

### Email Notifications

Configure email notifications for production deployments:

```yaml
- name: Send deployment notification
  if: success() && github.ref == 'refs/heads/main'
  uses: dawidd6/action-send-mail@v3
  with:
    server_address: smtp.gmail.com
    server_port: 465
    username: ${{ secrets.EMAIL_USERNAME }}
    password: ${{ secrets.EMAIL_PASSWORD }}
    subject: Production Deployment Successful
    body: AI Coding Agent deployed successfully to production
    to: <EMAIL>
    from: <EMAIL>
```

## Deployment Strategies

### Blue-Green Deployment

For zero-downtime deployments:

```yaml
# Deploy new version to green environment
- name: Deploy to Green
  run: |
    docker-compose -f docker-compose.yml -f docker-compose.green.yml up -d

# Test green environment
- name: Test Green Environment
  run: |
    # Run smoke tests against green environment
    ./scripts/test-smoke.sh http://green.ai-coding-agent.com

# Switch traffic to green
- name: Switch to Green
  run: |
    # Update load balancer to point to green
    ./scripts/switch-traffic.sh green
```

### Rolling Updates

For gradual rollouts:

```yaml
- name: Rolling update
  run: |
    for i in {1..3}; do
      docker service update --replicas-add=1 ai-orchestrator
      sleep 30
      # Run health checks
      curl -f http://localhost:8000/health
    done
```

## Pipeline Monitoring

### Dashboard Setup

Create a monitoring dashboard for pipeline metrics:

1. **Build Success Rate**: Percentage of successful builds
2. **Deployment Frequency**: How often deployments occur
3. **Mean Time to Recovery**: Average time to fix failed deployments
4. **Change Failure Rate**: Percentage of deployments that fail

### Alerts Configuration

Set up alerts for critical pipeline failures:

```yaml
- name: Alert on pipeline failure
  if: failure()
  run: |
    curl -X POST ${{ secrets.ALERT_WEBHOOK_URL }} \
      -H "Content-Type: application/json" \
      -d '{"text": "CI/CD pipeline failed for ${{ github.repository }}"}'
```

## Best Practices

### Pipeline Optimization

1. **Caching**: Use GitHub Actions caching for dependencies
2. **Parallel Jobs**: Run linting, testing, and security scans in parallel
3. **Matrix Testing**: Test against multiple Python versions and environments
4. **Conditional Steps**: Skip unnecessary steps based on changed files

### Security Considerations

1. **Secrets Management**: Never hardcode secrets in workflows
2. **Dependency Updates**: Regularly update dependencies and scan for vulnerabilities
3. **Access Control**: Use environment protection rules for production deployments
4. **Audit Logs**: Enable detailed logging for security audits

### Maintenance

1. **Regular Updates**: Update GitHub Actions versions regularly
2. **Performance Monitoring**: Monitor pipeline execution times
3. **Documentation**: Keep pipeline documentation up to date
4. **Backup Strategies**: Maintain backup deployment methods

## Next Steps

1. Implement the GitHub Actions workflows in `.github/workflows/`
2. Configure environment secrets in GitHub repository settings
3. Set up monitoring and alerting for pipeline metrics
4. Test the pipeline with sample deployments
5. Document any custom deployment procedures
6. Set up regular pipeline maintenance schedules

For more information on GitHub Actions, refer to the [GitHub Actions documentation](https://docs.github.com/en/actions).
