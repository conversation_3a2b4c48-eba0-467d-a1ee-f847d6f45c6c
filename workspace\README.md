# AI Coding Agent Development Workspace

This workspace provides a structured environment for developing the AI Coding Agent within code-server.

## Project Structure

- **`ai-orchestrator/src/`** - FastAPI backend source code (hot-reload enabled)
- **`ai-orchestrator/tests/`** - Backend tests
- **`containers/`** - All container configurations and source code
- **`docs/`** - Project documentation
- **`scripts/`** - Development and deployment scripts

## Development Workflow

### 1. Hot-Reload Development
When you edit Python files in `ai-orchestrator/src/`, the FastAPI server will automatically restart and reflect your changes.

**Test the hot-reload:**
1. Open `ai-orchestrator/src/main.py`
2. Make a small change (e.g., modify a docstring or add a comment)
3. Save the file
4. Check the ai-orchestrator container logs to see the automatic restart
5. Verify the change at http://localhost:8000

### 2. Accessing Services
- **Code-Server**: http://localhost:8080 (Browser-based VS Code)
- **AI Orchestrator API**: http://localhost:8000 (FastAPI backend)
- **User Portal**: http://localhost:3000 (Next.js frontend)
- **API Documentation**: http://localhost:8000/docs (Swagger UI)

### 3. Development Commands
```bash
# Start development environment with hot-reload
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch

# View logs for specific service
docker-compose logs -f ai-orchestrator

# Access container shell
docker-compose exec ai-orchestrator bash

# Run tests
docker-compose exec ai-orchestrator pytest tests/ -v
```

### 4. File Organization
This workspace mirrors the actual project structure, allowing you to:
- Edit Python source code with full IDE features
- Run and debug applications
- Access documentation and scripts
- Maintain proper project organization

## Quick Start Guide

1. **Start Development Environment**:
   ```bash
   ./scripts/start-dev.sh watch
   ```

2. **Open Code-Server**: Navigate to http://localhost:8080

3. **Open Project**: The workspace should automatically open in `/home/<USER>

4. **Test Hot-Reload**:
   - Navigate to `ai-orchestrator/src/main.py`
   - Make a small change and save
   - Check container logs to confirm restart

5. **Verify Changes**: Visit http://localhost:8000 to see your changes

## Troubleshooting

- **Port conflicts**: Ensure ports 8080, 8000, 3000 are available
- **File permissions**: Check Docker volume mount permissions
- **Hot-reload not working**: Verify the development compose configuration
- **Code-server not accessible**: Check container logs and network configuration