"""
Rate limiting configuration for the AI Orchestrator.
Separated from main.py to avoid circular imports.
"""

try:
    from slowapi import Limiter, _rate_limit_exceeded_handler  # type: ignore
    from slowapi.util import get_remote_address  # type: ignore
    SLOWAPI_AVAILABLE = True
except ImportError:
    # Graceful degradation if slowapi is not available
    Limiter = None  # type: ignore
    _rate_limit_exceeded_handler = None  # type: ignore
    get_remote_address = None  # type: ignore
    SLOWAPI_AVAILABLE = False

# Create the limiter instance
if SLOWAPI_AVAILABLE and Limiter:
    limiter = Limiter(key_func=get_remote_address)
else:
    limiter = None

def apply_rate_limit(limit_string: str):
    """
    Apply rate limiting to a FastAPI route.

    Args:
        limit_string: Rate limit specification (e.g., "10/minute")

    Returns:
        Decorator function or passthrough if rate limiting unavailable
    """
    def decorator(func):
        if SLOWAPI_AVAILABLE and limiter:
            return limiter.limit(limit_string)(func)
        return func  # No rate limiting if slowapi unavailable
    return decorator
