"""
Sequential Agents - Enhanced Base class

Defines an enhanced BaseAgent with advanced features including validation,
error recovery, resource locking, and task history tracking, while maintaining
the simple async execute interface for backward compatibility.
"""
from __future__ import annotations

import asyncio
import logging
import time
import os
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum


class ErrorType(Enum):
    """Error classification types for recovery strategies."""
    SYNTAX_ERROR = "syntax_error"
    IMPORT_ERROR = "import_error"
    CONFIGURATION_ERROR = "configuration_error"
    DEPENDENCY_ERROR = "dependency_error"
    NETWORK_ERROR = "network_error"
    DATABASE_ERROR = "database_error"
    PERMISSION_ERROR = "permission_error"
    UNKNOWN_ERROR = "unknown_error"


@dataclass
class ValidationResult:
    """Result of a validation check."""
    is_valid: bool
    error: Optional[str] = None
    warnings: List[str] = field(default_factory=list)

    @classmethod
    def success(cls, message: str = "Validation passed") -> "ValidationResult":
        """Create a successful validation result."""
        return cls(is_valid=True, error=None)

    @classmethod
    def failure(cls, error: str) -> "ValidationResult":
        """Create a failed validation result."""
        return cls(is_valid=False, error=error)

    @classmethod
    def from_checks(cls, checks: List["ValidationResult"]) -> "ValidationResult":
        """Combine multiple validation results."""
        failures = [check for check in checks if not check.is_valid]
        warnings = [warning for check in checks for warning in check.warnings]

        if failures:
            combined_error = "; ".join([f.error for f in failures if f.error])
            return cls(is_valid=False, error=combined_error, warnings=warnings)

        return cls(is_valid=True, warnings=warnings)


@dataclass
class RecoveryResult:
    """Result of an error recovery attempt."""
    success: bool
    actions_taken: str
    retry_recommended: bool
    recovery_suggestions: List[str] = field(default_factory=list)


class BaseAgent(ABC):
    """
    Enhanced abstract base class for AI agents with advanced features.

    Provides common functionality for:
    - Simple execute() interface for backward compatibility
    - Optional advanced features (validation, error recovery, resource locking)
    - Task execution tracking and history
    - Configurable retry logic with exponential backoff
    - Status reporting and monitoring
    """

    def __init__(
        self,
        max_concurrent_tasks: int = 1,
        enable_validation: bool = True,
        enable_error_recovery: bool = True,
        enable_task_history: bool = True,
        max_retries: int = 2
    ) -> None:
        self.logger = logging.getLogger(self.__class__.__name__)

        # Configuration
        self.max_concurrent_tasks = max_concurrent_tasks
        self.enable_validation = enable_validation
        self.enable_error_recovery = enable_error_recovery
        self.enable_task_history = enable_task_history
        self.max_retries = max_retries

        # Resource management
        self._execution_lock = asyncio.Semaphore(max_concurrent_tasks)
        self._is_active = False
        self._current_task_id: Optional[str] = None

        # Task tracking
        self._task_history: List[Dict[str, Any]] = []
        self._error_count = 0
        self._success_count = 0
        self._agent_id = f"{self.__class__.__name__.lower()}_{int(time.time())}"

        self.logger.info(f"Initialized {self.__class__.__name__} with ID: {self._agent_id}")

    @property
    def is_active(self) -> bool:
        """Check if agent is currently executing a task."""
        return self._is_active

    @property
    def current_task_id(self) -> Optional[str]:
        """Get current task ID if any."""
        return self._current_task_id

    @property
    def success_rate(self) -> float:
        """Calculate agent success rate."""
        total_tasks = self._success_count + self._error_count
        return self._success_count / total_tasks if total_tasks > 0 else 0.0

    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhanced execute method with optional advanced features.

        Args:
            task_input: Task input dictionary

        Returns:
            Dict containing execution result
        """
        async with self._execution_lock:
            self._is_active = True
            task_id = task_input.get("task_id", f"task_{int(time.time())}")
            self._current_task_id = task_id

            started_at = datetime.now()
            self.logger.info(f"Starting task: {task_id}")

            try:
                # Optional pre-execution validation
                if self.enable_validation:
                    validation = await self._pre_execution_validation(task_input)
                    if not validation.is_valid:
                        return self._create_error_result(
                            task_id, f"Pre-execution validation failed: {validation.error}"
                        )

                # Execute with optional retry logic
                result = await self._execute_with_retry(task_input)

                # Optional post-execution validation
                if self.enable_validation and result.get("success", False):
                    post_validation = await self._post_execution_validation(task_input, result)
                    if not post_validation.is_valid:
                        result["validation_warnings"] = post_validation.error

                # Update statistics and history
                if result.get("success", False):
                    self._success_count += 1
                else:
                    self._error_count += 1

                if self.enable_task_history:
                    self._record_task_execution(task_id, task_input, result, started_at)

                self.logger.info(f"Task completed: {task_id} - Success: {result.get('success', False)}")
                return result

            except Exception as e:
                self.logger.error(f"Task execution failed: {task_id} - Error: {str(e)}")
                self._error_count += 1

                error_result = self._create_error_result(task_id, str(e))
                if self.enable_task_history:
                    self._record_task_execution(task_id, task_input, error_result, started_at)

                return error_result

            finally:
                self._is_active = False
                self._current_task_id = None

    async def _execute_with_retry(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute task with optional retry logic and error recovery."""
        last_error = None

        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"Retry attempt {attempt} for task: {task_input.get('task_id', 'unknown')}")
                    task_input["retry_count"] = attempt

                # Core execution (implemented by subclasses)
                result = await self._execute_core(task_input)

                if result.get("success", False):
                    return result

                # Task failed, attempt recovery if enabled and not on last attempt
                if self.enable_error_recovery and attempt < self.max_retries:
                    error_msg = result.get("error", "Unknown error")
                    recovery_result = await self._attempt_error_recovery(task_input, error_msg)
                    if recovery_result.retry_recommended:
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                        continue

                return result  # Return the failed result

            except Exception as e:
                last_error = e
                self.logger.error(f"Task execution attempt {attempt + 1} failed: {str(e)}")

                if self.enable_error_recovery and attempt < self.max_retries:
                    recovery_result = await self._attempt_error_recovery(task_input, str(e))
                    if recovery_result.retry_recommended:
                        await asyncio.sleep(2 ** attempt)  # Exponential backoff
                        continue

        # All retries exhausted
        raise Exception(f"Task failed after {self.max_retries + 1} attempts. Last error: {last_error}")

    async def _pre_execution_validation(self, task_input: Dict[str, Any]) -> ValidationResult:
        """
        Validate prerequisites before task execution.
        Override in subclasses for specific validation logic.
        """
        checks = []

        # Basic validation
        if not task_input.get("task_id"):
            task_input["task_id"] = f"task_{int(time.time())}"

        # Agent-specific validation (override in subclasses)
        agent_validation = await self._validate_agent_specific_prerequisites(task_input)
        checks.append(agent_validation)

        return ValidationResult.from_checks(checks)

    async def _post_execution_validation(self, task_input: Dict[str, Any], result: Dict[str, Any]) -> ValidationResult:
        """
        Validate task completion and results.
        Override in subclasses for specific validation logic.
        """
        checks = []

        # Basic result validation
        if not result.get("success", False) and not result.get("error"):
            checks.append(ValidationResult.failure("Failed task must have error message"))

        # File existence validation
        if "expected_files" in task_input:
            file_validation = await self._validate_expected_files(task_input["expected_files"])
            checks.append(file_validation)

        # Agent-specific validation (override in subclasses)
        agent_validation = await self._validate_agent_specific_completion(task_input, result)
        checks.append(agent_validation)

        return ValidationResult.from_checks(checks)

    async def _attempt_error_recovery(self, task_input: Dict[str, Any], error: str) -> RecoveryResult:
        """
        Attempt to recover from task execution error.
        Override in subclasses for specific recovery logic.
        """
        error_type = self._classify_error(error)

        self.logger.info(f"Attempting error recovery for: {error_type}")

        # Basic recovery strategies
        if error_type == ErrorType.DEPENDENCY_ERROR:
            return await self._recover_dependency_error(task_input, error)
        elif error_type == ErrorType.CONFIGURATION_ERROR:
            return await self._recover_configuration_error(task_input, error)
        elif error_type == ErrorType.SYNTAX_ERROR:
            return await self._recover_syntax_error(task_input, error)
        else:
            # Generic recovery
            return RecoveryResult(
                success=False,
                actions_taken="No specific recovery strategy available",
                retry_recommended=True,
                recovery_suggestions=[
                    "Review task parameters",
                    "Check system resources",
                    "Verify network connectivity"
                ]
            )

    def _classify_error(self, error: str) -> ErrorType:
        """Classify error type based on error message."""
        error_lower = error.lower()

        if any(keyword in error_lower for keyword in ['syntax', 'invalid syntax', 'parsing']):
            return ErrorType.SYNTAX_ERROR
        elif any(keyword in error_lower for keyword in ['import', 'module', 'cannot import']):
            return ErrorType.IMPORT_ERROR
        elif any(keyword in error_lower for keyword in ['config', 'configuration', 'missing']):
            return ErrorType.CONFIGURATION_ERROR
        elif any(keyword in error_lower for keyword in ['dependency', 'package', 'not found']):
            return ErrorType.DEPENDENCY_ERROR
        elif any(keyword in error_lower for keyword in ['network', 'connection', 'timeout']):
            return ErrorType.NETWORK_ERROR
        elif any(keyword in error_lower for keyword in ['database', 'sql', 'connection']):
            return ErrorType.DATABASE_ERROR
        elif any(keyword in error_lower for keyword in ['permission', 'access', 'denied']):
            return ErrorType.PERMISSION_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR

    async def _validate_expected_files(self, expected_files: List[str]) -> ValidationResult:
        """Validate that expected files exist."""
        missing_files = []
        for file_path in expected_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)

        if missing_files:
            return ValidationResult.failure(f"Missing expected files: {', '.join(missing_files)}")

        return ValidationResult.success(f"All {len(expected_files)} expected files exist")

    def _record_task_execution(
        self,
        task_id: str,
        task_input: Dict[str, Any],
        result: Dict[str, Any],
        started_at: datetime
    ):
        """Record task execution for analysis and debugging."""
        completed_at = datetime.now()
        execution_record = {
            "task_id": task_id,
            "task_type": task_input.get("type", "unknown"),
            "agent_type": self.__class__.__name__,
            "started_at": started_at.isoformat(),
            "completed_at": completed_at.isoformat(),
            "duration_seconds": (completed_at - started_at).total_seconds(),
            "success": result.get("success", False),
            "retry_count": task_input.get("retry_count", 0),
            "error": result.get("error"),
            "metadata": result.get("metadata", {})
        }

        self._task_history.append(execution_record)

        # Keep only last 100 records
        if len(self._task_history) > 100:
            self._task_history = self._task_history[-100:]

    def _create_error_result(self, task_id: str, error: str) -> Dict[str, Any]:
        """Create a standardized error result."""
        return {
            "task_id": task_id,
            "success": False,
            "error": error,
            "metadata": {"exception_type": "ExecutionError"}
        }

    # Abstract methods to be implemented by subclasses
    @abstractmethod
    async def _execute_core(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        Core task execution logic - must be implemented by subclasses.

        Args:
            task_input: Task input dictionary

        Returns:
            Dict containing execution result
        """
        raise NotImplementedError

    @abstractmethod
    async def _validate_agent_specific_prerequisites(self, task_input: Dict[str, Any]) -> ValidationResult:
        """Agent-specific prerequisite validation."""
        return ValidationResult.success("No specific validation required")

    @abstractmethod
    async def _validate_agent_specific_completion(self, task_input: Dict[str, Any], result: Dict[str, Any]) -> ValidationResult:
        """Agent-specific completion validation."""
        return ValidationResult.success("No specific validation required")

    # Recovery methods (can be overridden by subclasses)
    async def _recover_dependency_error(self, task_input: Dict[str, Any], error: str) -> RecoveryResult:
        """Recover from dependency-related errors."""
        return RecoveryResult(
            success=False,
            actions_taken="Dependency error recovery not implemented for this agent",
            retry_recommended=False
        )

    async def _recover_configuration_error(self, task_input: Dict[str, Any], error: str) -> RecoveryResult:
        """Recover from configuration-related errors."""
        return RecoveryResult(
            success=False,
            actions_taken="Configuration error recovery not implemented for this agent",
            retry_recommended=False
        )

    async def _recover_syntax_error(self, task_input: Dict[str, Any], error: str) -> RecoveryResult:
        """Recover from syntax-related errors."""
        return RecoveryResult(
            success=False,
            actions_taken="Syntax error recovery not implemented for this agent",
            retry_recommended=False
        )

    # Status and monitoring methods
    async def get_status(self) -> Dict[str, Any]:
        """Get current agent status."""
        return {
            "agent_id": self._agent_id,
            "agent_type": self.__class__.__name__,
            "is_active": self.is_active,
            "current_task_id": self.current_task_id,
            "success_count": self._success_count,
            "error_count": self._error_count,
            "success_rate": self.success_rate,
            "task_history_count": len(self._task_history),
            "features_enabled": {
                "validation": self.enable_validation,
                "error_recovery": self.enable_error_recovery,
                "task_history": self.enable_task_history
            }
        }

    async def get_task_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent task execution history."""
        return self._task_history[-limit:] if self._task_history else []