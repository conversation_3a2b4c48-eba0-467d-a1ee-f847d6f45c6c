#!/bin/bash

# AI Coding Agent Production Startup Script
# This script starts the production environment with all services optimized for production
# Usage: ./scripts/start-prod.sh

set -e

echo " AI Coding Agent - Production Setup"
echo "====================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo " Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if required files exist
if [ ! -f "docker-compose.yml" ]; then
    echo " docker-compose.yml not found. Please run from project root."
    exit 1
fi

echo " Found Docker Compose configuration files"
echo " Using single-file configuration:"
echo "   • docker-compose.yml (base + production services)"
echo "   → code-server runs as templates only (no direct access)"
echo "   → All services use internal networking via Traefik"
echo ""

# Start the production environment
# -f docker-compose.yml: Production configuration with all services
# up -d: Start in detached mode (background)
# --build: Rebuild images if needed
# Note: No --watch flag for production (no hot-reloading)
docker-compose -f docker-compose.yml up -d --build

echo ""
echo " Production environment started successfully!"
echo ""
echo " Access points (via Traefik reverse proxy):"
echo "   API:              http://api.localhost"
echo "   User Portal:      http://portal.localhost"
echo "   Traefik Dashboard: http://traefik.localhost"
echo ""
echo " Production features:"
echo "   • Security hardening enabled"
echo "   • No exposed ports (internal networking only)"
echo "   • Optimized resource limits"
echo "   • Production logging levels"
echo "   • code-server templates for dynamic workspace provisioning"
echo ""
echo " Note: Individual code-server instances are created dynamically via the API"
echo ""
echo " To stop: docker-compose -f docker-compose.yml down"
echo " To view logs: docker-compose -f docker-compose.yml logs -f"
