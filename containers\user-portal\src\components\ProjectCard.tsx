"use client";
import { useState } from 'react';
import { Project } from '../types/project';
import toast from 'react-hot-toast';

interface ProjectCardProps {
  project: Project;
  onScan: () => void;
}

export function ProjectCard({ project, onScan }: ProjectCardProps) {
  const [isLaunching, setIsLaunching] = useState(false);

  const getEmbeddingStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'scanning': case 'indexing': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEmbeddingStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'uninitialized': return 'Not Scanned';
      case 'scanning': return 'Scanning';
      case 'indexing': return 'Indexing';
      case 'completed': return 'Ready';
      case 'failed': return 'Failed';
      default: return status;
    }
  };

  const handleLaunchEnvironment = async () => {
    setIsLaunching(true);
    toast.loading('Launching environment, this may take a moment...');

    try {
      const response = await fetch(`/api/projects/${project.id}/launch`, { method: 'POST' });
      const data = await response.json();

      toast.dismiss();

      if (response.ok && data.url) {
        toast.success('Environment launched! Redirecting...');
        window.location.href = data.url;
      } else {
        throw new Error(data.error || 'Failed to launch environment.');
      }
    } catch (error) {
      toast.dismiss();
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
      toast.error(`Launch failed: ${errorMessage}`);
      setIsLaunching(false);
    }
  };

  const embeddingStatusText = getEmbeddingStatusText(project.status || 'unknown');
  const canScan = !['scanning', 'indexing'].includes(project.status.toLowerCase());

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg flex flex-col">
      <div className="p-6 flex-grow">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-medium text-gray-900 truncate">{project.name}</h3>
            {project.description && <p className="mt-1 text-sm text-gray-500 truncate">{project.description}</p>}
          </div>
          <div className="ml-4 flex-shrink-0 flex flex-col items-end space-y-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${project.environment_status === 'Running' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
              <svg className={`-ml-0.5 mr-1.5 h-2 w-2 ${project.environment_status === 'Running' ? 'text-green-400' : 'text-gray-400'}`} fill="currentColor" viewBox="0 0 8 8">
                <circle cx="4" cy="4" r="3" />
              </svg>
              {project.environment_status || 'Stopped'}
            </span>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEmbeddingStatusColor(project.status)}`}>
              {embeddingStatusText}
            </span>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Scan Progress</span>
            <span>{Number(project.progress_percentage || 0)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-blue-600 h-2 rounded-full transition-all duration-300" style={{ width: `${Number(project.progress_percentage || 0)}%` }}></div>
          </div>
        </div>
      </div>

      <div className="p-6 bg-gray-50 border-t border-gray-200">
        {project.environment_status === 'Running' ? (
          <a
            href={project.environment_url}
            target="_blank"
            rel="noopener noreferrer"
            className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Open Environment
          </a>
        ) : (
          <button
            onClick={handleLaunchEnvironment}
            disabled={isLaunching}
            className={`w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md ${
              isLaunching
                ? 'text-gray-400 bg-gray-100 cursor-not-allowed'
                : 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
            }`}
          >
            {isLaunching && (
              <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            {isLaunching ? 'Launching...' : 'Launch Environment'}
          </button>
        )}

        <button
            onClick={onScan}
            disabled={!canScan || isLaunching}
            className={`mt-2 w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
              !canScan || isLaunching
                ? 'text-gray-400 bg-gray-100 cursor-not-allowed'
                : 'text-gray-700 bg-white hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500'
            }`}
          >
            {project.status.toLowerCase() === 'scanning' ? 'Scanning...' : 'Scan Files'}
        </button>
      </div>
    </div>
  );
}
