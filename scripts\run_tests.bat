@echo off
REM Windows batch script to run tests with proper Python path setup
REM This script ensures the Python environment is correctly configured

echo === AI Coding Agent Test Runner ===

REM Set the working directory to the script location
cd /d "%~dp0"

REM Set environment variables for testing
set ENVIRONMENT=testing
set USE_SUPABASE=false
set ENABLE_ROLE_CONFIG_INIT=false
set ENABLE_WEBSOCKET_CHAT=false
set DATABASE_URL=postgresql://test:test@localhost:5432/test_db
set REDIS_URL=redis://localhost:6379
set OLLAMA_BASE_URL=http://localhost:11434
set POSTGRES_PASSWORD=test_password
set JWT_SECRET=test_jwt_secret_key_for_testing_only_32_chars_minimum
set SUPABASE_URL=https://test.supabase.co
set SUPABASE_KEY=test_supabase_key
set SUPABASE_ANON_KEY=test_supabase_anon_key
set SUPABASE_SERVICE_KEY=test_supabase_service_key
set PYTHONIOENCODING=utf-8
set PYTH<PERSON>LEGACYWINDOWSSTDIO=1

REM Set Python path to include source directories
set PYTHONPATH=%cd%\containers\ai-orchestrator\src;%cd%\containers\ai-orchestrator;%cd%

echo Python path set to: %PYTHONPATH%
echo.

REM Check if first argument is provided
if "%1"=="" (
    echo Running all tests...
    python -m pytest tests/ -v
) else if "%1"=="import-test" (
    echo Running import test...
    python test_imports.py
) else if "%1"=="setup" (
    echo Running setup test environment...
    python setup_test_environment.py
) else (
    echo Running specific test: %*
    python -m pytest %*
)

echo.
echo === Test run complete ===
pause
