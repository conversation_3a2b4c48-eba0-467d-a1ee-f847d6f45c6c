# Project: AI Coding Agent
# Purpose: Comprehensive validation rules engine for task and phase validation

import logging
import subprocess
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Optional, Any, Type

from src.models.validation_models import (
    Task, Step, Phase, ValidationResult, TaskType, TaskResult
)


class ValidationRule(ABC):
    """Abstract base class for validation rules"""

    def __init__(self, rule_id: str, description: str, severity: str = "error"):
        self.rule_id = rule_id
        self.description = description
        self.severity = severity  # error, warning, info
        self.logger = logging.getLogger(f"validation_rule.{rule_id}")

    @abstractmethod
    async def validate(self, context: Dict[str, Any]) -> ValidationResult:
        """Execute the validation rule"""
        pass

    def is_applicable(self, context: Dict[str, Any]) -> bool:
        """Check if this rule applies to the given context"""
        return True


class FileExistsRule(ValidationRule):
    """Validates that required files exist"""

    def __init__(self, file_patterns: List[str], project_root: str = "/workspace"):
        super().__init__("file_exists", "Required files must exist")
        self.file_patterns = file_patterns
        self.project_root = Path(project_root)

    async def validate(self, context: Dict[str, Any]) -> ValidationResult:
        missing_files = []

        for pattern in self.file_patterns:
            if pattern.startswith('/') or '\\' in pattern:
                # Absolute path or Windows path
                file_path = Path(pattern)
            else:
                # Relative to project root
                file_path = self.project_root / pattern

            if not file_path.exists():
                missing_files.append(str(pattern))

        if missing_files:
            return ValidationResult.failure(
                error=f"Missing required files: {', '.join(missing_files)}"
            )

        return ValidationResult.success(f"All required files exist: {len(self.file_patterns)} files validated")


class CodeSyntaxRule(ValidationRule):
    """Validates code syntax for various programming languages"""

    def __init__(self, code_files: List[str], project_root: str = "/workspace"):
        super().__init__("code_syntax", "Code files must have valid syntax")
        self.code_files = code_files
        self.project_root = Path(project_root)

    async def validate(self, context: Dict[str, Any]) -> ValidationResult:
        syntax_errors = []

        for file_path in self.code_files:
            full_path = self.project_root / file_path if not Path(file_path).is_absolute() else Path(file_path)

            if not full_path.exists():
                syntax_errors.append(f"File not found: {file_path}")
                continue

            # Validate based on file extension
            if full_path.suffix == '.py':
                error = await self._validate_python_syntax(full_path)
            elif full_path.suffix in ['.js', '.jsx', '.ts', '.tsx']:
                error = await self._validate_javascript_syntax(full_path)
            else:
                continue  # Skip unknown file types

            if error:
                syntax_errors.append(f"{file_path}: {error}")

        if syntax_errors:
            return ValidationResult.failure(
                error=f"Syntax errors found: {'; '.join(syntax_errors)}"
            )

        return ValidationResult.success(f"All code files have valid syntax: {len(self.code_files)} files validated")

    async def _validate_python_syntax(self, file_path: Path) -> Optional[str]:
        """Validate Python syntax"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                code = f.read()
            compile(code, str(file_path), 'exec')
            return None
        except SyntaxError as e:
            return f"Python syntax error at line {e.lineno}: {e.msg}"
        except Exception as e:
            return f"Error reading file: {str(e)}"

    async def _validate_javascript_syntax(self, file_path: Path) -> Optional[str]:
        """Validate JavaScript/TypeScript syntax using Node.js"""
        try:
            result = subprocess.run(
                ['node', '-c', str(file_path)],
                capture_output=True,
                text=True,
                timeout=30
            )
            return None if result.returncode == 0 else f"JavaScript syntax error: {result.stderr}"
        except subprocess.TimeoutExpired:
            return "JavaScript validation timeout"
        except FileNotFoundError:
            return None  # Node.js not available, skip validation


class FunctionalTestRule(ValidationRule):
    """Validates that functional tests pass"""

    def __init__(self, test_commands: List[str], project_root: str = "/workspace"):
        super().__init__("functional_test", "Functional tests must pass")
        self.test_commands = test_commands
        self.project_root = Path(project_root)

    async def validate(self, context: Dict[str, Any]) -> ValidationResult:
        test_failures = []

        for command in self.test_commands:
            try:
                result = subprocess.run(
                    command.split(),
                    capture_output=True,
                    text=True,
                    timeout=300,  # 5 minutes
                    cwd=str(self.project_root)
                )

                if result.returncode != 0:
                    test_failures.append(f"Command '{command}' failed: {result.stderr or result.stdout}")

            except subprocess.TimeoutExpired:
                test_failures.append(f"Command '{command}' timed out")
            except Exception as e:
                test_failures.append(f"Command '{command}' error: {str(e)}")

        if test_failures:
            return ValidationResult.failure(
                error=f"Test failures: {'; '.join(test_failures)}"
            )

        return ValidationResult.success(f"All functional tests passed: {len(self.test_commands)} commands executed")


class IntegrationRule(ValidationRule):
    """Validates integration points between components"""

    def __init__(self, integration_checks: List[str]):
        super().__init__("integration", "Integration points must be functional")
        self.integration_checks = integration_checks

    async def validate(self, context: Dict[str, Any]) -> ValidationResult:
        integration_failures = []

        for check in self.integration_checks:
            try:
                if check.startswith('http'):
                    # HTTP endpoint check
                    result = await self._validate_http_endpoint(check)
                elif check.startswith('db:'):
                    # Database check
                    result = await self._validate_database_connection(check[3:])
                elif check.startswith('api:'):
                    # API integration check
                    result = await self._validate_api_integration(check[4:])
                else:
                    # Generic integration check
                    result = ValidationResult.success(f"Integration check noted: {check}")

                if not result.is_valid:
                    integration_failures.append(f"{check}: {result.error}")

            except Exception as e:
                integration_failures.append(f"{check}: {str(e)}")

        if integration_failures:
            return ValidationResult.failure(
                error=f"Integration failures: {'; '.join(integration_failures)}"
            )

        return ValidationResult.success(f"All integrations validated: {len(self.integration_checks)} checks passed")

    async def _validate_http_endpoint(self, url: str) -> ValidationResult:
        """Validate HTTP endpoint"""
        try:
            import aiohttp
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status < 400:
                        return ValidationResult.success(f"HTTP endpoint accessible: {url}")
                    else:
                        return ValidationResult.failure(f"HTTP endpoint error: {response.status}")
        except Exception as e:
            return ValidationResult.failure(f"HTTP endpoint check failed: {str(e)}")

    async def _validate_database_connection(self, connection_string: str) -> ValidationResult:
        """Validate database connection (placeholder)"""
        # This would be implemented based on specific database
        return ValidationResult.success(f"Database connection check: {connection_string}")

    async def _validate_api_integration(self, api_spec: str) -> ValidationResult:
        """Validate API integration (placeholder)"""
        return ValidationResult.success(f"API integration check: {api_spec}")


class ComponentValidationRule(ValidationRule):
    """Validates component-specific requirements"""

    def __init__(self, component_type: str, requirements: Dict[str, Any]):
        super().__init__(f"component_{component_type}", f"Component {component_type} validation")
        self.component_type = component_type
        self.requirements = requirements

    async def validate(self, context: Dict[str, Any]) -> ValidationResult:
        if self.component_type == "react_component":
            return await self._validate_react_component(context)
        elif self.component_type == "api_endpoint":
            return await self._validate_api_endpoint(context)
        elif self.component_type == "database_table":
            return await self._validate_database_table(context)
        else:
            return ValidationResult.success(f"Component type {self.component_type} validation not implemented")

    async def _validate_react_component(self, context: Dict[str, Any]) -> ValidationResult:
        """Validate React component requirements"""
        checks = []

        # Check component file exists
        if 'component_path' in self.requirements:
            component_path = Path(self.requirements['component_path'])
            if not component_path.exists():
                checks.append(f"Component file not found: {component_path}")

        # Check props interface (if TypeScript)
        if 'props_interface' in self.requirements:
            # This would check for TypeScript interface definition
            pass

        # Check component exports
        if 'export_name' in self.requirements:
            # This would verify the component is properly exported
            pass

        if checks:
            return ValidationResult.failure(error="; ".join(checks))

        return ValidationResult.success("React component validation passed")

    async def _validate_api_endpoint(self, context: Dict[str, Any]) -> ValidationResult:
        """Validate API endpoint requirements"""
        checks = []

        # Check endpoint registration
        if 'endpoint_url' in self.requirements:
            # This would check if the endpoint is properly registered
            pass

        # Check request/response models
        if 'models' in self.requirements:
            # This would validate Pydantic models
            pass

        if checks:
            return ValidationResult.failure(error="; ".join(checks))

        return ValidationResult.success("API endpoint validation passed")

    async def _validate_database_table(self, context: Dict[str, Any]) -> ValidationResult:
        """Validate database table requirements"""
        checks = []

        # Check table exists
        if 'table_name' in self.requirements:
            # This would check database schema
            pass

        # Check columns
        if 'columns' in self.requirements:
            # This would validate table structure
            pass

        if checks:
            return ValidationResult.failure(error="; ".join(checks))

        return ValidationResult.success("Database table validation passed")


class SecurityValidationRule(ValidationRule):
    """Validates security requirements"""

    def __init__(self, security_checks: List[str]):
        super().__init__("security", "Security requirements validation")
        self.security_checks = security_checks

    async def validate(self, context: Dict[str, Any]) -> ValidationResult:
        security_issues = []

        for check in self.security_checks:
            if check == "no_hardcoded_secrets":
                issues = await self._check_hardcoded_secrets(context)
                security_issues.extend(issues)
            elif check == "input_validation":
                issues = await self._check_input_validation(context)
                security_issues.extend(issues)
            elif check == "authentication":
                issues = await self._check_authentication(context)
                security_issues.extend(issues)

        if security_issues:
            return ValidationResult.failure(
                error=f"Security issues found: {'; '.join(security_issues)}"
            )

        return ValidationResult.success("Security validation passed")

    async def _check_hardcoded_secrets(self, context: Dict[str, Any]) -> List[str]:
        """Check for hardcoded secrets in code"""
        issues = []

        # This would scan code files for potential secrets
        # Common patterns: API keys, passwords, tokens
        # Implementation would scan project files

        return issues

    async def _check_input_validation(self, context: Dict[str, Any]) -> List[str]:
        """Check for proper input validation"""
        issues = []

        # This would check API endpoints for input validation

        return issues

    async def _check_authentication(self, context: Dict[str, Any]) -> List[str]:
        """Check authentication implementation"""
        issues = []

        # This would verify authentication is properly implemented

        return issues


class PerformanceValidationRule(ValidationRule):
    """Validates performance requirements"""

    def __init__(self, performance_thresholds: Dict[str, float]):
        super().__init__("performance", "Performance requirements validation")
        self.performance_thresholds = performance_thresholds

    async def validate(self, context: Dict[str, Any]) -> ValidationResult:
        performance_issues = []

        # Check build time
        if 'build_time_seconds' in self.performance_thresholds:
            build_time = await self._measure_build_time()
            threshold = self.performance_thresholds['build_time_seconds']
            if build_time > threshold:
                performance_issues.append(f"Build time {build_time:.2f}s exceeds threshold {threshold}s")

        # Check bundle size
        if 'bundle_size_mb' in self.performance_thresholds:
            bundle_size = await self._measure_bundle_size()
            threshold = self.performance_thresholds['bundle_size_mb']
            if bundle_size > threshold:
                performance_issues.append(f"Bundle size {bundle_size:.2f}MB exceeds threshold {threshold}MB")

        # Check API response time
        if 'api_response_ms' in self.performance_thresholds:
            response_time = await self._measure_api_response_time()
            threshold = self.performance_thresholds['api_response_ms']
            if response_time > threshold:
                performance_issues.append(f"API response time {response_time:.2f}ms exceeds threshold {threshold}ms")

        if performance_issues:
            return ValidationResult.failure(
                error=f"Performance issues: {'; '.join(performance_issues)}"
            )

        return ValidationResult.success("Performance validation passed")

    async def _measure_build_time(self) -> float:
        """Measure build time"""
        # This would actually measure build time
        return 0.0

    async def _measure_bundle_size(self) -> float:
        """Measure bundle size"""
        # This would measure actual bundle size
        return 0.0

    async def _measure_api_response_time(self) -> float:
        """Measure API response time"""
        # This would measure actual API response times
        return 0.0


class ValidationRuleEngine:
    """Engine for managing and executing validation rules"""

    def __init__(self):
        self.logger = logging.getLogger("validation_rule_engine")
        self.rules_registry: Dict[str, Type[ValidationRule]] = {
            "file_exists": FileExistsRule,
            "code_syntax": CodeSyntaxRule,
            "functional_test": FunctionalTestRule,
            "integration": IntegrationRule,
            "component": ComponentValidationRule,
            "security": SecurityValidationRule,
            "performance": PerformanceValidationRule
        }

        # Predefined rule sets for different contexts
        self.rule_sets = {
            TaskType.CREATE_COMPONENT: [
                "file_exists",
                "code_syntax",
                "component",
                "security"
            ],
            TaskType.CREATE_API_ENDPOINT: [
                "file_exists",
                "code_syntax",
                "component",
                "integration",
                "security"
            ],
            TaskType.DATABASE_MIGRATION: [
                "file_exists",
                "code_syntax",
                "integration"
            ],
            TaskType.SETUP_ROUTING: [
                "code_syntax",
                "integration"
            ],
            TaskType.TESTING: [
                "functional_test",
                "integration"
            ]
        }

    def register_rule(self, rule_name: str, rule_class: Type[ValidationRule]):
        """Register a new validation rule"""
        self.rules_registry[rule_name] = rule_class
        self.logger.info(f"Registered validation rule: {rule_name}")

    async def validate_task(self, task: Task, task_result: Optional[TaskResult] = None) -> ValidationResult:
        """Validate a task using appropriate rules"""
        context = {
            "task": task,
            "task_result": task_result,
            "task_type": task.type,
            "agent_type": task.agent_type,
            "project_root": "/workspace"
        }

        # Get applicable rules for this task type
        rule_names = self.rule_sets.get(task.type, [])

        # Add rules from task configuration
        if task.expected_files:
            rule_names.append("file_exists")
        if task.code_files:
            rule_names.append("code_syntax")
        if task.test_command:
            rule_names.append("functional_test")
        if task.integration_checks:
            rule_names.append("integration")

        return await self._execute_rules(rule_names, context, task)

    async def validate_step(self, step: Step) -> ValidationResult:
        """Validate a step by validating all its tasks"""
        task_results = []

        for task in step.tasks:
            result = await self.validate_task(task)
            task_results.append(result)

        return ValidationResult.from_checks(task_results)

    async def validate_phase(self, phase: Phase) -> ValidationResult:
        """Validate a phase by validating all its steps"""
        step_results = []

        for step in phase.steps:
            result = await self.validate_step(step)
            step_results.append(result)

        return ValidationResult.from_checks(step_results)

    async def _execute_rules(self, rule_names: List[str], context: Dict[str, Any], task: Task) -> ValidationResult:
        """Execute a set of validation rules"""
        results = []

        for rule_name in rule_names:
            if rule_name not in self.rules_registry:
                self.logger.warning(f"Unknown validation rule: {rule_name}")
                continue

            try:
                # Create rule instance with appropriate parameters
                rule = await self._create_rule_instance(rule_name, task, context)

                if rule and rule.is_applicable(context):
                    result = await rule.validate(context)
                    results.append(result)

            except Exception as e:
                self.logger.error(f"Error executing rule {rule_name}: {str(e)}")
                results.append(ValidationResult.failure(
                    error=f"Rule execution error: {rule_name} - {str(e)}"
                ))

        return ValidationResult.from_checks(results)

    async def _create_rule_instance(self, rule_name: str, task: Task, context: Dict[str, Any]) -> Optional[ValidationRule]:
        """Create a rule instance with appropriate parameters"""
        rule_class = self.rules_registry[rule_name]

        try:
            if rule_name == "file_exists":
                return rule_class(task.expected_files or [], context.get("project_root", "/workspace"))
            elif rule_name == "code_syntax":
                return rule_class(task.code_files or [], context.get("project_root", "/workspace"))
            elif rule_name == "functional_test":
                commands = [task.test_command] if task.test_command else []
                return rule_class(commands, context.get("project_root", "/workspace"))
            elif rule_name == "integration":
                return rule_class(task.integration_checks or [])
            elif rule_name == "component":
                component_type = task.parameters.get("component_type", "generic")
                requirements = task.parameters.get("requirements", {})
                return rule_class(component_type, requirements)
            elif rule_name == "security":
                security_checks = task.parameters.get("security_checks", ["no_hardcoded_secrets"])
                return rule_class(security_checks)
            elif rule_name == "performance":
                thresholds = task.parameters.get("performance_thresholds", {})
                return rule_class(thresholds)
            else:
                # Generic rule instantiation
                return rule_class()

        except Exception as e:
            self.logger.error(f"Error creating rule instance {rule_name}: {str(e)}")
            return None

    def get_available_rules(self) -> List[str]:
        """Get list of available validation rules"""
        return list(self.rules_registry.keys())

    def get_rule_sets(self) -> Dict[str, List[str]]:
        """Get predefined rule sets for different contexts"""
        return self.rule_sets.copy()