#!/bin/bash

# Supabase Deployment Script for AI Coding Agent
# This script automates the deployment of Supabase integration with proper configuration,
# security setup, and validation checks.

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$PROJECT_ROOT/deployment_supabase_${TIMESTAMP}.log"

# Default configuration
ENVIRONMENT="development"
DEPLOY_MODE="supabase"
VALIDATE_DEPLOYMENT="true"
BACKUP_EXISTING="true"
GENERATE_KEYS="true"
RUN_MIGRATIONS="true"
START_SERVICES="true"

# Function definitions
print_banner() {
    echo -e "${PURPLE}"
    echo ""
    echo "                   Supabase Deployment                     "
    echo "              AI Coding Agent Integration                     "
    echo "                                                              "
    echo "  • Authentication & User Management                          "
    echo "  • Vector Storage & Semantic Search                          "
    echo "  • RAG (Retrieval-Augmented Generation)                     "
    echo "  • Row Level Security (RLS)                                  "
    echo "  • Kong API Gateway                                          "
    echo ""
    echo -e "${NC}"
}

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "${BLUE}$*${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$*${NC}"
}

log_warning() {
    log "WARNING" "${YELLOW}$*${NC}"
}

log_error() {
    log "ERROR" "${RED}$*${NC}"
}

show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Deploy Supabase integration for AI Coding Agent"
    echo
    echo "OPTIONS:"
    echo "  -e, --environment ENV     Environment (development|staging|production) [default: development]"
    echo "  -m, --mode MODE          Deployment mode (supabase|hybrid|migration) [default: supabase]"
    echo "  --no-validation          Skip deployment validation"
    echo "  --no-backup              Skip existing data backup"
    echo "  --no-keys                Skip key generation"
    echo "  --no-migrations          Skip database migrations"
    echo "  --no-start               Don't start services after deployment"
    echo "  --dry-run                Show what would be done without executing"
    echo "  --help                   Show this help message"
    echo
    echo "MODES:"
    echo "  supabase    - Full Supabase deployment (new installation)"
    echo "  hybrid      - Run both PostgreSQL and Supabase (migration mode)"
    echo "  migration   - Migrate existing PostgreSQL data to Supabase"
    echo
    echo "Examples:"
    echo "  $0 --environment development --mode supabase"
    echo "  $0 --environment production --mode migration --no-start"
    echo "  $0 --dry-run --environment staging"
}

check_prerequisites() {
    log_info "Checking prerequisites..."

    local missing_deps=()

    # Check for required tools
    local required_tools=("docker" "docker-compose" "python3" "openssl")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_deps+=("$tool")
        fi
    done

    # Check for Python packages
    if ! python3 -c "import supabase" &> /dev/null; then
        missing_deps+=("python3-supabase")
    fi

    if ! python3 -c "import asyncpg" &> /dev/null; then
        missing_deps+=("python3-asyncpg")
    fi

    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Please install missing dependencies:"
        echo "  Docker: https://docs.docker.com/get-docker/"
        echo "  Python packages: pip install supabase asyncpg pgvector"
        exit 1
    fi

    # Check Docker daemon
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi

    log_success "Prerequisites check passed"
}

backup_existing_data() {
    if [ "$BACKUP_EXISTING" != "true" ]; then
        log_info "Skipping existing data backup"
        return 0
    fi

    log_info "Backing up existing data..."

    local backup_dir="$PROJECT_ROOT/backups/pre_supabase_${TIMESTAMP}"
    mkdir -p "$backup_dir"

    # Backup environment files
    if [ -f "$PROJECT_ROOT/.env" ]; then
        cp "$PROJECT_ROOT/.env" "$backup_dir/.env.backup"
        log_info "Environment file backed up"
    fi

    # Backup database if PostgreSQL is running
    if docker-compose -f "$PROJECT_ROOT/docker-compose.yml" ps postgresql | grep -q "Up"; then
        log_info "Backing up PostgreSQL database..."
        docker-compose -f "$PROJECT_ROOT/docker-compose.yml" exec -T postgresql pg_dump \
            -U postgres -d ai_coding_agent --clean --if-exists > "$backup_dir/postgresql_backup.sql" || true

        if [ -f "$backup_dir/postgresql_backup.sql" ]; then
            gzip "$backup_dir/postgresql_backup.sql"
            log_success "PostgreSQL backup created"
        fi
    fi

    # Backup volumes
    if [ -d "$PROJECT_ROOT/volumes" ]; then
        tar -czf "$backup_dir/volumes_backup.tar.gz" -C "$PROJECT_ROOT" volumes/ || true
        log_info "Volumes backed up"
    fi

    log_success "Backup completed: $backup_dir"
}

generate_supabase_keys() {
    if [ "$GENERATE_KEYS" != "true" ]; then
        log_info "Skipping key generation"
        return 0
    fi

    log_info "Generating Supabase keys and secrets..."

    # Run the key generation script
    if [ -f "$PROJECT_ROOT/scripts/generate-supabase-keys.sh" ]; then
        chmod +x "$PROJECT_ROOT/scripts/generate-supabase-keys.sh"
        cd "$PROJECT_ROOT"

        if [ "$DRY_RUN" = "true" ]; then
            log_info "[DRY RUN] Would generate Supabase keys"
        else
            "$PROJECT_ROOT/scripts/generate-supabase-keys.sh" generate
            log_success "Supabase keys generated"
        fi
    else
        log_warning "Key generation script not found, using default keys"

        # Generate basic keys
        local jwt_secret=$(openssl rand -base64 32)
        local postgres_password=$(openssl rand -base64 16)

        log_info "Generated basic secrets"
        echo "JWT_SECRET=$jwt_secret" >> "$PROJECT_ROOT/.env.supabase.generated"
        echo "POSTGRES_PASSWORD=$postgres_password" >> "$PROJECT_ROOT/.env.supabase.generated"
    fi
}

setup_environment() {
    log_info "Setting up environment configuration..."

    cd "$PROJECT_ROOT"

    # Copy base environment file if it doesn't exist
    if [ ! -f ".env" ]; then
        if [ -f ".env.supabase" ]; then
            cp ".env.supabase" ".env"
            log_info "Copied Supabase environment template"
        elif [ -f ".env.example" ]; then
            cp ".env.example" ".env"
            log_info "Copied example environment file"
        else
            log_error "No environment template found"
            exit 1
        fi
    fi

    # Update environment for Supabase
    if [ "$DRY_RUN" != "true" ]; then
        # Enable Supabase
        if grep -q "USE_SUPABASE=" .env; then
            sed -i 's/USE_SUPABASE=.*/USE_SUPABASE=true/' .env
        else
            echo "USE_SUPABASE=true" >> .env
        fi

        # Set environment
        if grep -q "ENVIRONMENT=" .env; then
            sed -i "s/ENVIRONMENT=.*/ENVIRONMENT=$ENVIRONMENT/" .env
        else
            echo "ENVIRONMENT=$ENVIRONMENT" >> .env
        fi

        log_success "Environment configured for Supabase"
    else
        log_info "[DRY RUN] Would configure environment for Supabase"
    fi
}

setup_docker_network() {
    log_info "Setting up Docker network..."

    if [ "$DRY_RUN" = "true" ]; then
        log_info "[DRY RUN] Would create Docker network"
        return 0
    fi

    # Create network if it doesn't exist
    if ! docker network ls | grep -q "ai-coding-agent-network"; then
        docker network create ai-coding-agent-network \
            --driver bridge \
            --subnet=**********/24 \
            --gateway=********** || true
        log_success "Docker network created"
    else
        log_info "Docker network already exists"
    fi
}

run_database_migrations() {
    if [ "$RUN_MIGRATIONS" != "true" ]; then
        log_info "Skipping database migrations"
        return 0
    fi

    log_info "Running database migrations..."

    if [ "$DRY_RUN" = "true" ]; then
        log_info "[DRY RUN] Would run database migrations"
        return 0
    fi

    # Wait for Supabase database to be ready
    log_info "Waiting for Supabase database to be ready..."
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f docker-compose.yml -f docker-compose.supabase.yml \
           exec -T supabase-db pg_isready -U postgres -d ai_coding_agent &> /dev/null; then
            break
        fi

        log_info "Waiting for database... (attempt $attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done

    if [ $attempt -gt $max_attempts ]; then
        log_error "Database failed to become ready"
        return 1
    fi

    # Run migrations
    log_info "Executing database migrations..."

    # Initial schema migration
    if [ -f "$PROJECT_ROOT/supabase/migrations/001_initial_schema.sql" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.supabase.yml \
            exec -T supabase-db psql -U postgres -d ai_coding_agent \
            -f /docker-entrypoint-initdb.d/001_initial_schema.sql || true
        log_success "Initial schema migration completed"
    fi

    # Vector functions migration
    if [ -f "$PROJECT_ROOT/supabase/migrations/002_vector_functions.sql" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.supabase.yml \
            exec -T supabase-db psql -U postgres -d ai_coding_agent \
            -f /docker-entrypoint-initdb.d/002_vector_functions.sql || true
        log_success "Vector functions migration completed"
    fi

    log_success "Database migrations completed"
}

deploy_supabase_services() {
    log_info "Deploying Supabase services..."

    cd "$PROJECT_ROOT"

    if [ "$DRY_RUN" = "true" ]; then
        log_info "[DRY RUN] Would deploy Supabase services"
        return 0
    fi

    # Stop existing services if running
    log_info "Stopping existing services..."
    docker-compose down --remove-orphans || true

    # Choose deployment strategy based on mode
    case "$DEPLOY_MODE" in
        "supabase")
            log_info "Deploying full Supabase stack..."
            docker-compose -f docker-compose.yml -f docker-compose.supabase.yml up -d --build
            ;;
        "hybrid")
            log_info "Deploying hybrid PostgreSQL + Supabase stack..."
            docker-compose -f docker-compose.yml up -d postgresql redis
            docker-compose -f docker-compose.supabase.yml up -d --build
            ;;
        "migration")
            log_info "Deploying for migration (both databases)..."
            docker-compose -f docker-compose.yml up -d postgresql
            docker-compose -f docker-compose.supabase.yml up -d --build
            ;;
        *)
            log_error "Unknown deployment mode: $DEPLOY_MODE"
            exit 1
            ;;
    esac

    log_success "Supabase services deployed"
}

wait_for_services() {
    log_info "Waiting for services to be healthy..."

    if [ "$DRY_RUN" = "true" ]; then
        log_info "[DRY RUN] Would wait for services"
        return 0
    fi

    local max_wait=300  # 5 minutes
    local wait_time=0
    local check_interval=10

    local services=("supabase-db" "supabase-auth" "supabase-rest" "supabase-kong")

    while [ $wait_time -lt $max_wait ]; do
        local all_healthy=true

        for service in "${services[@]}"; do
            if ! docker-compose -f docker-compose.yml -f docker-compose.supabase.yml \
                 ps "$service" | grep -q "healthy\|Up"; then
                all_healthy=false
                break
            fi
        done

        if [ "$all_healthy" = "true" ]; then
            log_success "All Supabase services are healthy"
            return 0
        fi

        log_info "Waiting for services to be ready... (${wait_time}s/${max_wait}s)"
        sleep $check_interval
        wait_time=$((wait_time + check_interval))
    done

    log_error "Services failed to become healthy within ${max_wait} seconds"
    return 1
}

validate_deployment() {
    if [ "$VALIDATE_DEPLOYMENT" != "true" ]; then
        log_info "Skipping deployment validation"
        return 0
    fi

    log_info "Validating Supabase deployment..."

    if [ "$DRY_RUN" = "true" ]; then
        log_info "[DRY RUN] Would validate deployment"
        return 0
    fi

    local validation_errors=0

    # Test database connectivity
    log_info "Testing database connectivity..."
    if docker-compose -f docker-compose.yml -f docker-compose.supabase.yml \
       exec -T supabase-db pg_isready -U postgres -d ai_coding_agent &> /dev/null; then
        log_success " Database connectivity"
    else
        log_error " Database connectivity failed"
        ((validation_errors++))
    fi

    # Test Kong gateway
    log_info "Testing Kong gateway..."
    if curl -f -s http://localhost:8000/ &> /dev/null; then
        log_success " Kong gateway responding"
    else
        log_error " Kong gateway not responding"
        ((validation_errors++))
    fi

    # Test Supabase Auth
    log_info "Testing Supabase Auth..."
    if curl -f -s http://localhost:8000/auth/v1/health &> /dev/null; then
        log_success " Supabase Auth responding"
    else
        log_warning " Supabase Auth health check failed (may be normal)"
    fi

    # Test AI Orchestrator
    log_info "Testing AI Orchestrator..."
    local max_attempts=10
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:8001/health &> /dev/null; then
            log_success " AI Orchestrator responding"
            break
        fi

        if [ $attempt -eq $max_attempts ]; then
            log_error " AI Orchestrator not responding"
            ((validation_errors++))
        else
            log_info "Waiting for AI Orchestrator... (attempt $attempt/$max_attempts)"
            sleep 5
        fi
        ((attempt++))
    done

    # Test Supabase integration
    log_info "Testing Supabase integration..."
    if curl -f -s -H "Content-Type: application/json" \
       http://localhost:8001/api/v1/supabase/health &> /dev/null; then
        log_success " Supabase integration responding"
    else
        log_warning " Supabase integration health check failed"
    fi

    if [ $validation_errors -eq 0 ]; then
        log_success "All validation checks passed"
        return 0
    else
        log_error "Validation failed with $validation_errors errors"
        return 1
    fi
}

show_deployment_info() {
    log_info " Supabase deployment completed!"
    echo
    echo "=== Deployment Summary ==="
    echo "Environment: $ENVIRONMENT"
    echo "Mode: $DEPLOY_MODE"
    echo "Timestamp: $(date)"
    echo
    echo "=== Service URLs ==="
    echo "Kong Gateway:       http://localhost:8000"
    echo "AI Orchestrator:    http://localhost:8001"
    echo "Admin Dashboard:    http://localhost:3000"
    echo "Supabase DB:        localhost:5433"
    echo
    echo "=== API Endpoints ==="
    echo "Health Check:       http://localhost:8001/health"
    echo "Supabase Health:    http://localhost:8001/api/v1/supabase/health"
    echo "User Registration:  POST http://localhost:8001/api/v1/supabase/auth/register"
    echo "User Login:         POST http://localhost:8001/api/v1/supabase/auth/login"
    echo "Document Search:    POST http://localhost:8001/api/v1/supabase/search"
    echo "RAG Generation:     POST http://localhost:8001/api/v1/supabase/rag/generate"
    echo
    echo "=== Next Steps ==="
    echo "1. Register a user account using the registration endpoint"
    echo "2. Create a project and upload documents"
    echo "3. Test vector search and RAG functionality"
    echo "4. Check logs: docker-compose -f docker-compose.yml -f docker-compose.supabase.yml logs"
    echo
    echo "=== Configuration Files ==="
    echo "Environment:        $PROJECT_ROOT/.env"
    echo "Docker Compose:     $PROJECT_ROOT/docker-compose.supabase.yml"
    echo "Migrations:         $PROJECT_ROOT/supabase/migrations/"
    echo "Deployment Log:     $LOG_FILE"
    echo
}

run_data_migration() {
    if [ "$DEPLOY_MODE" != "migration" ]; then
        return 0
    fi

    log_info "Running data migration from PostgreSQL to Supabase..."

    if [ "$DRY_RUN" = "true" ]; then
        log_info "[DRY RUN] Would run data migration"
        return 0
    fi

    # Check if migration script exists
    if [ -f "$PROJECT_ROOT/scripts/migrate-postgresql-to-supabase.py" ]; then
        log_info "Starting data migration..."

        python3 "$PROJECT_ROOT/scripts/migrate-postgresql-to-supabase.py" \
            --source-db "postgresql://postgres:${POSTGRES_PASSWORD:-postgres_password}@localhost:5432/ai_coding_agent" \
            --target-db "postgresql://postgres:${POSTGRES_PASSWORD:-postgres_password}@localhost:5433/ai_coding_agent" \
            --supabase-url "http://localhost:8000" \
            --supabase-key "${SERVICE_ROLE_KEY:-service_key}" \
            --batch-size 50

        if [ $? -eq 0 ]; then
            log_success "Data migration completed successfully"
        else
            log_error "Data migration failed"
            return 1
        fi
    else
        log_warning "Migration script not found, skipping data migration"
    fi
}

cleanup_on_error() {
    log_error "Deployment failed, cleaning up..."

    if [ "$DRY_RUN" != "true" ]; then
        # Stop services
        docker-compose -f docker-compose.yml -f docker-compose.supabase.yml down --remove-orphans || true

        # Show recent logs
        log_info "Recent service logs:"
        docker-compose -f docker-compose.yml -f docker-compose.supabase.yml logs --tail=20 || true
    fi
}

main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -m|--mode)
                DEPLOY_MODE="$2"
                shift 2
                ;;
            --no-validation)
                VALIDATE_DEPLOYMENT="false"
                shift
                ;;
            --no-backup)
                BACKUP_EXISTING="false"
                shift
                ;;
            --no-keys)
                GENERATE_KEYS="false"
                shift
                ;;
            --no-migrations)
                RUN_MIGRATIONS="false"
                shift
                ;;
            --no-start)
                START_SERVICES="false"
                shift
                ;;
            --dry-run)
                DRY_RUN="true"
                shift
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
        log_error "Invalid environment: $ENVIRONMENT"
        exit 1
    fi

    # Validate deploy mode
    if [[ ! "$DEPLOY_MODE" =~ ^(supabase|hybrid|migration)$ ]]; then
        log_error "Invalid deploy mode: $DEPLOY_MODE"
        exit 1
    fi

    # Main deployment flow
    trap cleanup_on_error ERR

    print_banner

    log_info "Starting Supabase deployment..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Mode: $DEPLOY_MODE"
    log_info "Dry run: ${DRY_RUN:-false}"

    check_prerequisites
    backup_existing_data
    generate_supabase_keys
    setup_environment
    setup_docker_network

    if [ "$START_SERVICES" = "true" ]; then
        deploy_supabase_services
        wait_for_services
        run_database_migrations
        run_data_migration
        validate_deployment
    fi

    show_deployment_info

    log_success "Supabase deployment completed successfully! "
}

# Run main function
main "$@"