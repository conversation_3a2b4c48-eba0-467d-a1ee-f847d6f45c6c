"""Redis key and channel namespacing utilities.

All Redis resources must be properly namespaced to prevent cross-tenant leakage.
Convention: ai:tenant:{tenant_id}:<parts...>
Optionally allow an env/system prefix via AI_REDIS_PREFIX.
"""
from __future__ import annotations

import os
from typing import Iterable

AI_REDIS_PREFIX = os.getenv("AI_REDIS_PREFIX", "ai")


def tenant_key(tenant_id: str, *parts: str) -> str:
    """Build a namespaced Redis key.

    Example: ai:tenant:{tenant_id}:llm:cache:{hash}
    """
    tenant_id = (tenant_id or "").strip()
    if not tenant_id:
        # Fallback to a safe literal to avoid collapsing keys across tenants
        tenant_id = "unknown"
    clean: list[str] = [p.strip(": ") for p in parts if p]
    return f"{AI_REDIS_PREFIX}:tenant:{tenant_id}:" + ":".join(clean)


def tenant_channel(tenant_id: str, *parts: str) -> str:
    """Build a namespaced Redis pub/sub channel name.

    Example: ai:tenant:{tenant_id}:tasks:updates
    """
    return tenant_key(tenant_id, *parts)


def tenant_stream(tenant_id: str, *parts: str) -> str:
    """Build a namespaced Redis stream name.

    Example: ai:tenant:{tenant_id}:tasks
    """
    return tenant_key(tenant_id, *parts)


def batch_tenant_keys(tenant_id: str, key_parts: Iterable[Iterable[str]]) -> list[str]:
    """Build multiple namespaced Redis keys from an iterable of key parts.

    Useful for batch operations where you need to generate multiple tenant-scoped keys.

    Args:
        tenant_id: Tenant identifier
        key_parts: Iterable of iterables, each representing parts for a single key

    Returns:
        List of namespaced Redis keys

    Example:
        >>> batch_tenant_keys("user123", [["cache", "llm"], ["session", "data"]])
        ["ai:tenant:user123:cache:llm", "ai:tenant:user123:session:data"]
    """
    return [tenant_key(tenant_id, *parts) for parts in key_parts]


def validate_key_parts(parts: Iterable[str]) -> list[str]:
    """Validate and clean key parts from an iterable.

    Ensures all parts are non-empty strings and properly formatted for Redis keys.

    Args:
        parts: Iterable of key parts to validate

    Returns:
        List of validated and cleaned key parts

    Raises:
        ValueError: If any part is invalid
    """
    clean_parts = []
    for part in parts:
        if not isinstance(part, str):
            raise ValueError(f"Key part must be string, got {type(part)}")
        cleaned = part.strip(": ")
        if not cleaned:
            raise ValueError("Key parts cannot be empty after cleaning")
        if ":" in cleaned:
            raise ValueError(f"Key part cannot contain colons: {cleaned}")
        clean_parts.append(cleaned)
    return clean_parts