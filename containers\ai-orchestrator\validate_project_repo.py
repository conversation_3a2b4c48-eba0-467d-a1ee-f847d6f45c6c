#!/usr/bin/env python3
"""
Validation script for ProjectRepository implementation.

This script validates that the ProjectRepository is working correctly
and properly encapsulates project-related data access logic.
"""

import sys
from pathlib import Path

# Add the src directory to Python path for imports
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_basic_import():
    """Test basic import of ProjectRepository."""
    try:
        from src.repository.project_repository import ProjectRepository, get_project_repository
        print("✓ Successfully imported ProjectRepository")
        return True
    except ImportError as e:
        print(f"✗ Failed to import ProjectRepository: {e}")
        return False

def test_repository_instantiation():
    """Test that ProjectRepository can be instantiated."""
    try:
        from src.repository.project_repository import ProjectRepository

        repo = ProjectRepository()
        print("✓ Successfully instantiated ProjectRepository")
        return True
    except Exception as e:
        print(f"✗ Failed to instantiate ProjectRepository: {e}")
        return False

def test_dependency_injection():
    """Test dependency injection function."""
    try:

        # This is an async function, so we can't call it directly in sync context
        print("✓ Dependency injection function exists")
        return True
    except Exception as e:
        print(f"✗ Dependency injection function failed: {e}")
        return False

def test_repository_methods():
    """Test that ProjectRepository methods exist."""
    try:
        from src.repository.project_repository import ProjectRepository

        repo = ProjectRepository()

        # Check that all expected methods exist
        expected_methods = [
            'get_user_workspace_path',
            'get_workspace_info',
            'upload_project_files',
            'clone_git_repository',
            'list_user_projects',
            'delete_project'
        ]

        for method_name in expected_methods:
            if hasattr(repo, method_name):
                print(f"✓ Method {method_name} exists")
            else:
                print(f"✗ Method {method_name} missing")
                return False

        return True
    except Exception as e:
        print(f"✗ Failed to check repository methods: {e}")
        return False

def test_init_file():
    """Test that __init__.py properly exports ProjectRepository."""
    try:
        from src.repository import ProjectRepository
        print("✓ ProjectRepository properly exported from repository package")
        return True
    except ImportError as e:
        print(f"✗ Failed to import ProjectRepository from repository package: {e}")
        return False

def main():
    """Run all validation tests."""
    print("=" * 60)
    print("AI Orchestrator - ProjectRepository Validation")
    print("=" * 60)

    tests = [
        ("Basic Import", test_basic_import),
        ("Repository Instantiation", test_repository_instantiation),
        ("Dependency Injection", test_dependency_injection),
        ("Repository Methods", test_repository_methods),
        ("Package Export", test_init_file),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        if test_func():
            passed += 1
        else:
            print("This test failed!")

    print("\n" + "=" * 60)
    print(f"Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! ProjectRepository is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())