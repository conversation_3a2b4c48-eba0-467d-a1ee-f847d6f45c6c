/**
 * ApiKeyInput Component
 * Secure input field for API keys with show/hide functionality
 */

import React, { useState } from 'react';
import { EyeIcon, EyeSlashIcon, KeyIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { LLMProvider } from '@/types/role';
import type { ApiKeyInputProps } from '@/types/role';

// Provider-specific API key validation patterns
const API_KEY_PATTERNS: Record<LLMProvider, { pattern: RegExp; placeholder: string; hint: string }> = {
  [LLMProvider.OPENAI]: {
    pattern: /^sk-[a-zA-Z0-9]{48}$/,
    placeholder: 'sk-1234567890abcdef...',
    hint: 'OpenAI API keys start with "sk-" and are 51 characters long',
  },
  [LLMProvider.ANTHROPIC]: {
    pattern: /^sk-ant-api03-[a-zA-Z0-9_-]{95}$/,
    placeholder: 'sk-ant-api03-...',
    hint: 'Anthropic API keys start with "sk-ant-api03-" and are 108 characters long',
  },
  [LLMProvider.OPENROUTER]: {
    pattern: /^sk-or-v1-[a-zA-Z0-9]{64}$/,
    placeholder: 'sk-or-v1-...',
    hint: 'OpenRouter API keys start with "sk-or-v1-" and are 74 characters long',
  },
  [LLMProvider.OLLAMA]: {
    pattern: /.*/,
    placeholder: 'Not required for Ollama',
    hint: 'Ollama runs locally and does not require an API key',
  },
};

const ApiKeyInput: React.FC<ApiKeyInputProps> = ({
  value,
  onChange,
  provider,
  required = false,
  disabled = false,
  error,
}) => {
  const [showApiKey, setShowApiKey] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const isOllama = provider === LLMProvider.OLLAMA;
  const providerConfig = API_KEY_PATTERNS[provider];

  // Local validation
  const isValidFormat = !value || providerConfig.pattern.test(value);
  const showValidationHint = isFocused && value && !isValidFormat && !isOllama;

  const handleToggleVisibility = () => {
    setShowApiKey(!showApiKey);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  if (isOllama) {
    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          API Key
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <div className="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
          <KeyIcon className="h-5 w-5 text-blue-600 mr-3" />
          <div>
            <p className="text-sm font-medium text-blue-800">No API Key Required</p>
            <p className="text-sm text-blue-600">
              Ollama runs locally and does not require an API key.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <label
        htmlFor="api-key-input"
        className="block text-sm font-medium text-gray-700"
      >
        API Key
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <KeyIcon className="h-5 w-5 text-gray-400" />
        </div>

        <input
          id="api-key-input"
          type={showApiKey ? 'text' : 'password'}
          value={value}
          onChange={handleInputChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          disabled={disabled}
          required={required}
          placeholder={providerConfig.placeholder}
          className={clsx(
            'block w-full pl-10 pr-12 py-2.5 border rounded-lg shadow-sm focus:ring-2 focus:ring-offset-0 transition-colors',
            {
              'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500': error || (!isValidFormat && value),
              'border-gray-300 placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500': !error && (isValidFormat || !value),
              'bg-gray-50 cursor-not-allowed opacity-50': disabled,
            }
          )}
          aria-describedby={error ? 'api-key-error' : 'api-key-hint'}
          aria-invalid={!!error || (!isValidFormat && !!value)}
        />

        <button
          type="button"
          onClick={handleToggleVisibility}
          disabled={disabled || !value}
          className={clsx(
            'absolute inset-y-0 right-0 pr-3 flex items-center transition-colors',
            {
              'text-gray-400 hover:text-gray-600': !disabled && value,
              'text-gray-300 cursor-not-allowed': disabled || !value,
            }
          )}
          aria-label={showApiKey ? 'Hide API key' : 'Show API key'}
        >
          {showApiKey ? (
            <EyeSlashIcon className="h-5 w-5" />
          ) : (
            <EyeIcon className="h-5 w-5" />
          )}
        </button>
      </div>

      {/* Error message */}
      {error && (
        <div className="flex items-start space-x-2 text-sm text-red-600" role="alert">
          <ExclamationTriangleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
          <span id="api-key-error">{error}</span>
        </div>
      )}

      {/* Format validation hint */}
      {showValidationHint && (
        <div className="flex items-start space-x-2 text-sm text-amber-600" role="alert">
          <ExclamationTriangleIcon className="h-4 w-4 mt-0.5 flex-shrink-0" />
          <span>Invalid API key format. {providerConfig.hint}</span>
        </div>
      )}

      {/* Provider hint */}
      {!error && !showValidationHint && (
        <p
          id="api-key-hint"
          className="text-sm text-gray-500"
        >
          {providerConfig.hint}
        </p>
      )}

      {/* Security notice */}
      {value && isValidFormat && (
        <div className="bg-green-50 border border-green-200 rounded-md p-3">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <KeyIcon className="h-4 w-4 text-green-400" />
            </div>
            <div className="ml-2 text-sm text-green-700">
              <strong>Secure:</strong> Your API key is encrypted and stored securely on the server.
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApiKeyInput;