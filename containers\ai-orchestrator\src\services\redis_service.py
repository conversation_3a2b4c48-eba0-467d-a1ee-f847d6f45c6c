"""
Redis Service Implementation for AI Coding Agent.

This module provides Redis integration following the documented Redis Integration Guide,
including caching strategies, session management, task queues, and real-time messaging.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import hashlib
import json
import logging
import os
from datetime import datetime, timedelta, timezone
from functools import lru_cache
from typing import Any, Awaitable, Callable, Dict, List, Optional
from uuid import uuid4

import redis.asyncio as redis
from redis import exceptions as redis_exceptions
from fastapi import Depends
from redis.asyncio import ConnectionPool
from src.schemas.user_schemas import SupabaseUser
from src.services.auth_dependencies import get_current_user_from_supabase

# Configure logging
logger = logging.getLogger(__name__)


async def get_current_tenant_id(
    current_user: SupabaseUser = Depends(get_current_user_from_supabase),
) -> str:
    """
    Extract tenant ID from current user context.

    Uses user ID as tenant identifier for multi-tenant isolation.
    Falls back to 'default' for system operations.

    Args:
        current_user: Authenticated user from Supabase

    Returns:
        str: Tenant identifier for namespace isolation
    """
    return str(current_user.id) if current_user else "default"


async def get_optional_tenant_id(
    current_user: Optional[SupabaseUser] = Depends(get_current_user_from_supabase),
) -> str:
    """
    Extract tenant ID with optional user context for public operations.

    Args:
        current_user: Optional authenticated user

    Returns:
        str: Tenant identifier, defaults to 'public' for unauthenticated access
    """
    return str(current_user.id) if current_user else "public"


class RedisManager:
    """
    Redis connection manager with async support and connection pooling.

    Provides centralized Redis connection management with health checks
    and automatic reconnection capabilities.
    """

    def __init__(self, redis_url: str):
        """Initialize Redis manager with connection URL."""
        self.redis_url = redis_url
        self._pool: Optional[ConnectionPool] = None
        self._client: Optional[redis.Redis] = None

    async def initialize(self) -> None:
        """Initialize Redis connection pool and client."""
        try:
            # Attempt to read password from a secrets-mounted file if provided.
            password = None
            redis_password_file = os.getenv("REDIS_PASSWORD_FILE")
            if redis_password_file:
                try:
                    if os.path.exists(redis_password_file):
                        with open(redis_password_file, "r", encoding="utf-8") as f:
                            password = f.read().strip()
                            logger.info("Loaded Redis password from file")
                    else:
                        logger.debug(f"Redis password file not found at {redis_password_file}")
                except Exception as e:
                    logger.error(f"Unable to read Redis password file: {e}")

            # Create connection pool with proper type handling. If a password was
            # read from the secret file, pass it explicitly so it is not stored
            # in environment variables or logs.
            pool_kwargs = {
                "max_connections": 20,
                "encoding": "utf-8",
                "decode_responses": True,
            }
            if password:
                pool_kwargs["password"] = password

            self._pool = ConnectionPool.from_url(self.redis_url, **pool_kwargs)

            # Create a Redis client using the connection pool
            self._client = redis.Redis(connection_pool=self._pool)

            # Test connection
            await self._client.ping()
            logger.info("Redis connection initialized successfully")

        except redis_exceptions.ConnectionError as e:
            logger.error(f"Failed to initialize Redis connection: connection error: {e}")
            logger.error(f"Redis URL: {self.redis_url}")
            raise
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Failed to initialize Redis connection: timeout: {e}")
            logger.error(f"Redis URL: {self.redis_url}")
            raise

    async def close(self) -> None:
        """Close Redis connections and cleanup resources."""
        try:
            if self._client:
                await self._client.close()
            if self._pool:
                await self._pool.disconnect()
            logger.info("Redis connections closed")
        except Exception as e:
            logger.error(f"Error closing Redis connections: {str(e)}")

    @property
    def client(self) -> redis.Redis:
        """Get Redis client instance."""
        if not self._client:
            raise RuntimeError("Redis client not initialized. Call initialize() first.")
        return self._client

    async def get_client(self) -> redis.Redis:
        """Get Redis client instance asynchronously with initialization check."""
        if not self._client:
            raise RuntimeError("Redis client not initialized. Call initialize() first.")
        return self._client

    def is_available(self) -> bool:
        """Check if Redis client is available and initialized."""
        return self._client is not None

    async def health_check(self) -> Dict[str, Any]:
        """Perform Redis health check and return status information."""
        try:
            # Test connectivity
            await self._client.ping()

            # Get server info
            info = await self._client.info("server")
            memory_info = await self._client.info("memory")

            return {
                "status": "healthy",
                "redis_version": info.get("redis_version"),
                "connected_clients": info.get("connected_clients"),
                "used_memory": memory_info.get("used_memory_human"),
                "used_memory_peak": memory_info.get("used_memory_peak_human"),
                "uptime_in_seconds": info.get("uptime_in_seconds"),
            }
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Redis health check failed: connection error: {e}")
            return {"status": "unhealthy", "error": str(e)}
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Redis health check failed: timeout: {e}")
            return {"status": "unhealthy", "error": str(e)}


class LLMCache:
    """
    LLM response caching implementation.

    Provides intelligent caching of LLM responses with configurable TTL
    and cache key generation based on prompt, model, and parameters.
    """

    def __init__(self, redis_client: redis.Redis, ttl: int = 3600):
        """
        Initialize LLM cache.

        Args:
            redis_client: Redis client instance
            ttl: Time to live for cached responses in seconds (default: 1 hour)
        """
        self.redis = redis_client
        self.prefix = "llm:cache"
        self.ttl = ttl

    def _generate_key(self, tenant_id: str, prompt: str, model: str, params: Dict[str, Any]) -> str:
        """
        Generate tenant-scoped cache key from prompt, model, and parameters.

        Args:
            tenant_id: Tenant identifier for namespacing
            prompt: Input prompt text
            model: Model name
            params: Model parameters

        Returns:
            Fully-qualified tenant-scoped Redis key
        """
        from src.utils.redis_keys import tenant_key  # local import to avoid cycles

        content = json.dumps({"prompt": prompt, "model": model, "params": params}, sort_keys=True)

        key_hash = hashlib.sha256(content.encode()).hexdigest()
        return tenant_key(tenant_id, "llm", "cache", key_hash)

    async def get(
        self, tenant_id: str, prompt: str, model: str, params: Dict[str, Any]
    ) -> Optional[str]:
        """
        Retrieve cached LLM response (tenant-scoped).

        Args:
            tenant_id: Tenant identifier
            prompt: Input prompt text
            model: Model name
            params: Model parameters

        Returns:
            Cached response or None if not found
        """
        try:
            key = self._generate_key(tenant_id, prompt, model, params)
            response = await self.redis.get(key)

            if response:
                logger.debug(f"Cache hit for LLM request: {key[:32]}...")
                return response

            logger.debug(f"Cache miss for LLM request: {key[:32]}...")
            return None
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error retrieving from LLM cache (connection): {e}")
            return None
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error retrieving from LLM cache (timeout): {e}")
            return None

    async def set(
        self, tenant_id: str, prompt: str, model: str, params: Dict[str, Any], response: str
    ) -> bool:
        """
        Store LLM response in cache (tenant-scoped).

        Args:
            tenant_id: Tenant identifier
            prompt: Input prompt text
            model: Model name
            params: Model parameters
            response: LLM response to cache

        Returns:
            True if successfully cached, False otherwise
        """
        try:
            key = self._generate_key(tenant_id, prompt, model, params)
            await self.redis.setex(key, self.ttl, response)

            logger.debug(f"Cached LLM response: {key[:32]}...")
            return True
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error caching LLM response (connection): {e}")
            return False
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error caching LLM response (timeout): {e}")
            return False

    async def invalidate_pattern(self, tenant_id: str, pattern: str) -> int:
        """
        Invalidate cache entries matching pattern within a tenant namespace.

        Args:
            tenant_id: Tenant identifier
            pattern: Redis key pattern to match (suffix portion)

        Returns:
            Number of keys deleted
        """
        try:
            from src.utils.redis_keys import tenant_key

            keys = await self.redis.keys(tenant_key(tenant_id, "llm", "cache", pattern))
            if keys:
                deleted = await self.redis.delete(*keys)
                logger.info(
                    f"Invalidated {deleted} cache entries for tenant={tenant_id} matching pattern: {pattern}"
                )
                return deleted
            return 0
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error invalidating cache pattern {pattern} (connection): {e}")
            return 0
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error invalidating cache pattern {pattern} (timeout): {e}")
            return 0


class ValidationCache:
    """
    Code validation result caching implementation.

    Caches validation results based on code content and language
    to avoid redundant validation operations.
    """

    def __init__(self, redis_client: redis.Redis, ttl: int = 1800):
        """
        Initialize validation cache.

        Args:
            redis_client: Redis client instance
            ttl: Time to live for cached results in seconds (default: 30 minutes)
        """
        self.redis = redis_client
        self.prefix = "validation"
        self.ttl = ttl

    def _generate_code_hash(self, code: str, language: str) -> str:
        """Generate hash for code content and language."""
        content = f"{language}:{code}"
        return hashlib.sha256(content.encode()).hexdigest()

    async def get_validation(
        self, tenant_id: str, code: str, language: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached validation result (tenant-scoped).

        Args:
            tenant_id: Tenant identifier
            code: Source code to validate
            language: Programming language

        Returns:
            Cached validation result or None if not found
        """
        try:
            from src.utils.redis_keys import tenant_key

            key = tenant_key(tenant_id, self.prefix, self._generate_code_hash(code, language))
            result = await self.redis.get(key)

            if result:
                logger.debug(f"Validation cache hit for {language} code in tenant {tenant_id}")
                return json.loads(result)

            logger.debug(f"Validation cache miss for {language} code in tenant {tenant_id}")
            return None
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error retrieving validation cache (connection): {e}")
            return None
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error retrieving validation cache (timeout): {e}")
            return None

    async def cache_validation(
        self, tenant_id: str, code: str, language: str, result: Dict[str, Any]
    ) -> bool:
        """
        Cache validation result (tenant-scoped).

        Args:
            tenant_id: Tenant identifier
            code: Source code that was validated
            language: Programming language
            result: Validation result to cache

        Returns:
            True if successfully cached, False otherwise
        """
        try:
            from src.utils.redis_keys import tenant_key

            key = tenant_key(tenant_id, self.prefix, self._generate_code_hash(code, language))

            # Add timestamp to result
            result_with_timestamp = {**result, "cached_at": datetime.now(timezone.utc).isoformat()}

            await self.redis.setex(key, self.ttl, json.dumps(result_with_timestamp))
            logger.debug(f"Cached validation result for {language} code in tenant {tenant_id}")
            return True
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error caching validation result (connection): {e}")
            return False
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error caching validation result (timeout): {e}")
            return False


class SessionManager:
    """
    User session and approval workflow management.

    Handles user sessions, approval requests, and workflow state management
    using Redis for distributed session storage.
    """

    def __init__(self, redis_client: redis.Redis):
        """
        Initialize session manager.

        Args:
            redis_client: Redis client instance
        """
        self.redis = redis_client
        self.session_ttl = 86400  # 24 hours
        self.approval_ttl = 1800  # 30 minutes

    async def create_session(self, user_id: str, data: Dict[str, Any]) -> str:
        """
        Create new user session.

        Args:
            user_id: User identifier
            data: Session data

        Returns:
            Session ID
        """
        try:
            session_id = str(uuid4())
            session_data = {
                "user_id": user_id,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "last_activity": datetime.now(timezone.utc).isoformat(),
                **data,
            }

            await self.redis.setex(
                f"session:{session_id}", self.session_ttl, json.dumps(session_data)
            )

            # Add to user's active sessions
            await self.redis.sadd(f"user_sessions:{user_id}", session_id)

            logger.info(f"Created session {session_id} for user {user_id}")
            return session_id
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error creating session for user {user_id} (connection): {e}")
            raise
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error creating session for user {user_id} (timeout): {e}")
            raise

    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve session data.

        Args:
            session_id: Session identifier

        Returns:
            Session data or None if not found
        """
        try:
            data = await self.redis.get(f"session:{session_id}")
            if data:
                session_data = json.loads(data)

                # Update last activity
                session_data["last_activity"] = datetime.now(timezone.utc).isoformat()
                await self.redis.setex(
                    f"session:{session_id}", self.session_ttl, json.dumps(session_data)
                )

                return session_data

            return None
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error retrieving session {session_id} (connection): {e}")
            return None
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error retrieving session {session_id} (timeout): {e}")
            return None

    async def delete_session(self, session_id: str) -> bool:
        """
        Delete user session.

        Args:
            session_id: Session identifier

        Returns:
            True if session was deleted, False otherwise
        """
        try:
            # Get session data to find user_id
            session_data = await self.get_session(session_id)
            if session_data:
                user_id = session_data.get("user_id")
                if user_id:
                    await self.redis.srem(f"user_sessions:{user_id}", session_id)

            # Delete session
            deleted = await self.redis.delete(f"session:{session_id}")

            logger.info(f"Deleted session {session_id}")
            return bool(deleted)
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error deleting session {session_id} (connection): {e}")
            return False
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error deleting session {session_id} (timeout): {e}")
            return False

    async def create_approval(
        self, tenant_id: str, operation: str, context: Dict[str, Any], user_id: str
    ) -> str:
        """
        Create approval request (tenant-scoped).

        Args:
            tenant_id: Tenant identifier
            operation: Operation requiring approval
            context: Operation context data
            user_id: User ID requesting approval

        Returns:
            Approval ID
        """
        try:
            from src.utils.redis_keys import tenant_key

            approval_id = str(uuid4())
            approval_data = {
                "tenant_id": tenant_id,
                "approval_id": approval_id,
                "operation": operation,
                "context": context,
                "user_id": user_id,
                "status": "pending",
                "created_at": datetime.now(timezone.utc).isoformat(),
            }

            await self.redis.setex(
                tenant_key(tenant_id, "approval", approval_id),
                self.approval_ttl,
                json.dumps(approval_data),
            )

            # Add to tenant-scoped pending list for the user
            await self.redis.lpush(tenant_key(tenant_id, "pending_approvals", user_id), approval_id)

            logger.info(f"[{tenant_id}] Created approval {approval_id} for operation {operation}")
            return approval_id
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error creating approval for operation {operation} (connection): {e}")
            raise
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error creating approval for operation {operation} (timeout): {e}")
            raise

    async def process_approval(
        self, tenant_id: str, approval_id: str, approved: bool, admin_id: str
    ) -> bool:
        """
        Process approval request (tenant-scoped).

        Args:
            tenant_id: Tenant identifier
            approval_id: Approval identifier
            approved: Whether request was approved
            admin_id: ID of admin processing the approval

        Returns:
            True if successfully processed, False otherwise
        """
        try:
            from src.utils.redis_keys import tenant_key

            key = tenant_key(tenant_id, "approval", approval_id)
            data = await self.redis.get(key)

            if not data:
                logger.warning(f"[{tenant_id}] Approval {approval_id} not found")
                return False

            approval = json.loads(data)

            # Basic tenant integrity check
            if approval.get("tenant_id") != tenant_id:
                logger.warning(f"Tenant mismatch for approval {approval_id}")
                return False

            approval.update(
                {
                    "status": "approved" if approved else "denied",
                    "processed_at": datetime.now(timezone.utc).isoformat(),
                    "processed_by": admin_id,
                }
            )

            # Extend TTL for audit trail
            await self.redis.setex(key, 86400, json.dumps(approval))  # Keep for 24 hours

            logger.info(
                f"[{tenant_id}] Processed approval {approval_id}: {'approved' if approved else 'denied'}"
            )
            return True
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error processing approval {approval_id} (connection): {e}")
            return False
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error processing approval {approval_id} (timeout): {e}")
            return False

    async def set_session_expiry(self, session_id: str, expiry: timedelta) -> bool:
        """
        Set session expiration using timedelta for readable time specifications.

        Args:
            session_id: Session identifier
            expiry: Time until expiration as timedelta

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            key = f"session:{session_id}"
            if await self.redis.exists(key):
                await self.redis.expire(key, int(expiry.total_seconds()))
                logger.debug(f"Set session {session_id} expiry to {expiry}")
                return True
            return False
        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error setting session expiry (connection): {e}")
            return False
        except redis_exceptions.TimeoutError as e:
            logger.error(f"Error setting session expiry (timeout): {e}")
            return False

    async def batch_expire_sessions(self, session_ids: List[str], expiry: timedelta) -> int:
        """
        Batch expire multiple sessions for efficient bulk operations.

        Args:
            session_ids: List of session identifiers to expire
            expiry: Time until expiration as timedelta

        Returns:
            int: Number of sessions successfully updated
        """
        if not session_ids:
            return 0

        success_count = 0
        expire_seconds = int(expiry.total_seconds())

        try:
            # Use pipeline for efficient batch operations
            async with self.redis.pipeline() as pipe:
                for session_id in session_ids:
                    pipe.expire(f"session:{session_id}", expire_seconds)
                results = await pipe.execute()

                success_count = sum(1 for result in results if result)
                logger.debug(f"Batch expired {success_count}/{len(session_ids)} sessions")

        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error batch expiring sessions (connection): {e}")

        return success_count

    async def cleanup_expired_sessions(self, user_id: str) -> List[str]:
        """
        Clean up expired sessions for a user and return list of cleaned session IDs.

        Args:
            user_id: User identifier

        Returns:
            List[str]: List of cleaned session IDs
        """
        cleaned_sessions: List[str] = []

        try:
            session_set_key = f"user_sessions:{user_id}"
            session_ids = await self.redis.smembers(session_set_key)

            for session_id in session_ids:
                session_key = f"session:{session_id}"
                if not await self.redis.exists(session_key):
                    # Session expired, remove from user's session set
                    await self.redis.srem(session_set_key, session_id)
                    cleaned_sessions.append(session_id)

            if cleaned_sessions:
                logger.info(f"Cleaned {len(cleaned_sessions)} expired sessions for user {user_id}")

        except redis_exceptions.ConnectionError as e:
            logger.error(f"Error cleaning expired sessions for user {user_id} (connection): {e}")

        return cleaned_sessions


class TaskQueue:
    """
    Redis Streams-based task queue for background processing.

    Implements distributed task processing using Redis Streams with
    consumer groups for reliable message delivery.
    """

    def __init__(self, redis_client: redis.Redis, tenant_id: str, stream_suffix: str = "tasks"):
        """
        Initialize task queue.

        Args:
            redis_client: Redis client instance
            tenant_id: Tenant identifier used to namespace stream and group
            stream_suffix: Suffix for stream name (default: 'tasks')
        """
        from src.utils.redis_keys import tenant_stream

        self.redis = redis_client
        self.tenant_id = tenant_id
        self.stream = tenant_stream(tenant_id, stream_suffix)
        self.group = f"group_{tenant_id}"
        self.consumer = f"worker_{uuid4().hex[:8]}"

    async def initialize(self) -> None:
        """Initialize consumer group for task processing (per-tenant)."""
        try:
            await self.redis.xgroup_create(self.stream, self.group, id="0", mkstream=True)
            logger.info(f"Initialized task queue consumer group: {self.group} on {self.stream}")
        except redis.ResponseError as e:
            if "BUSYGROUP" in str(e):
                logger.debug(f"Consumer group {self.group} already exists on {self.stream}")
            else:
                logger.error(f"Error creating consumer group: {str(e)}")
                raise

    async def add_task(self, task_type: str, data: Dict[str, Any], priority: int = 0) -> str:
        """
        Add task to tenant-scoped queue.

        Args:
            task_type: Type of task to execute
            data: Task data and parameters (should include tenant_id context if needed downstream)
            priority: Task priority (higher = more urgent)

        Returns:
            Task ID
        """
        try:
            task_id = str(uuid4())
            payload = {
                "task_id": task_id,
                "task_type": task_type,
                "data": json.dumps(data),
                "priority": str(priority),
                "created_at": datetime.now(timezone.utc).isoformat(),
                "tenant_id": self.tenant_id,
            }

            await self.redis.xadd(self.stream, payload)

            logger.info(
                f"Added task {task_id} of type {task_type} to queue for tenant {self.tenant_id}"
            )
            return task_id

        except Exception as e:
            logger.error(f"Error adding task to queue: {str(e)}")
            raise

    async def process_tasks(self, handlers: Dict[str, Callable]) -> None:
        """
        Process tasks from tenant-scoped queue using provided handlers.

        Args:
            handlers: Dict mapping task types to handler functions
        """
        await self.initialize()

        logger.info(
            f"Starting task processing with consumer {self.consumer} for tenant {self.tenant_id}"
        )

        while True:
            try:
                messages = await self.redis.xreadgroup(
                    streams={self.stream: ">"},
                    groupname=self.group,
                    consumername=self.consumer,
                    count=1,
                    block=1000,
                )

                for stream, msgs in messages:
                    logger.debug(f"Processing {len(msgs)} messages from stream: {stream}")
                    for msg_id, fields in msgs:
                        await self._process_message(msg_id, fields, handlers)

            except Exception as e:
                logger.error(f"Task processing error: {str(e)}")
                await asyncio.sleep(5)

    async def _process_message(
        self, msg_id: str, fields: Dict[str, str], handlers: Dict[str, Callable]
    ) -> None:
        """Process individual task message."""
        task_type = fields.get("task_type")
        task_id = fields.get("task_id")

        try:
            if not task_type:
                logger.warning(f"Missing task_type in message {msg_id}")
                return

            handler = handlers.get(task_type)
            if handler:
                data = json.loads(fields.get("data", "{}"))
                await handler(task_id, data)

                # Acknowledge successful processing
                await self.redis.xack(self.group, self.stream, msg_id)
                logger.debug(f"Processed task {task_id} successfully")
            else:
                logger.warning(f"No handler found for task type: {task_type}")

        except Exception as e:
            logger.error(f"Failed to process task {task_id}: {str(e)}")
            # Task will remain in pending list for retry


class RealTimeMessaging:
    """
    Redis pub/sub implementation for real-time messaging.

    Provides WebSocket-compatible real-time messaging using Redis
    publish/subscribe for broadcasting events across services.
    """

    def __init__(self, redis_client: redis.Redis, tenant_id: str):
        """
        Initialize real-time messaging (tenant-scoped).

        Args:
            redis_client: Redis client instance
            tenant_id: Tenant identifier for namespacing channels
        """
        from src.utils.redis_keys import tenant_channel

        self.redis = redis_client
        self.tenant_id = tenant_id
        self.channels = {
            "validation": tenant_channel(tenant_id, "validation", "progress"),
            "tasks": tenant_channel(tenant_id, "tasks", "updates"),
            "approvals": tenant_channel(tenant_id, "approvals", "requests"),
            "system": tenant_channel(tenant_id, "system", "events"),
        }

    async def publish_validation_progress(
        self, task_id: str, progress: int, message: str, user_id: Optional[str] = None
    ) -> None:
        """
        Publish validation progress event (tenant-scoped).

        Args:
            task_id: Task identifier
            progress: Progress percentage (0-100)
            message: Progress message
            user_id: Optional user ID for targeted messaging
        """
        event = {
            "type": "validation_progress",
            "task_id": task_id,
            "progress": progress,
            "message": message,
            "user_id": user_id,
            "tenant_id": self.tenant_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        try:
            await self.redis.publish(self.channels["validation"], json.dumps(event))
            logger.debug(
                f"[{self.tenant_id}] Published validation progress for task {task_id}: {progress}%"
            )
        except Exception as e:
            logger.error(f"Error publishing validation progress: {str(e)}")

    async def publish_task_update(
        self, task_id: str, status: str, details: Dict[str, Any], user_id: Optional[str] = None
    ) -> None:
        """
        Publish task status update (tenant-scoped).

        Args:
            task_id: Task identifier
            status: Task status
            details: Additional task details
            user_id: Optional user ID for targeted messaging
        """
        event = {
            "type": "task_update",
            "task_id": task_id,
            "status": status,
            "details": details,
            "user_id": user_id,
            "tenant_id": self.tenant_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        try:
            await self.redis.publish(self.channels["tasks"], json.dumps(event))
            logger.debug(f"[{self.tenant_id}] Published task update for {task_id}: {status}")
        except Exception as e:
            logger.error(f"Error publishing task update: {str(e)}")

    async def publish_approval_request(
        self, approval_id: str, operation: str, context: Dict[str, Any], user_id: str
    ) -> None:
        """
        Publish approval request event (tenant-scoped).

        Args:
            approval_id: Approval identifier
            operation: Operation requiring approval
            context: Operation context
            user_id: User requesting approval
        """
        event = {
            "type": "approval_request",
            "approval_id": approval_id,
            "operation": operation,
            "context": context,
            "user_id": user_id,
            "tenant_id": self.tenant_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        try:
            await self.redis.publish(self.channels["approvals"], json.dumps(event))
            logger.info(
                f"[{self.tenant_id}] Published approval request {approval_id} for operation {operation}"
            )
        except Exception as e:
            logger.error(f"Error publishing approval request: {str(e)}")

    async def subscribe_to_events(
        self, callback: Callable[[Dict[str, Any]], Awaitable[None]]
    ) -> None:
        """
        Subscribe to all tenant channels and process messages.

        Args:
            callback: Function to call with received events
        """
        try:
            pubsub = self.redis.pubsub()

            # Subscribe to all channels
            for channel in self.channels.values():
                await pubsub.subscribe(channel)
                logger.info(f"Subscribed to channel: {channel}")

            while True:
                message = await pubsub.get_message()
                if message and message["type"] == "message":
                    try:
                        event_data = json.loads(message["data"])
                        await callback(event_data)
                    except Exception as e:
                        logger.error(f"Error processing message: {str(e)}")

        except Exception as e:
            logger.error(f"Error in event subscription: {str(e)}")


class RateLimiter:
    """
    Redis-based rate limiting implementation.

    Provides sliding window rate limiting using Redis for
    API endpoint protection and abuse prevention.
    """

    def __init__(self, redis_client: redis.Redis):
        """
        Initialize rate limiter.

        Args:
            redis_client: Redis client instance
        """
        self.redis = redis_client

    async def check_rate_limit(self, key: str, limit: int, window: int) -> Dict[str, Any]:
        """
        Check if request is within rate limit.

        Args:
            key: Rate limiting key (e.g., user ID, IP address)
            limit: Maximum requests allowed
            window: Time window in seconds

        Returns:
            Dict with allowed status and current count
        """
        try:
            current_time = int(datetime.now(timezone.utc).timestamp())
            window_start = current_time - window

            # Remove expired entries
            await self.redis.zremrangebyscore(key, 0, window_start)

            # Count current requests
            current_count = await self.redis.zcard(key)

            if current_count < limit:
                # Add current request
                await self.redis.zadd(key, {str(uuid4()): current_time})
                await self.redis.expire(key, window)

                return {
                    "allowed": True,
                    "count": current_count + 1,
                    "limit": limit,
                    "reset_time": current_time + window,
                }
            else:
                return {
                    "allowed": False,
                    "count": current_count,
                    "limit": limit,
                    "reset_time": current_time + window,
                }

        except Exception as e:
            logger.error(f"Error checking rate limit for {key}: {str(e)}")
            # Fail open - allow request if Redis is unavailable
            return {"allowed": True, "count": 0, "limit": limit, "error": str(e)}


# Global Redis manager instance
redis_url = os.getenv("REDIS_URL", "redis://:ebubulbul1986@redis:6379/0")
redis_manager = RedisManager(redis_url)


async def get_redis_client() -> redis.Redis:
    """
    FastAPI dependency for Redis client.

    Returns:
        Redis client instance
    """
    if not redis_manager._client:
        await redis_manager.initialize()
    return redis_manager.client


@lru_cache()
def get_redis_manager() -> RedisManager:
    """
    Get Redis manager singleton.

    Returns:
        RedisManager instance
    """
    return redis_manager


# Service factory functions
def get_llm_cache(redis_client: redis.Redis = Depends(get_redis_client)) -> LLMCache:
    """Get LLM cache service."""
    return LLMCache(redis_client)


def get_validation_cache(redis_client: redis.Redis = Depends(get_redis_client)) -> ValidationCache:
    """Get validation cache service."""
    return ValidationCache(redis_client)


def get_session_manager(redis_client: redis.Redis = Depends(get_redis_client)) -> SessionManager:
    """Get session manager service."""
    return SessionManager(redis_client)


def get_task_queue(
    redis_client: redis.Redis = Depends(get_redis_client),
    tenant_id: str = Depends(get_current_tenant_id),
) -> TaskQueue:
    """Get task queue service with tenant isolation."""
    return TaskQueue(redis_client, tenant_id)


def get_realtime_messaging(
    redis_client: redis.Redis = Depends(get_redis_client),
    tenant_id: str = Depends(get_current_tenant_id),
) -> RealTimeMessaging:
    """Get real-time messaging service with tenant isolation."""
    return RealTimeMessaging(redis_client, tenant_id)


def get_rate_limiter(redis_client: redis.Redis = Depends(get_redis_client)) -> RateLimiter:
    """Get rate limiter service."""
    return RateLimiter(redis_client)


class ProjectCache:
    """
    Project-related caching implementation.

    Caches project lists and other project-related data to reduce
    filesystem and database load.
    """

    def __init__(self, redis_client: redis.Redis, ttl: int = 300):
        """
        Initialize project cache.

        Args:
            redis_client: Redis client instance
            ttl: Time to live for cached items in seconds (default: 5 minutes)
        """
        self.redis = redis_client
        self.prefix = "project"
        self.ttl = ttl

    async def get_project_list(self, user_id: str) -> Optional[List[Dict[str, Any]]]:
        """
        Retrieve cached project list for a user.

        Args:
            user_id: The user's ID.

        Returns:
            Cached project list or None if not found.
        """
        try:
            from src.utils.redis_keys import tenant_key

            key = tenant_key(user_id, self.prefix, "list")
            result = await self.redis.get(key)

            if result:
                logger.debug(f"Project list cache hit for user {user_id}")
                return json.loads(result)

            logger.debug(f"Project list cache miss for user {user_id}")
            return None
        except Exception as e:
            logger.error(f"Error retrieving project list from cache for user {user_id}: {e}")
            return None

    async def set_project_list(self, user_id: str, project_list: List[Dict[str, Any]]) -> bool:
        """
        Cache the project list for a user.

        Args:
            user_id: The user's ID.
            project_list: The list of projects to cache.

        Returns:
            True if successfully cached, False otherwise.
        """
        try:
            from src.utils.redis_keys import tenant_key

            key = tenant_key(user_id, self.prefix, "list")
            await self.redis.setex(key, self.ttl, json.dumps(project_list))
            logger.debug(f"Cached project list for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error caching project list for user {user_id}: {e}")
            return False

    async def invalidate_project_list(self, user_id: str) -> bool:
        """
        Invalidate the cached project list for a user.

        Args:
            user_id: The user's ID.

        Returns:
            True if successfully invalidated, False otherwise.
        """
        try:
            from src.utils.redis_keys import tenant_key

            key = tenant_key(user_id, self.prefix, "list")
            await self.redis.delete(key)
            logger.info(f"Invalidated project list cache for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error invalidating project list cache for user {user_id}: {e}")
            return False


def get_project_cache(redis_client: redis.Redis = Depends(get_redis_client)) -> ProjectCache:
    """Get project cache service."""
    return ProjectCache(redis_client)
