from fastapi import APIRouter

from src.router.auth_router import router as auth_router
from src.router.llm_router import router as llm_router
from src.router.role_management import router as role_router
from src.router.approval_router import router as approval_router
from src.router.interview_router import router as interview_router
from src.router.roadmap_router import router as roadmap_router

# Create main router that aggregates all sub-routers
# Note: This is an alternative to including routers directly in main.py
router = APIRouter()

# Include all routers
router.include_router(auth_router)
router.include_router(llm_router)
router.include_router(role_router)
router.include_router(approval_router)
router.include_router(interview_router)
router.include_router(roadmap_router)
