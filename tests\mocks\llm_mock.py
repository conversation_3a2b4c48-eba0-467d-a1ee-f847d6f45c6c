"""Mock LLM services for testing"""
import pytest
from unittest.mock import patch
from typing import Dict, Any, List

class MockLLMService:
    """Mock LLM service for testing"""

    def __init__(self):
        self.available_models = ["llama3.2", "codellama", "mistral"]

    async def generate_response(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Mock generate response"""
        return {
            "content": f"Mock response for: {request.get('prompt', '')[:50]}...",
            "model": request.get("model", "llama3.2"),
            "usage": {"tokens": len(request.get("prompt", "").split())}
        }

    async def list_available_models(self) -> List[str]:
        """Mock list available models"""
        return self.available_models

    async def check_model_availability(self, model: str) -> bool:
        """Mock check model availability"""
        return model in self.available_models

class MockOllamaService:
    """Mock Ollama service for testing"""

    async def generate(self, prompt: str, model: str = "llama3.2", **kwargs) -> Dict[str, Any]:
        """Mock Ollama generate"""
        return {
            "response": f"Mock Ollama response for: {prompt[:50]}...",
            "done": True,
            "model": model
        }

    async def list_models(self) -> List[Dict[str, Any]]:
        """Mock list Ollama models"""
        return [
            {"name": "llama3.2", "size": "4GB", "modified_at": "2024-01-01"},
            {"name": "codellama", "size": "8GB", "modified_at": "2024-01-01"}
        ]

    async def pull_model(self, model: str) -> bool:
        """Mock pull model"""
        return True

class MockOpenRouterService:
    """Mock OpenRouter service for testing"""

    async def generate(self, messages: List[Dict[str, str]], model: str = "anthropic/claude-3-haiku", **kwargs) -> Dict[str, Any]:
        """Mock OpenRouter generate"""
        return {
            "choices": [{
                "message": {
                    "content": f"Mock OpenRouter response for: {messages[0]['content'][:50]}..."
                }
            }],
            "usage": {"total_tokens": 150}
        }

@pytest.fixture
def mock_llm_service():
    """Mock LLM service fixture"""
    return MockLLMService()

@pytest.fixture
def mock_ollama_service():
    """Mock Ollama service fixture"""
    return MockOllamaService()

@pytest.fixture
def mock_openrouter_service():
    """Mock OpenRouter service fixture"""
    return MockOpenRouterService()

@pytest.fixture(autouse=True)
def mock_all_llm_services():
    """Mock all LLM services for tests"""
    with patch('src.services.llm_service.LLMService', MockLLMService), \
         patch('src.services.ollama_service.OllamaService', MockOllamaService), \
         patch('src.services.openrouter_service.OpenRouterService', MockOpenRouterService):
        yield
