{"name": "user-portal", "version": "1.0.0", "description": "User Portal for Role-based LLM Management in AI Coding Agent", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development next dev", "build": "cross-env NODE_ENV=production next build", "start": "cross-env NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "cypress run", "analyze": "cross-env ANALYZE=true npm run build", "fix-symlinks": "echo Symlinks fixed (Windows)", "dev:safe": "npm run fix-symlinks && cross-env NODE_ENV=development next dev"}, "dependencies": {"@21st-extension/react": "^0.5.14", "@21st-extension/toolbar-next": "^0.5.14", "@auth/supabase-adapter": "^1.6.0", "@heroicons/react": "^2.0.18", "@supabase/supabase-js": "^2.45.0", "axios": "^1.6.2", "clsx": "^2.0.0", "next": "^14.2.32", "next-auth": "^4.24.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-select": "^5.8.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "autoprefixer": "^10.4.18", "cross-env": "^7.0.3", "cypress": "^13.13.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.32", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.12", "tailwindcss": "^3.4.1", "typescript": "^5.5.2"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "optionalDependencies": {"@next/swc-linux-x64-gnu": "*", "@next/swc-linux-x64-musl": "*"}}