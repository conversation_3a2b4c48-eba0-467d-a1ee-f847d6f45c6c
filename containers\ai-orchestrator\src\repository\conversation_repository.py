# Project: AI Coding Agent
# Purpose: ConversationRepository for managing conversation history

"""
Repository for CRUD operations on ConversationHistory entities.
"""

from __future__ import annotations

from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import select, update, delete, and_

from src.models import Conversation<PERSON><PERSON><PERSON>, InterviewSession, InterviewState


class ConversationRepository:
    """Repository for managing conversation history data."""

    @staticmethod
    async def save_response(
        db: Session,
        *,
        project_id: int,
        user_id: str,
        session_id: str,
        question_key: str,
        question_text: str,
        user_response: Optional[str] = None,
        sequence_order: int,
        is_followup: bool = False,
    ) -> ConversationHistory:
        """Save a user response to the conversation history.

        Args:
            db: Database session
            project_id: ID of the project
            user_id: ID of the user
            session_id: Unique session identifier
            question_key: Key identifier for the question
            question_text: The question text
            user_response: User's response (optional)
            sequence_order: Order in the interview sequence
            is_followup: Whether this is a follow-up question

        Returns:
            ConversationHistory: The created conversation record
        """
        conversation = ConversationHistory(
            project_id=project_id,
            user_id=user_id,
            session_id=session_id,
            question_key=question_key,
            question_text=question_text,
            user_response=user_response,
            sequence_order=sequence_order,
            is_followup=is_followup,
        )
        db.add(conversation)
        db.commit()
        db.refresh(conversation)
        return conversation

    @staticmethod
    async def get_session_history(
        db: Session,
        session_id: str
    ) -> List[ConversationHistory]:
        """Get all conversation history for a session, ordered by sequence.

        Args:
            db: Database session
            session_id: Unique session identifier

        Returns:
            List[ConversationHistory]: Ordered list of conversation records
        """
        stmt = (
            select(ConversationHistory)
            .where(ConversationHistory.session_id == session_id)
            .order_by(ConversationHistory.sequence_order.asc())
        )
        result = db.execute(stmt)
        return list(result.scalars().all())

    @staticmethod
    async def get_project_conversations(
        db: Session,
        project_id: int,
        user_id: Optional[str] = None
    ) -> List[ConversationHistory]:
        """Get all conversation history for a project.

        Args:
            db: Database session
            project_id: ID of the project
            user_id: Optional user ID filter

        Returns:
            List[ConversationHistory]: List of conversation records
        """
        query = select(ConversationHistory).where(
            ConversationHistory.project_id == project_id
        )

        if user_id:
            query = query.where(ConversationHistory.user_id == user_id)

        query = query.order_by(
            ConversationHistory.session_id.asc(),
            ConversationHistory.sequence_order.asc()
        )

        result = db.execute(query)
        return list(result.scalars().all())

    @staticmethod
    async def update_response(
        db: Session,
        conversation_id: int,
        user_response: str
    ) -> ConversationHistory:
        """Update a conversation response.

        Args:
            db: Database session
            conversation_id: ID of the conversation record
            user_response: New response text

        Returns:
            ConversationHistory: Updated conversation record
        """
        stmt = select(ConversationHistory).where(ConversationHistory.id == conversation_id)
        conversation = db.execute(stmt).scalars().first()

        if not conversation:
            raise ValueError(f"Conversation {conversation_id} not found")

        conversation.user_response = user_response
        conversation.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(conversation)
        return conversation

    @staticmethod
    async def get_latest_session_for_project(
        db: Session,
        project_id: int
    ) -> Optional[str]:
        """Get the most recent session ID for a project.

        Args:
            db: Database session
            project_id: ID of the project

        Returns:
            Optional[str]: Latest session ID or None
        """
        stmt = (
            select(ConversationHistory.session_id)
            .where(ConversationHistory.project_id == project_id)
            .order_by(ConversationHistory.created_at.desc())
            .limit(1)
        )
        result = db.execute(stmt)
        row = result.first()
        return row[0] if row else None

    @staticmethod
    async def delete_session_history(
        db: Session,
        session_id: str
    ) -> int:
        """Delete all conversation history for a session.

        Args:
            db: Database session
            session_id: Session ID to delete

        Returns:
            int: Number of records deleted
        """
        stmt = delete(ConversationHistory).where(ConversationHistory.session_id == session_id)
        result = db.execute(stmt)
        db.commit()
        return result.rowcount

    @staticmethod
    async def get_session_summary(
        db: Session,
        session_id: str
    ) -> Dict[str, Any]:
        """Get a summary of responses for a session.

        Args:
            db: Database session
            session_id: Session ID

        Returns:
            Dict[str, Any]: Summary of the session
        """
        conversations = await ConversationRepository.get_session_history(db, session_id)

        summary = {
            "session_id": session_id,
            "total_questions": len(conversations),
            "answered_questions": len([c for c in conversations if c.user_response]),
            "responses": {}
        }

        for conv in conversations:
            summary["responses"][conv.question_key] = {
                "question": conv.question_text,
                "response": conv.user_response,
                "is_followup": conv.is_followup,
                "sequence_order": conv.sequence_order
            }

        return summary

    @staticmethod
    async def create_interview_session(
        db: Session,
        *,
        project_id: int,
        user_id: str,
        session_id: str,
        total_questions: int = 0,
        metadata: Optional[str] = None,
    ) -> InterviewSession:
        """Create a new interview session.

        Args:
            db: Database session
            project_id: ID of the project
            user_id: ID of the user
            session_id: Unique session identifier
            total_questions: Total number of questions in the interview
            metadata: Additional session metadata

        Returns:
            InterviewSession: The created interview session
        """
        session = InterviewSession(
            project_id=project_id,
            user_id=user_id,
            session_id=session_id,
            current_state=InterviewState.GREETING.value,
            current_question_index=0,
            total_questions=total_questions,
            is_active=True,
            session_metadata=metadata,
        )
        db.add(session)
        db.commit()
        db.refresh(session)
        return session

    @staticmethod
    async def get_interview_session(
        db: Session,
        session_id: str
    ) -> Optional[InterviewSession]:
        """Get an interview session by session ID.

        Args:
            db: Database session
            session_id: Unique session identifier

        Returns:
            Optional[InterviewSession]: The interview session or None if not found
        """
        stmt = select(InterviewSession).where(InterviewSession.session_id == session_id)
        result = db.execute(stmt)
        return result.scalar_one_or_none()

    @staticmethod
    async def update_interview_session_state(
        db: Session,
        session_id: str,
        new_state: InterviewState,
        current_question_index: Optional[int] = None,
        metadata: Optional[str] = None,
    ) -> Optional[InterviewSession]:
        """Update the state of an interview session.

        Args:
            db: Database session
            session_id: Unique session identifier
            new_state: New state for the session
            current_question_index: New question index (optional)
            metadata: Updated metadata (optional)

        Returns:
            Optional[InterviewSession]: The updated session or None if not found
        """
        values_to_update = {
            "current_state": new_state.value,
            "updated_at": datetime.now(),
        }
        if current_question_index is not None:
            values_to_update["current_question_index"] = current_question_index
        if metadata is not None:
            values_to_update["session_metadata"] = metadata

        stmt = (
            update(InterviewSession)
            .where(InterviewSession.session_id == session_id)
            .values(**values_to_update)
        )
        db.execute(stmt)
        db.commit()

        # Return the updated session
        return await ConversationRepository.get_interview_session(db, session_id)

    @staticmethod
    async def complete_interview_session(
        db: Session,
        session_id: str
    ) -> bool:
        """Mark an interview session as completed.

        Args:
            db: Database session
            session_id: Unique session identifier

        Returns:
            bool: True if session was completed successfully
        """
        stmt = (
            update(InterviewSession)
            .where(InterviewSession.session_id == session_id)
            .values(
                current_state=InterviewState.COMPLETE.value,
                is_active=False,
                updated_at=datetime.now(),
            )
        )
        result = db.execute(stmt)
        db.commit()
        return result.rowcount > 0

    @staticmethod
    async def get_next_unanswered_question(
        db: Session,
        session_id: str
    ) -> Optional[ConversationHistory]:
        """Get the next unanswered question in the interview sequence.

        Args:
            db: Database session
            session_id: Unique session identifier

        Returns:
            Optional[ConversationHistory]: The next unanswered question or None
        """
        stmt = (
            select(ConversationHistory)
            .where(
                and_(
                    ConversationHistory.session_id == session_id,
                    ConversationHistory.user_response.is_(None)
                )
            )
            .order_by(ConversationHistory.sequence_order.asc())
            .limit(1)
        )
        result = db.execute(stmt)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_session_progress(
        db: Session,
        session_id: str
    ) -> Dict[str, Any]:
        """Get the progress summary for an interview session.

        Args:
            db: Database session
            session_id: Session ID

        Returns:
            Dict[str, Any]: Progress summary including completion percentage
        """
        conversations = await ConversationRepository.get_session_history(db, session_id)

        total_questions = len(conversations)
        answered_questions = len([c for c in conversations if c.user_response])

        progress = {
            "session_id": session_id,
            "total_questions": total_questions,
            "answered_questions": answered_questions,
            "completion_percentage": (answered_questions / total_questions * 100) if total_questions > 0 else 0,
            "is_complete": answered_questions == total_questions,
        }

        return progress
