# Project: AI Coding Agent
# Purpose: RoadmapRepository for managing hierarchical roadmaps

"""
Repository for CRUD operations on RoadmapItem entities.
"""

from __future__ import annotations

from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session
from sqlalchemy import select, delete, and_

from src.models import RoadmapItem


class RoadmapRepository:
    """Repository for managing hierarchical roadmap data."""

    @staticmethod
    async def create_roadmap_item(
        db: Session,
        *,
        project_id: int,
        parent_id: Optional[int] = None,
        level: int,
        sequence_order: int,
        title: str,
        description: Optional[str] = None,
        item_type: str,
        agent_role: Optional[str] = None,
        estimated_effort: Optional[str] = None,
        dependencies: Optional[List[int]] = None,
    ) -> RoadmapItem:
        """Create a new roadmap item.

        Args:
            db: Database session
            project_id: ID of the project
            parent_id: Parent roadmap item ID (for hierarchy)
            level: Hierarchy level (1=Phase, 2=Step, 3=Task)
            sequence_order: Order within the same level
            title: Title of the roadmap item
            description: Description of the roadmap item
            item_type: Type of item ('phase', 'step', 'task')
            agent_role: Agent role for task items
            estimated_effort: Estimated effort level
            dependencies: List of dependent item IDs

        Returns:
            RoadmapItem: The created roadmap item
        """
        roadmap_item = RoadmapItem(
            project_id=project_id,
            parent_id=parent_id,
            level=level,
            sequence_order=sequence_order,
            title=title,
            description=description,
            item_type=item_type,
            agent_role=agent_role,
            estimated_effort=estimated_effort,
            dependencies=dependencies or [],
        )
        db.add(roadmap_item)
        db.commit()
        db.refresh(roadmap_item)
        return roadmap_item

    @staticmethod
    async def get_project_roadmap(
        db: Session,
        project_id: int,
        include_completed: bool = True
    ) -> List[RoadmapItem]:
        """Get the complete hierarchical roadmap for a project.

        Args:
            db: Database session
            project_id: ID of the project
            include_completed: Whether to include completed items

        Returns:
            List[RoadmapItem]: Hierarchical roadmap items
        """
        query = select(RoadmapItem).where(RoadmapItem.project_id == project_id)

        if not include_completed:
            query = query.where(RoadmapItem.status != "completed")

        query = query.order_by(
            RoadmapItem.level.asc(),
            RoadmapItem.sequence_order.asc()
        )

        result = db.execute(query)
        return list(result.scalars().all())

    @staticmethod
    async def get_roadmap_item(
        db: Session,
        item_id: int
    ) -> Optional[RoadmapItem]:
        """Get a specific roadmap item by ID.

        Args:
            db: Database session
            item_id: ID of the roadmap item

        Returns:
            Optional[RoadmapItem]: The roadmap item or None
        """
        stmt = select(RoadmapItem).where(RoadmapItem.id == item_id)
        result = db.execute(stmt)
        return result.scalars().first()

    @staticmethod
    async def get_child_items(
        db: Session,
        parent_id: int
    ) -> List[RoadmapItem]:
        """Get all child items for a parent roadmap item.

        Args:
            db: Database session
            parent_id: Parent item ID

        Returns:
            List[RoadmapItem]: Child roadmap items
        """
        stmt = (
            select(RoadmapItem)
            .where(RoadmapItem.parent_id == parent_id)
            .order_by(RoadmapItem.sequence_order.asc())
        )
        result = db.execute(stmt)
        return list(result.scalars().all())

    @staticmethod
    async def update_item_status(
        db: Session,
        item_id: int,
        status: str
    ) -> RoadmapItem:
        """Update the status of a roadmap item.

        Args:
            db: Database session
            item_id: ID of the roadmap item
            status: New status

        Returns:
            RoadmapItem: Updated roadmap item
        """
        stmt = select(RoadmapItem).where(RoadmapItem.id == item_id)
        item = db.execute(stmt).scalars().first()

        if not item:
            raise ValueError(f"Roadmap item {item_id} not found")

        item.status = status
        db.commit()
        db.refresh(item)
        return item

    @staticmethod
    async def get_phases_for_project(
        db: Session,
        project_id: int
    ) -> List[RoadmapItem]:
        """Get all phases (level 1) for a project.

        Args:
            db: Database session
            project_id: ID of the project

        Returns:
            List[RoadmapItem]: Phase-level roadmap items
        """
        stmt = (
            select(RoadmapItem)
            .where(
                and_(
                    RoadmapItem.project_id == project_id,
                    RoadmapItem.level == 1
                )
            )
            .order_by(RoadmapItem.sequence_order.asc())
        )
        result = db.execute(stmt)
        return list(result.scalars().all())

    @staticmethod
    async def get_tasks_for_project(
        db: Session,
        project_id: int,
        agent_role: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[RoadmapItem]:
        """Get all tasks (level 3) for a project with optional filtering.

        Args:
            db: Database session
            project_id: ID of the project
            agent_role: Optional filter by agent role
            status: Optional filter by status

        Returns:
            List[RoadmapItem]: Task-level roadmap items
        """
        conditions = [
            RoadmapItem.project_id == project_id,
            RoadmapItem.level == 3
        ]

        if agent_role:
            conditions.append(RoadmapItem.agent_role == agent_role)

        if status:
            conditions.append(RoadmapItem.status == status)

        stmt = (
            select(RoadmapItem)
            .where(and_(*conditions))
            .order_by(RoadmapItem.sequence_order.asc())
        )
        result = db.execute(stmt)
        return list(result.scalars().all())

    @staticmethod
    async def save_roadmap_from_dict(
        db: Session,
        project_id: int,
        roadmap_data: Dict[str, Any]
    ) -> List[RoadmapItem]:
        """Save a complete roadmap structure from dictionary data.

        Args:
            db: Database session
            project_id: ID of the project
            roadmap_data: Roadmap structure as dictionary

        Returns:
            List[RoadmapItem]: Created roadmap items
        """
        created_items = []

        # Process phases
        for phase_idx, phase_data in enumerate(roadmap_data.get("phases", [])):
            phase = await RoadmapRepository.create_roadmap_item(
                db=db,
                project_id=project_id,
                level=1,
                sequence_order=phase_idx,
                title=phase_data["title"],
                description=phase_data.get("description"),
                item_type="phase"
            )
            created_items.append(phase)

            # Process steps within the phase
            for step_idx, step_data in enumerate(phase_data.get("steps", [])):
                step = await RoadmapRepository.create_roadmap_item(
                    db=db,
                    project_id=project_id,
                    parent_id=phase.id,
                    level=2,
                    sequence_order=step_idx,
                    title=step_data["title"],
                    description=step_data.get("description"),
                    item_type="step"
                )
                created_items.append(step)

                # Process tasks within the step
                for task_idx, task_data in enumerate(step_data.get("tasks", [])):
                    task = await RoadmapRepository.create_roadmap_item(
                        db=db,
                        project_id=project_id,
                        parent_id=step.id,
                        level=3,
                        sequence_order=task_idx,
                        title=task_data["title"],
                        description=task_data.get("description"),
                        item_type="task",
                        agent_role=task_data.get("agent_role"),
                        estimated_effort=task_data.get("estimated_effort")
                    )
                    created_items.append(task)

        return created_items

    @staticmethod
    async def delete_project_roadmap(
        db: Session,
        project_id: int
    ) -> int:
        """Delete all roadmap items for a project.

        Args:
            db: Database session
            project_id: ID of the project

        Returns:
            int: Number of items deleted
        """
        stmt = delete(RoadmapItem).where(RoadmapItem.project_id == project_id)
        result = db.execute(stmt)
        db.commit()
        return result.rowcount

    @staticmethod
    async def get_roadmap_progress(
        db: Session,
        project_id: int
    ) -> Dict[str, Any]:
        """Get progress statistics for a project's roadmap.

        Args:
            db: Database session
            project_id: ID of the project

        Returns:
            Dict[str, Any]: Progress statistics
        """
        items = await RoadmapRepository.get_project_roadmap(db, project_id)

        total_items = len(items)
        completed_items = len([item for item in items if item.status == "completed"])
        in_progress_items = len([item for item in items if item.status == "in_progress"])

        # Group by level
        phases = [item for item in items if item.level == 1]
        steps = [item for item in items if item.level == 2]
        tasks = [item for item in items if item.level == 3]

        return {
            "total_items": total_items,
            "completed_items": completed_items,
            "in_progress_items": in_progress_items,
            "pending_items": total_items - completed_items - in_progress_items,
            "completion_percentage": (completed_items / total_items * 100) if total_items > 0 else 0,
            "phases": {
                "total": len(phases),
                "completed": len([p for p in phases if p.status == "completed"])
            },
            "steps": {
                "total": len(steps),
                "completed": len([s for s in steps if s.status == "completed"])
            },
            "tasks": {
                "total": len(tasks),
                "completed": len([t for t in tasks if t.status == "completed"])
            }
        }
