# Project: AI Coding Agent - Unit Tests
# Purpose: Comprehensive unit tests for error recovery system

import pytest
from unittest.mock import patch

from src.services.error_recovery import ErrorRecoverySystem
from src.models.validation_models import (
    Task,
    TaskType,
    AgentType,
    ErrorType,
    RecoveryResult,
)


class TestErrorRecoverySystem:
    """Comprehensive test suite for ErrorRecoverySystem"""

    @pytest.fixture
    def recovery_system(self):
        """Create an ErrorRecoverySystem instance for testing"""
        return ErrorRecoverySystem(project_root="/test/workspace")

    @pytest.fixture
    def sample_task(self):
        """Create a sample task for testing"""
        return Task(
            title="Test Task",
            description="A test task for error recovery",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND,
            code_files=["src/component.tsx"]
        )

    # Error classification tests

    def test_classify_syntax_error(self, recovery_system):
        """Test classification of syntax errors"""
        syntax_errors = [
            "SyntaxError: invalid syntax (line 5)",
            "IndentationError: expected an indented block",
            "TabError: inconsistent use of tabs and spaces",
            "Parsing error: unexpected token",
            "Missing ) in parenthetical"
        ]

        for error in syntax_errors:
            error_type = recovery_system._classify_error(error)
            assert error_type == ErrorType.SYNTAX_ERROR

    def test_classify_import_error(self, recovery_system):
        """Test classification of import errors"""
        import_errors = [
            "ModuleNotFoundError: No module named 'requests'",
            "ImportError: cannot import name 'Component' from 'react'",
            "Import failed: missing dependency",
            "Package not found: numpy"
        ]

        for error in import_errors:
            error_type = recovery_system._classify_error(error)
            assert error_type == ErrorType.IMPORT_ERROR

    def test_classify_configuration_error(self, recovery_system):
        """Test classification of configuration errors"""
        config_errors = [
            "Configuration file not found",
            "Invalid setting in config.json",
            "Missing environment variable DATABASE_URL",
            "Settings validation failed"
        ]

        for error in config_errors:
            error_type = recovery_system._classify_error(error)
            assert error_type == ErrorType.CONFIGURATION_ERROR

    def test_classify_network_error(self, recovery_system):
        """Test classification of network errors"""
        network_errors = [
            "Connection refused to localhost:8080",
            "Timeout error: request timed out",
            "DNS resolution failed for example.com",
            "SSL certificate error"
        ]

        for error in network_errors:
            error_type = recovery_system._classify_error(error)
            assert error_type == ErrorType.NETWORK_ERROR

    def test_classify_database_error(self, recovery_system):
        """Test classification of database errors"""
        db_errors = [
            "Database connection failed",
            "SQL syntax error in query",
            "Table 'users' does not exist",
            "Constraint violation: unique key"
        ]

        for error in db_errors:
            error_type = recovery_system._classify_error(error)
            assert error_type == ErrorType.DATABASE_ERROR

    def test_classify_permission_error(self, recovery_system):
        """Test classification of permission errors"""
        permission_errors = [
            "Permission denied accessing file",
            "Access forbidden: insufficient privileges",
            "Authentication failed",
            "Unauthorized access attempt"
        ]

        for error in permission_errors:
            error_type = recovery_system._classify_error(error)
            assert error_type == ErrorType.PERMISSION_ERROR

    def test_classify_dependency_error(self, recovery_system):
        """Test classification of dependency errors"""
        dependency_errors = [
            "Version conflict: package A requires B>=2.0, but B==1.5 is installed",
            "Dependency error: incompatible versions",
            "Missing requirement: pytest>=6.0",
            "Package conflict detected"
        ]

        for error in dependency_errors:
            error_type = recovery_system._classify_error(error)
            assert error_type == ErrorType.DEPENDENCY_ERROR

    def test_classify_unknown_error(self, recovery_system):
        """Test classification of unknown errors"""
        unknown_error = "Some completely unrecognized error message"
        error_type = recovery_system._classify_error(unknown_error)
        assert error_type == ErrorType.UNKNOWN_ERROR

    # Main error handling tests

    @pytest.mark.asyncio
    async def test_handle_task_failure_syntax_error(self, recovery_system, sample_task):
        """Test handling task failure with syntax error"""
        syntax_error = SyntaxError("invalid syntax (line 5)")

        # Mock the syntax error recovery method
        expected_result = RecoveryResult(
            success=True,
            actions_taken="Fixed syntax error in component.tsx",
            retry_recommended=True,
            recovery_suggestions=["Re-run the task to validate fix"]
        )

        with patch.object(recovery_system, '_recover_syntax_error', return_value=expected_result) as mock_recover:
            result = await recovery_system.handle_task_failure(sample_task, syntax_error)

            mock_recover.assert_called_once_with(sample_task, str(syntax_error))
            assert result.success
            assert "Fixed syntax error" in result.actions_taken

    @pytest.mark.asyncio
    async def test_handle_task_failure_import_error(self, recovery_system, sample_task):
        """Test handling task failure with import error"""
        import_error = ImportError("No module named 'requests'")

        expected_result = RecoveryResult(
            success=True,
            actions_taken="Installed missing package: requests",
            retry_recommended=True
        )

        with patch.object(recovery_system, '_recover_import_error', return_value=expected_result) as mock_recover:
            result = await recovery_system.handle_task_failure(sample_task, import_error)

            mock_recover.assert_called_once_with(sample_task, str(import_error))
            assert result.success
            assert "Installed missing package" in result.actions_taken

    @pytest.mark.asyncio
    async def test_handle_task_failure_unknown_error(self, recovery_system, sample_task):
        """Test handling task failure with unknown error"""
        unknown_error = Exception("Completely unknown error type")

        expected_result = RecoveryResult(
            success=False,
            actions_taken="Error type not recognized, no specific recovery available",
            retry_recommended=True
        )

        with patch.object(recovery_system, '_recover_unknown_error', return_value=expected_result) as mock_recover:
            result = await recovery_system.handle_task_failure(sample_task, unknown_error)

            mock_recover.assert_called_once_with(sample_task, str(unknown_error))
            assert not result.success
            assert "not recognized" in result.actions_taken

    @pytest.mark.asyncio
    async def test_handle_task_failure_exception_in_recovery(self, recovery_system, sample_task):
        """Test handling task failure when recovery system encounters an error"""
        syntax_error = SyntaxError("invalid syntax")

        # Mock recovery method to raise an exception
        with patch.object(recovery_system, '_recover_syntax_error', side_effect=Exception("Recovery failed")):
            result = await recovery_system.handle_task_failure(sample_task, syntax_error)

            assert not result.success
            assert "Recovery system encountered error" in result.actions_taken

    # Syntax error recovery tests

    @pytest.mark.asyncio
    async def test_recover_syntax_error_success(self, recovery_system, sample_task):
        """Test successful syntax error recovery"""
        error_message = 'File "src/component.tsx", line 5: SyntaxError: invalid syntax'

        # Mock the helper methods
        with patch.object(recovery_system, '_parse_syntax_error') as mock_parse:
            mock_parse.return_value = {
                'file_path': 'src/component.tsx',
                'line_number': '5',
                'error_message': 'invalid syntax'
            }

            with patch.object(recovery_system, '_read_file_with_retry') as mock_read:
                mock_read.return_value = "const component = (\nreturn <div>Hello</div>\n"

                with patch.object(recovery_system, '_fix_syntax_with_llm') as mock_fix:
                    mock_fix.return_value = "const component = () => {\nreturn <div>Hello</div>;\n}"

                    with patch.object(recovery_system, '_apply_code_fix_with_backup') as mock_apply:
                        mock_apply.return_value = True

                        with patch.object(recovery_system, '_validate_syntax_fix') as mock_validate:
                            mock_validate.return_value = True

                            result = await recovery_system._recover_syntax_error(sample_task, error_message)

                            assert result.success
                            assert "Fixed syntax error" in result.actions_taken
                            assert result.retry_recommended

    @pytest.mark.asyncio
    async def test_recover_syntax_error_file_not_found(self, recovery_system, sample_task):
        """Test syntax error recovery when file cannot be identified"""
        error_message = "SyntaxError: invalid syntax (no file specified)"

        with patch.object(recovery_system, '_parse_syntax_error') as mock_parse:
            mock_parse.return_value = {}  # No file path found

            result = await recovery_system._recover_syntax_error(sample_task, error_message)

            assert not result.success
            assert "Could not identify file" in result.actions_taken

    @pytest.mark.asyncio
    async def test_recover_syntax_error_read_file_failure(self, recovery_system, sample_task):
        """Test syntax error recovery when file cannot be read"""
        error_message = 'File "src/component.tsx", line 5: SyntaxError: invalid syntax'

        with patch.object(recovery_system, '_parse_syntax_error') as mock_parse:
            mock_parse.return_value = {'file_path': 'src/component.tsx'}

            with patch.object(recovery_system, '_read_file_with_retry') as mock_read:
                mock_read.return_value = None  # File read failed

                result = await recovery_system._recover_syntax_error(sample_task, error_message)

                assert not result.success
                assert "Could not read file" in result.actions_taken

    @pytest.mark.asyncio
    async def test_recover_syntax_error_llm_unavailable(self, recovery_system, sample_task):
        """Test syntax error recovery when LLM service is unavailable"""
        error_message = 'File "src/component.tsx", line 5: SyntaxError: invalid syntax'

        with patch.object(recovery_system, '_parse_syntax_error') as mock_parse:
            mock_parse.return_value = {'file_path': 'src/component.tsx'}

            with patch.object(recovery_system, '_read_file_with_retry') as mock_read:
                mock_read.return_value = "invalid code"

                with patch.object(recovery_system, '_fix_syntax_with_llm') as mock_fix:
                    mock_fix.return_value = None  # LLM unavailable

                    result = await recovery_system._recover_syntax_error(sample_task, error_message)

                    assert not result.success
                    assert "LLM service unavailable" in result.actions_taken

    @pytest.mark.asyncio
    async def test_recover_syntax_error_fix_validation_failure(self, recovery_system, sample_task):
        """Test syntax error recovery when fix validation fails"""
        error_message = 'File "src/component.tsx", line 5: SyntaxError: invalid syntax'

        with patch.object(recovery_system, '_parse_syntax_error') as mock_parse:
            mock_parse.return_value = {'file_path': 'src/component.tsx'}

            with patch.object(recovery_system, '_read_file_with_retry') as mock_read:
                mock_read.return_value = "original code"

                with patch.object(recovery_system, '_fix_syntax_with_llm') as mock_fix:
                    mock_fix.return_value = "fixed code"

                    with patch.object(recovery_system, '_apply_code_fix_with_backup') as mock_apply:
                        mock_apply.return_value = True

                        with patch.object(recovery_system, '_validate_syntax_fix') as mock_validate:
                            mock_validate.return_value = False  # Validation failed

                            with patch.object(recovery_system, '_restore_from_backup') as mock_restore:
                                result = await recovery_system._recover_syntax_error(sample_task, error_message)

                                mock_restore.assert_called_once()
                                assert not result.success
                                assert "was invalid" in result.actions_taken

    # Import error recovery tests

    @pytest.mark.asyncio
    async def test_recover_import_error_success(self, recovery_system, sample_task):
        """Test successful import error recovery"""
        error_message = "ModuleNotFoundError: No module named 'requests'"

        with patch.object(recovery_system, '_extract_missing_modules') as mock_extract:
            mock_extract.return_value = ['requests']

            with patch.object(recovery_system, '_install_python_package_enhanced') as mock_install:
                mock_install.return_value = True  # Installation successful

                result = await recovery_system._recover_import_error(sample_task, error_message)

                assert result.success
                assert "Installed packages: requests" in result.actions_taken
                assert result.retry_recommended

    @pytest.mark.asyncio
    async def test_recover_import_error_no_modules_identified(self, recovery_system, sample_task):
        """Test import error recovery when no modules can be identified"""
        error_message = "Some unclear import error"

        with patch.object(recovery_system, '_extract_missing_modules') as mock_extract:
            mock_extract.return_value = []  # No modules identified

            result = await recovery_system._recover_import_error(sample_task, error_message)

            assert not result.success
            assert "Could not identify missing module" in result.actions_taken

    @pytest.mark.asyncio
    async def test_recover_import_error_installation_failure(self, recovery_system, sample_task):
        """Test import error recovery when package installation fails"""
        error_message = "ModuleNotFoundError: No module named 'nonexistent_package'"

        with patch.object(recovery_system, '_extract_missing_modules') as mock_extract:
            mock_extract.return_value = ['nonexistent_package']

            with patch.object(recovery_system, '_install_python_package_enhanced') as mock_install:
                mock_install.return_value = False  # Installation failed

                result = await recovery_system._recover_import_error(sample_task, error_message)

                assert not result.success
                assert "Failed to install any of the required packages" in result.actions_taken

    @pytest.mark.asyncio
    async def test_recover_import_error_mixed_results(self, recovery_system, sample_task):
        """Test import error recovery with mixed installation results"""
        error_message = "Multiple missing modules: requests, numpy, nonexistent"

        with patch.object(recovery_system, '_extract_missing_modules') as mock_extract:
            mock_extract.return_value = ['requests', 'numpy', 'nonexistent']

            # Mock installation to succeed for some, fail for others
            def mock_install_side_effect(package):
                return package in ['requests', 'numpy']

            with patch.object(recovery_system, '_install_python_package_enhanced', side_effect=mock_install_side_effect):
                result = await recovery_system._recover_import_error(sample_task, error_message)

                assert not result.success  # Partial failure
                assert "requests, numpy" in result.actions_taken
                assert "nonexistent" in result.recovery_suggestions[1]

    # Configuration error recovery tests

    @pytest.mark.asyncio
    async def test_recover_configuration_error_missing_env_var(self, recovery_system, sample_task):
        """Test configuration error recovery for missing environment variable"""
        error_message = "Missing environment variable DATABASE_URL"

        with patch.object(recovery_system, '_parse_configuration_error') as mock_parse:
            mock_parse.return_value = {'missing_env_var': 'DATABASE_URL'}

            with patch.object(recovery_system, '_get_default_env_value') as mock_default:
                mock_default.return_value = 'sqlite:///test.db'

                with patch('os.environ', {}) as mock_environ:
                    result = await recovery_system._recover_configuration_error(sample_task, error_message)

                    assert result.success
                    assert "Set environment variable DATABASE_URL" in result.actions_taken

    @pytest.mark.asyncio
    async def test_recover_configuration_error_missing_config_file(self, recovery_system, sample_task):
        """Test configuration error recovery for missing configuration file"""
        error_message = "Configuration file config.json not found"

        with patch.object(recovery_system, '_parse_configuration_error') as mock_parse:
            mock_parse.return_value = {'missing_config_file': 'config.json'}

            with patch.object(recovery_system, '_create_default_config_file') as mock_create:
                mock_create.return_value = True

                result = await recovery_system._recover_configuration_error(sample_task, error_message)

                assert result.success
                assert "Created default configuration file" in result.actions_taken

    # Network error recovery tests

    @pytest.mark.asyncio
    async def test_recover_network_error_connectivity_restored(self, recovery_system, sample_task):
        """Test network error recovery when connectivity can be restored"""
        error_message = "Connection refused to api.example.com"

        with patch.object(recovery_system, '_test_network_connectivity_enhanced') as mock_test:
            mock_test.return_value = True  # Connectivity restored

            result = await recovery_system._recover_network_error(sample_task, error_message)

            assert result.success
            assert "Network connectivity verified and restored" in result.actions_taken

    @pytest.mark.asyncio
    async def test_recover_network_error_dns_flush_helps(self, recovery_system, sample_task):
        """Test network error recovery when DNS flush resolves the issue"""
        error_message = "DNS resolution failed for api.example.com"

        with patch.object(recovery_system, '_test_network_connectivity_enhanced') as mock_test:
            # First call fails, second call (after DNS flush) succeeds
            mock_test.side_effect = [False, True]

            with patch.object(recovery_system, '_flush_dns_cache') as mock_flush:
                mock_flush.return_value = True

                result = await recovery_system._recover_network_error(sample_task, error_message)

                assert result.success
                assert "Network connectivity restored after DNS flush" in result.actions_taken

    @pytest.mark.asyncio
    async def test_recover_network_error_cannot_restore(self, recovery_system, sample_task):
        """Test network error recovery when connectivity cannot be restored"""
        error_message = "Network unreachable"

        with patch.object(recovery_system, '_test_network_connectivity_enhanced') as mock_test:
            mock_test.return_value = False  # Cannot restore connectivity

            with patch.object(recovery_system, '_flush_dns_cache') as mock_flush:
                mock_flush.return_value = False

                result = await recovery_system._recover_network_error(sample_task, error_message)

                assert not result.success
                assert "Network connectivity could not be restored" in result.actions_taken
                assert result.retry_recommended  # May recover later

    # Helper method tests

    def test_parse_syntax_error(self, recovery_system):
        """Test parsing syntax error details"""
        error_message = 'File "src/component.py", line 42: SyntaxError: invalid syntax'

        details = recovery_system._parse_syntax_error(error_message)

        assert details['file_path'] == 'src/component.py'
        assert details['line_number'] == '42'
        assert details['error_message'] == 'invalid syntax'

    def test_parse_syntax_error_no_match(self, recovery_system):
        """Test parsing syntax error when pattern doesn't match"""
        error_message = "Some unstructured syntax error"

        details = recovery_system._parse_syntax_error(error_message)

        assert 'file_path' not in details
        assert 'line_number' not in details

    def test_extract_missing_modules(self, recovery_system):
        """Test extracting missing module names from error messages"""
        error_messages = [
            "ModuleNotFoundError: No module named 'requests'",
            "No module named pandas",
            "ImportError: cannot import name 'Component' from 'react'",
            "Multiple modules: requests, numpy not found"
        ]

        # Test first error message
        modules = recovery_system._extract_missing_modules(error_messages[0])
        assert 'requests' in modules

        # Test second error message
        modules = recovery_system._extract_missing_modules(error_messages[1])
        assert 'pandas' in modules

    @pytest.mark.asyncio
    async def test_install_python_package_success(self, recovery_system):
        """Test successful Python package installation"""
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0

            result = await recovery_system._install_python_package('requests')

            assert result is True
            mock_run.assert_called_once()

    @pytest.mark.asyncio
    async def test_install_python_package_failure(self, recovery_system):
        """Test Python package installation failure"""
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 1

            result = await recovery_system._install_python_package('nonexistent_package')

            assert result is False

    @pytest.mark.asyncio
    async def test_install_python_package_exception(self, recovery_system):
        """Test Python package installation with exception"""
        with patch('subprocess.run', side_effect=Exception("Installation error")):
            result = await recovery_system._install_python_package('some_package')

            assert result is False

    # Retry and circuit breaker integration tests

    def test_setup_retry_policies(self, recovery_system):
        """Test that retry policies are properly set up"""
        # This tests that the initialization doesn't raise errors
        # In a real implementation, we would test with the actual retry manager
        assert recovery_system.max_recovery_attempts == 3
        assert recovery_system.recovery_timeout == 300

    def test_setup_circuit_breakers(self, recovery_system):
        """Test that circuit breakers are properly set up"""
        # This tests that the initialization doesn't raise errors
        # In a real implementation, we would test with the actual circuit breaker registry
        assert hasattr(recovery_system, 'logger')

    # Statistics and monitoring tests

    def test_recovery_statistics_tracking(self, recovery_system):
        """Test that recovery statistics are properly tracked"""
        assert recovery_system._recovery_attempts == 0
        assert recovery_system._successful_recoveries == 0
        assert len(recovery_system._recovery_history) == 0

    @pytest.mark.asyncio
    async def test_recovery_statistics_update(self, recovery_system, sample_task):
        """Test that recovery statistics are updated after attempts"""
        syntax_error = SyntaxError("test error")

        # Mock a successful recovery
        with patch.object(recovery_system, '_recover_syntax_error') as mock_recover:
            mock_recover.return_value = RecoveryResult(success=True, actions_taken="Fixed", retry_recommended=True)

            await recovery_system.handle_task_failure(sample_task, syntax_error)

            assert recovery_system._recovery_attempts == 1
            # Note: successful recoveries count may vary based on implementation
            assert len(recovery_system._recovery_history) == 1

    # Edge cases and error handling

    @pytest.mark.asyncio
    async def test_llm_service_property_import_error(self, recovery_system):
        """Test LLM service property when import fails"""
        # Mock import to fail
        with patch('builtins.__import__', side_effect=ImportError("LLM service not available")):
            llm_service = recovery_system.llm_service

            assert llm_service is None

    @pytest.mark.asyncio
    async def test_recover_database_error_not_implemented(self, recovery_system, sample_task):
        """Test that database error recovery returns not implemented"""
        result = await recovery_system._recover_database_error(sample_task, "Database error")

        assert not result.success
        assert "not fully implemented" in result.actions_taken

    @pytest.mark.asyncio
    async def test_recover_permission_error_requires_manual_intervention(self, recovery_system, sample_task):
        """Test that permission error recovery requires manual intervention"""
        result = await recovery_system._recover_permission_error(sample_task, "Permission denied")

        assert not result.success
        assert "manual intervention" in result.actions_taken

    @pytest.mark.asyncio
    async def test_record_recovery_attempt(self, recovery_system, sample_task):
        """Test that recovery attempts are properly recorded"""
        error = Exception("Test error")
        error_type = ErrorType.SYNTAX_ERROR
        recovery_result = RecoveryResult(success=True, actions_taken="Test fix", retry_recommended=True)
        recovery_time = 1.5

        # This method would be called internally, but we can test it directly
        recovery_system._record_recovery_attempt(sample_task, error, error_type, recovery_result, recovery_time)

        # Check that the recovery attempt was recorded with expected fields
        history_entry = recovery_system._recovery_history[0]
        assert 'actions_taken' in history_entry
        assert 'error_message' in history_entry
        assert 'error_type' in history_entry
        assert history_entry['error_type'] == error_type