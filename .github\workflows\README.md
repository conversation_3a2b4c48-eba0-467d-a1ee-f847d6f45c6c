# GitHub Actions Workflows

This directory contains all GitHub Actions workflows for the AI Coding Agent project. Each workflow is designed for specific CI/CD purposes and follows our container-first development approach.

## 🚀 Available Workflows

### Core CI/CD Workflows

#### `ci.yml` - Main CI Pipeline
**Purpose**: Comprehensive continuous integration for the entire codebase

- **Triggers**: Push to main/develop branches, pull requests
- **Actions**:
  - Code quality checks (linting, formatting)
  - Security scanning
  - Unit and integration tests
  - Container image building and validation
  - Dependency vulnerability checks

#### `integration-tests.yml` - Integration Testing
**Purpose**: End-to-end testing of the complete system

- **Triggers**: After successful CI, on release branches
- **Actions**:
  - Full system deployment in test environment
  - API endpoint testing
  - Database operations validation
  - Cross-service communication testing
  - Performance benchmarking

### Deployment Workflows

#### `deploy.yml` - Staging Deployment
**Purpose**: Automated deployment to staging environment

- **Triggers**: Successful CI on develop branch
- **Actions**:
  - Build production-ready containers
  - Deploy to staging environment
  - Run smoke tests
  - Update staging environment URLs

#### `deploy-production.yml` - Production Deployment
**Purpose**: Production deployment with enhanced safety checks

- **Triggers**: Manual trigger or successful staging validation
- **Actions**:
  - Production container building with security scanning
  - Blue-green deployment strategy
  - Database migrations with rollback capability
  - Production health checks
  - Monitoring and alerting setup

### Docker & Infrastructure Workflows

#### `docker-build.yml` - Container Building
**Purpose**: Optimized container image building and caching

- **Triggers**: Changes to Dockerfiles or dependencies
- **Actions**:
  - Multi-stage build optimization
  - Layer caching for faster builds
  - Security scanning of container images
  - Push to container registry

#### `docker-lint.yml` - Docker Configuration Validation
**Purpose**: Ensure Docker configurations follow best practices

- **Triggers**: Changes to Docker-related files
- **Actions**:
  - Dockerfile linting with Hadolint
  - Docker Compose validation
  - Security best practices checking
  - Performance optimization suggestions

#### `publish-images.yml` - Image Publishing
**Purpose**: Publish container images to registries

- **Triggers**: Successful builds, version tags
- **Actions**:
  - Multi-registry publishing (Docker Hub, GHCR, ECR)
  - Image signing and SBOM generation
  - Vulnerability scanning before publish
  - Automated tagging strategies

## 🔧 Configuration

### Environment Variables
All workflows use consistent environment variables defined in repository secrets:

- `DOCKER_REGISTRY`: Container registry URL
- `PRODUCTION_URL`: Production deployment URL
- `STAGING_URL`: Staging deployment URL
- `DATABASE_URL`: Database connection string
- `REDIS_URL`: Redis connection string

### Branch Protection
Workflows are designed to work with branch protection rules:

- Require CI to pass before merge
- Require reviews for production deployments
- Prevent direct pushes to main branch

## 📊 Monitoring & Alerts

### Success/Failure Notifications

- Slack notifications for deployment status
- Email alerts for critical failures
- GitHub issues creation for workflow failures

### Performance Metrics

- Build time tracking
- Test execution time monitoring
- Deployment success rate analytics

## 🛠️ Maintenance

### Adding New Workflows

1. Create new `.yml` file in this directory
2. Follow naming convention: `purpose-action.yml`
3. Include comprehensive documentation
4. Test in feature branch before merging

### Workflow Dependencies

- Workflows use `needs:` to establish dependencies
- CI must pass before deployment workflows run
- Integration tests require successful CI completion

## 🔒 Security

### Secrets Management

- All sensitive data stored in GitHub Secrets
- No hardcoded credentials in workflow files
- Regular rotation of access tokens

### Access Controls

- Workflows respect repository permissions
- Deployment to production requires maintainer approval
- Automated security scanning on all code changes

## 📝 Best Practices

- **Modular Design**: Each workflow has single responsibility
- **Reusable Actions**: Common steps extracted to composite actions
- **Error Handling**: Comprehensive error reporting and cleanup
- **Documentation**: All workflows include inline documentation
- **Testing**: Workflows tested in feature branches before production use

## 🚨 Troubleshooting

### Common Issues

- **Workflow not triggering**: Check branch protection rules and file paths
- **Container build failures**: Verify Dockerfile syntax and base images
- **Deployment timeouts**: Check network connectivity and service dependencies

### Debug Mode
Set `ACTIONS_RUNNER_DEBUG=true` secret to enable debug logging for troubleshooting.

---

For questions about specific workflows, refer to the inline comments in each workflow file or contact the DevOps team.
