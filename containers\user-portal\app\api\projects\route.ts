import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { logger } from "@/lib/logger";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { name, description, template_name } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json({ error: "Project name is required" }, {
        status: 400,
      });
    }

    // Make request to backend API to create project
    const backendResponse = await fetch(
      `${process.env.BACKEND_API_URL}/api/projects`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${session.accessToken}`,
        },
        body: JSON.stringify({
          name,
          description,
          template_name,
        }),
      },
    );

    if (!backendResponse.ok) {
      const error = await backendResponse.text();
      logger.error("Backend project creation failed", { error });
      return NextResponse.json({ error: "Failed to create project" }, {
        status: backendResponse.status,
      });
    }

    const project = await backendResponse.json();
    return NextResponse.json(project);
  } catch (error) {
    logger.error("Project creation error", { error });
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Make request to backend API to get projects
    const backendResponse = await fetch(
      `${process.env.BACKEND_API_URL}/api/projects`,
      {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${session.accessToken}`,
        },
      },
    );

    if (!backendResponse.ok) {
      const error = await backendResponse.text();
      logger.error("Backend projects fetch failed", { error });
      return NextResponse.json({ error: "Failed to fetch projects" }, {
        status: backendResponse.status,
      });
    }

    const projects = await backendResponse.json();
    return NextResponse.json(projects);
  } catch (error) {
    logger.error("Projects fetch error", { error });
    return NextResponse.json({ error: "Internal server error" }, {
      status: 500,
    });
  }
}
