export interface Project {
  id: string;
  name: string;
  description?: string;
  status: string;
  progress_percentage: number;
  total_files_scanned?: number;
  total_files_indexed?: number;
  created_at: string;
  environment_status?: 'Running' | 'Stopped';
  environment_url?: string;
}

export interface EmbeddingProgressUpdate {
  event: "embedding_progress";
  projectId: string;
  status: string;
  progress: number;
  message: string;
  timestamp: string;
}
