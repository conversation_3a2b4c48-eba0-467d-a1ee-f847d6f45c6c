#!/bin/sh
# Ollama container entrypoint script (POSIX shell compatible)
# Handles model preloading and Ollama service startup only
# ChromaDB has been moved to ai-orchestrator-chroma container

set -e

OLLAMA_PORT=${OLLAMA_PORT:-11434}
OLLAMA_HOST=${OLLAMA_HOST:-0.0.0.0}

log() { printf '[ollama-entrypoint] %s\n' "$*"; }

log "Starting Ollama container (ollama: ${OLLAMA_HOST}:${OLLAMA_PORT})"

# Start Ollama service in background, prefer starting as 'ollama' user when running as root
log "Launching ollama serve (will drop to 'ollama' user when running as root)..."
if [ "$(id -u)" = "0" ]; then
    log "Running as root: ensuring model directory ownership and starting service as 'ollama'"
    chown -R ollama:ollama /home/<USER>/.ollama 2>/dev/null || true
    # Start ollama serve as the ollama user and capture its PID
       # Use exec inside the su command so the su process stays as the parent
       # which makes $! point to a child we can wait on and forward signals to.
       su -s /bin/sh - ollama -c 'exec ollama serve' &
       OLLAMA_PID=$!
       # give shell a moment to register child
       sleep 1
       if ! kill -0 "${OLLAMA_PID}" 2>/dev/null; then
           # fallback: start as non-root if the su-wrapped process didn't stay
           ollama serve &
           OLLAMA_PID=$!
    fi
else
    ollama serve &
    OLLAMA_PID=$!
fi

# Forward signals to Ollama process
cleanup() {
    log "Received termination signal, forwarding to Ollama"
    if [ -n "${OLLAMA_PID:-}" ]; then
        log "Stopping Ollama (pid=${OLLAMA_PID})"
        kill -TERM "${OLLAMA_PID}" 2>/dev/null || true
        wait "${OLLAMA_PID}" 2>/dev/null || true
    fi
    exit 0
}

# Set up signal handlers (ignore errors if signals not available)
trap 'cleanup' TERM INT HUP 2>/dev/null || true

# Wait for Ollama HTTP to become responsive
log "Waiting for Ollama HTTP API on port ${OLLAMA_PORT}..."
i=0
while [ "$i" -lt 60 ]; do
    if curl -sS "http://localhost:${OLLAMA_PORT}/api/tags" >/dev/null 2>&1; then
        log "Ollama HTTP API is responsive"
        break
    fi
    i=$((i+1))
    log "Waiting for Ollama... (${i}/60)"
    sleep 2
done

# If models manifest exists, parse and pull each model, then wait for availability
if [ -f "/opt/ollama/models/models.json" ]; then
    log "Found model manifest at /opt/ollama/models/models.json; preloading models"
    if command -v jq >/dev/null 2>&1; then
        models=$(jq -r '.models[].name' /opt/ollama/models/models.json)
        for model in $models; do
            log "Pulling model: ${model}"
            if ollama pull "${model}"; then
                log "Pull initiated for ${model}"
            else
                log "Warning: failed to pull ${model} (continuing)"
                continue
            fi

            # Wait for the model to be reported by the API
            j=0
            while [ "$j" -lt 30 ]; do
                if curl -sS "http://localhost:${OLLAMA_PORT}/api/tags" | grep -F "${model}" >/dev/null 2>&1; then
                    log "Model ${model} is available via API"
                    break
                fi
                j=$((j+1))
                log "Waiting for model ${model} to appear in /api/tags (${j}/30)"
                sleep 2
            done
        done
    else
        log "jq not installed; skipping automatic model pull. To load models manually: ollama pull <model-name>"
    fi
else
    log "No model manifest found at /opt/ollama/models/models.json; skipping pre-pull"
fi

log "Ollama initialization complete; serving on http://localhost:${OLLAMA_PORT}"

log "Waiting for Ollama process (PID: ${OLLAMA_PID})..."
wait "${OLLAMA_PID}"
