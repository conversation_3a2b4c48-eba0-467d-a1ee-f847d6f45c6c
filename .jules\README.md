# Jules Integration Guide for AI Coding Agent Project

## 🚀 **Quick Start for Jules**

Welcome! This project is a **container-first AI coding agent system** optimized for <PERSON> development. Here's everything <PERSON> needs to know:

### **Immediate Setup (Required)**

```bash
# Run this first - it sets up everything <PERSON> needs
cd .jules && ./setup.sh
```

### **Project Architecture Overview**

- **Backend**: FastAPI (Python 3.13+) with Supabase, Redis, PostgreSQL
- **Frontend**: Next.js 14+ with Tailwind CSS
- **Infrastructure**: Multi-container Docker setup with <PERSON><PERSON><PERSON><PERSON> reverse proxy
- **AI Agents**: Multi-agent system with Architect, Backend, Frontend, and Shell agents

### **Critical Jules Guidelines**

#### 📋 **Task Execution Rules**

1. **Read AGENTS.md first** - Contains all agent interfaces and schemas
2. **Follow Copilot Instructions** - Located in `.github/copilot-instructions.md`
3. **Container-Only Development** - All work must happen in Docker containers
4. **Import Preservation** - NEVER remove "unused" imports - utilize them instead
5. **Incremental Changes** - Make minimal, focused changes representing single logical steps

#### 🔧 **Code Quality Standards**

- **Absolute Imports Only**: `from src.services.vector_service import VectorStorageService`
- **Type Hints Required**: Full type annotations for all Python code
- **Async/Await**: Use for all I/O operations (database, network calls)
- **Error Handling**: Catch specific exceptions with structured logging
- **Testing**: 90%+ coverage with pytest

#### 🛡️ **Security Requirements**

- **Non-Root Containers**: All Dockerfiles must specify non-root USER
- **Row Level Security**: All Supabase access protected by RLS policies
- **No Hardcoded Secrets**: Use environment variables or Docker secrets
- **Input Validation**: Pydantic models for all inputs

### **Recommended Jules Task Prompts**

#### **Full Project Review**

```
#### **Full Project Review**

```text
Use the JULES_COMPREHENSIVE_CODEBASE_REVIEW.md prompt for complete project analysis covering all directories, containers, configurations, and documentation.
```

#### **Targeted Service Repair**

```text
Use the JULES_SERVICES_REPAIR_PROMPT.md for systematic repair of the critical services directory only.
```

#### **New Feature Implementation**

```text
Implement [specific feature] following the patterns in AGENTS.md and the Copilot Instructions. Use the existing agent architecture and ensure proper error handling and testing.
```

### **File Structure Jules Should Know**

```text
├── containers/           # All microservices
│   ├── ai-orchestrator/  # Main FastAPI backend
│   ├── user-portal/      # Next.js frontend
│   ├── code-server/      # VS Code in browser
│   └── [other services]
├── AGENTS.md            # Agent interfaces and schemas
├── .github/copilot-instructions.md  # Development guidelines
├── .jules/setup.sh      # Environment setup script
└── docker-compose.yml   # Container orchestration
```

#### **Targeted Service Repair**

```text
Use the JULES_SERVICES_REPAIR_PROMPT.md for systematic repair of the critical services directory only.
```

#### **New Feature Implementation**

```text
Implement [specific feature] following the patterns in AGENTS.md and the Copilot Instructions. Use the existing agent architecture and ensure proper error handling and testing.
```

### **Project File Structure**

```text
├── containers/           # All microservices
│   ├── ai-orchestrator/  # Main FastAPI backend
│   ├── user-portal/      # Next.js frontend
│   ├── code-server/      # VS Code in browser
│   └── [other services]
├── AGENTS.md            # Agent interfaces and schemas
├── .github/copilot-instructions.md  # Development guidelines
├── .jules/setup.sh      # Environment setup script
└── docker-compose.yml   # Container orchestration
```

### **Common Jules Tasks**

1. **Bug Fixes**: Use specific error messages and stack traces
2. **Feature Implementation**: Reference existing agent patterns
3. **Code Reviews**: Check against Copilot Instructions standards
4. **Documentation**: Update AGENTS.md for any interface changes
5. **Testing**: Add comprehensive test coverage for new features

### **Jules Success Criteria**

- ✅ Zero compilation errors project-wide
- ✅ Full type safety with mypy compliance
- ✅ All containers build and start successfully
- ✅ Complete test coverage with passing tests
- ✅ Documentation accuracy and completeness
- ✅ Security vulnerabilities addressed
- ✅ Performance bottlenecks identified and fixed

### **Getting Help**

- **AGENTS.md**: Complete agent interface documentation
- **Copilot Instructions**: Detailed development standards
- **JULES_*.md files**: Specialized prompts for different tasks
- **README.md**: General project overview

**Remember**: This is a complex multi-agent system. Always read AGENTS.md first and follow the Copilot Instructions precisely. The "one agent at a time" rule is enforced by the LockManager for safety.
