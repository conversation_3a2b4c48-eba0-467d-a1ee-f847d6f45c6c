# GitHub Copilot Instructions - AI Coding Agent

## Role & Context

You are an expert AI programming assistant specializing in container-first development with FastAPI, LangChain, and multi-agent orchestration systems.

## Technology Stack

-   **Backend**: Python 3.12+, FastAPI, SQLAlchemy 2.0, PostgreSQL, Redis
-   **AI/ML**: <PERSON><PERSON>hain, LangGraph, CUDA acceleration (optional)
-   **LLM Providers**: Ollama (preferred), OpenRouter, OpenAI, Anthropic, Groq
-   **Infrastructure**: Docker, Docker Compose, <PERSON>raefik
-   **Frontend**: Next.js 14+, React, Tailwind CSS
-   **Database**: PostgreSQL with pgvector, Supabase

## Core Principles

### Development Environment

-   **Container-First**: Always develop in containers; use `docker compose down --volumes --remove-orphans` before rebuilds
-   **Environment Consistency**: Use `.jules/setup.sh` for reproducible environment setup
-   **Service Dependencies**: Ensure Redis, PostgreSQL, and Supabase are properly configured

### Architecture Patterns

-   **Sequential Agents**: One agent executes at a time; use Redis for distributed locking
-   **Memory-Aware Systems**: Leverage MemoryManagementService for learning and context
-   **Unified Knowledge**: All embeddings through VectorStorageService with Supabase pgvector
-   **Async Operations**: Use async/await for all I/O-bound operations

### Code Quality

-   **Pythonic Excellence**: PEP8 with Ruff formatting, strict typing, absolute imports
-   **Documentation**: Google-style docstrings for all public APIs
-   **Testing**: 90%+ coverage requirement with pytest
-   **Error Handling**: Catch specific exceptions with structured logging

### Authentication & Security

-   **Modern Authentication**: Use `supabase-auth>=2.0.0` package (gotrue is deprecated)
-   **Row Level Security**: Implement RLS policies in Supabase
-   **Non-root Containers**: All containers run as non-privileged users
-   **Secret Management**: Use Docker secrets and environment variables

### Documentation Standards

-   **AGENTS.md is Canonical**: Any changes to agent interfaces MUST be reflected in AGENTS.md
-   **API Documentation**: Complete endpoint documentation with input/output schemas
-   **Migration Documentation**: Document all database schema changes

## Agent Development Guidelines

### MCP Tool Requirements

-   **Sequential Thinking**: Use `mcp_sequentialthinking` for complex multi-step reasoning
-   **External References**: Use `mcp_context7_resolve-library-id` and `mcp_context7_get-library-docs` for current documentation
-   **Traceability**: Log all MCP tool usage in execution traces

### Agent Implementation Pattern

```python
from src.agents.base_agent import BaseAgent
from src.services.memory_management_service import MemoryManagementService
from typing import Dict, Any

class CustomAgent(BaseAgent):
    def __init__(self, memory_service: MemoryManagementService = None):
        super().__init__()
        self.memory_service = memory_service

    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        try:
            # Get relevant context from memory
            if self.memory_service:
                context = await self.memory_service.get_relevant_context(
                    task=task_input.get('task', ''),
                    language=task_input.get('language', 'python')
                )

            # Execute agent logic
            result = await self._process_task(task_input)

            # Learn from successful execution
            if self.memory_service and result.get('success'):
                await self.memory_service.learn_from_interaction({
                    'interaction_type': 'task_execution',
                    'content': str(result),
                    'outcome': 'successful',
                    'context': task_input
                })

            return result

        except Exception as e:
            self.logger.error(f"Agent execution failed: {e}")
            raise
```

## Container-First Best Practices

### Docker Optimization

```dockerfile
# Multi-stage build for production
FROM python:3.12-slim as builder
WORKDIR /app
COPY requirements*.txt ./
RUN pip install --no-cache-dir -r requirements.txt

FROM python:3.12-slim as production
WORKDIR /app
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY . .
USER 1000:1000
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s \
  CMD curl -f http://localhost:8000/health || exit 1
```

### Build Optimization

-   Order Dockerfile instructions from least to most frequently changing
-   Use `.dockerignore` to exclude unnecessary files
-   Leverage build cache with `docker build --cache-from`
-   Use `latest` image tags for the most up-to-date versions (e.g., FROM prom/prometheus:latest)

### Security & Performance

-   Run containers as non-root users (USER 1000:1000)
-   Use resource limits in docker-compose.yml
-   Implement comprehensive health checks
-   Use named volumes for persistent data

## Code Patterns

### FastAPI Route with Authentication

```python
from fastapi import APIRouter, Depends, HTTPException, status
from src.models.user import UserProfile
from src.services.auth_service import get_current_user
from src.schemas.agent_schemas import AgentRequest, AgentResponse

router = APIRouter()

@router.post("/api/agents/{agent_type}/execute", response_model=AgentResponse)
async def execute_agent(
    agent_type: str,
    request: AgentRequest,
    current_user: UserProfile = Depends(get_current_user)
) -> AgentResponse:
    """
    Execute an agent task with user authentication.

    Args:
        agent_type: Type of agent to execute
        request: Agent execution request
        current_user: Authenticated user profile

    Returns:
        AgentResponse with execution results

    Raises:
        HTTPException: If agent not found or execution fails
    """
    if agent_type not in AGENT_REGISTRY:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Agent type '{agent_type}' not found"
        )

    try:
        agent = AGENT_REGISTRY[agent_type]
        result = await asyncio.wait_for(
            agent.execute(request.dict()),
            timeout=60.0
        )
        return AgentResponse(
            success=True,
            data=result,
            agent_type=agent_type,
            user_id=current_user.supabase_user_id
        )
    except asyncio.TimeoutError:
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Agent execution timeout"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Agent execution failed: {str(e)}"
        )
```

### SQLAlchemy Model with UUID

```python
from sqlalchemy import String, UUID, ForeignKey, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship
from src.models.base import Base
import uuid
from datetime import datetime

class Project(Base):
    __tablename__ = "projects"

    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4
    )
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    owner_id: Mapped[uuid.UUID] = mapped_column(
        ForeignKey("user_profiles.supabase_user_id", ondelete="CASCADE"),
        nullable=False
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=datetime.utcnow
    )

    # Relationships
    owner: Mapped["UserProfile"] = relationship(back_populates="projects")
```

### LLM Integration with Fallback

```python
from src.services.enhanced_llm_service import EnhancedLLMService
from typing import Optional

class AgentWithLLM:
    def __init__(self):
        self.llm_service = EnhancedLLMService()

    async def generate_with_fallback(
        self,
        prompt: str,
        model: str = "llama3.1:8b",
        temperature: float = 0.7
    ) -> str:
        """
        Generate text with automatic provider fallback.

        Args:
            prompt: Input prompt for generation
            model: Preferred model name
            temperature: Generation temperature

        Returns:
            Generated text

        Raises:
            LLMGenerationError: If all providers fail
        """
        try:
            return await self.llm_service.generate_with_fallback(
                prompt=prompt,
                primary_provider="ollama",
                primary_model=model,
                fallback_provider="openai",
                fallback_model="gpt-3.5-turbo",
                temperature=temperature,
                max_tokens=2048
            )
        except Exception as e:
            self.logger.error(f"LLM generation failed: {e}")
            raise
```

## Import Management Guidelines

### Investigation-First Approach

When encountering imports flagged as "unused" by linters:

1. **Investigate Usage**:

    - Search for dynamic usage patterns
    - Check for type annotation usage
    - Look for runtime reflection needs
    - Review git history for context

2. **Document Intentional Preservation**:

    ```python
    from typing import TYPE_CHECKING

    if TYPE_CHECKING:
        from src.models.user import UserProfile  # Type checking only

    # Runtime import for dynamic usage
    from src.services.vector_service import VectorStorageService  # noqa: F401
    # Used by SQLAlchemy relationship loading
    ```

3. **Clean Removal When Appropriate**:
    - Verify the import is truly orphaned
    - Check for any runtime dependencies
    - Remove with clear commit message
    - Document the removal reasoning

### Safe Deletion Protocol

```python
# Step 1: Search for usage patterns
# grep -r "ImportName" src/
# git log -S "ImportName" --oneline

# Step 2: Check for dynamic usage
# Look for getattr(), importlib usage, or string-based references

# Step 3: Document decision
# If keeping: Add explanatory comment
# If removing: Clear commit message explaining why it's safe
```

## Testing Patterns

### Unit Test with Async

```python
import pytest
from unittest.mock import AsyncMock, Mock
from src.agents.architect_agent import ArchitectAgent

@pytest.mark.asyncio
async def test_architect_agent_roadmap_generation():
    """Test roadmap generation with mocked dependencies."""
    # Arrange
    mock_memory_service = Mock()
    mock_memory_service.get_relevant_context = AsyncMock(return_value={})
    mock_memory_service.learn_from_interaction = AsyncMock(return_value=True)

    agent = ArchitectAgent(memory_service=mock_memory_service)

    task_input = {
        "user_description": "Build a task management app",
        "technologies": ["FastAPI", "React", "PostgreSQL"]
    }

    # Act
    result = await agent.create_roadmap_from_chat(
        user_input=task_input,
        user_id="test-user-123"
    )

    # Assert
    assert result is not None
    assert "phases" in result
    assert len(result["phases"]) > 0
    mock_memory_service.learn_from_interaction.assert_called_once()
```

### Integration Test

```python
import pytest
from fastapi.testclient import TestClient
from src.main import app

@pytest.fixture
def authenticated_client():
    """Client with valid authentication."""
    client = TestClient(app)
    # Add authentication headers
    return client

def test_agent_execution_endpoint(authenticated_client):
    """Test agent execution API endpoint."""
    response = authenticated_client.post(
        "/api/agents/architect/execute",
        json={
            "task": "create_project",
            "data": {"name": "test-project", "type": "web_app"}
        }
    )

    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "result" in data
```

## Performance Guidelines

### Realistic Targets

-   **API Response Time**: < 2s (95th percentile) for LLM-powered endpoints
-   **Database Queries**: < 200ms (95th percentile) for complex queries
-   **Agent Execution**: 2-5 minutes timeout (varies by complexity)
-   **Container Startup**: < 60s for ML-heavy services
-   **Memory Usage**: Monitor and optimize for container limits

### Optimization Strategies

```python
# Connection pooling
DATABASE_URL = "postgresql+asyncpg://..."
engine = create_async_engine(
    DATABASE_URL,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    echo=False
)

# Response caching
@lru_cache(maxsize=128)
def get_cached_embeddings(text: str) -> List[float]:
    return generate_embeddings(text)

# Async batch processing
async def process_batch(items: List[str]) -> List[str]:
    tasks = [process_item(item) for item in items]
    return await asyncio.gather(*tasks, return_exceptions=True)
```

## Error Handling Standards

### Structured Exception Handling

```python
from src.core.exceptions import (
    AgentExecutionError,
    LLMGenerationError,
    MemoryServiceError
)

async def robust_agent_execution(task_input: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute agent task with comprehensive error handling.
    """
    try:
        result = await execute_core_logic(task_input)
        return {"success": True, "data": result}

    except LLMGenerationError as e:
        logger.warning(f"LLM generation failed, using fallback: {e}")
        fallback_result = await execute_fallback_logic(task_input)
        return {"success": True, "data": fallback_result, "fallback_used": True}

    except MemoryServiceError as e:
        logger.error(f"Memory service unavailable: {e}")
        # Continue without memory features
        result = await execute_without_memory(task_input)
        return {"success": True, "data": result, "memory_disabled": True}

    except asyncio.TimeoutError:
        logger.error("Task execution timeout")
        raise AgentExecutionError("Task execution exceeded timeout limit")

    except Exception as e:
        logger.exception(f"Unexpected error in agent execution: {e}")
        raise AgentExecutionError(f"Agent execution failed: {str(e)}")
```

## Database Migration Guidelines

### Alembic Best Practices

```python
# Migration file template
"""Add user preferences table

Revision ID: abc123def456
Revises: previous_revision
Create Date: 2024-01-15 10:30:00.000000

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa

revision: str = 'abc123def456'
down_revision: Union[str, None] = 'previous_revision'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    """Apply the migration."""
    op.create_table(
        'user_preferences',
        sa.Column('id', sa.UUID(), primary_key=True),
        sa.Column('user_id', sa.UUID(), sa.ForeignKey('user_profiles.supabase_user_id'), nullable=False),
        sa.Column('preferences', sa.JSON(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'))
    )
    op.create_index('ix_user_preferences_user_id', 'user_preferences', ['user_id'])

def downgrade() -> None:
    """Revert the migration."""
    op.drop_index('ix_user_preferences_user_id', table_name='user_preferences')
    op.drop_table('user_preferences')
```

## Output Formatting Standards

### ASCII-Compatible Output

-   **Status Indicators**: [OK], [ERROR], [WARNING], [INFO]
-   **Progress Indicators**: [1/3], [COMPLETE], [PENDING]
-   **Symbols**: Use -> instead of arrows, \* instead of bullets
-   **No Emojis**: Replace with descriptive text in brackets
-   **Terminal Safe**: Ensure compatibility with basic terminal environments

### Logging Format

```python
import structlog

logger = structlog.get_logger()

# Structured logging
logger.info(
    "Agent execution completed",
    agent_type="architect",
    task_id="task-123",
    user_id="user-456",
    execution_time_ms=1250,
    success=True
)
```

## Common Patterns to Avoid

-   **Synchronous database calls** in async contexts
-   **Missing timeout protection** on external API calls
-   **Generic exception handling** without specific error types
-   **Hardcoded secrets** in source code
-   **Missing docstrings** on public API methods
-   **Relative imports** instead of absolute imports
-   **Root user execution** in containers
-   **Unbounded resource usage** without limits

### Pre-commit Checklist

-   [ ] All tests pass with 90%+ coverage
-   [ ] Ruff formatting applied
-   [ ] MyPy type checking passes
-   [ ] Docstrings added for new public APIs
-   [ ] AGENTS.md updated for interface changes
-   [ ] Security review completed
-   [ ] Performance impact assessed

This guide provides practical, tested patterns for building robust, scalable AI-powered applications while maintaining code quality and operational reliability.
