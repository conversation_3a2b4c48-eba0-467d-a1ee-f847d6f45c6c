# __PROJECT_NAME__ Environment Configuration
# Copy this file to .env and update the values

# Application Configuration
APP_NAME=__PROJECT_NAME__
APP_PORT=8000
ENVIRONMENT=development
LOG_LEVEL=INFO
DEBUG=true

# Security Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# Database Configuration
DATABASE_URL=***************************************************************/__PROJECT_NAME__
POSTGRES_DB=__PROJECT_NAME__
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-db-password

# Redis Configuration
REDIS_URL=redis://:your-redis-password@__PROJECT_NAME__-redis:6379/0
REDIS_PASSWORD=your-redis-password

# API Configuration
API_V1_STR=/api/v1
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]
ALLOWED_HOSTS=["localhost", "127.0.0.1"]

# File Upload Configuration
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=./uploads

# Logging Configuration
LOG_FORMAT=json
LOG_FILE=./logs/__PROJECT_NAME__.log
LOG_ROTATION=daily
LOG_RETENTION=30

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5
HEALTH_CHECK_INTERVAL=30

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# Build Configuration (for Docker)
BUILD_DATE=
VERSION=1.0.0

# Development Configuration
RELOAD=true
WORKERS=1

# Production Configuration (uncomment for production)
# ENVIRONMENT=production
# DEBUG=false
# RELOAD=false
# WORKERS=4
# LOG_LEVEL=WARNING
