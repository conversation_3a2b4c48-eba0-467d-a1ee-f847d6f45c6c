"""
Integration Test Configuration
Pytest configuration and fixtures for integration testing
"""

import os
import pytest
import time
import requests
from typing import Generator, Dict, Any

# Test environment configuration
TEST_CONFIG = {
    # Default API URL changed to match docker-compose mapping (host port 9003)
    # CI or local runs may override with TEST_API_BASE_URL env var.
    "api_base_url": os.getenv("TEST_API_BASE_URL", "http://127.0.0.1:9003"),
    "frontend_base_url": os.getenv("TEST_FRONTEND_BASE_URL", "http://127.0.0.1:3000"),
    # Increase default startup timeout for slower CI environments; can be overridden with TEST_TIMEOUT
    "test_timeout": int(os.getenv("TEST_TIMEOUT", "120")),
    "poll_interval": float(os.getenv("TEST_POLL_INTERVAL", "2")),
    "cleanup_on_failure": os.getenv("TEST_CLEANUP_ON_FAILURE", "true").lower() == "true",
}

def pytest_configure(config):
    """Configure pytest for integration testing"""
    # Add custom markers
    config.addinivalue_line("markers", "integration: mark test as integration test")
    config.addinivalue_line("markers", "performance: mark test as performance test")
    config.addinivalue_line("markers", "slow: mark test as slow running")

    # Set test environment
    os.environ["PYTEST_CURRENT_TEST"] = "true"

    # Print configuration
    print("\nIntegration Test Configuration:")
    for key, value in TEST_CONFIG.items():
        print(f"  {key}: {value}")


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers"""
    for item in items:
        # Add integration marker to all tests in integration directory
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)

        # Add slow marker to tests that might take longer
        if "performance" in item.name or "multiple" in item.name:
            item.add_marker(pytest.mark.slow)


@pytest.fixture(scope="session")
def test_config() -> Dict[str, Any]:
    """Test configuration fixture"""
    return TEST_CONFIG


def wait_for_service(url: str, timeout: int = 60, poll_interval: float = 2) -> bool:
    """Wait for a service to become available"""
    start_time = time.time()

    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{url}/health" if "/health" not in url else url, timeout=5)
            if response.status_code == 200:
                print(f"Service {url} is ready!")
                return True
        except requests.RequestException:
            pass

        time.sleep(poll_interval)

    print(f"Service {url} did not become available within {timeout} seconds")
    return False


@pytest.fixture(scope="session")
def services_ready(test_config) -> bool:
    """Ensure all services are ready before running tests"""
    print("\nWaiting for services to be ready...")

    api_ready = wait_for_service(test_config["api_base_url"], test_config["test_timeout"])
    frontend_ready = wait_for_service(test_config["frontend_base_url"], test_config["test_timeout"])

    if not (api_ready and frontend_ready):
        pytest.skip("Required services are not available")

    return True


@pytest.fixture(scope="session")
def docker_services():
    """Fixture to manage Docker services for testing"""
    # This could be used to start/stop Docker containers for isolated testing
    # For now, we assume services are already running

    print("Using existing Docker services for integration testing")
    yield

    # Cleanup would go here if we were managing containers


@pytest.fixture
def test_data_cleanup():
    """Fixture to track and cleanup test data"""
    created_resources = {
        "roles": [],
        "configurations": [],
    }

    yield created_resources

    # Cleanup after each test
    if created_resources["roles"]:
        print(f"Cleaning up {len(created_resources['roles'])} test roles...")
        # Cleanup logic would go here


@pytest.fixture
def api_session() -> Generator[requests.Session, None, None]:
    """HTTP session for API requests"""
    session = requests.Session()
    session.headers.update({
        "Content-Type": "application/json",
        "User-Agent": "IntegrationTest/1.0",
    })

    # Add authentication if needed
    auth_token = os.getenv("TEST_AUTH_TOKEN")
    if auth_token:
        session.headers["Authorization"] = f"Bearer {auth_token}"

    yield session

    session.close()


@pytest.fixture
def performance_monitor():
    """Monitor performance during tests"""
    performance_data = {
        "start_time": time.time(),
        "requests": [],
        "errors": [],
    }

    yield performance_data

    # Report performance data
    total_time = time.time() - performance_data["start_time"]
    print("\nTest Performance Summary:")
    print(f"  Total time: {total_time:.2f}s")
    print(f"  Requests made: {len(performance_data['requests'])}")
    print(f"  Errors: {len(performance_data['errors'])}")


def pytest_runtest_setup(item):
    """Setup before each test"""
    if "integration" in [marker.name for marker in item.iter_markers()]:
        # NOTE: Avoid performing a blocking service readiness check before every single
        # test. A session-scoped `services_ready` fixture performs startup checks once.
        # Per-test checks can cause repeated long waits and apparent suite timeouts.
        # If a test truly needs a runtime check, it should use the `services_ready` fixture
        # or call `wait_for_service` explicitly with a short timeout.
        return


def pytest_runtest_teardown(item, nextitem):
    """Cleanup after each test"""
    # Add any test-specific cleanup here
    pass


def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """Custom terminal summary"""
    if hasattr(config, 'integration_stats'):
        terminalreporter.write_sep("=", "Integration Test Summary")
        stats = config.integration_stats
        for key, value in stats.items():
            terminalreporter.write_line(f"{key}: {value}")


# Pytest hooks for better error reporting
def pytest_exception_interact(node, call, report):
    """Custom exception handling"""
    if report.failed and "integration" in str(node.fspath):
        print(f"\nIntegration test failed: {node.name}")
        print(f"Error: {call.excinfo.value}")

        # Could add custom debugging information here
        # like service logs, request/response data, etc.


# Custom assertion helpers
def assert_api_response(response, expected_status=200):
    """Helper for asserting API response structure"""
    assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}: {response.text}"

    if response.headers.get("content-type", "").startswith("application/json"):
        data = response.json()
        if expected_status < 400:
            assert "success" in data or "data" in data or "status" in data
        return data

    return response.text


def assert_role_structure(role_data: Dict[str, Any]):
    """Helper for asserting role configuration structure"""
    required_fields = {"provider", "selected_model", "enabled", "created_at", "updated_at"}
    assert all(field in role_data for field in required_fields), f"Missing required fields in role data: {role_data}"

    assert role_data["provider"] in {"ollama", "openrouter", "openai", "anthropic"}
    assert isinstance(role_data["enabled"], bool)
    assert role_data["created_at"] is not None
    assert role_data["updated_at"] is not None