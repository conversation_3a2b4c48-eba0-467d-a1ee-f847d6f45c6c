import os
import sys
from os.path import abspath, dirname
from pathlib import Path
from typing import Optional

from sqlalchemy import engine_from_config, pool
from alembic import context
from dotenv import load_dotenv

# Add the ai-orchestrator directory to sys.path. This allows `from src...` imports
# as <PERSON> will look for a `src` package within that directory.
# The path is resolved from this file's location: env.py -> alembic -> ai-orchestrator
AI_ORCHESTRATOR_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(AI_ORCHESTRATOR_ROOT))

# Load environment variables from the main project root's .env file.
# The project root is two levels up from the ai-orchestrator directory.
PROJECT_ROOT = AI_ORCHESTRATOR_ROOT.parents[1]
dotenv_path = PROJECT_ROOT / ".env"
if dotenv_path.exists():
    load_dotenv(dotenv_path=dotenv_path)
else:
    print(f"Warning: .env file not found at {dotenv_path}. Using fallback configuration.")

# Function to read secrets from Docker secrets or environment variables
def get_secret_value(secret_name: str, env_var_name: str, default: Optional[str] = None) -> str:
    """Get secret value from Docker secret file or environment variable."""
    # Try Docker secrets first
    secret_file = Path("/run/secrets") / secret_name
    if secret_file.exists():
        try:
            with open(secret_file, 'r') as f:
                value = f.read().strip()
                if value:
                    return value
        except Exception:
            pass

    # Fall back to environment variable
    value = os.environ.get(env_var_name)
    if value:
        return value

    # Use default if provided
    if default:
        return default

    raise ValueError(f"Required secret '{secret_name}' not found in file or environment variable {env_var_name}")

# Set required environment variables from secrets
try:
    os.environ.setdefault('POSTGRES_PASSWORD', get_secret_value('postgres_password', 'POSTGRES_PASSWORD', 'postgres'))
    os.environ.setdefault('JWT_SECRET', get_secret_value('jwt_secret', 'JWT_SECRET', 'super-secret-jwt-token-with-at-least-32-characters-long'))
    os.environ.setdefault('SUPABASE_URL', get_secret_value('supabase_url', 'SUPABASE_URL', 'http://127.0.0.1:54321'))
    os.environ.setdefault('SUPABASE_KEY', get_secret_value('supabase_key', 'SUPABASE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'))
    os.environ.setdefault('SUPABASE_SERVICE_KEY', get_secret_value('supabase_service_key', 'SUPABASE_SERVICE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'))
except ValueError as e:
    print(f"Warning: {e}")
    print("Using default values for missing secrets")

# Alembic Config
config = context.config

# Get the DATABASE_URL from the environment. The `dotenv_path` logic above ensures
# that the .env file has been loaded.
database_url = os.environ.get("DATABASE_URL")
if not database_url:
    raise ValueError("DATABASE_URL is not set. Please check your .env file.")

# Inject the database URL into the config.
# This will override the placeholder in alembic.ini
config.set_main_option("sqlalchemy.url", database_url)

# Target metadata for autogenerate - use the shared Base.metadata
try:
    from src.models.database import Base
    target_metadata = Base.metadata
except ImportError as e:
    print(f"Warning: Could not import models: {e}")
    print("This may be due to missing dependencies or configuration issues")
    target_metadata = None


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode - generates SQL without connecting to DB."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """Run migrations in 'online' mode with database connection."""
    # Get the database URL
    url = config.get_main_option("sqlalchemy.url")
    if url is None:
        raise ValueError("Database URL not configured")

    # Create connectable
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
