from sqlalchemy import Table, <PERSON>umn, Integer, String, DateTime, ForeignKey
from src.models.database import Base
from datetime import datetime, timezone
from src.models.custom_types import UuidVariant

user_project_association = Table(
    'user_project_association', Base.metadata,
    Column('user_id', Integer, Foreign<PERSON>ey('user_profiles.id', ondelete='CASCADE'), primary_key=True),
    <PERSON>umn('project_id', UuidVariant, ForeignKey('projects.id', ondelete='CASCADE'), primary_key=True),
    <PERSON>umn('role', String(50), default='member', nullable=False),
    Column('created_at', DateTime(timezone=True), default=lambda: datetime.now(timezone.utc)),
    extend_existing=True,
)
