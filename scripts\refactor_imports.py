import ast
import os
from pathlib import Path

class ImportRefactorer(ast.NodeTransformer):
    def __init__(self, file_path):
        self.file_path = file_path
        self.changed = False

    def visit_ImportFrom(self, node: ast.ImportFrom):
        if node.module and 'src.' in node.module:
            return node

        # This is a simplified example. A real implementation would need
        # to be much more robust and handle many edge cases.
        if node.module and not node.module.startswith('src'):
            new_module = f"src.{node.module}"
            self.changed = True
            return ast.ImportFrom(module=new_module, names=node.names, level=node.level)
        return node

def refactor_imports_in_file(file_path):
    with open(file_path, 'r+') as f:
        content = f.read()
        tree = ast.parse(content)
        refactorer = ImportRefactorer(file_path)
        new_tree = refactorer.visit(tree)
        if refactorer.changed:
            f.seek(0)
            f.write(ast.unparse(new_tree))
            f.truncate()
            print(f"Refactored imports in {file_path}")

def main():
    start_dir = Path("./containers/ai-orchestrator/src")
    for root, _, files in os.walk(start_dir):
        for file in files:
            if file.endswith(".py"):
                refactor_imports_in_file(Path(root) / file)

if __name__ == "__main__":
    main()
