#!/usr/bin/env python3
"""
Setup script to create default role configuration for AI Coding Agent.

This script ensures that the role configuration file exists with proper
default settings for all supported agent roles.
"""

import os
import json
import asyncio
from datetime import datetime
from pathlib import Path


def create_default_config() -> dict:
    """
    Create default configuration with API keys from environment variables.

    Returns:
        dict: Default configuration with environment-based API keys
    """
    # Read API keys from environment variables
    openrouter_key = os.getenv("OPENROUTER_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")

    now = datetime.utcnow().isoformat()

    return {
        "roles": {
            "architect": {
                "provider": "openrouter",
                "available_models": ["anthropic/claude-3-sonnet", "openai/gpt-4"],
                "selected_model": "anthropic/claude-3-sonnet",
                "api_key": openrouter_key,
                "cost_limit": 100.0,
                "max_tokens": 4096,
                "temperature": 0.7,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            },
            "backend": {
                "provider": "ollama",
                "available_models": ["codellama:13b", "deepseek-coder:6.7b"],
                "selected_model": "codellama:13b",
                "api_key": None,  # Ollama doesn't need API key
                "cost_limit": None,
                "max_tokens": 4096,
                "temperature": 0.3,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            },
            "frontend": {
                "provider": "openai",
                "available_models": ["gpt-4o", "gpt-3.5-turbo"],
                "selected_model": "gpt-4o",
                "api_key": openai_key,
                "cost_limit": 75.0,
                "max_tokens": 2048,
                "temperature": 0.5,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            },
            "issue_fix": {
                "provider": "anthropic",
                "available_models": ["claude-3-sonnet", "claude-3-haiku"],
                "selected_model": "claude-3-sonnet",
                "api_key": anthropic_key,
                "cost_limit": 50.0,
                "max_tokens": 3072,
                "temperature": 0.4,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            },
            "reviewer": {
                "provider": "ollama",
                "available_models": ["llama3:8b", "mistral:7b"],
                "selected_model": "llama3:8b",
                "api_key": None,
                "cost_limit": None,
                "max_tokens": 2048,
                "temperature": 0.2,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            },
            "tester": {
                "provider": "openrouter",
                "available_models": ["anthropic/claude-3-haiku", "openai/gpt-3.5-turbo"],
                "selected_model": "anthropic/claude-3-haiku",
                "api_key": openrouter_key,
                "cost_limit": 25.0,
                "max_tokens": 1024,
                "temperature": 0.3,
                "enabled": True,
                "created_at": now,
                "updated_at": now
            }
        },
        "metadata": {
            "version": "1.0.0",
            "created_at": now,
            "updated_at": now,
            "total_roles": 6
        }
    }


async def setup_configuration():
    """Create the default role configuration file if it doesn't exist."""

    # Ensure data directory exists
    config_dir = Path("/app/data")
    config_dir.mkdir(exist_ok=True, parents=True)

    # Set proper permissions (appuser:appuser with 755)
    os.chmod(config_dir, 0o755)

    config_file = config_dir / "role_config.json"

    if not config_file.exists():
        print(f"Creating default role configuration at: {config_file}")

        # Create default configuration
        default_config = create_default_config()

        # Write configuration file
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)

        # Set proper file permissions
        os.chmod(config_file, 0o644)

        print("✅ Default role configuration created successfully")
        print(f"📁 Configuration file: {config_file}")
        print(f"🔧 Roles configured: {len(default_config['roles'])}")

        # Display configured roles
        for role_name, role_config in default_config['roles'].items():
            provider = role_config['provider']
            model = role_config['selected_model']
            enabled = "✅" if role_config['enabled'] else "❌"
            print(f"   {enabled} {role_name}: {provider}/{model}")

    else:
        print(f"✅ Role configuration already exists at: {config_file}")

        # Validate existing configuration
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                existing_config = json.load(f)

            if 'roles' in existing_config and existing_config['roles']:
                print(f"🔧 Found {len(existing_config['roles'])} configured roles")
                for role_name in existing_config['roles'].keys():
                    print(f"   - {role_name}")
            else:
                print("⚠️ Warning: Configuration file exists but appears to be invalid")

        except (json.JSONDecodeError, IOError) as e:
            print(f"❌ Error reading existing configuration: {e}")


def main():
    """Main entry point for the setup script."""
    print("🚀 Setting up AI Coding Agent role configuration...")
    print(f"📍 Working directory: {os.getcwd()}")
    print(f"👤 Running as user: {os.getuid() if hasattr(os, 'getuid') else 'unknown'}")

    # Run the async setup
    asyncio.run(setup_configuration())

    print("✅ Setup completed successfully!")


if __name__ == "__main__":
    main()
