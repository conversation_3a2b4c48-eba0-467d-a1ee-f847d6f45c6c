#!/bin/bash
# Comprehensive monitoring stack health check script
# Tests all monitoring services: Prometheus, Loki, Grafana, Promtail

set -e

echo " Comprehensive Monitoring Stack Health Check"
echo "=============================================="
echo ""

SERVICES=("prometheus" "loki" "grafana" "promtail")
HEALTH_ENDPOINTS=(
    "http://localhost:9090/-/ready"    # Prometheus readiness
    "http://localhost:3100/ready"      # Loki readiness
    "http://localhost:3000/api/health" # Grafana health
    "http://localhost:9080/ready"      # Promtail readiness
)

# Function to check service health
check_service() {
    local service=$1
    local endpoint=$2
    local expected_code=${3:-200}

    echo "Checking $service..."

    if ! docker ps | grep -q "$service"; then
        echo " $service container is not running"
        return 1
    fi

    # Wait for service to be ready
    local retries=30
    local count=0

    while [ $count -lt $retries ]; do
        if curl -s -o /dev/null -w "%{http_code}" "$endpoint" | grep -q "$expected_code"; then
            echo " $service is healthy"
            return 0
        fi
        count=$((count + 1))
        sleep 2
    done

    echo " $service health check failed after $retries attempts"
    return 1
}

# Check each service
FAILED_SERVICES=()
for i in "${!SERVICES[@]}"; do
    service="${SERVICES[$i]}"
    endpoint="${HEALTH_ENDPOINTS[$i]}"

    if ! check_service "$service" "$endpoint"; then
        FAILED_SERVICES+=("$service")
    fi
    echo ""
done

# Summary
echo " Health Check Summary"
echo "======================"

if [ ${#FAILED_SERVICES[@]} -eq 0 ]; then
    echo " All monitoring services are healthy!"
    echo ""
    echo "Service URLs:"
    echo "- Prometheus: http://localhost:9090"
    echo "- Grafana:    http://localhost:3000"
    echo "- Loki:       http://localhost:3100"
    echo ""
    echo "Health Endpoints:"
    echo "- Prometheus: http://localhost:9090/-/ready"
    echo "- Prometheus: http://localhost:9090/-/healthy"
    echo "- Grafana:    http://localhost:3000/api/health"
    echo "- Loki:       http://localhost:3100/ready"
    echo "- Promtail:   http://localhost:9080/ready"
else
    echo " Failed services: ${FAILED_SERVICES[*]}"
    echo ""
    echo "Troubleshooting tips:"
    echo "1. Check container logs: docker logs <service_name>"
    echo "2. Verify network connectivity: docker network ls"
    echo "3. Check service dependencies and startup order"
    exit 1
fi
