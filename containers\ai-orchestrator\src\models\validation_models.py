# Project: AI Coding Agent
# Purpose: Core validation models and data structures for roadmap enforcement

from datetime import datetime, timedelta
from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import uuid


class TaskType(str, Enum):
    """Enumeration of supported task types for validation"""
    CREATE_COMPONENT = "create_component"
    CREATE_API_ENDPOINT = "create_api_endpoint"
    DATABASE_MIGRATION = "database_migration"
    SETUP_ROUTING = "setup_routing"
    CONTAINER_SETUP = "container_setup"
    CONFIGURATION = "configuration"
    TESTING = "testing"
    DEPLOYMENT = "deployment"


class ErrorType(str, Enum):
    """Classification of errors for targeted recovery"""
    SYNTAX_ERROR = "syntax_error"
    IMPORT_ERROR = "import_error"
    CONFIGURATION_ERROR = "configuration_error"
    DEPENDENCY_ERROR = "dependency_error"
    VALIDATION_ERROR = "validation_error"
    NETWORK_ERROR = "network_error"
    DATABASE_ERROR = "database_error"
    PERMISSION_ERROR = "permission_error"
    UNKNOWN_ERROR = "unknown_error"


class AgentType(str, Enum):
    """Types of AI agents in the system"""
    ARCHITECT = "architect"
    FRONTEND = "frontend"
    BACKEND = "backend"
    SHELL = "shell"
    ISSUE_FIX = "issue_fix"


class ExecutionStatus(str, Enum):
    """Status of execution for tasks, steps, and phases"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLBACK = "rollback"
    CANCELLED = "cancelled"


class ApprovalStatus(str, Enum):
    """Status of approval requests"""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    TIMEOUT = "timeout"


class ValidationResult(BaseModel):
    """Result of a validation check"""
    is_valid: bool
    details: Optional[str] = None
    error: Optional[str] = None
    warnings: List[str] = Field(default_factory=list)
    metrics: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime = Field(default_factory=datetime.now)

    @classmethod
    def success(cls, details: str = "Validation passed") -> "ValidationResult":
        """Create a successful validation result"""
        return cls(is_valid=True, details=details)

    @classmethod
    def failure(cls, error: str, details: Optional[str] = None) -> "ValidationResult":
        """Create a failed validation result"""
        return cls(is_valid=False, error=error, details=details)

    @classmethod
    def from_checks(cls, checks: List["ValidationResult"]) -> "ValidationResult":
        """Aggregate multiple validation results"""
        all_valid = all(check.is_valid for check in checks)
        errors = [check.error for check in checks if check.error]
        warnings = []
        for check in checks:
            warnings.extend(check.warnings)

        return cls(
            is_valid=all_valid,
            error="; ".join(errors) if errors else None,
            details=f"Aggregated {len(checks)} validation checks",
            warnings=warnings
        )


class TaskResult(BaseModel):
    """Result of task execution"""
    success: bool
    output: Optional[str] = None
    error: Optional[str] = None
    files_created: List[str] = Field(default_factory=list)
    files_modified: List[str] = Field(default_factory=list)
    duration_seconds: Optional[float] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Task(BaseModel):
    """Individual task within a step"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    description: str
    type: TaskType
    agent_type: AgentType

    # Validation requirements
    expected_files: List[str] = Field(default_factory=list)
    code_files: List[str] = Field(default_factory=list)
    test_command: Optional[str] = None
    integration_checks: List[str] = Field(default_factory=list)

    # Task-specific parameters
    parameters: Dict[str, Any] = Field(default_factory=dict)

    # Status tracking
    status: ExecutionStatus = ExecutionStatus.PENDING
    assigned_agent: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3


class Step(BaseModel):
    """Collection of tasks that form a logical unit"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    description: str
    tasks: List[Task]

    # Dependencies
    depends_on: List[str] = Field(default_factory=list)  # Step IDs

    # Status tracking
    status: ExecutionStatus = ExecutionStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class Phase(BaseModel):
    """Major phase containing multiple steps"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    description: str
    steps: List[Step]

    # Phase configuration
    requires_approval: bool = False
    create_checkpoint: bool = True

    # Status tracking
    status: ExecutionStatus = ExecutionStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class Roadmap(BaseModel):
    """Complete roadmap containing all phases"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    description: str
    phases: List[Phase]

    # Configuration
    user_id: str
    strict_validation: bool = True
    auto_recovery_enabled: bool = True

    # Status tracking
    status: ExecutionStatus = ExecutionStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class RecoveryResult(BaseModel):
    """Result of error recovery attempt"""
    success: bool
    actions_taken: str
    retry_recommended: bool
    recovery_suggestions: List[str] = Field(default_factory=list)
    estimated_fix_time: Optional[int] = None  # seconds


class HealthCheckResult(BaseModel):
    """Result of health check validation"""
    is_healthy: bool
    checks_passed: int
    total_checks: int
    failed_checks: List[str] = Field(default_factory=list)
    performance_metrics: Dict[str, float] = Field(default_factory=dict)
    recommendations: List[str] = Field(default_factory=list)

    @classmethod
    def aggregate(cls, results: List["HealthCheckResult"]) -> "HealthCheckResult":
        """Aggregate multiple health check results"""
        total_healthy = sum(1 for r in results if r.is_healthy)
        all_failed = []
        all_recommendations = []
        combined_metrics = {}

        for result in results:
            all_failed.extend(result.failed_checks)
            all_recommendations.extend(result.recommendations)
            combined_metrics.update(result.performance_metrics)

        return cls(
            is_healthy=total_healthy == len(results),
            checks_passed=total_healthy,
            total_checks=len(results),
            failed_checks=all_failed,
            performance_metrics=combined_metrics,
            recommendations=all_recommendations
        )


class ApprovalRequest(BaseModel):
    """User approval request"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str
    item_type: str  # 'phase', 'step', 'task'
    item_id: str
    title: str
    description: str

    status: ApprovalStatus = ApprovalStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.now)
    timeout_at: datetime = Field(default_factory=lambda: datetime.now() + timedelta(hours=24))
    responded_at: Optional[datetime] = None

    # Context for approval
    changes_summary: List[str] = Field(default_factory=list)
    risk_assessment: Optional[str] = None


class Checkpoint(BaseModel):
    """System checkpoint for rollback capability"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    roadmap_id: str
    type: str  # 'initial', 'phase_start', 'step_complete', etc.
    timestamp: datetime = Field(default_factory=datetime.now)

    # State information
    project_hash: str
    database_backup_path: Optional[str] = None
    files_backup_path: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

    # Recovery information
    rollback_instructions: List[str] = Field(default_factory=list)


class ProductionReadinessResult(BaseModel):
    """Result of production readiness assessment"""
    is_ready: bool
    checks: List[ValidationResult]
    recommendations: List[str] = Field(default_factory=list)
    critical_issues: List[str] = Field(default_factory=list)
    security_score: Optional[float] = None
    performance_score: Optional[float] = None


class ValidationGateStatus(str, Enum):
    """Status of validation gates in pipeline execution"""
    PENDING = "pending"
    VALIDATING = "validating"
    PASSED = "passed"
    FAILED = "failed"
    BYPASSED = "bypassed"


class PipelineStage(BaseModel):
    """Individual stage in the execution pipeline"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str

    # Stage configuration
    requires_validation: bool = True
    can_run_parallel: bool = False
    timeout_seconds: int = 300

    # Dependencies
    depends_on: List[str] = Field(default_factory=list)  # Stage IDs

    # Execution tracking
    status: ExecutionStatus = ExecutionStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    # Associated tasks/steps
    tasks: List[str] = Field(default_factory=list)  # Task IDs


class ValidationGate(BaseModel):
    """Validation gate that blocks pipeline progression"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str

    # Gate configuration
    gate_type: str  # 'automatic', 'manual', 'approval'
    blocking: bool = True
    timeout_seconds: Optional[int] = None

    # Validation rules
    validation_rules: List[str] = Field(default_factory=list)
    custom_validators: List[str] = Field(default_factory=list)

    # Gate status
    status: ValidationGateStatus = ValidationGateStatus.PENDING
    validation_results: List[ValidationResult] = Field(default_factory=list)

    # Timing
    opened_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None


class ExecutionPipeline(BaseModel):
    """Complete execution pipeline with stages and validation gates"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str

    # Pipeline components
    stages: List[PipelineStage]
    validation_gates: List[ValidationGate]

    # Configuration
    roadmap_id: str
    user_id: str
    strict_mode: bool = True

    # Execution state
    status: ExecutionStatus = ExecutionStatus.PENDING
    current_stage_id: Optional[str] = None

    # Timing
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_duration_seconds: Optional[int] = None


class ExecutionState(BaseModel):
    """Current state of pipeline execution"""
    pipeline_id: str

    # Current position
    current_stage_id: Optional[str] = None
    current_phase: str = "initialization"
    progress_percentage: float = 0.0

    # Execution context
    variables: Dict[str, Any] = Field(default_factory=dict)
    artifacts: Dict[str, str] = Field(default_factory=dict)  # artifact_name -> path

    # State persistence
    serialized_state: Dict[str, Any] = Field(default_factory=dict)
    last_checkpoint_id: Optional[str] = None

    # Status tracking
    status: ExecutionStatus = ExecutionStatus.PENDING
    error_context: Optional[Dict[str, Any]] = None

    # Timing
    updated_at: datetime = Field(default_factory=datetime.now)


class RetryPolicy(BaseModel):
    """Configuration for retry strategies"""
    max_attempts: int = 3
    base_delay_seconds: float = 1.0
    max_delay_seconds: float = 60.0
    backoff_multiplier: float = 2.0

    # Retry conditions
    retry_on_errors: List[str] = Field(default_factory=list)  # Error types to retry
    no_retry_on_errors: List[str] = Field(default_factory=list)  # Error types to not retry


class CircuitBreakerState(str, Enum):
    """State of circuit breaker"""
    CLOSED = "closed"  # Normal operation
    OPEN = "open"     # Failing, rejecting calls
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreakerConfig(BaseModel):
    """Circuit breaker configuration"""
    failure_threshold: int = 5
    recovery_timeout_seconds: int = 60
    success_threshold: int = 3  # Successes needed to close from half-open

    # Monitoring window
    monitoring_window_seconds: int = 300
    min_calls_threshold: int = 10


class ApprovalRequestDetails(BaseModel):
    """Detailed information for approval requests"""
    request_type: str  # 'phase_completion', 'destructive_operation', 'manual_gate'

    # Context information
    affected_components: List[str] = Field(default_factory=list)
    changes_summary: List[str] = Field(default_factory=list)
    risk_assessment: str = "low"

    # Files and changes
    modified_files: List[str] = Field(default_factory=list)
    created_files: List[str] = Field(default_factory=list)
    deleted_files: List[str] = Field(default_factory=list)

    # Impact analysis
    potential_issues: List[str] = Field(default_factory=list)
    rollback_plan: List[str] = Field(default_factory=list)

    # Validation results
    validation_passed: bool = True
    validation_warnings: List[str] = Field(default_factory=list)


class ApprovalDecision(BaseModel):
    """User's approval decision with context"""
    approval_id: str
    decision: ApprovalStatus

    # Decision context
    comments: Optional[str] = None
    conditions: List[str] = Field(default_factory=list)

    # User information
    user_id: str
    decided_at: datetime = Field(default_factory=datetime.now)

    # Additional actions
    require_revalidation: bool = False
    create_checkpoint: bool = False


class StateSnapshot(BaseModel):
    """Snapshot of system state for rollback purposes"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    checkpoint_id: str

    # State data
    execution_state: ExecutionState
    pipeline_state: Dict[str, Any] = Field(default_factory=dict)

    # File system state
    file_checksums: Dict[str, str] = Field(default_factory=dict)
    directory_structure: Dict[str, Any] = Field(default_factory=dict)

    # Database state (if applicable)
    database_schema_hash: Optional[str] = None
    database_backup_path: Optional[str] = None

    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    size_bytes: int = 0
    compression_used: bool = False


class RollbackPlan(BaseModel):
    """Plan for rolling back system state"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    target_checkpoint_id: str

    # Rollback steps
    rollback_steps: List[Dict[str, Any]] = Field(default_factory=list)

    # Safety checks
    verify_steps: List[str] = Field(default_factory=list)
    safety_conditions: List[str] = Field(default_factory=list)

    # Risk assessment
    risk_level: str = "medium"
    estimated_duration_seconds: int = 60

    # Approval requirements
    requires_approval: bool = True
    created_at: datetime = Field(default_factory=datetime.now)


class ValidationMetrics(BaseModel):
    """Metrics for validation performance tracking"""
    total_validations: int = 0
    passed_validations: int = 0
    failed_validations: int = 0

    # Timing metrics
    average_validation_time_ms: float = 0.0
    max_validation_time_ms: float = 0.0
    min_validation_time_ms: float = 0.0

    # Error tracking
    error_categories: Dict[str, int] = Field(default_factory=dict)
    recovery_success_rate: float = 0.0

    # Performance indicators
    validation_throughput: float = 0.0  # validations per second
    resource_usage: Dict[str, float] = Field(default_factory=dict)

    # Time window
    measurement_start: datetime = Field(default_factory=datetime.now)
    measurement_end: Optional[datetime] = None