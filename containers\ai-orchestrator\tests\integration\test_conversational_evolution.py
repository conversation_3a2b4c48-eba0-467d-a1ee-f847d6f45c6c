"""
Integration tests for Conversational Evolution functionality.

This test suite validates the end-to-end workflows for:
1. Request Changes flow (FrontendAgent + DeploymentAgent)
2. Maintenance Agent dependency updates

All external dependencies (LLM, file operations, deployments) are mocked
to ensure fast, reliable, and deterministic test execution.
"""

import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from httpx import AsyncClient
from sqlalchemy.orm import Session
from src.agents.deployment_agent import DeploymentAgent
from src.agents.frontend_agent import FrontendAgent
from src.agents.maintenance_agent import MaintenanceAgent
from src.agents.shell_agent import ShellAgent
from src.models import Project, UserProfile
from src.repository.project_repository import ProjectRepository
from src.services.redis_service import get_project_cache


@pytest.mark.asyncio
async def test_modification_workflow(async_client: AsyncClient):
    """
    Test the full "request changes" flow from prompt to preview URL.

    This test validates the integration between:
    - Project retrieval
    - FrontendAgent code modification
    - Git operations via ShellAgent
    - Preview deployment creation
    """
    # Setup: Create a test project
    project_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())

    # Mock project data
    mock_project = MagicMock()
    mock_project.id = project_id
    mock_project.project_name = "test-project"
    mock_project.project_hostname = "test-project.example.com"
    mock_project.status = "active"

    # Mock user
    mock_user = MagicMock()
    mock_user.supabase_user_id = user_id

    # Setup mocks for project creation and retrieval
    with (
        patch("src.repository.project_repository.ProjectRepository") as mock_repo_class,
        patch("src.agents.frontend_agent.FrontendAgent") as mock_frontend_class,
        patch("src.agents.deployment_agent.DeploymentAgent") as mock_deployment_class,
        patch("src.agents.shell_agent.ShellAgent") as mock_shell_class,
    ):
        # Configure repository mock
        mock_repo_instance = AsyncMock()
        mock_repo_instance.get_project_by_id.return_value = mock_project
        mock_repo_instance.get_project_workspace_path.return_value = "/tmp/test-project"
        mock_repo_class.return_value = mock_repo_instance

        # Configure FrontendAgent mock
        mock_frontend_instance = AsyncMock()
        mock_frontend_instance.execute.return_value = {
            "success": True,
            "files": ["src/components/TestComponent.tsx"],
            "status": "success",
        }
        mock_frontend_class.return_value = mock_frontend_instance

        # Configure DeploymentAgent mock
        mock_deployment_instance = AsyncMock()
        mock_deployment_instance.create_preview_deployment.return_value = {
            "success": True,
            "preview_url": "https://preview-abc123.test-project.example.com",
        }
        mock_deployment_class.return_value = mock_deployment_instance

        # Configure ShellAgent mock for git operations
        mock_shell_instance = AsyncMock()
        mock_shell_instance.execute.return_value = {
            "returncode": 0,
            "stdout": "success",
            "stderr": "",
        }
        mock_shell_class.return_value = mock_shell_instance

        # Mock authentication
        with (
            patch("src.router.project_router.get_current_user", return_value=mock_user),
            patch(
                "src.router.project_router.get_project_repository", return_value=mock_repo_instance
            ),
            patch("src.router.project_router.get_shell_agent", return_value=mock_shell_instance),
            patch(
                "src.router.project_router.get_frontend_agent", return_value=mock_frontend_instance
            ),
            patch(
                "src.router.project_router.get_deployment_agent",
                return_value=mock_deployment_instance,
            ),
        ):
            # Execute the request
            response = await async_client.post(
                f"/api/projects/{project_id}/request-changes",
                json={"prompt": "change the header color to red"},
            )

            # Assertions
            assert response.status_code == 200

            response_data = response.json()
            assert "preview_url" in response_data
            assert response_data["preview_url"] == "https://preview-abc123.test-project.example.com"
            assert "branch_name" in response_data
            assert "commit_hash" in response_data

            # Verify that agents were called correctly
            mock_frontend_instance.execute.assert_called_once()
            call_args = mock_frontend_instance.execute.call_args[0][0]
            assert call_args["prompt"] == "change the header color to red"
            assert call_args["project_id"] == project_id
            assert call_args["user_id"] == user_id

            mock_deployment_instance.create_preview_deployment.assert_called_once()


@pytest.mark.asyncio
async def test_maintenance_dependency_update_workflow():
    """
    Test the MaintenanceAgent's dependency update workflow.

    This test validates:
    - Git branch creation
    - Dependency update execution
    - Test running
    - Commit and push operations
    - Preview deployment creation
    """
    # Setup test data
    project_path = "/tmp/test-maintenance-project"
    project_id = str(uuid.uuid4())

    # Create MaintenanceAgent with mocked dependencies
    with (
        patch("src.agents.shell_agent.ShellAgent") as mock_shell_class,
        patch("src.agents.deployment_agent.DeploymentAgent") as mock_deployment_class,
    ):
        # Configure ShellAgent mock
        mock_shell_instance = AsyncMock()
        mock_shell_instance.execute.side_effect = [
            # Git checkout -b
            {"returncode": 0, "stdout": "", "stderr": ""},
            # npm update
            {"returncode": 0, "stdout": "updated dependencies", "stderr": ""},
            # npm test
            {"returncode": 0, "stdout": "tests passed", "stderr": ""},
            # git add
            {"returncode": 0, "stdout": "", "stderr": ""},
            # git commit
            {"returncode": 0, "stdout": "", "stderr": ""},
            # git push
            {"returncode": 0, "stdout": "", "stderr": ""},
        ]
        mock_shell_class.return_value = mock_shell_instance

        # Configure DeploymentAgent mock
        mock_deployment_instance = AsyncMock()
        mock_deployment_instance.execute.return_value = {
            "success": True,
            "data": {"preview_url": "https://preview-maintenance-123.example.com"},
        }
        mock_deployment_class.return_value = mock_deployment_instance

        # Create MaintenanceAgent instance
        maintenance_agent = MaintenanceAgent(
            shell_agent=mock_shell_instance, deployment_agent=mock_deployment_instance
        )

        # Execute dependency update
        result = await maintenance_agent.execute(
            "dependency_update", project_path=project_path, project_id=project_id
        )

        # Assertions
        assert "status" in result
        assert result["status"] in ["preview_created", "success"]

        if result["status"] == "preview_created":
            assert "url" in result
            assert result["url"] == "https://preview-maintenance-123.example.com"
            assert "branch" in result

        # Verify ShellAgent calls
        assert mock_shell_instance.execute.call_count >= 6  # All git and npm operations

        # Verify DeploymentAgent was called for preview creation
        mock_deployment_instance.execute.assert_called_once()
        deployment_call_args = mock_deployment_instance.execute.call_args[0][0]
        assert deployment_call_args["action"] == "create_preview_deployment"
        assert deployment_call_args["project_id"] == project_id


@pytest.mark.asyncio
async def test_maintenance_dependency_update_with_test_failure():
    """
    Test MaintenanceAgent behavior when tests fail after dependency update.

    This ensures the agent properly rolls back changes when tests fail.
    """
    project_path = "/tmp/test-maintenance-fail-project"
    project_id = str(uuid.uuid4())

    with (
        patch("src.agents.shell_agent.ShellAgent") as mock_shell_class,
        patch("src.agents.deployment_agent.DeploymentAgent") as mock_deployment_class,
    ):
        # Configure ShellAgent mock with test failure
        mock_shell_instance = AsyncMock()
        mock_shell_instance.execute.side_effect = [
            # Git checkout -b
            {"returncode": 0, "stdout": "", "stderr": ""},
            # npm update
            {"returncode": 0, "stdout": "updated dependencies", "stderr": ""},
            # npm test (failure)
            {"returncode": 1, "stdout": "", "stderr": "tests failed"},
            # Rollback: git reset
            {"returncode": 0, "stdout": "", "stderr": ""},
            # Rollback: git checkout main
            {"returncode": 0, "stdout": "", "stderr": ""},
            # Rollback: git branch -D
            {"returncode": 0, "stdout": "", "stderr": ""},
        ]
        mock_shell_class.return_value = mock_shell_instance

        # Deployment agent should not be called when tests fail
        mock_deployment_instance = AsyncMock()
        mock_deployment_class.return_value = mock_deployment_instance

        maintenance_agent = MaintenanceAgent(
            shell_agent=mock_shell_instance, deployment_agent=mock_deployment_instance
        )

        result = await maintenance_agent.execute(
            "dependency_update", project_path=project_path, project_id=project_id
        )

        # Assertions
        assert result["status"] == "failed"
        assert "Tests failed after update" in result["reason"]

        # Verify rollback operations were called
        assert mock_shell_instance.execute.call_count == 6

        # Verify deployment was NOT called
        mock_deployment_instance.execute.assert_not_called()


@pytest.mark.asyncio
async def test_modification_workflow_with_frontend_failure(async_client: AsyncClient):
    """
    Test request-changes flow when FrontendAgent fails.

    This ensures proper error handling and cleanup when code modification fails.
    """
    project_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())

    mock_project = MagicMock()
    mock_project.id = project_id
    mock_project.project_name = "test-project"
    mock_project.project_hostname = "test-project.example.com"
    mock_project.status = "active"

    mock_user = MagicMock()
    mock_user.supabase_user_id = user_id

    with (
        patch("src.repository.project_repository.ProjectRepository") as mock_repo_class,
        patch("src.agents.frontend_agent.FrontendAgent") as mock_frontend_class,
        patch("src.agents.deployment_agent.DeploymentAgent") as mock_deployment_class,
        patch("src.agents.shell_agent.ShellAgent") as mock_shell_class,
    ):
        mock_repo_instance = AsyncMock()
        mock_repo_instance.get_project_by_id.return_value = mock_project
        mock_repo_instance.get_project_workspace_path.return_value = "/tmp/test-project"
        mock_repo_class.return_value = mock_repo_instance

        # FrontendAgent fails
        mock_frontend_instance = AsyncMock()
        mock_frontend_instance.execute.return_value = {
            "success": False,
            "error": "Failed to generate component",
        }
        mock_frontend_class.return_value = mock_frontend_instance

        # These should not be called due to frontend failure
        mock_deployment_instance = AsyncMock()
        mock_deployment_class.return_value = mock_deployment_instance

        mock_shell_instance = AsyncMock()
        mock_shell_class.return_value = mock_shell_instance

        with (
            patch("src.router.project_router.get_current_user", return_value=mock_user),
            patch(
                "src.router.project_router.get_project_repository", return_value=mock_repo_instance
            ),
            patch("src.router.project_router.get_shell_agent", return_value=mock_shell_instance),
            patch(
                "src.router.project_router.get_frontend_agent", return_value=mock_frontend_instance
            ),
            patch(
                "src.router.project_router.get_deployment_agent",
                return_value=mock_deployment_instance,
            ),
        ):
            response = await async_client.post(
                f"/api/projects/{project_id}/request-changes",
                json={"prompt": "change the header color to red"},
            )

            # Should return 500 due to frontend failure
            assert response.status_code == 500

            response_data = response.json()
            assert "detail" in response_data
            assert "Code modification failed" in response_data["detail"]

            # Verify deployment was not called
            mock_deployment_instance.create_preview_deployment.assert_not_called()
