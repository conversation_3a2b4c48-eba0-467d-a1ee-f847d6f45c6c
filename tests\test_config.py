"""Test configuration and environment setup"""
import os
import pytest
from unittest.mock import patch, AsyncMock, Mock

# Set test environment variables
os.environ.update({
    "TESTING": "1",
    "DATABASE_URL": "sqlite:///test.db",
    "ENVIRONMENT": "test",
    "OLLAMA_BASE_URL": "http://localhost:11434",
    "OPENROUTER_API_KEY": "test_key",
    "SUPABASE_URL": "https://test.supabase.co",
    "SUPABASE_ANON_KEY": "test_anon_key",
    "SUPABASE_SERVICE_KEY": "test_service_key",
    "JWT_SECRET": "test_jwt_secret",
    "POSTGRES_PASSWORD": "test_password"
})

@pytest.fixture
def mock_llm_service():
    """Mock LLM service that returns predictable responses"""
    mock_service = Mock()
    mock_service.generate_response = AsyncMock(return_value={
        "content": "Mock LLM response",
        "model": "test-model",
        "usage": {"tokens": 100}
    })
    mock_service.list_available_models = AsyncMock(return_value=["llama3.2", "codellama"])
    mock_service.check_model_availability = AsyncMock(return_value=True)
    return mock_service

@pytest.fixture
def mock_ollama_service():
    """Mock Ollama service"""
    mock_service = Mock()
    mock_service.generate = AsyncMock(return_value={
        "response": "Mock Ollama response",
        "done": True
    })
    mock_service.list_models = AsyncMock(return_value=[
        {"name": "llama3.2", "size": "4GB"},
        {"name": "codellama", "size": "8GB"}
    ])
    return mock_service

@pytest.fixture
def mock_openrouter_service():
    """Mock OpenRouter service"""
    mock_service = Mock()
    mock_service.generate = AsyncMock(return_value={
        "choices": [{"message": {"content": "Mock OpenRouter response"}}],
        "usage": {"total_tokens": 150}
    })
    return mock_service

@pytest.fixture(autouse=True)
def mock_external_dependencies():
    """Mock external dependencies for all tests"""
    # Mock aiohttp properly
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.text = AsyncMock(return_value="OK")

    mock_session = AsyncMock()
    mock_session.get.return_value.__aenter__ = AsyncMock(return_value=mock_response)
    mock_session.get.return_value.__aexit__ = AsyncMock(return_value=None)

    with patch('aiohttp.ClientSession', return_value=mock_session), \
         patch('aiohttp.ClientTimeout', return_value=AsyncMock()):
        yield
