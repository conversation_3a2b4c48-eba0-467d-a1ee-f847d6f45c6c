import logging
import os
import uuid
from typing import Optional

import aiofiles.os
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from src.agents.shell_agent import ShellAgent, ShellAgentError
from src.core.config import settings
from src.models.project import Project, ProjectStatus
from src.schemas.project_schemas import ProjectCreateRequest

logger = logging.getLogger(__name__)


class ProjectRepositoryError(Exception):
    """Custom exception for project repository errors."""

    pass


class ProjectService:
    """
    Service for managing user projects within the shared workspace.

    Handles creation and deletion of project directories and Git repositories,
    along with database persistence of project metadata.
    """

    def __init__(self, db: Session):
        """
        Initialize the ProjectService.

        Args:
            db: SQLAlchemy database session
        """
        self.db = db
        self.shell_agent = ShellAgent()
        self.workspace_base_path = "/workspace"

    async def create_project(self, project_data: ProjectCreateRequest, user_id: int) -> Project:
        """
        Create a new project, optionally from a template.

        This method implements the foundational infrastructure for project workspaces:
        1. Creates a project record in the database to get a unique project ID.
        2. Creates a dedicated directory for the project inside the shared workspace volume.
        3. If a template is specified, copies the template files into the new directory.
        4. Initializes a Git repository inside the new directory using the ShellAgent.
        5. Updates the project record with the path information.

        Args:
            project_data: Project creation data, including optional template name.
            user_id: ID of the user creating the project.

        Returns:
            The newly created Project object.

        Raises:
            ProjectRepositoryError: If any step in the project creation fails.
        """
        logger.info(f"Creating project '{project_data.name}' for user {user_id}")

        project = Project(
            name=project_data.name,
            description=project_data.description,
            owner_id=user_id,
            status=ProjectStatus.CREATING.value,
            progress_percentage=0.0,
        )
        self.db.add(project)
        self.db.commit()
        self.db.refresh(project)

        project_path = os.path.join(self.workspace_base_path, str(user_id), str(project.id))
        logger.info(f"Project path will be: {project_path}")

        try:
            # Step 2: Create project directory
            mkdir_result = await self.shell_agent.execute(
                {"command": ["mkdir", "-p", project_path]}
            )
            if mkdir_result.get("returncode") != 0:
                raise ProjectRepositoryError(
                    f"Failed to create project directory: {mkdir_result.get('stderr')}"
                )
            logger.info(f"Successfully created project directory: {project_path}")

            # Step 3: Handle project template if provided
            if project_data.template_name:
                logger.info(f"Initializing project from template: {project_data.template_name}")
                template_path = os.path.join(
                    "templates/project-templates", project_data.template_name
                )

                if not os.path.isdir(template_path):
                    raise ProjectRepositoryError(
                        f"Template '{project_data.template_name}' not found at {template_path}"
                    )

                copy_command = ["cp", "-R", f"{template_path}/.", project_path]
                copy_result = await self.shell_agent.execute({"command": copy_command})
                if copy_result.get("returncode") != 0:
                    raise ProjectRepositoryError(
                        f"Failed to copy template files: {copy_result.get('stderr')}"
                    )
                logger.info(f"Successfully copied template files to {project_path}")

            # Step 4: Initialize Git repository
            git_init_success = await self._initialize_git_repository(
                project_path=project_path,
                project_name=project_data.name,
                from_template=bool(project_data.template_name),
            )
            if not git_init_success:
                raise ProjectRepositoryError("Git repository initialization failed")
            logger.info(f"Successfully initialized Git repository in: {project_path}")

            # Step 5: Update project record with path and set status to active
            project.docker_volume_name = project_path
            project.status = ProjectStatus.ACTIVE.value
            project.progress_percentage = 10.0
            self.db.commit()
            self.db.refresh(project)

            logger.info(
                f"Successfully created and provisioned project: {project.name} (ID: {project.id})"
            )
            return project

        except Exception as e:
            logger.error(
                f"Project creation failed for project {project.id}. Cleaning up. Error: {e}",
                exc_info=True,
            )
            if await aiofiles.os.path.exists(project_path):
                await self._cleanup_project_directory(project_path)
            self.db.delete(project)
            self.db.commit()
            raise ProjectRepositoryError(f"Failed to create project: {e}") from e

    async def _initialize_git_repository(
        self, project_path: str, project_name: str, from_template: bool = False
    ) -> bool:
        """
        Initialize a Git repository inside the specified project directory using ShellAgent.

        Args:
            project_path: The absolute path to the project directory inside the container.
            project_name: Name of the project for initial commit message.
            from_template: If true, indicates the project was created from a template.

        Returns:
            True if Git initialization was successful, False otherwise.
        """
        logger.info(f"Starting Git initialization in: {project_path}")
        try:
            if not from_template:
                # Create a README file for empty projects
                readme_content = (
                    f"# {project_name}\n\nThis project was created by the AI Coding Agent."
                )
                async with aiofiles.open(os.path.join(project_path, "README.md"), "w") as f:
                    await f.write(readme_content)

            commit_message = f"Initial commit: Project '{project_name}' created by AI Coding Agent"
            if from_template:
                commit_message += " from template"

            # Sequence of shell commands to initialize git
            git_commands = [
                ["git", "init"],
                ["git", "config", "user.email", "<EMAIL>"],
                ["git", "config", "user.name", "AI Coding Agent"],
                ["git", "add", "."],  # Add all files in the directory
                ["git", "commit", "-m", commit_message],
            ]

            for cmd in git_commands:
                result = await self.shell_agent.execute({"command": cmd, "cwd": project_path})
                if result.get("returncode") != 0:
                    logger.error(
                        f"Git command failed: {' '.join(cmd)}. Error: {result.get('stderr')}"
                    )
                    return False

            logger.info(f"Git repository successfully initialized in: {project_path}")
            return True

        except (ShellAgentError, Exception) as e:
            logger.error(
                f"Unexpected error during Git initialization in {project_path}: {e}", exc_info=True
            )
            return False

    async def _cleanup_project_directory(self, project_path: str) -> None:
        """Clean up project directory if project creation fails or on deletion."""
        logger.warning(f"Cleaning up project directory: {project_path}")
        try:
            if await aiofiles.os.path.isdir(project_path):
                result = await self.shell_agent.execute({"command": ["rm", "-rf", project_path]})
                if result.get("returncode") == 0:
                    logger.info(f"Successfully cleaned up directory: {project_path}")
                else:
                    logger.error(
                        f"Failed to cleanup directory {project_path}: {result.get('stderr')}"
                    )
        except Exception as e:
            logger.error(f"Error during directory cleanup for {project_path}: {e}", exc_info=True)

    async def delete_project(self, project_id: int, user_id: int) -> None:
        """
        Delete a project and clean up its directory.

        Args:
            project_id: ID of the project to delete
            user_id: ID of the user requesting deletion

        Raises:
            ValueError: If project not found or user doesn't own it
            Exception: If database or filesystem operations fail
        """
        logger.info(f"Deleting project: {project_id} for user: {user_id}")

        project: Optional[Project] = (
            self.db.query(Project)
            .filter(Project.id == project_id, Project.owner_id == user_id)
            .first()
        )

        if not project:
            logger.warning(
                f"User {user_id} failed to delete project {project_id}. Reason: Not found or no permission."
            )
            raise ValueError(
                "Project not found or user does not have permission to delete this project"
            )

        project_path = project.docker_volume_name  # This field now holds the path
        if project_path and await aiofiles.os.path.exists(project_path):
            await self._cleanup_project_directory(project_path)

        logger.info(f"Removing project from database: {project_id}")
        try:
            self.db.delete(project)
            self.db.commit()
            logger.info(f"Successfully deleted project: {project.name} (ID: {project_id})")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to delete project from database: {e}", exc_info=True)
            raise Exception(f"Failed to delete project from database: {e}") from e

    async def get_project_by_id(self, project_id: int, user_id: int) -> Optional[Project]:
        """
        Retrieve a project by ID, ensuring it belongs to the specified user.
        This is an async-compatible method for use in API endpoints.
        """
        # This is a synchronous DB call, but the service method is async
        # to fit into the async-first design of the application.
        # For a fully async setup, an async DB driver (e.g., asyncpg) would be used.
        project = (
            self.db.query(Project)
            .filter(Project.id == project_id, Project.owner_id == user_id)
            .first()
        )

        if not project:
            logger.warning(
                f"User {user_id} tried to access unauthorized or non-existent project {project_id}"
            )
            return None

        return project

    async def list_user_projects(self, user_id: int) -> list[Project]:
        """
        List all projects for a specific user.

        Args:
            user_id: ID of the user

        Returns:
            List of Project objects owned by the user
        """
        stmt = select(Project).where(Project.owner_id == user_id)
        result = self.db.execute(stmt)
        return list(result.scalars().all())

    async def get_project_info(self, project_id: int, user_id: int) -> Optional[dict]:
        """
        Get information for a project, with ownership verification.
        """
        project = await self.get_project_by_id(project_id, user_id)
        if not project:
            return None

        return {
            "project_id": project.id,
            "project_name": project.name,
            "project_path": project.docker_volume_name,  # Field is repurposed
            "status": project.status,
            "created_at": project.created_at.isoformat() if project.created_at else None,
        }

    async def verify_project_resources(self, project_id: int, user_id: int) -> dict:
        """
        Verify that the directory for a project exists, with ownership verification.
        """
        project = await self.get_project_by_id(project_id, user_id)
        if not project:
            return {"error": "Project not found or user not authorized"}

        results = {
            "project_id": project.id,
            "project_name": project.name,
            "directory_exists": False,
            "path_configured": bool(project.docker_volume_name),
            "error": None,
        }

        if not results["path_configured"]:
            results["error"] = "Project path is not configured in the database."
            return results

        try:
            results["directory_exists"] = await aiofiles.os.path.isdir(project.docker_volume_name)
        except Exception as e:
            results["error"] = f"Error checking directory: {e}"
            logger.error(
                f"Error verifying project resources for project {project_id}: {e}", exc_info=True
            )

        return results

    @staticmethod
    async def get_project_completion_percentage(project_id: uuid.UUID, db: AsyncSession) -> float:
        """
        Calculate project completion percentage asynchronously.

        Args:
            project_id: UUID of the project
            db: Async database session

        Returns:
            Completion percentage as a float (0.0 to 100.0)
        """
        from src.models.roadmap import RoadmapItem

        # Count total roadmap items for this project
        total_items_result = await db.execute(
            select(RoadmapItem).filter(RoadmapItem.project_id == project_id)
        )
        total_items = len(total_items_result.scalars().all())

        if total_items == 0:
            return 0.0

        # Count completed items
        completed_items_result = await db.execute(
            select(RoadmapItem).filter(
                RoadmapItem.project_id == project_id, RoadmapItem.status == "completed"
            )
        )
        completed_items = len(completed_items_result.scalars().all())

        return (completed_items / total_items) * 100.0


# Dependency injection function for FastAPI
def get_project_service(db: Session) -> ProjectService:
    """
    Dependency injection function for ProjectService.
    """
    return ProjectService(db=db)
