# AI Coding Agent

A comprehensive AI-powered coding assistant platform built with modern web
technologies and container orchestration.

## 🚀 Features

- **Multi-Provider LLM Support**: OpenRouter, Ollama, OpenAI, Anthropic
- **Container-First Architecture**: Complete Docker orchestration with
  code-server integration
- **Real-time Collaboration**: WebSocket-based communication and workspace
  sharing
- **Vector Database**: Supabase pgvector-powered semantic search and RAG
  capabilities
- **Multi-tenant Workspaces**: Dynamic code-server provisioning with
  template-based setup
- **Interview-First Project Creation**: AI-powered template selection based on
  user requirements
- **Production-Ready**: Security hardening, monitoring, and scalable deployment

## 🏗️ Architecture

- **Backend**: FastAPI with async SQLAlchemy and Pydantic validation
- **Frontend**: Next.js 14+ with Tailwind CSS and TypeScript
- **Database**: PostgreSQL with pgvector for embeddings (Supabase)
- **Cache**: Redis for session management and caching
- **Vector Store**: Supabase pgvector for unified semantic search
- **LLM Server**: Ollama for local model serving
- **Reverse Proxy**: Traefik for routing and SSL termination
- **Code Server**: VS Code in the browser with extensions support
- **Monitoring**: Prometheus/Grafana stack for observability

### Service Architecture

- **ai-orchestrator**: FastAPI backend with Supabase pgvector integration
- **ollama**: Local LLM server for model inference
- **user-portal**: Next.js frontend application
- **redis**: Caching and session management
- **traefik**: Reverse proxy and load balancer

### 🔒 Two-Network Security Model

The application implements a dual-network architecture for enhanced security:

- **Web Network (DMZ)**: Public-facing services accessible via Traefik
  - Traefik, AI Orchestrator, User Portal, Code Server instances
- **Internal Network**: Secure backend services (never exposed externally)
  - Redis, Ollama, PostgreSQL, Docker Proxy

The AI Orchestrator serves as a controlled bridge between networks, implementing
the principle of least privilege at the network level. See
[Two-Network Security Model](docs/TWO_NETWORK_SECURITY_MODEL.md) for detailed
documentation.

## 🛠️ Quick Start

### Prerequisites

- Docker and Docker Compose (Docker Desktop recommended for host networking)
- Node.js 18+ and Python 3.11+
- Supabase CLI (for local development)
- At least 8GB RAM and 4 CPU cores recommended
- 20GB+ available disk space

**Note**: The application uses `gateway.docker.internal` to connect to host
services (Supabase CLI). This requires Docker Desktop or equivalent host
networking support. If using Docker Engine without Desktop, you may need to
adjust network configurations.

### Development Setup

1. **Start Supabase** (local database):

   ```bash
   supabase start
   ```

2. **Start Application Services**:

   ```bash
   # 🚀 RECOMMENDED: Use the startup script
   ./scripts/start-dev.sh

   # OR: Manual command (includes code-server for development)
   docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d
   ```

3. **Access Services**:
   - **User Portal**: `http://portal.localhost`
   - **AI Orchestrator API**: `http://api.localhost`
   - **Code Server**: `http://localhost:8080`
   - **Supabase Studio**: `http://127.0.0.1:54323`

### Production Setup

```bash
# Use production startup script
./scripts/start-prod.sh

# OR: Manual command
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 📚 Documentation

- **[Development Guide](DEVELOPMENT.md)**: Complete setup and development
  workflow
- **[Docker Compose Guide](docs/docker-compose-guide.md)**: Understanding the
  multi-file configuration
- **[Architecture Overview](docs/architecture/)**: System design and component
  interactions
- **[API Documentation](docs/api/)**: REST API endpoints and WebSocket protocols

## 🔧 Configuration Files

| File                          | Purpose                                    |
| ----------------------------- | ------------------------------------------ |
| `docker-compose.yml`          | Base services (core application)           |
| `docker-compose.dev.yml`      | Development overrides (code-server, ports) |
| `docker-compose.prod.yml`     | Production hardening (security, limits)    |
| `docker-compose.supabase.yml` | Local Supabase stack                       |

**⚠️ Important**: Always use multi-file compose for development to include
code-server:

```bash
# ✅ Correct
docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# ❌ Wrong (missing code-server)
docker compose up -d
```

## 🐳 Docker Hub Rate Limit Avoidance

To avoid Docker Hub rate limits, the project includes alternative container
registries and configuration options:

### Quick Setup

```bash
# Use alternative registries
cp .env.dockerhub .env
docker-compose --env-file .env up -d

# Or authenticate with Docker Hub for higher limits
docker login
docker-compose up -d
```

### Advanced Options

- **Local Registry Mirror**: Use
  `docker-compose -f docker-compose.registry.yml up -d`
- **Alternative Registries**: Bitnami, CrunchyData, and specific version tags
- **Authentication**: Docker Hub authentication provides higher rate limits

See
[Docker Hub Rate Limit Avoidance Guide](docs/docker/DOCKER_HUB_RATE_LIMIT_AVOIDANCE.md)
for detailed instructions.

## Running Tests

### Running Unit Tests (Fast)

To run all fast unit tests, simply run the `pytest` command from the root of the
repository:

```bash
pytest
```

### Running Integration Tests (Slow)

Integration tests require the Docker services to be running.

Start the services:

```bash
docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

Run the integration tests: Use the -m flag to select only the tests marked with
integration.

```bash
pytest -m "integration"
```

## 📊 Monitoring

Access monitoring dashboards:

- **Traefik Dashboard**: `http://traefik.localhost`
- **Prometheus**: `http://localhost:9090`
- **Grafana**: `http://localhost:3000`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature`
3. Make your changes following the
   [coding guidelines](docs/CODING_GUIDELINES.md)
4. Run tests: `python -m pytest`
5. Submit a pull request

## 📄 License

This project is licensed under the terms specified in [LICENSE](LICENSE).

## 🔗 Related Links

- [Supabase CLI Documentation](https://supabase.com/docs/guides/cli)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
