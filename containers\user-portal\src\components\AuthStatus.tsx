"use client";
import React from 'react';
import { useSession, signIn, signOut } from "next-auth/react";
import Link from "next/link";

export function AuthStatus(): React.JSX.Element {
  const { data: session, status } = useSession();

  if (status === 'loading') return <span className="text-sm">Loading...</span>;
  if (!session) {
    return (
      <button onClick={() => signIn()} className="text-sm underline">Login</button>
    );
  }
  return (
    <div className="flex items-center gap-3 text-sm">
      <span>{session.user?.email}</span>
      <Link href="/dashboard" className="underline">Dashboard</Link>
      <button onClick={() => signOut()} className="underline">Logout</button>
    </div>
  );
}

export default AuthStatus;
