// API client and interfaces for Project Importer Extension

import axios, { AxiosResponse, AxiosError } from 'axios';

// Configuration
export const API_BASE_URL = process.env.NODE_ENV === 'production'
    ? 'http://ai-orchestrator:8000/api/v1'
    : 'http://localhost:8000/api/v1';

// TypeScript interfaces for API request/response models

export interface CloneProjectRequest {
    url: string;
    project_name: string;
}

export interface CloneProjectResponse {
    id: string;
    name: string;
    status: string;
    message?: string;
}

export interface UploadProjectRequest {
    files: FileData[];
    project_name: string;
    description?: string;
}

export interface FileData {
    name: string;
    path: string;
    content: string;
    size: number;
}

export interface UploadProjectResponse {
    id: string;
    name: string;
    status: string;
    message?: string;
}

export interface ApiError {
    code: string;
    message: string;
    details?: any;
}

// API client class
export class ApiClient {
    private baseUrl: string;

    constructor(baseUrl: string = API_BASE_URL) {
        this.baseUrl = baseUrl;
    }

    async cloneProject(request: CloneProjectRequest): Promise<CloneProjectResponse> {
        try {
            const response: AxiosResponse<CloneProjectResponse> = await axios.post(
                `${this.baseUrl}/projects/clone`,
                request,
                {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );
            return response.data;
        } catch (error) {
            if (axios.isAxiosError(error) && error.response) {
                const axiosError = error as AxiosError<ApiError>;
                throw new Error(axiosError.response?.data?.message || 'Clone failed');
            }
            throw error as Error;
        }
    }

    async uploadProject(request: UploadProjectRequest): Promise<UploadProjectResponse> {
        try {
            const response: AxiosResponse<UploadProjectResponse> = await axios.post(
                `${this.baseUrl}/projects/upload`,
                request,
                {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );
            return response.data;
        } catch (error) {
            if (axios.isAxiosError(error) && error.response) {
                const axiosError = error as AxiosError<ApiError>;
                throw new Error(axiosError.response?.data?.message || 'Upload failed');
            }
            throw error as Error;
        }
    }
}

// Export a default instance
export const apiClient = new ApiClient();
