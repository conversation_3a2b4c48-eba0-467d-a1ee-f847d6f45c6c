-- __PROJECT_NAME__ Database Initialization Script
-- This script sets up the initial database schema and data

-- Create database if it doesn't exist (PostgreSQL)
-- Note: This runs in the context of the default database

-- Create application user (if needed)
-- CREATE USER __PROJECT_NAME___user WITH PASSWORD 'secure_password';

-- Grant permissions
-- GRANT ALL PRIVILEGES ON DATABASE __PROJECT_NAME__ TO __PROJECT_NAME___user;

-- Connect to the application database
\c __PROJECT_NAME__;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create basic tables for the application
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_superuser BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_projects_owner_id ON projects(owner_id);
CREATE INDEX IF NOT EXISTS idx_projects_name ON projects(name);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at 
    BEFORE UPDATE ON projects 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default data (optional)
-- INSERT INTO users (email, username, hashed_password, is_superuser) 
-- VALUES ('admin@__PROJECT_NAME__.com', 'admin', crypt('admin123', gen_salt('bf')), true)
-- ON CONFLICT (email) DO NOTHING;

-- Grant permissions to application user (if created)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO __PROJECT_NAME___user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO __PROJECT_NAME___user;
