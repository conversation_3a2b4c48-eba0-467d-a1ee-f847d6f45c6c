# Project: AI Coding Agent
# Purpose: Simplified source package initialization

"""
AI Orchestrator Source Package

This package contains all components for the AI Orchestrator service.
For optimal performance and maintainability, import modules directly
rather than using this central __init__.py file.

Example:
    from src.models.user import UserProfile
    from src.services.redis_service import get_redis_manager
    from src.agents.architect_agent import ArchitectAgent
"""

import logging

logger = logging.getLogger(__name__)

# Package metadata
__version__ = "2.0.0"
__author__ = "AI Coding Agent Team"
__description__ = "AI Orchestrator Service - Container-first AI development platform"

# Simple package availability check
def get_package_status() -> dict:
    """
    Get basic package availability status.

    Returns:
        dict: Package availability information
    """
    import importlib
    from pathlib import Path

    src_path = Path(__file__).parent
    packages = ["agents", "models", "services", "router", "utils"]

    status = {}
    for package in packages:
        package_path = src_path / package
        module_name = f"src.{package}"

        try:
            # Check if package directory exists and has __init__.py
            if package_path.is_dir() and (package_path / "__init__.py").exists():
                # Try to import the package
                importlib.import_module(module_name)
                status[package] = True
            else:
                status[package] = False
        except Exception as e:
            logger.debug(f"Package {package} not available: {e}")
            status[package] = False

    return status

# Log initialization
logger.info("AI Orchestrator source package initialized")
logger.debug(f"Package status: {get_package_status()}")

# Minimal exports for package introspection
__all__ = [
    "__version__",
    "__author__",
    "__description__",
    "get_package_status",
]
