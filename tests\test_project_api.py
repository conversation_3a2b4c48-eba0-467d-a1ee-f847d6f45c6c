#!/usr/bin/env python3
"""
Test script for Project Management API endpoints
Run this inside the ai-orchestrator container to test the new endpoints
"""

import requests
import sys

# Add src to path for imports
sys.path.insert(0, '/app')

def test_project_endpoints():
    """Test the new project management endpoints"""
    base_url = "http://localhost:8000"

    print("🧪 Testing Project Management API Endpoints")
    print("=" * 50)

    # Test 1: Get projects list
    try:
        print("\n1. Testing GET /projects")
        response = requests.get(f"{base_url}/projects")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            projects = response.json()
            print(f"✅ Found {len(projects)} projects")
        else:
            print(f"❌ Failed: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 2: Create a new project
    try:
        print("\n2. Testing POST /projects")
        project_data = {
            "name": "Test Project",
            "description": "A test project for API validation",
            "status": "active"
        }
        response = requests.post(f"{base_url}/projects", json=project_data)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            created_project = response.json()
            project_id = created_project['id']
            print(f"✅ Created project with ID: {project_id}")
        else:
            print(f"❌ Failed: {response.text}")
            return
    except Exception as e:
        print(f"❌ Error: {e}")
        return

    # Test 3: Get specific project
    try:
        print(f"\n3. Testing GET /projects/{project_id}")
        response = requests.get(f"{base_url}/projects/{project_id}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            project = response.json()
            print(f"✅ Retrieved project: {project['name']}")
            print(f"   Status: {project['status']}")
            print(f"   Progress: {project['progress_percentage']}%")
        else:
            print(f"❌ Failed: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 4: Update project
    try:
        print(f"\n4. Testing PUT /projects/{project_id}")
        update_data = {
            "name": "Updated Test Project",
            "description": "Updated description",
            "status": "in_progress"
        }
        response = requests.put(f"{base_url}/projects/{project_id}", json=update_data)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            updated_project = response.json()
            print(f"✅ Updated project: {updated_project['name']}")
            print(f"   New status: {updated_project['status']}")
        else:
            print(f"❌ Failed: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test 5: Test roadmap endpoints (if project has roadmap)
    try:
        print(f"\n5. Testing GET /roadmap/projects/{project_id}/roadmap")
        response = requests.get(f"{base_url}/roadmap/projects/{project_id}/roadmap")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            roadmap_data = response.json()
            print(f"✅ Retrieved roadmap with {roadmap_data['total_items']} items")
        else:
            print(f"ℹ️  No roadmap found (expected for new project): {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")

    print("\n" + "=" * 50)
    print("🎉 Project Management API tests completed!")

if __name__ == "__main__":
    test_project_endpoints()
