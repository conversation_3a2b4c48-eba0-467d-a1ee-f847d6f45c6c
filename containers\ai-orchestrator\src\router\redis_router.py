"""
Redis API Router for AI Coding Agent.

This module provides REST API endpoints for Redis health monitoring,
cache management, and real-time messaging capabilities.

Author: AI Coding Agent
Version: 1.0.0
"""

from typing import Dict, Any, Optional
from datetime import datetime
import logging

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from pydantic import BaseModel, Field

from src.services.redis_service import (
    get_redis_client, get_redis_manager, get_llm_cache, get_session_manager, get_task_queue, get_realtime_messaging, get_rate_limiter,
    RedisManager, LLMCache, SessionManager, TaskQueue,
    RealTimeMessaging, RateLimiter
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/redis", tags=["redis"])


# ==================================================================================
# PYDANTIC MODELS
# ==================================================================================

class RedisHealthResponse(BaseModel):
    """Redis health check response model."""
    status: str = Field(..., description="Health status")
    redis_version: Optional[str] = Field(None, description="Redis server version")
    connected_clients: Optional[int] = Field(None, description="Number of connected clients")
    used_memory: Optional[str] = Field(None, description="Used memory (human readable)")
    used_memory_peak: Optional[str] = Field(None, description="Peak memory usage")
    uptime_in_seconds: Optional[int] = Field(None, description="Server uptime in seconds")
    error: Optional[str] = Field(None, description="Error message if unhealthy")


class CacheStatsResponse(BaseModel):
    """Cache statistics response model."""
    llm_cache_keys: int = Field(..., description="Number of LLM cache entries")
    validation_cache_keys: int = Field(..., description="Number of validation cache entries")
    active_sessions: int = Field(..., description="Number of active user sessions")
    pending_tasks: int = Field(..., description="Number of pending tasks in queue")
    memory_usage: Dict[str, Any] = Field(..., description="Memory usage statistics")


class TaskRequest(BaseModel):
    """Task creation request model."""
    task_type: str = Field(..., description="Type of task to execute")
    data: Dict[str, Any] = Field(..., description="Task data and parameters")
    priority: int = Field(0, ge=0, le=10, description="Task priority (0-10)")


class TaskResponse(BaseModel):
    """Task creation response model."""
    task_id: str = Field(..., description="Generated task ID")
    message: str = Field(..., description="Response message")


class MessageRequest(BaseModel):
    """Real-time message publish request model."""
    channel: str = Field(..., description="Message channel")
    event_type: str = Field(..., description="Event type")
    data: Dict[str, Any] = Field(..., description="Message data")
    user_id: Optional[str] = Field(None, description="Target user ID")


class RateLimitResponse(BaseModel):
    """Rate limit check response model."""
    allowed: bool = Field(..., description="Whether request is allowed")
    count: int = Field(..., description="Current request count")
    limit: int = Field(..., description="Rate limit threshold")
    reset_time: Optional[int] = Field(None, description="Reset time (Unix timestamp)")
    error: Optional[str] = Field(None, description="Error message if any")


# ==================================================================================
# HEALTH AND MONITORING ENDPOINTS
# ==================================================================================

@router.get("/health", response_model=RedisHealthResponse)
async def redis_health_check(
    redis_manager: RedisManager = Depends(get_redis_manager)
):
    """
    Get Redis health status and server information.

    Returns comprehensive health information including server version,
    memory usage, connected clients, and uptime statistics.
    """
    try:
        health_data = await redis_manager.health_check()
        return RedisHealthResponse(**health_data)

    except Exception as e:
        logger.error(f"Redis health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Redis health check failed: {str(e)}"
        )


@router.get("/stats", response_model=CacheStatsResponse)
async def get_cache_statistics(
    redis_client = Depends(get_redis_client)
):
    """
    Get Redis cache and service statistics.

    Returns detailed statistics about cache usage, active sessions,
    pending tasks, and memory consumption.
    """
    try:
        # Get key counts for different services
        llm_keys = await redis_client.eval(
            "return #redis.call('keys', ARGV[1])", 0, "llm:cache:*"
        )

        validation_keys = await redis_client.eval(
            "return #redis.call('keys', ARGV[1])", 0, "validation:*"
        )

        session_keys = await redis_client.eval(
            "return #redis.call('keys', ARGV[1])", 0, "session:*"
        )

        # Get task queue length
        try:
            task_queue_length = await redis_client.xlen("ai_tasks")
        except Exception:
            task_queue_length = 0

        # Get memory info
        memory_info = await redis_client.info("memory")

        return CacheStatsResponse(
            llm_cache_keys=llm_keys or 0,
            validation_cache_keys=validation_keys or 0,
            active_sessions=session_keys or 0,
            pending_tasks=task_queue_length,
            memory_usage={
                "used_memory": memory_info.get("used_memory_human"),
                "used_memory_peak": memory_info.get("used_memory_peak_human"),
                "used_memory_percentage": memory_info.get("used_memory_rss_human")
            }
        )

    except Exception as e:
        logger.error(f"Error getting cache statistics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get cache statistics: {str(e)}"
        )


# ==================================================================================
# CACHE MANAGEMENT ENDPOINTS
# ==================================================================================

@router.delete("/cache/llm")
async def clear_llm_cache(
    pattern: Optional[str] = None,
    llm_cache: LLMCache = Depends(get_llm_cache)
):
    """
    Clear LLM response cache.

    Args:
        pattern: Optional pattern to match specific cache entries

    Returns:
        Number of cache entries cleared
    """
    try:
        if pattern:
            cleared = await llm_cache.invalidate_pattern(pattern)
        else:
            cleared = await llm_cache.invalidate_pattern("*")

        return {
            "message": f"Cleared {cleared} LLM cache entries",
            "entries_cleared": cleared
        }

    except Exception as e:
        logger.error(f"Error clearing LLM cache: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear LLM cache: {str(e)}"
        )


@router.delete("/cache/validation")
async def clear_validation_cache(
    redis_client = Depends(get_redis_client)
):
    """
    Clear code validation cache.

    Returns:
        Number of cache entries cleared
    """
    try:
        keys = await redis_client.keys("validation:*")
        if keys:
            cleared = await redis_client.delete(*keys)
        else:
            cleared = 0

        return {
            "message": f"Cleared {cleared} validation cache entries",
            "entries_cleared": cleared
        }

    except Exception as e:
        logger.error(f"Error clearing validation cache: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear validation cache: {str(e)}"
        )


# ==================================================================================
# TASK QUEUE ENDPOINTS
# ==================================================================================

@router.post("/tasks", response_model=TaskResponse)
async def create_task(
    task_request: TaskRequest,
    background_tasks: BackgroundTasks,
    task_queue: TaskQueue = Depends(get_task_queue)
):
    """
    Create a new background task.

    Adds a task to the Redis Streams-based task queue for
    asynchronous processing by worker processes.
    """
    try:
        task_id = await task_queue.add_task(
            task_type=task_request.task_type,
            data=task_request.data,
            priority=task_request.priority
        )

        return TaskResponse(
            task_id=task_id,
            message=f"Task {task_id} created successfully"
        )

    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create task: {str(e)}"
        )


@router.get("/tasks/stats")
async def get_task_queue_stats(
    redis_client = Depends(get_redis_client)
):
    """
    Get task queue statistics.

    Returns information about pending tasks, consumer groups,
    and processing statistics.
    """
    try:
        stream_info = await redis_client.xinfo_stream("ai_tasks")

        # Get consumer group info
        try:
            groups_info = await redis_client.xinfo_groups("ai_tasks")
        except Exception:
            groups_info = []

        return {
            "stream_length": stream_info.get("length", 0),
            "last_generated_id": stream_info.get("last-generated-id"),
            "consumer_groups": len(groups_info),
            "groups": groups_info
        }

    except Exception as e:
        logger.error(f"Error getting task queue stats: {str(e)}")
        return {
            "stream_length": 0,
            "last_generated_id": None,
            "consumer_groups": 0,
            "groups": [],
            "error": str(e)
        }


# ==================================================================================
# REAL-TIME MESSAGING ENDPOINTS
# ==================================================================================

@router.post("/publish")
async def publish_message(
    message_request: MessageRequest,
    realtime_messaging: RealTimeMessaging = Depends(get_realtime_messaging)
):
    """
    Publish a real-time message to Redis pub/sub.

    Broadcasts messages to subscribed WebSocket clients
    through the Redis publish/subscribe system.
    """
    try:
        # Route to appropriate publish method based on channel
        if message_request.channel == "validation":
            await realtime_messaging.publish_validation_progress(
                task_id=message_request.data.get("task_id", ""),
                progress=message_request.data.get("progress", 0),
                message=message_request.data.get("message", ""),
                user_id=message_request.user_id
            )
        elif message_request.channel == "tasks":
            await realtime_messaging.publish_task_update(
                task_id=message_request.data.get("task_id", ""),
                status=message_request.data.get("status", ""),
                details=message_request.data.get("details", {}),
                user_id=message_request.user_id
            )
        elif message_request.channel == "approvals":
            await realtime_messaging.publish_approval_request(
                approval_id=message_request.data.get("approval_id", ""),
                operation=message_request.data.get("operation", ""),
                context=message_request.data.get("context", {}),
                user_id=message_request.user_id or ""
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unknown channel: {message_request.channel}"
            )

        return {"message": "Message published successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error publishing message: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to publish message: {str(e)}"
        )


# ==================================================================================
# RATE LIMITING ENDPOINTS
# ==================================================================================

@router.get("/rate-limit/{key}")
async def check_rate_limit(
    key: str,
    limit: int = 60,
    window: int = 60,
    rate_limiter: RateLimiter = Depends(get_rate_limiter)
):
    """
    Check rate limit for a given key.

    Args:
        key: Rate limiting key (user ID, IP, etc.)
        limit: Maximum requests allowed (default: 60)
        window: Time window in seconds (default: 60)

    Returns:
        Rate limit status and current usage
    """
    try:
        result = await rate_limiter.check_rate_limit(key, limit, window)
        return RateLimitResponse(**result)

    except Exception as e:
        logger.error(f"Error checking rate limit: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check rate limit: {str(e)}"
        )


# ==================================================================================
# SESSION MANAGEMENT ENDPOINTS
# ==================================================================================

@router.get("/sessions/stats")
async def get_session_stats(
    redis_client = Depends(get_redis_client)
):
    """
    Get session management statistics.

    Returns information about active sessions and user activity.
    """
    try:
        # Get all session keys
        session_keys = await redis_client.keys("session:*")
        user_session_keys = await redis_client.keys("user_sessions:*")

        return {
            "total_sessions": len(session_keys),
            "users_with_sessions": len(user_session_keys),
            "session_keys": session_keys[:10] if session_keys else [],  # Sample
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting session stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get session statistics: {str(e)}"
        )


@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: str,
    session_manager: SessionManager = Depends(get_session_manager)
):
    """
    Delete a specific user session.

    Args:
        session_id: Session identifier to delete

    Returns:
        Success/failure status
    """
    try:
        deleted = await session_manager.delete_session(session_id)

        if deleted:
            return {"message": f"Session {session_id} deleted successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Session {session_id} not found"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete session: {str(e)}"
        )


# ==================================================================================
# CONFIGURATION ENDPOINTS
# ==================================================================================

@router.get("/config")
async def get_redis_config(
    redis_client = Depends(get_redis_client)
):
    """
    Get Redis server configuration.

    Returns current Redis configuration settings
    relevant to the AI Coding Agent services.
    """
    try:
        # Get relevant configuration settings
        config_keys = [
            "maxmemory",
            "maxmemory-policy",
            "timeout",
            "tcp-keepalive",
            "databases",
            "save",
            "appendonly"
        ]

        config = {}
        for key in config_keys:
            try:
                value = await redis_client.config_get(key)
                config[key] = value.get(key) if value else None
            except Exception:
                config[key] = "N/A"

        return {
            "redis_config": config,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting Redis config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Redis configuration: {str(e)}"
        )


# ==================================================================================
# ADMIN ENDPOINTS
# ==================================================================================

@router.post("/admin/flushall")
async def flush_all_data(
    confirm: bool = False,
    redis_client = Depends(get_redis_client)
):
    """
    Flush all Redis data (DANGEROUS OPERATION).

    Args:
        confirm: Must be True to proceed with operation

    Returns:
        Confirmation of data flush

    Warning:
        This will delete ALL data in Redis including sessions,
        cache entries, and queued tasks. Use with extreme caution.
    """
    if not confirm:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Must set confirm=True to flush all Redis data"
        )

    try:
        await redis_client.flushall()

        logger.warning("ALL Redis data has been flushed")

        return {
            "message": "All Redis data has been flushed successfully",
            "timestamp": datetime.utcnow().isoformat(),
            "warning": "All sessions, cache, and queue data has been deleted"
        }

    except Exception as e:
        logger.error(f"Error flushing Redis data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to flush Redis data: {str(e)}"
        )