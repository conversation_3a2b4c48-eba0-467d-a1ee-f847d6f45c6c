import pytest
from unittest.mock import Mock, patch
import jwt
from datetime import datetime, timedelta
import os

# Test the auth utilities
class TestAuthUtils:

    def test_create_access_token(self):
        """Test JWT token creation"""
        from src.utils.auth import create_access_token, SECRET_KEY

        data = {"sub": "test-user", "email": "<EMAIL>"}
        token = create_access_token(data)

        # Decode token to verify contents
        decoded = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        assert decoded["sub"] == "test-user"
        assert decoded["email"] == "<EMAIL>"
        assert "exp" in decoded

    def test_create_access_token_with_expiration(self):
        """Test JWT token creation with custom expiration"""
        from src.utils.auth import create_access_token, SECRET_KEY

        data = {"sub": "test-user"}
        expires_delta = timedelta(minutes=10)
        token = create_access_token(data, expires_delta)

        decoded = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        assert "exp" in decoded
        # Check that expiration is set (roughly)
        assert decoded["exp"] > datetime.utcnow().timestamp()

    def test_hash_password(self):
        """Test password hashing"""
        from src.utils.auth import hash_password

        password = "testpassword123"
        hashed = hash_password(password)
        assert hashed is not None
        assert isinstance(hashed, str)
        assert len(hashed) > 0

    def test_verify_password(self):
        """Test password verification"""
        from src.utils.auth import hash_password, verify_password

        password = "testpassword123"
        hashed = hash_password(password)
        assert verify_password(password, hashed) == True
        assert verify_password("wrongpassword", hashed) == False

    @patch('src.utils.auth.supabase')
    def test_get_supabase_initialization(self, mock_supabase):
        """Test Supabase client initialization"""
        from src.utils.auth import init_supabase, get_supabase

        # Mock environment variables and settings
        with patch.dict(os.environ, {
            'SUPABASE_URL': 'https://test.supabase.co',
            'SUPABASE_ANON_KEY': 'test-anon-key',
            'USE_SUPABASE': 'true'
        }), patch('src.utils.auth.settings') as mock_settings:
            # Mock settings to return True for USE_SUPABASE
            mock_settings.USE_SUPABASE = True
            mock_settings.SUPABASE_URL = 'https://test.supabase.co'
            mock_settings.SUPABASE_ANON_KEY = 'test-anon-key'

            # Mock the create_client function
            mock_client = Mock()
            with patch('src.utils.auth.create_client', return_value=mock_client):
                init_supabase()
                client = get_supabase()
                assert client is not None

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
