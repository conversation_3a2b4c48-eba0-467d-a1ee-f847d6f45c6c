from unittest.mock import AsyncMock, patch

import pytest
from src.agents.deployment_agent import DeploymentAgent


class TestDeploymentAgent:
    """Unit tests for DeploymentAgent class."""

    @pytest.fixture
    def deployment_agent(self):
        """Create a DeploymentAgent instance with mocked dependencies."""
        with (
            patch("src.agents.deployment_agent.ShellAgent") as mock_shell_agent,
            patch(
                "src.agents.deployment_agent.aiofiles.os.path.isdir", AsyncMock(return_value=True)
            ),
        ):
            mock_shell_agent.return_value = AsyncMock()
            mock_shell_agent.return_value.execute.return_value = {
                "returncode": 0,
                "stdout": "success",
                "stderr": "",
            }
            agent = DeploymentAgent()
            agent.shell_agent = mock_shell_agent.return_value
            return agent

    @pytest.mark.asyncio
    async def test_deploy_main_branch_success(self, deployment_agent):
        """Test successful deployment of main branch."""
        deployment_agent.shell_agent.execute.side_effect = [
            {"returncode": 0, "stdout": "git pull successful", "stderr": ""},
            {"returncode": 0, "stdout": "build successful", "stderr": ""},
            {"returncode": 0, "stdout": "mkdir parent successful", "stderr": ""},
            {"returncode": 0, "stdout": "mkdir release successful", "stderr": ""},
            {"returncode": 0, "stdout": "copy successful", "stderr": ""},
            {"returncode": 0, "stdout": "mkdir site parent successful", "stderr": ""},
            {"returncode": 0, "stdout": "symlink created", "stderr": ""},
        ]

        result = await deployment_agent.execute(
            {
                "action": "deploy_main_branch",
                "project_id": "test-project",
                "project_hostname": "test.example.com",
                "commit_hash": "abc123",
                "project_workspace_path": "/tmp/test-project",
            }
        )

        assert result["success"] is True
        assert "data" in result
        assert "deployment_id" in result["data"]
        assert deployment_agent.shell_agent.execute.call_count == 7

    @pytest.mark.asyncio
    async def test_deploy_main_branch_git_pull_failure(self, deployment_agent):
        """Test deployment failure when git pull fails."""
        with patch(
            "src.agents.deployment_agent.aiofiles.os.path.isdir", AsyncMock(return_value=True)
        ):
            deployment_agent.shell_agent.execute.return_value = {
                "returncode": 1,
                "stdout": "",
                "stderr": "git pull failed",
            }

            result = await deployment_agent.execute(
                {
                    "action": "deploy_main_branch",
                    "project_id": "test-project",
                    "project_hostname": "test.example.com",
                    "commit_hash": "abc123",
                    "project_workspace_path": "/tmp/test-project",
                }
            )

            assert result["success"] is False
            assert "git pull failed" in result["error"]

    @pytest.mark.asyncio
    async def test_deploy_main_branch_build_failure(self, deployment_agent):
        """Test deployment failure when build fails."""
        with patch(
            "src.agents.deployment_agent.aiofiles.os.path.isdir", AsyncMock(return_value=True)
        ):
            deployment_agent.shell_agent.execute.side_effect = [
                {"returncode": 0, "stdout": "git pull successful", "stderr": ""},
                {"returncode": 1, "stdout": "", "stderr": "build failed"},
            ]

            result = await deployment_agent.execute(
                {
                    "action": "deploy_main_branch",
                    "project_id": "test-project",
                    "project_hostname": "test.example.com",
                    "commit_hash": "abc123",
                    "project_workspace_path": "/tmp/test-project",
                }
            )

            assert result["success"] is False
            assert "build failed" in result["error"]

    @pytest.mark.asyncio
    async def test_create_release_directory(self, deployment_agent):
        """Test creation of release directory."""
        deployment_agent.shell_agent.execute.side_effect = [
            {"returncode": 0, "stdout": "mkdir parent successful", "stderr": ""},
            {"returncode": 0, "stdout": "mkdir release successful", "stderr": ""},
        ]

        release_path = await deployment_agent._create_release_directory("test.example.com")

        # Check that the path contains the expected components (cross-platform)
        from pathlib import Path

        path_obj = Path(release_path)
        assert "hosted_sites" in path_obj.parts
        assert "releases" in path_obj.parts
        assert "test.example.com" in path_obj.parts
        assert deployment_agent.shell_agent.execute.call_count == 2

    @pytest.mark.asyncio
    async def test_cleanup_old_releases_no_releases(self, deployment_agent):
        """Test cleanup when no releases exist."""
        with patch("aiofiles.os.path.isdir", AsyncMock(return_value=False)):
            await deployment_agent._cleanup_old_releases("test.example.com")

    @pytest.mark.asyncio
    async def test_cleanup_old_releases_with_cleanup(self, deployment_agent):
        """Test cleanup when releases need to be deleted."""
        with (
            patch("aiofiles.os.path.isdir", AsyncMock(return_value=True)),
            patch(
                "aiofiles.os.listdir",
                AsyncMock(return_value=["release1", "release2", "release3", "release4"]),
            ),
            patch("aiofiles.os.path.isdir", AsyncMock(return_value=True)),
        ):
            deployment_agent.keep_releases = 2
            await deployment_agent._cleanup_old_releases("test.example.com")
            assert deployment_agent.shell_agent.execute.call_count == 2

    @pytest.mark.asyncio
    async def test_cleanup_old_releases_error_handling(self, deployment_agent):
        """Test cleanup error handling doesn't break deployment."""
        with patch("aiofiles.os.path.isdir", AsyncMock(side_effect=Exception("Cleanup error"))):
            await deployment_agent._cleanup_old_releases("test.example.com")

    @pytest.mark.asyncio
    async def test_atomic_symlink_swap(self, deployment_agent):
        """Test atomic symlink swap operation."""
        deployment_agent.shell_agent.execute.return_value = {
            "returncode": 0,
            "stdout": "symlink created",
            "stderr": "",
        }

        result = await deployment_agent._atomic_symlink_swap(
            "test.example.com", "/releases/test.example.com/20250101120000"
        )

        assert result is True
        deployment_agent.shell_agent.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_atomic_symlink_swap_failure(self, deployment_agent):
        """Test atomic symlink swap failure."""
        deployment_agent.shell_agent.execute.return_value = {
            "returncode": 1,
            "stdout": "",
            "stderr": "symlink failed",
        }

        result = await deployment_agent._atomic_symlink_swap(
            "test.example.com", "/releases/test.example.com/20250101120000"
        )

        assert result is False

    def test_generate_release_name(self, deployment_agent):
        """Test release name generation."""
        release_name = deployment_agent._generate_release_name()
        assert len(release_name) == 14
        assert release_name.isdigit()

    @pytest.mark.asyncio
    async def test_execute_deployment_commands(self, deployment_agent):
        """Test execution of deployment commands."""
        deployment_agent.shell_agent.execute.side_effect = [
            {"returncode": 0, "stdout": "command1 success", "stderr": ""},
            {"returncode": 0, "stdout": "command2 success", "stderr": ""},
        ]

        commands = [["echo", "hello"], ["echo", "world"]]
        results = await deployment_agent._execute_deployment_commands(commands)

        assert len(results) == 2
        assert all(r["success"] for r in results)
        assert deployment_agent.shell_agent.execute.call_count == 2

    @pytest.mark.asyncio
    async def test_execute_deployment_commands_partial_failure(self, deployment_agent):
        """Test execution with partial command failures."""
        deployment_agent.shell_agent.execute.side_effect = [
            {"returncode": 0, "stdout": "command1 success", "stderr": ""},
            {"returncode": 1, "stdout": "", "stderr": "command2 failed"},
        ]

        commands = [["echo", "hello"], ["echo", "world"]]
        results = await deployment_agent._execute_deployment_commands(commands)

        assert len(results) == 2
        assert results[0]["success"] is True
        assert results[1]["success"] is False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
