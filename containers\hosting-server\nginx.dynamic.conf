# Optimized Nginx Configuration for Dynamic Project Hosting
# containers/hosting-server/nginx.dynamic.conf

# Rate limiting zone for dynamic projects
limit_req_zone $host zone=dynamic_projects:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=client_limit:10m rate=30r/m;

server {
    listen 80;
    server_name _;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;" always;

    # Hide nginx version
    server_tokens off;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 30;
    client_max_body_size 50M;
    client_body_timeout 30s;
    client_header_timeout 30s;
    send_timeout 30s;

    # Gzip compression for better performance
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Use the incoming hostname to construct the root path dynamically
    # Supports patterns like: project-abc123.localhost, preview-xyz789.project.localhost
    set $project_root /var/www/sites/$host;

    # Validate that the project directory exists
    location / {
        # Rate limiting for dynamic projects
        limit_req zone=dynamic_projects burst=20 nodelay;
        limit_req zone=client_limit burst=50 nodelay;

        # Check if project directory exists
        if (!-d $project_root) {
            return 404 "Project not found";
        }

        # Set root directory for this host
        root $project_root;
        index index.html index.htm;

        # Standard SPA fallback configuration
        try_files $uri $uri/ /index.html;

        # Cache static assets aggressively
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp|avif)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Cache-Status "STATIC";
            gzip_static on;

            # Prevent access to hidden files
            location ~ /\. {
                deny all;
                return 403;
            }
        }

        # Prevent serving hidden files for security
        location ~ /\. {
            deny all;
            return 403;
        }

        # API routes (if any) - no caching
        location /api/ {
            try_files $uri $uri/ =404;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }

    # Health check endpoint that doesn't interfere with user projects
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Favicon handling
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }

    # Robots.txt
    location = /robots.txt {
        log_not_found off;
        access_log off;
        return 200 "User-agent: *\nDisallow: /\n";
        add_header Content-Type text/plain;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }

    # Custom error page for project not found
    location = /404.html {
        internal;
        return 404 "Project not found";
        add_header Content-Type text/html;
    }
}

# Default server for invalid hostnames
server {
    listen 80 default_server;
    server_name _;

    # Return 444 to close connection without response for invalid hosts
    return 444;
}
