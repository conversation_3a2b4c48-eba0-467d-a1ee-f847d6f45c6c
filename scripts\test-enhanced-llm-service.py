#!/usr/bin/env python3
"""
Project: AI Coding Agent
Author: AI Coding Agent Team
Purpose: Test script to verify the enhanced LLM service is working correctly

Usage: python scripts/test-enhanced-llm-service.py
"""

import asyncio
import aiohttp
import os
import sys
from typing import Optional

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'containers', 'ai-orchestrator', 'src'))

BASE_URL = "http://localhost:8000"
TEST_TIMEOUT = 30


class EnhancedLLMServiceTester:
    """Test the enhanced LLM service functionality."""

    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None
        self.test_results = {}

    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=TEST_TIMEOUT)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def test_basic_health_check(self) -> bool:
        """Test basic health check endpoint."""
        print(" Testing basic health check...")
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("status") == "ok":
                        print(" Health check passed")
                        self.test_results["health_check"] = True
                        return True
                    else:
                        print(f" Health check failed: {data}")
                        return False
                else:
                    print(f" Health check failed with status: {response.status}")
                    return False
        except Exception as e:
            print(f" Health check failed with exception: {str(e)}")
            return False

    async def test_system_info(self) -> bool:
        """Test system information endpoint."""
        print(" Testing system information...")
        try:
            async with self.session.get(f"{self.base_url}/api/system/info") as response:
                if response.status == 200:
                    data = await response.json()
                    if "service" in data and data["service"] == "ai-orchestrator":
                        print(" System info check passed")
                        print(f"   Version: {data.get('version', 'unknown')}")
                        print(f"   Enhanced LLM: {data.get('features', {}).get('enhanced_llm', False)}")
                        self.test_results["system_info"] = True
                        return True
                    else:
                        print(f" System info check failed: {data}")
                        return False
                else:
                    print(f" System info failed with status: {response.status}")
                    return False
        except Exception as e:
            print(f" System info failed with exception: {str(e)}")
            return False

    async def test_llm_health_check(self) -> bool:
        """Test LLM service health check."""
        print(" Testing LLM health check...")
        try:
            async with self.session.get(f"{self.base_url}/api/llm/health") as response:
                if response.status == 200:
                    data = await response.json()
                    if "status" in data and "providers" in data:
                        print(" LLM health check passed")
                        print(f"   Status: {data['status']}")
                        print(f"   Providers tested: {len(data['providers'])}")

                        # Show provider status
                        for provider in data['providers']:
                            status_emoji = "" if provider['available'] else ""
                            print(f"   {status_emoji} {provider['provider']}: {provider['available']}")

                        self.test_results["llm_health"] = True
                        return True
                    else:
                        print(f" LLM health check failed: {data}")
                        return False
                else:
                    print(f" LLM health check failed with status: {response.status}")
                    return False
        except Exception as e:
            print(f" LLM health check failed with exception: {str(e)}")
            return False

    async def test_list_providers(self) -> bool:
        """Test list providers endpoint."""
        print(" Testing list providers...")
        try:
            async with self.session.get(f"{self.base_url}/api/llm/providers") as response:
                if response.status == 200:
                    data = await response.json()
                    if "providers" in data:
                        print(" List providers passed")
                        providers = data["providers"]
                        print(f"   Found {len(providers)} providers")

                        for provider in providers:
                            name = provider.get('provider', 'unknown')
                            enabled = provider.get('enabled', False)
                            available = provider.get('available', False)

                            status_emoji = "" if (enabled and available) else ("" if enabled else "")
                            print(f"   {status_emoji} {name}: enabled={enabled}, available={available}")

                        self.test_results["list_providers"] = True
                        return True
                    else:
                        print(f" List providers failed: {data}")
                        return False
                elif response.status == 401:
                    print("  List providers requires authentication (expected in production)")
                    self.test_results["list_providers"] = "auth_required"
                    return True
                else:
                    print(f" List providers failed with status: {response.status}")
                    return False
        except Exception as e:
            print(f" List providers failed with exception: {str(e)}")
            return False

    async def test_list_models(self) -> bool:
        """Test list models endpoint."""
        print(" Testing list models...")
        try:
            async with self.session.get(f"{self.base_url}/api/llm/models") as response:
                if response.status == 200:
                    data = await response.json()
                    if "models" in data:
                        models = data["models"]
                        print(" List models passed")
                        print(f"   Found {len(models)} models")

                        # Group by provider
                        providers = {}
                        for model in models:
                            provider = model.get('provider', 'unknown')
                            if provider not in providers:
                                providers[provider] = []
                            providers[provider].append(model['name'])

                        for provider, model_list in providers.items():
                            print(f"    {provider}: {len(model_list)} models")
                            for model_name in model_list[:3]:  # Show first 3
                                print(f"      - {model_name}")
                            if len(model_list) > 3:
                                print(f"      ... and {len(model_list) - 3} more")

                        self.test_results["list_models"] = True
                        return True
                    else:
                        print(f" List models failed: {data}")
                        return False
                elif response.status == 401:
                    print("  List models requires authentication (expected in production)")
                    self.test_results["list_models"] = "auth_required"
                    return True
                else:
                    print(f" List models failed with status: {response.status}")
                    return False
        except Exception as e:
            print(f" List models failed with exception: {str(e)}")
            return False

    async def test_legacy_ollama_status(self) -> bool:
        """Test legacy Ollama status endpoint."""
        print(" Testing legacy Ollama status...")
        try:
            async with self.session.get(f"{self.base_url}/api/ollama/status") as response:
                if response.status == 200:
                    data = await response.json()
                    if "connected" in data:
                        print(" Legacy Ollama status passed")
                        print(f"   Connected: {data['connected']}")
                        print(f"   Base URL: {data.get('base_url', 'unknown')}")
                        if "response_time_ms" in data:
                            print(f"   Response time: {data['response_time_ms']}ms")
                        self.test_results["ollama_status"] = True
                        return True
                    else:
                        print(f" Legacy Ollama status failed: {data}")
                        return False
                else:
                    print(f" Legacy Ollama status failed with status: {response.status}")
                    return False
        except Exception as e:
            print(f" Legacy Ollama status failed with exception: {str(e)}")
            return False

    async def test_api_key_validation(self) -> bool:
        """Test API key validation endpoint."""
        print(" Testing API key validation...")
        try:
            async with self.session.post(f"{self.base_url}/api/llm/validate-keys") as response:
                if response.status == 200:
                    data = await response.json()
                    if "api_keys" in data:
                        print(" API key validation passed")
                        api_keys = data["api_keys"]

                        for provider, info in api_keys.items():
                            valid = info.get('valid', False)
                            configured = info.get('configured', False)

                            status_emoji = "" if (configured and valid) else ("" if configured else "")
                            print(f"   {status_emoji} {provider}: configured={configured}, valid={valid}")

                        self.test_results["api_key_validation"] = True
                        return True
                    else:
                        print(f" API key validation failed: {data}")
                        return False
                elif response.status == 401:
                    print("  API key validation requires authentication (expected in production)")
                    self.test_results["api_key_validation"] = "auth_required"
                    return True
                else:
                    print(f" API key validation failed with status: {response.status}")
                    return False
        except Exception as e:
            print(f" API key validation failed with exception: {str(e)}")
            return False

    def print_summary(self):
        """Print test summary."""
        print("\n" + "="*60)
        print(" TEST SUMMARY")
        print("="*60)

        passed = 0
        total = 0
        auth_required = 0

        for test_name, result in self.test_results.items():
            total += 1
            if result is True:
                passed += 1
                print(f" {test_name}: PASSED")
            elif result == "auth_required":
                auth_required += 1
                print(f"  {test_name}: AUTH REQUIRED (expected)")
            else:
                print(f" {test_name}: FAILED")

        print("-" * 60)
        print(f"Results: {passed}/{total} tests passed")
        if auth_required > 0:
            print(f"         {auth_required} tests require authentication")

        if passed == total or (passed + auth_required) == total:
            print(" All tests passed! Enhanced LLM service is working correctly.")
            return True
        else:
            print(" Some tests failed. Check the logs above for details.")
            return False

    async def run_all_tests(self) -> bool:
        """Run all tests."""
        print(" Starting Enhanced LLM Service Tests")
        print("=" * 60)

        tests = [
            self.test_basic_health_check,
            self.test_system_info,
            self.test_llm_health_check,
            self.test_list_providers,
            self.test_list_models,
            self.test_legacy_ollama_status,
            self.test_api_key_validation,
        ]

        for test in tests:
            await test()
            print()  # Add spacing between tests

        return self.print_summary()


async def main():
    """Main test function."""
    print("Enhanced LLM Service Test Suite")
    print("=" * 60)
    print(f"Testing service at: {BASE_URL}")
    print(f"Timeout: {TEST_TIMEOUT} seconds")
    print()

    # Check if service is running
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/health", timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status != 200:
                    print(" Service does not appear to be running.")
                    print("   Please start the service with:")
                    print("   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d")
                    return False
    except Exception as e:
        print(" Cannot connect to service.")
        print(f"   Error: {str(e)}")
        print("   Please start the service with:")
        print("   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d")
        return False

    # Run tests
    async with EnhancedLLMServiceTester() as tester:
        success = await tester.run_all_tests()

    if success:
        print("\n All tests completed successfully!")
        print("Your enhanced LLM service is ready for production!")
        return True
    else:
        print("\n Some tests failed.")
        print("Please check the configuration and try again.")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n  Tests interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n Test suite failed with exception: {str(e)}")
        sys.exit(1)