# Optimized Docker Compose Configuration for Nginx Containers
# This snippet shows how to integrate the optimized nginx containers
# with proper resource limits, health checks, and zero-downtime deployment

# Nginx Reverse Proxy Service (handles SSL termination and load balancing)
nginx:
  build:
    context: ./containers/nginx
    dockerfile: Dockerfile
    target: production # Use multi-stage build if available
    args:
      - NGINX_VERSION=1.25-alpine
  image: ${NGINX_IMAGE:-ai-coding-agent/nginx:latest}
  container_name: nginx
  restart: unless-stopped
  volumes:
    # Configuration with environment variable substitution
    - ./containers/nginx/nginx.conf.template:/etc/nginx/nginx.conf.template:ro
    - nginx_config:/etc/nginx/conf.d
    # SSL certificates (should be managed securely)
    - ./ssl:/etc/nginx/ssl:ro
    # Let's Encrypt certificates
    - ./letsencrypt:/etc/letsencrypt:ro
    # Logs for monitoring
    - nginx_logs:/var/log/nginx
  environment:
    # Environment variables for dynamic configuration
    - NGINX_WORKER_PROCESSES=auto
    - NGINX_WORKER_CONNECTIONS=4096
    - NGINX_CLIENT_MAX_BODY_SIZE=64M
    - NGINX_KEEPALIVE_TIMEOUT=65
    - AI_ORCHESTRATOR_HOST=ai-orchestrator
    - AI_ORCHESTRATOR_PORT=8000
    - USER_PORTAL_HOST=user-portal
    - USER_PORTAL_PORT=3000
    - HOSTING_SERVER_HOST=hosting-server
    - HOSTING_SERVER_PORT=80
    - API_DOMAIN=${API_DOMAIN:-api.localhost}
    - ADMIN_DOMAIN=${ADMIN_DOMAIN:-admin.localhost}
    - PROJECTS_DOMAIN=${PROJECTS_DOMAIN:-*.localhost}
    - SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
    - SSL_KEY_PATH=/etc/nginx/ssl/key.pem
  networks:
    - web
  ports:
    - "80:80" # HTTP
    - "443:443" # HTTPS
  depends_on:
    ai-orchestrator:
      condition: service_healthy
    user-portal:
      condition: service_healthy
    hosting-server:
      condition: service_healthy
  healthcheck:
    test: [ "CMD", "curl", "-f", "-H", "Host: localhost", "http://localhost/health" ]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 10s
  deploy:
    resources:
      limits:
        cpus: '1.0'
        memory: 256M
      reservations:
        cpus: '0.25'
        memory: 64M
    restart_policy:
      condition: on-failure
      delay: 5s
      max_attempts: 3
      window: 120s
  labels:
    - "traefik.enable=false" # This is the reverse proxy itself
    - "com.docker.compose.project=${COMPOSE_PROJECT_NAME}"
    - "com.docker.compose.service=nginx"
    - "security.access-level=public"
    - "security.privilege-level=system"
    - "monitoring.enable=true"
    - "backup.enable=false"
  command: >
    sh -c "
      # Generate configuration from template
      envsubst < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf &&
      # Validate configuration
      nginx -t &&
      # Start nginx
      nginx -g 'daemon off;'
    "

# Hosting Server Service (handles dynamic project hosting)
hosting-server:
  build:
    context: ./containers/hosting-server
    dockerfile: Dockerfile
    target: production
    args:
      - NGINX_VERSION=1.25-alpine
  image: ${HOSTING_SERVER_IMAGE:-ai-coding-agent/hosting-server:latest}
  container_name: hosting-server
  restart: unless-stopped
  volumes:
    # Dynamic project files (read-only for security)
    - hosted_sites_data:/var/www/sites:ro
    # Configuration
    - ./containers/hosting-server/nginx.dynamic.conf:/etc/nginx/conf.d/default.conf:ro
    # Logs
    - hosting_server_logs:/var/log/nginx
  environment:
    - NGINX_WORKER_PROCESSES=auto
    - NGINX_WORKER_CONNECTIONS=1024
    - NGINX_CLIENT_MAX_BODY_SIZE=50M
    - NGINX_KEEPALIVE_TIMEOUT=30
  networks:
    - web
  expose:
    - "80" # Only exposed internally, accessed via nginx reverse proxy
  depends_on:
    - nginx # Ensure reverse proxy is ready first
  healthcheck:
    test: [ "CMD", "curl", "-f", "-H", "Host: health.localhost", "http://localhost/health" ]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 5s
  deploy:
    resources:
      limits:
        cpus: '0.5'
        memory: 128M
      reservations:
        cpus: '0.1'
        memory: 32M
    restart_policy:
      condition: on-failure
      delay: 5s
      max_attempts: 3
      window: 120s
  labels:
    - "traefik.enable=true"
    - "traefik.http.routers.hosting-server.rule=HostRegexp(`{name:.+}.localhost`)"
    - "traefik.http.services.hosting-server.loadbalancer.server.port=80"
    - "traefik.docker.network=${TRAEFIK_NETWORK:-ai-coding-agent_web_network}"
    - "com.docker.compose.project=${COMPOSE_PROJECT_NAME}"
    - "com.docker.compose.service=hosting-server"
    - "security.access-level=internal"
    - "security.privilege-level=user"
    - "monitoring.enable=true"
    - "backup.enable=true"

# Volumes for persistent data
volumes:
  nginx_config:
    driver: local
  nginx_logs:
    driver: local
  hosting_server_logs:
    driver: local
  hosted_sites_data:
    driver: local
  ssl_certs:
    driver: local

# Networks
networks:
  web:
    external: true
    name: ${WEB_NETWORK:-ai-coding-agent_web_network}

# Health check dependencies (example)
x-healthcheck-config: &healthcheck-config
  test: [ "CMD", "curl", "-f", "http://localhost/health" ]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 30s
