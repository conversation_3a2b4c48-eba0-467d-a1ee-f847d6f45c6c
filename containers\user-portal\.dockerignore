# Comprehensive .dockerignore for User Portal (Next.js)
# Reduces build context size and prevents sensitive files from being copied

# Version control
.git
.gitignore
.gitattributes
.gitmodules

# Environment files (sensitive)
.env
.env.*
!.env.example
!.env.local.example

# Node.js dependencies and cache
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
lib-cov
coverage/
*.lcov
.nyc_output

# Next.js build outputs
.next/
out/
dist/
build/

# Next.js cache
.next/cache/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local
.env.local

# Parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# ESLint cache
.eslintcache

# Prettier cache
.prettier-cache

# Webpack
.webpack/

# Docker files (avoid recursive copying)
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
docs/
*.md
!README.md

# Secrets and certificates
secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# Test files
__tests__/
**/*.test.js
**/*.test.ts
**/*.test.tsx
**/*.spec.js
**/*.spec.ts
**/*.spec.tsx
jest.config.js
jest.setup.js

# Temporary files
*.tmp
*.temp
tmp/
temp/

# Backup files
*.bak
*.backup
*.orig

# Application specific
uploads/
static/media/

# Turbo
.turbo

# Sentry
.sentryclirc

# Local Netlify folder
.netlify

# Vercel
.vercel

# Tailwind CSS
tailwind.config.js.map