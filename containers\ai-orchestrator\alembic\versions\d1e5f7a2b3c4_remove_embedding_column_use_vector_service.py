"""remove_embedding_column_use_vector_service

Revision ID: d1e5f7a2b3c4
Revises: c5556e16d703
Create Date: 2025-09-09 20:28:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "d1e5f7a2b3c4"
down_revision: Union[str, None] = "c5556e16d703"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Drop embedding column from memories table in favor of VectorStorageService."""
    with op.batch_alter_table("memories") as batch_op:
        # Safe to drop since embeddings are now externalized
        batch_op.drop_column("embedding")


def downgrade() -> None:
    """Recreate embedding column as JSON (for rollback only)."""
    with op.batch_alter_table("memories") as batch_op:
        batch_op.add_column(sa.Column("embedding", sa.JSON(), nullable=True))
