import pytest
from unittest.mock import AsyncMock, MagicMock
from src.agents.architect_agent import ArchitectAgent
from src.approval.approval_models import ApprovalRequest, ApprovalType

class ConcreteArchitectAgent(ArchitectAgent):
    async def _execute_core(self, task_input):
        pass

@pytest.mark.asyncio
async def test_send_approval_request(mocker):
    # Arrange
    # Mock the dependencies
    get_chat_manager_mock = mocker.patch('src.agents.architect_agent.get_chat_manager')
    chat_manager_mock = get_chat_manager_mock.return_value

    agent = ConcreteArchitectAgent()
    agent.approval_manager = AsyncMock()
    agent.approval_manager.create_approval_request.return_value = ApprovalRequest(
        id="test_token",
        user_id="test_user",
        approval_type=ApprovalType.DEPLOYMENT,
        title="Approve Preview Deployment",
        description="A new preview for project test_project is ready for your review.",
        item_type="project",
        item_id="test_project",
    )

    # Act
    result = await agent.send_approval_request(
        user_id="test_user",
        project_id="test_project",
        commit_hash="test_commit",
        preview_url="http://test.com",
    )

    # Assert
    assert result["success"] is True
    assert result["approval_token"] == "test_token"
    agent.approval_manager.create_approval_request.assert_called_once()
    chat_manager_mock.send_to_user.assert_called_once()
