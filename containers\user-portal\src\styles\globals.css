@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for the admin dashboard */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  code, pre {
    font-family: 'JetBrains Mono', monospace;
  }
}

@layer components {
  /* React Select custom styling */
  .react-select-container {
    @apply relative;
  }

  .react-select-container .react-select__control {
    @apply min-h-[42px] border border-gray-300 rounded-lg shadow-sm;
  }

  .react-select-container .react-select__control--is-focused {
    @apply border-blue-500 ring-2 ring-blue-500 ring-opacity-20;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .react-select-container .react-select__option {
    @apply py-3 px-4;
  }

  .react-select-container .react-select__option--is-selected {
    @apply bg-blue-600 text-white;
  }

  .react-select-container .react-select__option--is-focused {
    @apply bg-gray-50;
  }

  .react-select-container .react-select__placeholder {
    @apply text-gray-400;
  }

  .react-select-container .react-select__single-value {
    @apply text-gray-700;
  }

  /* Custom button styles */
  .btn-primary {
    @apply inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed;
  }

  .btn-danger {
    @apply inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:bg-gray-400 disabled:cursor-not-allowed;
  }

  /* Form input styles */
  .form-input {
    @apply block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-500;
  }

  .form-input-error {
    @apply border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply p-6;
  }

  /* Status badges */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-error {
    @apply bg-red-100 text-red-800;
  }

  .badge-info {
    @apply bg-blue-100 text-blue-800;
  }

  /* Loading animations */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-200 border-t-blue-600;
  }

  /* Alert styles */
  .alert {
    @apply p-4 rounded-md;
  }

  .alert-info {
    @apply bg-blue-50 border border-blue-200 text-blue-700;
  }

  .alert-success {
    @apply bg-green-50 border border-green-200 text-green-700;
  }

  .alert-warning {
    @apply bg-yellow-50 border border-yellow-200 text-yellow-700;
  }

  .alert-error {
    @apply bg-red-50 border border-red-200 text-red-700;
  }
}

@layer utilities {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }

  /* Text truncation utilities */
  .truncate-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .truncate-3-lines {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
  }

  /* Transition utilities */
  .transition-all {
    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-colors {
    transition: color 150ms cubic-bezier(0.4, 0, 0.2, 1),
                background-color 150ms cubic-bezier(0.4, 0, 0.2, 1),
                border-color 150ms cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  * {
    color: black !important;
    background: white !important;
  }
}