"""
Advanced AI Orchestrator Service

Combines LangGraph workflow orchestration with CUDA acceleration for
optimal performance in complex multi-agent AI systems.

Author: AI Coding Agent Team
Version: 1.0.0
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from services.langgraph_orchestrator import get_langgraph_orchestrator
from services.cuda_accelerated_service import get_cuda_accelerated_service
from services.enhanced_llm_service import EnhancedLLMService

logger = logging.getLogger(__name__)


class AdvancedAIOrchestrator:
    """
    Advanced AI Orchestrator combining multiple optimization techniques.

    Features:
    - LangGraph workflow orchestration
    - CUDA GPU acceleration
    - Intelligent caching and memory management
    - Performance monitoring and optimization
    - Adaptive resource allocation
    """

    def __init__(self):
        self.langgraph_orchestrator = None
        self.cuda_service = None
        self.llm_service = EnhancedLLMService()
        self.performance_metrics = {}
        self.initialized = False

    async def initialize(self):
        """Initialize all orchestration components."""
        if self.initialized:
            return

        try:
            # Initialize LangGraph orchestrator
            self.langgraph_orchestrator = await get_langgraph_orchestrator()
            logger.info("LangGraph orchestrator initialized")

            # Initialize CUDA service
            self.cuda_service = await get_cuda_accelerated_service()
            logger.info("CUDA accelerated service initialized")

            self.initialized = True
            logger.info("Advanced AI Orchestrator initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize orchestrator: {e}")
            raise

    async def execute_complex_task(
        self,
        task: str,
        use_cuda: bool = True,
        thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Execute a complex task using optimized orchestration.

        Args:
            task: The task description
            use_cuda: Whether to use CUDA acceleration
            thread_id: Optional thread ID for persistence

        Returns:
            Execution results with performance metrics
        """
        if not self.initialized:
            await self.initialize()

        start_time = datetime.now()
        task_id = f"task_{datetime.now().isoformat()}"

        try:
            logger.info(f"Starting complex task execution: {task_id}")

            # Pre-process task with CUDA if available
            if use_cuda and self.cuda_service:
                processed_task = await self._preprocess_task_with_cuda(task)
            else:
                processed_task = task

            # Execute using LangGraph workflow
            workflow_result = await self.langgraph_orchestrator.execute_workflow(
                processed_task,
                thread_id
            )

            # Post-process results with CUDA optimization
            if use_cuda and self.cuda_service:
                optimized_results = await self._optimize_results_with_cuda(workflow_result)
            else:
                optimized_results = workflow_result

            # Calculate performance metrics
            end_time = datetime.now()
            total_duration = (end_time - start_time).total_seconds()

            performance_data = {
                "task_id": task_id,
                "total_duration": total_duration,
                "cuda_used": use_cuda and self.cuda_service is not None,
                "workflow_steps": len(optimized_results.get("results", {})),
                "errors_count": len(optimized_results.get("errors", [])),
                "timestamp": datetime.now().isoformat()
            }

            self.performance_metrics[task_id] = performance_data

            result = {
                **optimized_results,
                "performance": performance_data,
                "orchestrator": "advanced_ai"
            }

            logger.info(f"Complex task completed: {task_id} in {total_duration:.2f}s")
            return result

        except Exception as e:
            logger.error(f"Complex task execution failed: {e}")
            return {
                "success": False,
                "errors": [str(e)],
                "performance": {
                    "task_id": task_id,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            }

    async def _preprocess_task_with_cuda(self, task: str) -> str:
        """Preprocess task using CUDA acceleration for better understanding."""
        try:
            # Generate task embedding for analysis
            task_embedding = await self.cuda_service.generate_embeddings([task])
            logger.info("Task preprocessed with CUDA acceleration")

            # Use embedding for task enhancement
            enhancement_prompt = f"""
            Based on the task analysis, enhance the following task for better execution:

            Original Task: {task}

            Provide an enhanced version that includes:
            1. Clear objectives
            2. Success criteria
            3. Required components
            4. Potential challenges

            Enhanced Task:
            """

            from src.models.llm_models import GenerateRequest
            request = GenerateRequest(
                prompt=enhancement_prompt,
                model="llama3.1:7b",
                temperature=0.1,
                max_tokens=500
            )

            response = await self.llm_service.generate(request)
            return response.content.strip()

        except Exception as e:
            logger.warning(f"CUDA preprocessing failed, using original task: {e}")
            return task

    async def _optimize_results_with_cuda(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize workflow results using CUDA acceleration."""
        try:
            # Extract text content from results for optimization
            result_texts = []
            for agent, result in results.get("results", {}).items():
                if isinstance(result, dict) and "status" in result:
                    for key, value in result.items():
                        if isinstance(value, str) and len(value) > 10:
                            result_texts.append(f"{agent}_{key}: {value}")

            if result_texts:
                # Generate embeddings for result analysis
                embeddings = await self.cuda_service.generate_embeddings(result_texts)

                # Use embeddings for result clustering/optimization
                logger.info("Results optimized with CUDA acceleration")

            return results

        except Exception as e:
            logger.warning(f"CUDA result optimization failed: {e}")
            return results

    async def get_performance_analytics(self) -> Dict[str, Any]:
        """Get performance analytics for the orchestrator."""
        if not self.initialized:
            await self.initialize()

        try:
            # Get CUDA service health
            cuda_health = await self.cuda_service.health_check() if self.cuda_service else {"status": "unavailable"}

            # Calculate aggregate metrics
            total_tasks = len(self.performance_metrics)
            successful_tasks = sum(1 for m in self.performance_metrics.values() if m.get("errors_count", 0) == 0)
            avg_duration = sum(m.get("total_duration", 0) for m in self.performance_metrics.values()) / max(total_tasks, 1)

            return {
                "orchestrator_status": "healthy" if self.initialized else "initializing",
                "cuda_status": cuda_health.get("status", "unknown"),
                "total_tasks_processed": total_tasks,
                "success_rate": successful_tasks / max(total_tasks, 1),
                "average_task_duration": avg_duration,
                "performance_history": list(self.performance_metrics.values())[-10:],  # Last 10 tasks
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Performance analytics failed: {e}")
            return {
                "orchestrator_status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def optimize_resources(self):
        """Optimize system resources for better performance."""
        try:
            # Clear CUDA memory if available
            if self.cuda_service:
                await self.cuda_service.optimize_memory()

            # Clear old performance metrics (keep last 100)
            if len(self.performance_metrics) > 100:
                # Keep most recent 100 entries
                sorted_metrics = sorted(
                    self.performance_metrics.items(),
                    key=lambda x: x[1]["timestamp"],
                    reverse=True
                )
                self.performance_metrics = dict(sorted_metrics[:100])

            logger.info("Resource optimization completed")

        except Exception as e:
            logger.error(f"Resource optimization failed: {e}")

    async def execute_parallel_tasks(
        self,
        tasks: List[str],
        max_concurrent: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Execute multiple tasks in parallel with resource optimization.

        Args:
            tasks: List of task descriptions
            max_concurrent: Maximum concurrent executions

        Returns:
            List of execution results
        """
        import asyncio

        if not self.initialized:
            await self.initialize()

        async def execute_single_task(task: str) -> Dict[str, Any]:
            return await self.execute_complex_task(task, use_cuda=True)

        # Execute tasks with concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)
        results = []

        async def execute_with_semaphore(task: str):
            async with semaphore:
                result = await execute_single_task(task)
                results.append(result)

        # Create tasks
        execution_tasks = [execute_with_semaphore(task) for task in tasks]

        # Execute all tasks
        await asyncio.gather(*execution_tasks)

        return results


# Global orchestrator instance
_advanced_orchestrator_instance = None

async def get_advanced_ai_orchestrator() -> AdvancedAIOrchestrator:
    """Get or create the global advanced AI orchestrator instance."""
    global _advanced_orchestrator_instance

    if _advanced_orchestrator_instance is None:
        _advanced_orchestrator_instance = AdvancedAIOrchestrator()
        await _advanced_orchestrator_instance.initialize()

    return _advanced_orchestrator_instance
