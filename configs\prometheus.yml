global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Scrape Loki metrics
  - job_name: 'loki'
    static_configs:
      - targets: ['loki:3100']
    metrics_path: '/metrics'

  # Scrape Promtail metrics
  - job_name: 'promtail'
    static_configs:
      - targets: ['promtail:9080']
    metrics_path: '/metrics'

  # Add more scrape configs here for application metrics
  # Example for ai-orchestrator:
  # - job_name: 'ai-orchestrator'
  #   static_configs:
  #     - targets: ['ai-orchestrator:8000']
  #   metrics_path: '/metrics'
