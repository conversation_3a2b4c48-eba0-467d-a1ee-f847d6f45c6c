{"include": ["containers/*/src", "scripts", "tests"], "exclude": ["**/__pycache__", "**/node_modules", "**/.pytest_cache", "**/venv", "**/env"], "reportMissingImports": "warning", "reportMissingModuleSource": "warning", "reportImportCycles": "warning", "reportUnusedImport": "warning", "reportDuplicateImport": "warning", "reportUnusedClass": "warning", "reportUnusedFunction": "warning", "reportUnusedVariable": "warning", "reportOptionalMemberAccess": "warning", "reportOptionalSubscript": "warning", "reportUnknownMemberType": "warning", "reportUnknownArgumentType": "warning", "reportUnknownVariableType": "warning", "reportGeneralTypeIssues": "error", "typeCheckingMode": "basic", "pythonVersion": "3.12", "pythonPlatform": "Linux", "executionEnvironments": [{"root": "containers/ai-orchestrator", "pythonVersion": "3.12", "pythonPlatform": "Linux", "extraPaths": ["src"]}, {"root": "tests", "pythonVersion": "3.12", "pythonPlatform": "Linux", "extraPaths": ["../containers/ai-orchestrator/src"]}]}