# The correct content for src/models/__init__.py

# Only import database components by default to avoid model conflicts
from src.models.database import Base, get_db
from src.models.association import user_project_association

# Lazy import models to avoid conflicts during database testing
def __getattr__(name):
    if name == "UserProfile":
        from src.models.user import UserProfile
        return UserProfile
    elif name == "User":
        # Backward compatibility - redirect to UserProfile
        from src.models.user import UserProfile
        return UserProfile
    elif name == "Project":
        from src.models.project import Project
        return Project

    elif name == "Task":
        from src.models.task import Task
        return Task
    elif name == "AgentState":
        from src.models.agent_state import AgentState
        return AgentState
    elif name == "LLMProvider":
        from src.models.llm_config import LLMProvider
        return LLMProvider
    elif name == "LLMModel":
        from src.models.llm_config import LLMModel
        return LLMModel
    elif name == "AgentModelAssignment":
        from src.models.llm_config import AgentModelAssignment
        return AgentModelAssignment
    elif name == "ConversationHistory":
        from src.models.conversation import ConversationHistory
        return ConversationHistory
    elif name == "InterviewSession":
        from src.models.conversation import InterviewSession
        return InterviewSession
    elif name == "InterviewState":
        from src.models.conversation import InterviewState
        return InterviewState
    elif name == "RoadmapItem":
        from src.models.roadmap import RoadmapItem
        return RoadmapItem
    elif name == "Roadmap":
        from src.models.roadmap import Roadmap
        return Roadmap
    elif name == "RoadmapSummary":
        from src.models.roadmap import RoadmapSummary
        return RoadmapSummary
    elif name == "RoadmapSourceReference":
        from src.models.roadmap import RoadmapSourceReference
        return RoadmapSourceReference
    elif name == "Workspace":
        from src.models.workspace import Workspace
        return Workspace
    elif name == "IngestionError":
        from src.models.ingestion_errors import IngestionError
        return IngestionError
    elif name == "user_project_association":
        from src.models.association import user_project_association
        return user_project_association
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

# Remove __all__ since we're using lazy loading
