# Project: AI Coding Agent
# Author: AI Coding Agent Team
# Purpose: LLM service monitoring and metrics collection

import time
import psutil
import asyncio
import logging
from typing import TYPE_CHECKING, Any, Dict
from dataclasses import dataclass, asdict
from datetime import datetime
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

# Create mock classes to avoid import errors when prometheus_client is not available
class MockMetric:
    """Mock prometheus metric that supports method chaining for graceful degradation."""
    def __init__(self, *args, **kwargs):
        pass

    def set(self, value):
        return self

    def inc(self, value=1):
        return self

    def observe(self, value):
        return self

    def labels(self, **kwargs):
        return self  # Return self to support chaining

# Type-safe optional imports for monitoring
if TYPE_CHECKING:
    # For type checking, assume prometheus_client is available
    from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry
    import prometheus_client
    PROMETHEUS_AVAILABLE = True
else:
    # Runtime imports with graceful fallback
    try:
        import prometheus_client  # type: ignore[import-untyped]
        from prometheus_client import Counter, Histogram, Gauge  # type: ignore[import-untyped]
        from prometheus_client import REGISTRY as PROM_REGISTRY  # type: ignore
        PROMETHEUS_AVAILABLE = True
    except ImportError:
        PROMETHEUS_AVAILABLE = False
        prometheus_client = None
        PROM_REGISTRY = None
        logger.warning("prometheus_client not available - metrics collection will be disabled")

        # Use mock classes for graceful degradation
        Counter = Histogram = Gauge = MockMetric
        PROM_REGISTRY = None


@dataclass
class ResourceMetrics:
    """System resource metrics."""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    network_bytes_sent: int
    network_bytes_recv: int


@dataclass
class LLMPerformanceMetrics:
    """LLM-specific performance metrics."""
    provider: str
    model: str
    request_count: int
    success_count: int
    error_count: int
    avg_response_time_ms: float
    avg_tokens_per_second: float
    total_tokens_processed: int
    total_cost_usd: float
    last_request_time: float


class LLMResourceMonitor:
    """Monitors resource usage and performance for LLM containers."""

    def __init__(self, collection_interval: int = 30):
        """Initialize the resource monitor.

        Args:
            collection_interval: Seconds between metric collections
        """
        self.collection_interval = collection_interval
        self.running = False

        # Metrics storage
        self.resource_history: deque = deque(maxlen=1000)  # Last 1000 measurements
        self.llm_performance: Dict[str, LLMPerformanceMetrics] = {}

        # Prometheus metrics registry
    # Do not create a module-local CollectorRegistry by default. Use the
    # global registry to avoid duplicate time-series across imports. If an
    # explicit registry is required in the future, accept it as a ctor
    # argument.
    self.registry = PROM_REGISTRY if PROMETHEUS_AVAILABLE else None
    self._setup_prometheus_metrics()

        # Alerting thresholds
        self.thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage_percent': 90.0,
            'avg_response_time_ms': 10000.0,  # 10 seconds
            'error_rate_threshold': 0.1  # 10% error rate
        }

        # Alert state tracking
        self.alert_state = defaultdict(bool)
        self.alert_timestamps = defaultdict(float)

    def _setup_prometheus_metrics(self):
        """Setup Prometheus metrics for monitoring."""
        if not PROMETHEUS_AVAILABLE:
            logger.info("Prometheus client not available - using mock metrics")
            self.prometheus_metrics = {
                'system_cpu_percent': MockMetric(),
                'system_memory_percent': MockMetric(),
                'system_memory_used_mb': MockMetric(),
                'llm_requests_per_second': MockMetric(),
                'llm_avg_response_time': MockMetric(),
                'llm_tokens_per_second': MockMetric(),
                'llm_error_rate': MockMetric(),
                'llm_total_cost_usd': MockMetric(),
            }
            return

        # Helper to reuse existing metrics from the global registry to avoid
        # duplicate metric registration warnings when modules import each
        # other repeatedly.
        def _get_or_create_metric(metric_class, name, description, labelnames=None, **kwargs):
            try:
                # Use the global registry if available
                if PROM_REGISTRY is not None and hasattr(PROM_REGISTRY, '_names_to_collectors'):
                    if name in PROM_REGISTRY._names_to_collectors:
                        return PROM_REGISTRY._names_to_collectors[name]
                # Create metric; pass registry only if we have one
                if labelnames:
                    if self.registry is not None:
                        return metric_class(name, description, labelnames, registry=self.registry, **kwargs)
                    return metric_class(name, description, labelnames, **kwargs)
                else:
                    if self.registry is not None:
                        return metric_class(name, description, registry=self.registry, **kwargs)
                    return metric_class(name, description, **kwargs)
            except Exception:
                # Fall back to unguarded creation
                if labelnames:
                    return metric_class(name, description, labelnames, **kwargs)
                return metric_class(name, description, **kwargs)

        self.prometheus_metrics = {
            'system_cpu_percent': _get_or_create_metric(Gauge, 'system_cpu_percent', 'System CPU usage percentage'),
            'system_memory_percent': _get_or_create_metric(Gauge, 'system_memory_percent', 'System memory usage percentage'),
            'system_memory_used_mb': _get_or_create_metric(Gauge, 'system_memory_used_mb', 'System memory used in MB'),
            'llm_requests_per_second': _get_or_create_metric(Gauge, 'llm_requests_per_second', 'LLM requests per second', ['provider', 'model']),
            'llm_avg_response_time': _get_or_create_metric(Gauge, 'llm_avg_response_time_seconds', 'Average LLM response time', ['provider', 'model']),
            'llm_tokens_per_second': _get_or_create_metric(Gauge, 'llm_tokens_per_second', 'LLM tokens processed per second', ['provider', 'model']),
            'llm_error_rate': _get_or_create_metric(Gauge, 'llm_error_rate', 'LLM error rate (errors/total requests)', ['provider', 'model']),
            'llm_total_cost_usd': _get_or_create_metric(Counter, 'llm_total_cost_usd', 'Total LLM cost in USD', ['provider']),
        }

    async def start_monitoring(self):
        """Start the monitoring loop."""
        if self.running:
            logger.warning("Monitor is already running")
            return

        self.running = True
        logger.info(f"Starting LLM resource monitoring (interval: {self.collection_interval}s)")

        try:
            while self.running:
                await self._collect_metrics()
                await asyncio.sleep(self.collection_interval)
        except Exception as e:
            logger.error(f"Error in monitoring loop: {str(e)}")
        finally:
            self.running = False
            logger.info("LLM resource monitoring stopped")

    def stop_monitoring(self):
        """Stop the monitoring loop."""
        self.running = False

    async def _collect_metrics(self):
        """Collect system and LLM metrics."""
        try:
            # Collect system metrics
            system_metrics = await self._collect_system_metrics()
            self.resource_history.append(system_metrics)

            # Update Prometheus metrics
            self._update_prometheus_system_metrics(system_metrics)
            self._update_prometheus_llm_metrics()

            # Check for alerts
            await self._check_alerts(system_metrics)

            # Log summary every 10 collections (5 minutes with 30s interval)
            if len(self.resource_history) % 10 == 0:
                await self._log_metrics_summary()

        except Exception as e:
            logger.error(f"Error collecting metrics: {str(e)}")

    async def _collect_system_metrics(self) -> ResourceMetrics:
        """Collect system resource metrics."""
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)

        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / (1024 * 1024)
        memory_available_mb = memory.available / (1024 * 1024)

        # Disk usage (cross-platform compatible)
        try:
            import os
            if os.name == 'nt':  # Windows
                disk_path = 'C:\\'
            else:  # Unix-like systems
                disk_path = '/'
            disk = psutil.disk_usage(disk_path)
            disk_usage_percent = (disk.used / disk.total) * 100
        except Exception as e:
            logger.warning(f"Could not get disk usage: {e}")
            disk_usage_percent = 0.0

        # Network I/O
        network = psutil.net_io_counters()
        network_bytes_sent = network.bytes_sent
        network_bytes_recv = network.bytes_recv

        return ResourceMetrics(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            memory_available_mb=memory_available_mb,
            disk_usage_percent=disk_usage_percent,
            network_bytes_sent=network_bytes_sent,
            network_bytes_recv=network_bytes_recv
        )

    def _update_prometheus_system_metrics(self, metrics: ResourceMetrics):
        """Update Prometheus system metrics."""
        self.prometheus_metrics['system_cpu_percent'].set(metrics.cpu_percent)
        self.prometheus_metrics['system_memory_percent'].set(metrics.memory_percent)
        self.prometheus_metrics['system_memory_used_mb'].set(metrics.memory_used_mb)

    def _update_prometheus_llm_metrics(self):
        """Update Prometheus LLM metrics."""
        for key, perf in self.llm_performance.items():
            labels = {'provider': perf.provider, 'model': perf.model}

            # Calculate requests per second (over last 5 minutes)
            recent_time_window = time.time() - 300  # 5 minutes
            if perf.last_request_time > recent_time_window:
                rps = perf.request_count / min(300, time.time() - perf.last_request_time)
                self.prometheus_metrics['llm_requests_per_second'].labels(**labels).set(rps)

            # Response time (convert to seconds)
            response_time_s = perf.avg_response_time_ms / 1000.0
            self.prometheus_metrics['llm_avg_response_time'].labels(**labels).set(response_time_s)

            # Tokens per second
            self.prometheus_metrics['llm_tokens_per_second'].labels(**labels).set(
                perf.avg_tokens_per_second
            )

            # Error rate
            error_rate = perf.error_count / max(perf.request_count, 1)
            self.prometheus_metrics['llm_error_rate'].labels(**labels).set(error_rate)

    async def _check_alerts(self, system_metrics: ResourceMetrics):
        """Check for alert conditions."""
        alerts_triggered = []

        # System resource alerts
        if system_metrics.cpu_percent > self.thresholds['cpu_percent']:
            alerts_triggered.append(('high_cpu', system_metrics.cpu_percent))

        if system_metrics.memory_percent > self.thresholds['memory_percent']:
            alerts_triggered.append(('high_memory', system_metrics.memory_percent))

        if system_metrics.disk_usage_percent > self.thresholds['disk_usage_percent']:
            alerts_triggered.append(('high_disk', system_metrics.disk_usage_percent))

        # LLM performance alerts
        for key, perf in self.llm_performance.items():
            if perf.avg_response_time_ms > self.thresholds['avg_response_time_ms']:
                alerts_triggered.append(('slow_llm_response', f"{perf.provider}/{perf.model}"))

            error_rate = perf.error_count / max(perf.request_count, 1)
            if error_rate > self.thresholds['error_rate_threshold']:
                alerts_triggered.append(('high_llm_error_rate', f"{perf.provider}/{perf.model}"))

        # Process alerts
        for alert_type, value in alerts_triggered:
            await self._handle_alert(alert_type, value)

    async def _handle_alert(self, alert_type: str, value: Any):
        """Handle alert conditions."""
        current_time = time.time()

        # Avoid spam - only alert every 5 minutes for the same condition
        if (current_time - self.alert_timestamps.get(alert_type, 0)) < 300:
            return

        self.alert_timestamps[alert_type] = current_time

        if not self.alert_state[alert_type]:
            self.alert_state[alert_type] = True
            logger.warning(f"ALERT TRIGGERED: {alert_type} - Value: {value}")

            # Here you could integrate with alerting systems like:
            # - Send email notification
            # - Post to Slack/Discord
            # - Create Grafana annotation
            # - Trigger auto-scaling

            await self._suggest_optimization(alert_type, value)

    async def _suggest_optimization(self, alert_type: str, value: Any):
        """Suggest optimizations based on alert type."""
        optimizations = {
            'high_cpu': [
                "Consider reducing concurrent LLM requests",
                "Scale to multiple instances",
                "Use CPU-optimized instance types",
                "Implement request queuing"
            ],
            'high_memory': [
                "Consider smaller models or quantized versions",
                "Implement model caching strategies",
                "Scale to instances with more memory",
                "Clear unused model cache"
            ],
            'high_disk': [
                "Clean up old model files",
                "Implement log rotation",
                "Move to larger storage volume",
                "Compress model artifacts"
            ],
            'slow_llm_response': [
                "Check network latency to provider",
                "Consider local model deployment",
                "Implement response caching",
                "Optimize prompt length"
            ],
            'high_llm_error_rate': [
                "Check provider API status",
                "Verify API keys and quotas",
                "Implement better retry logic",
                "Switch to fallback provider"
            ]
        }

        suggestions = optimizations.get(alert_type, ["Contact system administrator"])
        logger.info(f"Optimization suggestions for {alert_type}:")
        for i, suggestion in enumerate(suggestions, 1):
            logger.info(f"  {i}. {suggestion}")

    async def _log_metrics_summary(self):
        """Log a summary of current metrics."""
        if not self.resource_history:
            return

        recent_metrics = list(self.resource_history)[-10:]  # Last 10 measurements

        # Calculate averages
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory_mb = sum(m.memory_used_mb for m in recent_metrics) / len(recent_metrics)

        logger.info(f"System Metrics Summary (last {len(recent_metrics)} measurements):")
        logger.info(f"  CPU: {avg_cpu:.1f}% | Memory: {avg_memory:.1f}% ({avg_memory_mb:.0f}MB)")

        # LLM performance summary
        if self.llm_performance:
            logger.info("LLM Performance Summary:")
            for key, perf in self.llm_performance.items():
                error_rate = (perf.error_count / max(perf.request_count, 1)) * 100
                logger.info(
                    f"  {perf.provider}/{perf.model}: "
                    f"{perf.request_count} reqs, "
                    f"{perf.avg_response_time_ms:.0f}ms avg, "
                    f"{error_rate:.1f}% errors, "
                    f"${perf.total_cost_usd:.4f} cost"
                )

    def update_llm_performance(self, provider: str, model: str, response_time_ms: float,
                             tokens_processed: int, success: bool, cost_usd: float = 0.0):
        """Update LLM performance metrics."""
        key = f"{provider}/{model}"

        if key not in self.llm_performance:
            self.llm_performance[key] = LLMPerformanceMetrics(
                provider=provider,
                model=model,
                request_count=0,
                success_count=0,
                error_count=0,
                avg_response_time_ms=0.0,
                avg_tokens_per_second=0.0,
                total_tokens_processed=0,
                total_cost_usd=0.0,
                last_request_time=0.0
            )

        perf = self.llm_performance[key]

        # Update counters
        perf.request_count += 1
        if success:
            perf.success_count += 1
        else:
            perf.error_count += 1

        # Update averages (simple moving average)
        alpha = 0.1  # Weight for new values
        perf.avg_response_time_ms = (
            (1 - alpha) * perf.avg_response_time_ms + alpha * response_time_ms
        )

        # Calculate tokens per second
        if response_time_ms > 0:
            tokens_per_second = (tokens_processed * 1000.0) / response_time_ms
            perf.avg_tokens_per_second = (
                (1 - alpha) * perf.avg_tokens_per_second + alpha * tokens_per_second
            )

        # Update totals
        perf.total_tokens_processed += tokens_processed
        perf.total_cost_usd += cost_usd
        perf.last_request_time = time.time()

    def get_metrics_summary(self, time_window_hours: int = 1) -> Dict[str, Any]:
        """Get a summary of metrics for the specified time window."""
        current_time = time.time()
        cutoff_time = current_time - (time_window_hours * 3600)

        # Filter recent resource metrics
        recent_resources = [
            m for m in self.resource_history
            if m.timestamp > cutoff_time
        ]

        if not recent_resources:
            return {"error": "No metrics available for specified time window"}

        # Calculate resource statistics
        resource_stats = {
            "cpu_percent": {
                "avg": sum(m.cpu_percent for m in recent_resources) / len(recent_resources),
                "max": max(m.cpu_percent for m in recent_resources),
                "min": min(m.cpu_percent for m in recent_resources)
            },
            "memory_percent": {
                "avg": sum(m.memory_percent for m in recent_resources) / len(recent_resources),
                "max": max(m.memory_percent for m in recent_resources),
                "min": min(m.memory_percent for m in recent_resources)
            },
            "memory_used_mb": {
                "avg": sum(m.memory_used_mb for m in recent_resources) / len(recent_resources),
                "max": max(m.memory_used_mb for m in recent_resources),
                "min": min(m.memory_used_mb for m in recent_resources)
            }
        }

        # LLM performance summary
        llm_summary = {}
        for key, perf in self.llm_performance.items():
            if perf.last_request_time > cutoff_time:
                llm_summary[key] = asdict(perf)

        return {
            "time_window_hours": time_window_hours,
            "measurement_count": len(recent_resources),
            "resource_stats": resource_stats,
            "llm_performance": llm_summary,
            "active_alerts": [k for k, v in self.alert_state.items() if v],
            "generated_at": datetime.fromtimestamp(current_time).isoformat()
        }

    def export_prometheus_metrics(self) -> str:
        """Export metrics in Prometheus format."""
        if not PROMETHEUS_AVAILABLE or not prometheus_client or not self.registry:
            logger.warning("Prometheus client not available - cannot export metrics")
            return "# Prometheus client not available\n"

        return prometheus_client.generate_latest(self.registry).decode('utf-8')


# Global monitor instance
monitor = LLMResourceMonitor()


async def start_monitoring(collection_interval: int = 30):
    """Start the global monitoring instance."""
    global monitor
    monitor.collection_interval = collection_interval
    await monitor.start_monitoring()


def stop_monitoring():
    """Stop the global monitoring instance."""
    global monitor
    monitor.stop_monitoring()


def update_llm_metrics(provider: str, model: str, response_time_ms: float,
                      tokens_processed: int, success: bool, cost_usd: float = 0.0):
    """Update LLM performance metrics (convenience function)."""
    global monitor
    monitor.update_llm_performance(
        provider, model, response_time_ms, tokens_processed, success, cost_usd
    )


def get_metrics_summary(time_window_hours: int = 1) -> Dict[str, Any]:
    """Get metrics summary (convenience function)."""
    global monitor
    return monitor.get_metrics_summary(time_window_hours)