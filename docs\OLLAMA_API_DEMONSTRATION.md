# Ollama API Usage Guide

## Overview

This guide demonstrates how to use the Ollama API within the AI Coding Agent Docker environment. The setup includes model persistence, API access, and examples for both embedding and text generation models.

## Current Setup

- **Base URL**: `http://ollama:11434` (internal) or `http://localhost:11434` (external)
- **Available Models**: `nomic-embed-text:latest` (embeddings)
- **Persistence**: Models are stored locally at `C:\Users\<USER>\.ollama` and persist between container restarts
- **API Access**: RESTful HTTP API with JSON responses

## API Endpoints

### 1. List Available Models

```bash
curl http://localhost:11434/api/tags
```

**Response:**

```json
{
  "models": [
    {
      "name": "nomic-embed-text:latest",
      "size": 274289600,
      "digest": "0a109f422b47...",
      "details": {
        "format": "gguf",
        "family": "nomic-bert",
        "families": ["nomic-bert"],
        "parameter_size": "137M",
        "quantization_level": "F16"
      },
      "modified_at": "2025-08-31T01:33:00Z"
    }
  ]
}
```

### 2. Generate Embeddings

```bash
curl -X POST http://localhost:11434/api/embeddings \
  -H "Content-Type: application/json" \
  -d '{
    "model": "nomic-embed-text",
    "prompt": "def fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)"
  }'
```

**Response:**

```json
{
  "embedding": [
    -0.7175257205963135,
    -1.1097725629806519,
    -3.068136692047119,
    // ... 764 more dimensions
  ]
}
```

### 3. Check Model Information

```bash
curl http://localhost:11434/api/show \
  -H "Content-Type: application/json" \
  -d '{"name": "nomic-embed-text"}'
```

## Python Examples

### Basic API Usage

```python
import requests
import json

class OllamaClient:
    def __init__(self, base_url="http://localhost:11434"):
        self.base_url = base_url

    def list_models(self):
        """List all available models"""
        response = requests.get(f"{self.base_url}/api/tags")
        return response.json()

    def generate_embedding(self, text, model="nomic-embed-text"):
        """Generate embeddings for text"""
        payload = {
            "model": model,
            "prompt": text
        }
        response = requests.post(
            f"{self.base_url}/api/embeddings",
            json=payload
        )
        return response.json()["embedding"]

    def check_model_info(self, model_name):
        """Get detailed model information"""
        payload = {"name": model_name}
        response = requests.post(
            f"{self.base_url}/api/show",
            json=payload
        )
        return response.json()

# Usage example
client = OllamaClient()

# List models
models = client.list_models()
print("Available models:", [m["name"] for m in models["models"]])

# Generate embedding
code_sample = """
def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)
"""

embedding = client.generate_embedding(code_sample)
print(f"Embedding dimensions: {len(embedding)}")
print(f"First 5 values: {embedding[:5]}")
```

### Code Search Example

```python
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

class CodeEmbeddingSearch:
    def __init__(self, ollama_client):
        self.client = ollama_client
        self.codebase_embeddings = []
        self.codebase_chunks = []

    def add_code_to_index(self, code_chunk, metadata=None):
        """Add code to the search index"""
        embedding = self.client.generate_embedding(code_chunk)
        self.codebase_embeddings.append(embedding)
        self.codebase_chunks.append({
            "code": code_chunk,
            "metadata": metadata
        })

    def search_similar_code(self, query, top_k=5):
        """Find similar code using embeddings"""
        query_embedding = self.client.generate_embedding(query)

        # Calculate similarities
        similarities = cosine_similarity(
            [query_embedding],
            self.codebase_embeddings
        )[0]

        # Get top results
        top_indices = np.argsort(similarities)[::-1][:top_k]

        results = []
        for idx in top_indices:
            results.append({
                "code": self.codebase_chunks[idx]["code"],
                "similarity": similarities[idx],
                "metadata": self.codebase_chunks[idx]["metadata"]
            })

        return results

# Usage example
search_engine = CodeEmbeddingSearch(client)

# Add some code samples to the index
search_engine.add_code_to_index("""
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quicksort(left) + middle + quicksort(right)
""", {"language": "python", "algorithm": "sorting"})

search_engine.add_code_to_index("""
def binary_search(arr, target):
    left, right = 0, len(arr) - 1
    while left <= right:
        mid = (left + right) // 2
        if arr[mid] == target:
            return mid
        elif arr[mid] < target:
            left = mid + 1
        else:
            right = mid - 1
    return -1
""", {"language": "python", "algorithm": "search"})

# Search for similar code
query = "sort an array efficiently"
results = search_engine.search_similar_code(query)

for result in results:
    print(f"Similarity: {result['similarity']:.3f}")
    print(f"Code: {result['code'][:100]}...")
    print("---")
```

## Docker Commands

### Model Management

```bash
# Check running models
docker exec ollama ollama list

# Pull additional models
docker exec ollama ollama pull llama3.2:3b
docker exec ollama ollama pull codellama:7b

# Remove models
docker exec ollama ollama rm nomic-embed-text

# Check model info
docker exec ollama ollama show nomic-embed-text
```

### API Testing

```bash
# Test API connectivity
docker exec ollama curl http://localhost:11434/api/tags

# Test embeddings with curl
docker exec ollama curl -X POST http://localhost:11434/api/embeddings \
  -H "Content-Type: application/json" \
  -d '{"model": "nomic-embed-text", "prompt": "test"}'
```

## Model Persistence

### How It Works

1. **Local Storage**: Models are stored at `C:\Users\<USER>\.ollama\models`
2. **Bind Mount**: Docker uses `- 'C:\Users\<USER>\.ollama:/root/.ollama:rw'` to mount local directory
3. **Persistence**: Models survive container restarts and recreation
4. **Sharing**: Same models can be used by multiple Ollama instances

### Verification

```bash
# Check local model storage
ls -la "C:\Users\<USER>\.ollama\models"

# Check container model storage
docker exec ollama ls -la /root/.ollama/models

# Verify persistence after restart
docker compose restart ollama
docker exec ollama ollama list
```

## Integration with AI Coding Agent

### FastAPI Example

```python
from fastapi import FastAPI, HTTPException
from typing import List, Dict
import requests

app = FastAPI()

class OllamaService:
    def __init__(self, base_url="http://ollama:11434"):
        self.base_url = base_url

    def embed_code(self, code: str) -> List[float]:
        """Generate embeddings for code snippets"""
        try:
            response = requests.post(
                f"{self.base_url}/api/embeddings",
                json={"model": "nomic-embed-text", "prompt": code},
                timeout=30
            )
            response.raise_for_status()
            return response.json()["embedding"]
        except requests.RequestException as e:
            raise HTTPException(status_code=500, detail=f"Ollama API error: {str(e)}")

ollama_service = OllamaService()

@app.post("/embed")
async def embed_code(code: str):
    """Endpoint to embed code for the AI Coding Agent"""
    embedding = ollama_service.embed_code(code)
    return {
        "embedding": embedding,
        "dimensions": len(embedding),
        "model": "nomic-embed-text"
    }

@app.get("/models")
async def list_models():
    """List available models"""
    try:
        response = requests.get(f"{ollama_service.base_url}/api/tags")
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        raise HTTPException(status_code=500, detail=f"Ollama API error: {str(e)}")
```

## Troubleshooting

### Common Issues

1. **Model not found**

   ```bash
   docker exec ollama ollama pull nomic-embed-text
   ```

2. **API connection failed**

   ```bash
   # Check if Ollama is running
   docker compose ps ollama

   # Check Ollama logs
   docker compose logs ollama
   ```

3. **Model persistence issues**

   ```bash
   # Verify mount is working
   docker exec ollama ls -la /root/.ollama/models

   # Check local models
   ls -la "C:\Users\<USER>\.ollama\models"
   ```

4. **Memory issues**

   ```bash
   # Check container resource usage
   docker stats ollama

   # Adjust memory limits in docker-compose.yml
   deploy:
     resources:
       limits:
         memory: 8G
   ```

## Performance Tips

1. **Pre-load models**: Pull models during container build or startup
2. **Use appropriate model sizes**: Smaller models for faster inference
3. **Batch processing**: Process multiple embeddings in parallel
4. **Caching**: Cache frequently used embeddings
5. **Resource limits**: Set appropriate CPU/memory limits based on your hardware

## Next Steps

1. Add more models for different use cases (code generation, analysis, etc.)
2. Implement model switching based on task requirements
3. Add monitoring and metrics for API usage
4. Implement retry logic and fallback mechanisms
5. Add support for streaming responses for larger models
