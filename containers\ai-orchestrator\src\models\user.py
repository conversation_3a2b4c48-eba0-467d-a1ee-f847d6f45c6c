# Project: AI Coding Agent
# Purpose: User SQLAlchemy model definition
# Author: AI Coding Agent Team

"""
User model for the AI Orchestrator service.

This module defines the SQLAlchemy User model with all necessary fields,
indexes, and constraints for user management and authentication.
"""

from typing import Optional
from sqlalchemy import <PERSON>umn, Inte<PERSON>, <PERSON>, DateTime, Boolean, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from src.models.database import Base
from src.models.custom_types import UuidVariant


class UserProfile(Base):
    """
    User model for authentication and user management.

    This model stores user account information including credentials,
    profile data, and audit timestamps.

    Attributes:
        id: Primary key, auto-incrementing integer
        username: Unique username for login (indexed)
        email: Unique email address for account identification
        hashed_password: Bcrypt hashed password (never store plain text)
        full_name: Optional full display name
        is_active: Account activation status (default: True)
        is_superuser: Admin privileges flag (default: False)
        created_at: Timestamp when account was created
        updated_at: Timestamp when account was last modified
        last_login: Timestamp of last successful login
        profile_data: JSON field for additional profile information
    """

    __tablename__ = "user_profiles"

    # Primary key
    id = Column(
        Integer,
        primary_key=True,
        index=True,
        autoincrement=True,
        comment="Unique user identifier"
    )

    # Supabase Auth integration
    supabase_user_id = Column(
        UuidVariant,
        # ForeignKey('auth_users.id' if os.getenv('TESTING') == '1' else 'auth.users.id', ondelete='CASCADE'),
        unique=True,
        index=True,
        nullable=True,  # Temporarily nullable to allow migration without auth.users
        comment="Foreign key to Supabase auth.users table"
    )

    # Authentication fields
    username = Column(
        String(50),
        unique=True,
        index=True,
        nullable=False,
        comment="Unique username for login"
    )

    email = Column(
        String(255),
        unique=True,
        index=True,
        nullable=False,
        comment="User email address"
    )

    hashed_password = Column(
        String(255),
        nullable=False,
        comment="Bcrypt hashed password"
    )

    # Profile fields
    full_name = Column(
        String(255),
        nullable=True,
        comment="User's full display name"
    )

    # Status fields
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Account activation status"
    )

    is_superuser = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="Admin privileges flag"
    )

    # Audit timestamps
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Account creation timestamp"
    )

    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Last modification timestamp"
    )

    last_login = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="Last successful login timestamp"
    )

    # Additional profile data (JSON field for extensibility)
    profile_data = Column(
        Text,
        nullable=True,
        comment="JSON field for additional profile information"
    )

    # Resource management
    storage_usage_bytes = Column(
        Integer,
        default=0,
        nullable=False,
        comment="Total storage usage in bytes for user's documents and embeddings"
    )

    def __repr__(self) -> str:
        """String representation for debugging."""
        return f"<User(id={self.id}, supabase_id='{self.supabase_user_id}', username='{self.username}', email='{self.email}')>"

    def __str__(self) -> str:
        """Human-readable string representation."""
        return f"{self.username} ({self.email})"

    @property
    def display_name(self) -> str:
        """Get the best available display name for the user."""
        return getattr(self, 'full_name', None) or getattr(self, 'username', '')

    @property
    def supabase_user_uuid(self) -> Optional[uuid.UUID]:
        """
        Get the Supabase user ID as a UUID object.

        This property handles conversion from string format (SQLite) to UUID object.

        Returns:
            Optional[uuid.UUID]: The Supabase user ID as UUID, or None if not set
        """
        if not self.supabase_user_id:
            return None

        from src.models.database import get_db_dialect
        if get_db_dialect() == "postgresql":
            # PostgreSQL UUID type returns UUID object directly
            return self.supabase_user_id
        else:
            # SQLite CHAR(32) stores as string without hyphens, convert back to UUID
            if len(str(self.supabase_user_id)) == 32:
                # Add hyphens back to create valid UUID format
                uuid_str = f"{self.supabase_user_id[:8]}-{self.supabase_user_id[8:12]}-{self.supabase_user_id[12:16]}-{self.supabase_user_id[16:20]}-{self.supabase_user_id[20:]}"
                return uuid.UUID(uuid_str)
            else:
                # Already in UUID format or invalid
                try:
                    return uuid.UUID(str(self.supabase_user_id))
                except ValueError:
                    return None

    def is_authenticated(self) -> bool:
        """Check if user is authenticated (has valid account)."""
        return getattr(self, 'is_active', False)

    def has_permission(self, permission: str) -> bool:
        """
        Check if user has a specific permission.

        Args:
            permission: Permission name to check

        Returns:
            bool: True if user has permission

        Note:
            This is a placeholder for future role-based access control.
            Currently, only superusers have all permissions.
        """
        return getattr(self, 'is_superuser', False)

    # Projects owned by the user
    owned_projects = relationship("Project", back_populates="owner", lazy='selectin')

    # Projects the user is a member of
    projects = relationship(
        "Project",
        secondary="user_project_association",
        back_populates="members",
        lazy='selectin'
    )

    # Deployment integrations relationship
    deployment_integrations = relationship(
        "DeploymentIntegration",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy='selectin'
    )

    @classmethod
    def create_from_supabase_user(
        cls,
        supabase_user_id: uuid.UUID,
        email: str,
        username: Optional[str] = None,
        full_name: Optional[str] = None,
        **kwargs
    ) -> 'UserProfile':
        """
        Create User instance from Supabase auth user data.

        Args:
            supabase_user_id: UUID from Supabase auth.users.id
            email: User email address
            username: Optional username (will generate from email if not provided)
            full_name: Optional full name
            **kwargs: Additional user fields

        Returns:
            User: New User instance (not yet saved to database)
        """
        if not username:
            # Generate username from email
            username = email.split('@')[0].lower()

        # Convert UUID to string format compatible with our database schema
        from src.models.database import get_db_dialect
        if get_db_dialect() == "postgresql":
            # PostgreSQL UUID type handles UUID objects directly
            user_id_value = supabase_user_id
        else:
            # SQLite CHAR(32) expects string without hyphens
            user_id_value = str(supabase_user_id).replace('-', '')

        return cls(
            supabase_user_id=user_id_value,
            email=email,
            username=username,
            full_name=full_name,
            hashed_password="",  # Password managed by Supabase
            **kwargs
        )
