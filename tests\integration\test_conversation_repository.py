import pytest
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from src.models import Base, InterviewSession, InterviewState
from src.repository.conversation_repository import ConversationRepository

# No longer needed, will be handled by PYTHONPATH

DATABASE_URL = "sqlite:///:memory:"
engine = create_engine(DATABASE_URL)

@pytest.fixture(scope="function")
def db_session() -> Session:
    Base.metadata.create_all(bind=engine)
    session = Session(engine)
    yield session
    session.rollback()
    Base.metadata.drop_all(bind=engine)

@pytest.mark.asyncio
async def test_update_interview_session_state_conditional(db_session: Session):
    # 1. Create an initial interview session
    initial_session = InterviewSession(
        project_id=1,
        user_id="test_user",
        session_id="test_session",
        current_state=InterviewState.GREETING.value,
        current_question_index=5,
        is_active=True,
    )
    db_session.add(initial_session)
    db_session.commit()
    db_session.refresh(initial_session)

    # 2. Update state without changing the question index
    updated_session = await ConversationRepository.update_interview_session_state(
        db=db_session,
        session_id="test_session",
        new_state=InterviewState.IN_PROGRESS,
        current_question_index=None,  # This should not change the index
    )

    assert updated_session is not None
    assert updated_session.current_state == InterviewState.IN_PROGRESS.value
    assert updated_session.current_question_index == 5  # Should remain unchanged

    # 3. Update state and question index
    final_session = await ConversationRepository.update_interview_session_state(
        db=db_session,
        session_id="test_session",
        new_state=InterviewState.AWAITING_RESPONSE,
        current_question_index=10,
    )

    assert final_session is not None
    assert final_session.current_state == InterviewState.AWAITING_RESPONSE.value
    assert final_session.current_question_index == 10  # Should be updated
