# Database Operations Guide

## Architectural Overview

Our system uses a local Supabase stack for development, which includes its own PostgreSQL instance with pgvector support. The ai-orchestrator application manages its own schema within this Supabase database, using SQLAlchemy ORM with Alembic for migration management.

## Developer Workflow for Schema Changes

### 1. Modify the SQLAlchemy models

Edit the relevant model files in `src/models/` to add, modify, or remove fields/tables.

### 2. Generate a migration script

Run the following command to create an Alembic migration:

```bash
docker-compose exec ai-orchestrator alembic revision --autogenerate -m "Your descriptive message"
```

### 3. Review the generated migration script

Check the generated script in `alembic/versions/` for accuracy and make any necessary adjustments.

### 4. Apply the migration

Execute the migration to update the database schema:

```bash
docker-compose exec ai-orchestrator alembic upgrade head
```

## Connecting with a GUI Tool

To connect to the local Supabase database from a GUI tool like DBeaver or TablePlus:

- **Host:** 127.0.0.1
- **Port:** 54322 (mapped from supabase-db:5432 when using the Supabase CLI default)
- **User:** postgres
- **Password:** postgres
- **Database:** postgres

Note: Ensure the Docker containers are running before attempting to connect.</content>
<parameter name="filePath">c:\Users\<USER>\Desktop\codingagenttwo\SYSTEM_INTEGRATION_AUDIT_REPORT.md
