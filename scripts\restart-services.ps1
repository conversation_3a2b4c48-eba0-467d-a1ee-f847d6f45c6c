Write-Host "Stopping all services..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down

Write-Host "Removing old containers and networks..."
docker system prune -f

Write-Host "Starting services..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

Write-Host "Waiting for services to be ready..."
Start-Sleep -Seconds 10

Write-Host "Checking service status..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml ps

Write-Host ""
Write-Host "Testing endpoints..."
Write-Host "Testing ai-orchestrator health:"
try {
  $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -ErrorAction SilentlyContinue
  if ($response.StatusCode -eq 200) {
    Write-Host "ai-orchestrator is responding"
  }
  else {
    Write-Host "ai-orchestrator not ready yet"
  }
}
catch {
  Write-Host "ai-orchestrator not ready yet"
}

Write-Host ""
Write-Host "Testing user-portal:"
try {
  $response = Invoke-WebRequest -Uri "http://localhost:3000/" -ErrorAction SilentlyContinue
  if ($response.StatusCode -eq 200 -and $response.Content -match "html") {
    Write-Host "user-portal is responding"
  }
  else {
    Write-Host "user-portal not ready yet"
  }
}
catch {
  Write-Host "user-portal not ready yet"
}

Write-Host ""
Write-Host "Testing Ollama:"
try {
  $response = Invoke-WebRequest -Uri "http://localhost:11434/api/tags" -ErrorAction SilentlyContinue
  if ($response.StatusCode -eq 200 -and $response.Content -match "models") {
    Write-Host "Ollama is responding"
  }
  else {
    Write-Host "Ollama not ready yet"
  }
}
catch {
  Write-Host "Ollama not ready yet"
}

Write-Host ""
Write-Host "To view logs, run: docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f"
