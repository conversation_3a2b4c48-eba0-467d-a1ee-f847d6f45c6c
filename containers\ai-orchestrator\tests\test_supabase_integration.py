"""
Comprehensive Test Suite for Supabase Integration.

This module provides comprehensive tests for all Supabase-related functionality
including authentication, vector storage, RAG services, and API endpoints
with proper mocking and fixtures.

Author: AI Coding Agent
Version: 1.0.0
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

# Test framework imports
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Internal imports
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.services.supabase_service import SupabaseService, SupabaseConfig
from src.services.auth_service import AuthService, UserProfile, UserRole, AuthTokens
from src.services.vector_service import VectorStorageService, VectorConfig, SearchResult
from src.services.rag_service import RAGService, RAGConfig, RAGResponse
from src.main import app


# ==================================================================================
# FIXTURES AND SETUP
# ==================================================================================

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_supabase_config():
    """Mock Supabase configuration for testing."""
    return SupabaseConfig(
        url="http://localhost:8000",
        anon_key="test_anon_key",
        service_key="test_service_key",
        database_url="postgresql://postgres:password@localhost:5433/test_db",
        jwt_secret="test_jwt_secret_with_at_least_32_characters",
        max_connections=5,
        min_connections=1
    )


@pytest.fixture
def mock_vector_config():
    """Mock vector configuration for testing."""
    return VectorConfig(
        embedding_dimension=384,
        chunk_size=512,
        chunk_overlap=50,
        similarity_threshold=0.7,
        max_search_results=5
    )


@pytest.fixture
def mock_rag_config():
    """Mock RAG configuration for testing."""
    return RAGConfig(
        max_context_tokens=2000,
        max_search_results=3,
        similarity_threshold=0.7,
        temperature=0.1
    )


@pytest.fixture
def sample_user_profile():
    """Sample user profile for testing."""
    return UserProfile(
        id="test_user_id",
        email="<EMAIL>",
        username="testuser",
        full_name="Test User",
        role=UserRole.USER,
        created_at=datetime.utcnow(),
        email_verified=True,
        is_active=True
    )


@pytest.fixture
def sample_auth_tokens():
    """Sample authentication tokens for testing."""
    return AuthTokens(
        access_token="test_access_token",
        refresh_token="test_refresh_token",
        expires_in=1800
    )


@pytest.fixture
def sample_search_results():
    """Sample search results for testing."""
    return [
        SearchResult(
            id="result_1",
            document_id="doc_1",
            content="This is a test document about Python programming.",
            similarity=0.85,
            metadata={"file_type": "python", "tags": ["test", "programming"]}
        ),
        SearchResult(
            id="result_2",
            document_id="doc_2",
            content="FastAPI is a modern web framework for building APIs.",
            similarity=0.78,
            metadata={"file_type": "documentation", "tags": ["fastapi", "web"]}
        )
    ]


@pytest.fixture
async def mock_supabase_service(mock_supabase_config):
    """Mock Supabase service for testing."""
    with patch('src.services.supabase_service.create_client') as mock_create_client, \
         patch('src.services.supabase_service.asyncpg') as mock_asyncpg:

        # Mock Supabase clients
        mock_client = Mock()
        mock_create_client.return_value = mock_client

        # Mock database pool
        mock_pool = AsyncMock()
        mock_asyncpg.create_pool.return_value = mock_pool

        service = SupabaseService(mock_supabase_config)
        service._initialized = True
        service._client = mock_client
        service._service_client = mock_client
        service._db_pool = mock_pool

        yield service


@pytest.fixture
async def mock_auth_service(mock_supabase_service):
    """Mock authentication service for testing."""
    auth_service = AuthService(mock_supabase_service)
    yield auth_service


@pytest.fixture
async def mock_vector_service(mock_supabase_service, mock_vector_config):
    """Mock vector service for testing."""
    with patch('src.services.vector_service.SentenceTransformer') as mock_st:
        mock_model = Mock()
        mock_model.encode.return_value.tolist.return_value = [0.1] * 384
        mock_st.return_value = mock_model

        service = VectorStorageService(mock_supabase_service, mock_vector_config)
        service._embedding_model = mock_model

        yield service


@pytest.fixture
async def mock_rag_service(mock_vector_service, mock_rag_config):
    """Mock RAG service for testing."""
    service = RAGService(mock_vector_service, mock_rag_config)
    yield service


@pytest.fixture
def test_client():
    """FastAPI test client."""
    with TestClient(app) as client:
        yield client


@pytest.fixture
async def async_test_client():
    """Async FastAPI test client."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


# ==================================================================================
# SUPABASE SERVICE TESTS
# ==================================================================================

class TestSupabaseService:
    """Test cases for Supabase service."""

    @pytest.mark.asyncio
    async def test_service_initialization(self, mock_supabase_config):
        """Test Supabase service initialization."""
        with patch('src.services.supabase_service.create_client') as mock_create_client, \
             patch('src.services.supabase_service.asyncpg') as mock_asyncpg:

            # Setup mocks
            mock_client = Mock()
            mock_create_client.return_value = mock_client
            mock_pool = AsyncMock()
            mock_asyncpg.create_pool.return_value = mock_pool

            # Test initialization
            service = SupabaseService(mock_supabase_config)
            await service.initialize()

            assert service.is_initialized
            assert service._client == mock_client
            assert service._service_client == mock_client
            assert service._db_pool == mock_pool

    @pytest.mark.asyncio
    async def test_health_check(self, mock_supabase_service):
        """Test health check functionality."""
        # Mock database query
        mock_supabase_service._db_pool.acquire().__aenter__.return_value.fetchval.return_value = 1

        # Mock API client
        mock_supabase_service._service_client.table.return_value.select.return_value.limit.return_value.execute.return_value = Mock(data=[])

        health_status = await mock_supabase_service.health_check()

        assert health_status['status'] in ['healthy', 'degraded']
        assert 'database' in health_status
        assert 'api' in health_status
        assert 'stats' in health_status

    @pytest.mark.asyncio
    async def test_execute_query(self, mock_supabase_service):
        """Test query execution."""
        # Mock database connection
        mock_conn = AsyncMock()
        mock_conn.fetch.return_value = [{'id': 1, 'name': 'test'}]
        mock_supabase_service._db_pool.acquire.return_value.__aenter__.return_value = mock_conn

        result = await mock_supabase_service.execute_query(
            "SELECT * FROM test_table",
            fetch_type='all'
        )

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]['id'] == 1


# ==================================================================================
# AUTHENTICATION SERVICE TESTS
# ==================================================================================

class TestAuthService:
    """Test cases for authentication service."""

    @pytest.mark.asyncio
    async def test_user_registration(self, mock_auth_service, sample_user_profile, sample_auth_tokens):
        """Test user registration."""
        # Mock Supabase auth response
        mock_response = Mock()
        mock_response.user = Mock()
        mock_response.user.id = sample_user_profile.id
        mock_response.user.email = sample_user_profile.email
        mock_response.user.email_confirmed_at = datetime.utcnow()

        mock_auth_service.supabase_service.service_client.auth.sign_up.return_value = mock_response
        mock_auth_service.supabase_service.service_client.table.return_value.insert.return_value.execute.return_value.data = [sample_user_profile.to_dict()]

        with patch.object(mock_auth_service, '_generate_tokens', return_value=sample_auth_tokens):
            user_profile, tokens = await mock_auth_service.register_user(
                email="<EMAIL>",
                password="test_password_123",
                username="testuser",
                full_name="Test User"
            )

            assert user_profile.email == "<EMAIL>"
            assert tokens.access_token == "test_access_token"

    @pytest.mark.asyncio
    async def test_user_login(self, mock_auth_service, sample_user_profile, sample_auth_tokens):
        """Test user login."""
        # Mock Supabase auth response
        mock_response = Mock()
        mock_response.user = Mock()
        mock_response.user.id = sample_user_profile.id
        mock_response.user.email = sample_user_profile.email
        mock_response.session = Mock()

        mock_auth_service.supabase_service.service_client.auth.sign_in_with_password.return_value = mock_response

        with patch.object(mock_auth_service, '_get_user_profile', return_value=sample_user_profile), \
             patch.object(mock_auth_service, '_generate_tokens', return_value=sample_auth_tokens), \
             patch.object(mock_auth_service, '_update_last_login'):

            user_profile, tokens = await mock_auth_service.login_user(
                email="<EMAIL>",
                password="test_password_123"
            )

            assert user_profile.email == "<EMAIL>"
            assert tokens.access_token == "test_access_token"

    @pytest.mark.asyncio
    async def test_token_verification(self, mock_auth_service):
        """Test JWT token verification."""
        # Mock valid token
        import jwt
        payload = {
            'sub': 'test_user_id',
            'email': '<EMAIL>',
            'role': 'user',
            'type': 'access',
            'exp': datetime.utcnow() + timedelta(hours=1)
        }

        token = jwt.encode(payload, mock_auth_service.config.jwt_secret, algorithm='HS256')

        decoded_payload = await mock_auth_service.verify_token(token)

        assert decoded_payload['sub'] == 'test_user_id'
        assert decoded_payload['email'] == '<EMAIL>'
        assert decoded_payload['type'] == 'access'

    def test_password_validation(self, mock_auth_service):
        """Test password validation."""
        # Valid password
        mock_auth_service._validate_password("ValidPass123")

        # Invalid passwords
        with pytest.raises(Exception):
            mock_auth_service._validate_password("short")  # Too short

        with pytest.raises(Exception):
            mock_auth_service._validate_password("nouppercase123")  # No uppercase

        with pytest.raises(Exception):
            mock_auth_service._validate_password("NOLOWERCASE123")  # No lowercase

        with pytest.raises(Exception):
            mock_auth_service._validate_password("NoNumbers")  # No digits


# ==================================================================================
# VECTOR SERVICE TESTS
# ==================================================================================

class TestVectorService:
    """Test cases for vector storage service."""

    @pytest.mark.asyncio
    async def test_embedding_generation(self, mock_vector_service):
        """Test embedding generation."""
        embedding = await mock_vector_service.generate_embedding("Test text")

        assert isinstance(embedding, list)
        assert len(embedding) == 384
        assert all(isinstance(x, float) for x in embedding)

    @pytest.mark.asyncio
    async def test_document_processing(self, mock_vector_service):
        """Test document processing and chunking."""
        # Mock database operations
        mock_vector_service.supabase_service.execute_query.return_value = [{'count': 1}]

        # Mock chunk storage
        chunk_ids = ['chunk_1', 'chunk_2', 'chunk_3']
        with patch.object(mock_vector_service, '_store_document_chunk', side_effect=chunk_ids):

            result_chunk_ids = await mock_vector_service.process_document(
                document_id="test_doc_id",
                content="This is a test document with enough content to be split into multiple chunks. " * 50,
                user_id="test_user_id",
                metadata={"file_type": "text"}
            )

            assert len(result_chunk_ids) > 0
            assert all(isinstance(chunk_id, str) for chunk_id in result_chunk_ids)

    @pytest.mark.asyncio
    async def test_vector_search(self, mock_vector_service, sample_search_results):
        """Test vector similarity search."""
        # Mock search results
        mock_vector_service.supabase_service.execute_query.return_value = [
            {
                'id': 'result_1',
                'document_id': 'doc_1',
                'content': 'Test content',
                'similarity': 0.85,
                'metadata': {}
            }
        ]

        results = await mock_vector_service.search_documents(
            query="test query",
            user_id="test_user_id",
            similarity_threshold=0.7,
            max_results=5
        )

        assert isinstance(results, list)
        assert len(results) > 0
        assert all(hasattr(result, 'similarity') for result in results)

    @pytest.mark.asyncio
    async def test_user_stats(self, mock_vector_service):
        """Test user statistics retrieval."""
        # Mock stats query
        mock_vector_service.supabase_service.execute_query.return_value = {
            'total_documents': 10,
            'total_sections': 50,
            'total_embeddings': 45,
            'avg_sections_per_doc': 5.0
        }

        stats = await mock_vector_service.get_user_stats("test_user_id")

        assert stats.total_documents == 10
        assert stats.total_chunks == 50
        assert stats.total_embeddings == 45
        assert stats.avg_chunks_per_document == 5.0


# ==================================================================================
# RAG SERVICE TESTS
# ==================================================================================

class TestRAGService:
    """Test cases for RAG service."""

    @pytest.mark.asyncio
    async def test_context_building(self, mock_rag_service, sample_search_results):
        """Test context building from search results."""
        # Mock vector search
        mock_rag_service.vector_service.hybrid_search.return_value = sample_search_results

        context = await mock_rag_service._build_context(
            query="test query",
            user_id="test_user_id",
            project_id=None
        )

        assert len(context.documents) > 0
        assert context.total_tokens > 0
        assert context.query == "test query"

    @pytest.mark.asyncio
    async def test_rag_generation(self, mock_rag_service, sample_user_profile, sample_search_results):
        """Test RAG code generation."""
        # Mock dependencies
        mock_rag_service.vector_service.hybrid_search.return_value = sample_search_results

        with patch.object(mock_rag_service, '_call_llm', return_value=("Generated code", 100)):
            response = await mock_rag_service.generate_code_with_context(
                query="Generate a Python function",
                user_profile=sample_user_profile,
                project_id=None
            )

            assert isinstance(response, RAGResponse)
            assert response.response == "Generated code"
            assert response.context_tokens > 0
            assert response.response_tokens == 100

    @pytest.mark.asyncio
    async def test_prompt_building(self, mock_rag_service):
        """Test prompt template building."""
        from src.services.rag_service import RAGContext, ContextDocument, ContextType

        # Create mock context
        context_docs = [
            ContextDocument(
                id="doc_1",
                content="def hello_world(): print('Hello')",
                similarity=0.8,
                metadata={},
                context_type=ContextType.CODE
            )
        ]

        context = RAGContext(
            documents=context_docs,
            total_tokens=100,
            context_types=[ContextType.CODE],
            query="Generate a function"
        )

        prompt = mock_rag_service._build_prompt("Generate a function", context)

        assert isinstance(prompt, str)
        assert "Generate a function" in prompt
        assert "def hello_world()" in prompt

    def test_content_overlap_calculation(self, mock_rag_service):
        """Test content overlap calculation for deduplication."""
        content1 = "This is a test document with some content"
        content2 = "This is a test document with different content"

        overlap = mock_rag_service._calculate_content_overlap(content1, content2)

        assert 0.0 <= overlap <= 1.0
        assert overlap > 0  # Should have some overlap


# ==================================================================================
# API ENDPOINT TESTS
# ==================================================================================

class TestSupabaseAPI:
    """Test cases for Supabase API endpoints."""

    def test_health_endpoint(self, test_client):
        """Test health check endpoint."""
        response = test_client.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "ok"

    def test_root_endpoint(self, test_client):
        """Test root endpoint with Supabase features."""
        response = test_client.get("/")
        data = response.json()

        assert response.status_code == 200
        assert "message" in data
        assert "version" in data
        assert "features" in data
        assert "endpoints" in data

    @pytest.mark.asyncio
    async def test_protected_endpoint_without_auth(self, async_test_client):
        """Test protected endpoint without authentication."""
        # This test assumes USE_SUPABASE is enabled
        with patch.dict(os.environ, {"USE_SUPABASE": "true"}):
            response = await async_test_client.get("/api/v1/supabase/user/profile")
            assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_user_registration_endpoint(self, async_test_client):
        """Test user registration endpoint."""
        with patch.dict(os.environ, {"USE_SUPABASE": "true"}):
            registration_data = {
                "email": "<EMAIL>",
                "password": "TestPassword123",
                "username": "testuser",
                "full_name": "Test User"
            }

            # Mock the auth service
            with patch('src.services.auth_service.get_auth_service') as mock_get_auth:
                mock_auth_service = AsyncMock()
                mock_get_auth.return_value = mock_auth_service

                # Mock successful registration
                mock_user_profile = Mock()
                mock_user_profile.to_dict.return_value = {
                    "id": "test_id",
                    "email": "<EMAIL>",
                    "username": "testuser"
                }
                mock_tokens = Mock()
                mock_tokens.access_token = "test_token"
                mock_tokens.refresh_token = "test_refresh"
                mock_tokens.token_type = "bearer"
                mock_tokens.expires_in = 1800

                mock_auth_service.register_user.return_value = (mock_user_profile, mock_tokens)

                response = await async_test_client.post(
                    "/api/v1/supabase/auth/register",
                    json=registration_data
                )

                # Note: This might fail due to dependency injection in real testing
                # In a full test environment, you'd need proper mocking of FastAPI dependencies


# ==================================================================================
# INTEGRATION TESTS
# ==================================================================================

class TestIntegration:
    """Integration tests for complete workflows."""

    @pytest.mark.asyncio
    async def test_complete_rag_workflow(self, mock_supabase_service, mock_vector_service, mock_rag_service, sample_user_profile):
        """Test complete RAG workflow from document processing to generation."""
        # 1. Process a document
        chunk_ids = ['chunk_1', 'chunk_2']
        with patch.object(mock_vector_service, 'process_document', return_value=chunk_ids):
            result = await mock_vector_service.process_document(
                document_id="test_doc",
                content="This is test content for RAG workflow testing.",
                user_id=sample_user_profile.id
            )
            assert len(result) == 2

        # 2. Search for relevant content
        search_results = [
            SearchResult(
                id="chunk_1",
                document_id="test_doc",
                content="This is test content",
                similarity=0.85,
                metadata={}
            )
        ]
        with patch.object(mock_vector_service, 'hybrid_search', return_value=search_results):
            results = await mock_vector_service.hybrid_search(
                query="test query",
                user_id=sample_user_profile.id
            )
            assert len(results) == 1

        # 3. Generate response using RAG
        with patch.object(mock_rag_service, 'generate_code_with_context') as mock_generate:
            mock_generate.return_value = RAGResponse(
                response="Generated code based on context",
                context_used=["chunk_1"],
                context_tokens=50,
                response_tokens=25,
                similarity_scores=[0.85],
                llm_provider="ollama",
                processing_time_ms=100.0,
                query="test query"
            )

            response = await mock_rag_service.generate_code_with_context(
                query="test query",
                user_profile=sample_user_profile
            )

            assert response.response == "Generated code based on context"
            assert len(response.context_used) == 1


# ==================================================================================
# PERFORMANCE TESTS
# ==================================================================================

class TestPerformance:
    """Performance tests for critical operations."""

    @pytest.mark.asyncio
    async def test_embedding_generation_performance(self, mock_vector_service):
        """Test embedding generation performance."""
        import time

        texts = ["Test text"] * 10
        start_time = time.time()

        embeddings = []
        for text in texts:
            embedding = await mock_vector_service.generate_embedding(text)
            embeddings.append(embedding)

        end_time = time.time()

        assert len(embeddings) == 10
        assert (end_time - start_time) < 1.0  # Should complete in under 1 second with mocking

    @pytest.mark.asyncio
    async def test_search_performance(self, mock_vector_service):
        """Test search performance with multiple queries."""
        import time

        # Mock search results
        mock_vector_service.supabase_service.execute_query.return_value = [
            {'id': f'result_{i}', 'document_id': f'doc_{i}', 'content': f'Content {i}', 'similarity': 0.8, 'metadata': {}}
            for i in range(5)
        ]

        queries = [f"test query {i}" for i in range(5)]
        start_time = time.time()

        results = []
        for query in queries:
            result = await mock_vector_service.search_documents(
                query=query,
                user_id="test_user_id"
            )
            results.append(result)

        end_time = time.time()

        assert len(results) == 5
        assert (end_time - start_time) < 2.0  # Should complete in under 2 seconds


# ==================================================================================
# RUN TESTS
# ==================================================================================

if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([
        __file__,
        "-v",  # Verbose output
        "-s",  # Don't capture output
        "--tb=short",  # Short traceback format
        "--asyncio-mode=auto"  # Auto detect asyncio tests
    ])