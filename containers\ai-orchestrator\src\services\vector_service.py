"""
Vector Storage Service for AI Coding Agent with Supabase.

This module provides comprehensive vector storage and retrieval functionality
with permission-aware search, embedding management, and RAG capabilities
using pgvector and Supabase Row Level Security.

Author: AI Coding Agent
Version: 1.0.0
"""

import asyncio
import hashlib
import json
import logging
import os
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

# External imports - delayed to avoid startup issues
try:
    import openai

    openai_available = True
except ImportError:
    openai = None
    openai_available = False

# Sentence transformers will be imported when needed
sentence_transformers_available = False
SentenceTransformer = None  # type: ignore

from src.services.supabase_service import SupabaseService


# Minimal no-op supabase shim used when a real SupabaseService isn't provided
class _NoOpSupabase:
    async def execute_query(self, *args, **kwargs):
        return []

    async def upsert_vector(self, *args, **kwargs):
        return {}

    async def search_vectors(self, *args, **kwargs):
        return []

    async def create_table_if_not_exists(self, *args, **kwargs):
        return True


# Configure logging
logger = logging.getLogger(__name__)


class EmbeddingProvider(str, Enum):
    """Supported embedding providers."""

    OPENAI = "openai"
    SENTENCE_TRANSFORMERS = "sentence_transformers"
    HUGGINGFACE = "huggingface"
    OLLAMA = "ollama"


class KnowledgeTier(str, Enum):
    """Knowledge tier for vector storage."""

    PUBLIC = "public"
    PRIVATE = "private"


@dataclass
class VectorConfig:
    """Configuration for vector storage service."""

    embedding_provider: EmbeddingProvider = EmbeddingProvider.SENTENCE_TRANSFORMERS
    embedding_model: str = "bge-large-en-v1.5"
    embedding_dimension: int = 1024
    chunk_size: int = 512
    chunk_overlap: int = 50
    similarity_threshold: float = 0.7
    max_search_results: int = 10
    enable_hybrid_search: bool = True
    cache_embeddings: bool = True

    @classmethod
    def from_env(cls) -> "VectorConfig":
        """Create configuration from centralized settings."""
        from src.core.config import settings

        return cls(
            embedding_provider=EmbeddingProvider(settings.EMBEDDING_PROVIDER.value),
            embedding_model=settings.EMBEDDING_MODEL,
            embedding_dimension=settings.EMBEDDING_DIMENSION,
            chunk_size=settings.CHUNK_SIZE,
            chunk_overlap=settings.CHUNK_OVERLAP,
            similarity_threshold=settings.SIMILARITY_THRESHOLD,
            max_search_results=settings.MAX_SEARCH_RESULTS,
            enable_hybrid_search=settings.ENABLE_HYBRID_SEARCH,
            cache_embeddings=settings.CACHE_EMBEDDINGS,
        )


@dataclass
class DocumentChunk:
    """Document chunk with metadata and knowledge tier support."""

    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    embedding: Optional[List[float]] = None
    chunk_index: int = 0
    knowledge_tier: KnowledgeTier = KnowledgeTier.PRIVATE
    user_id: Optional[str] = None
    project_id: Optional[str] = None

    def __post_init__(self):
        """Post-initialization processing."""
        if not self.metadata:
            self.metadata = {}

        # Add content hash for deduplication
        self.metadata["content_hash"] = hashlib.md5(self.content.encode("utf-8")).hexdigest()

        # Add tier and ownership info to metadata
        self.metadata["knowledge_tier"] = self.knowledge_tier.value
        if self.user_id:
            self.metadata["user_id"] = self.user_id
        if self.project_id:
            self.metadata["project_id"] = self.project_id


@dataclass
class SearchResult:
    """Vector search result."""

    id: str
    document_id: str
    content: str
    similarity: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    document_name: Optional[str] = None
    file_type: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "document_id": self.document_id,
            "content": self.content,
            "similarity": self.similarity,
            "metadata": self.metadata,
            "document_name": self.document_name,
            "file_type": self.file_type,
        }


@dataclass
class DocumentStats:
    """Document processing statistics."""

    total_documents: int = 0
    total_chunks: int = 0
    total_embeddings: int = 0
    avg_chunks_per_document: float = 0.0
    storage_size_mb: float = 0.0
    last_updated: Optional[datetime] = None


class VectorStorageError(Exception):
    """Base exception for vector storage errors."""

    pass


class EmbeddingError(VectorStorageError):
    """Embedding generation errors."""

    pass


class SearchError(VectorStorageError):
    """Search operation errors."""

    pass


class VectorStorageService:
    """
    Comprehensive vector storage service providing:
    - Document chunking and embedding generation
    - Permission-aware vector search
    - Hybrid search (vector + full-text)
    - Document management with metadata
    - Performance optimization and caching
    """

    def __init__(
        self,
        supabase_service: Optional[SupabaseService] = None,
        config: Optional[VectorConfig] = None,
    ):
        """
        Initialize vector storage service.

        Args:
            supabase_service: Supabase service instance.
            config: Vector storage configuration.
        """
        # Allow creating the service without a SupabaseService for tests or degraded mode
        self.supabase_service = supabase_service or _NoOpSupabase()
        self.config = config or VectorConfig.from_env()
        self._embedding_model = None
        self._embedding_cache: Dict[str, List[float]] = {}
        self._initialization_failed: bool = (
            False  # Flag set when embedding model initialization failed at startup
        )

        logger.info(f"Vector storage service initialized with {self.config.embedding_provider}")

    async def initialize(self) -> None:
        """Initialize embedding model and dependencies."""
        try:
            await self._initialize_embedding_model()
            logger.info("Vector storage service initialization completed")
        except Exception as e:
            # Do not fail the whole application if an optional external embedding
            # provider (like Ollama) is unreachable during startup. Log and
            # continue in degraded mode — embeddings will raise if invoked.
            logger.error(f"Failed to initialize vector storage service: {str(e)}")
            logger.warning(
                "Continuing in degraded mode: embedding provider unavailable. "
                "Embedding generation requests will fail until provider is reachable."
            )
            self._initialization_failed = True
            # keep running without raising to allow the HTTP server to start

    async def _initialize_embedding_model(self) -> None:
        """Initialize the embedding model based on configuration."""
        global SentenceTransformer, sentence_transformers_available

        if self.config.embedding_provider == EmbeddingProvider.SENTENCE_TRANSFORMERS:
            if not sentence_transformers_available:
                try:
                    from sentence_transformers import SentenceTransformer

                    sentence_transformers_available = True
                except (ImportError, AttributeError) as e:
                    logger.warning(
                        f"SentenceTransformers not available due to compatibility issue: {str(e)}. "
                        "Falling back to OpenAI embeddings if available."
                    )
                    # Try to fall back to OpenAI if available
                    if openai_available:
                        logger.info("Falling back to OpenAI embeddings")
                        self.config.embedding_provider = EmbeddingProvider.OPENAI
                        await self._initialize_openai_embeddings()
                        return
                    else:
                        raise EmbeddingError(
                            f"SentenceTransformers failed to load: {str(e)}. "
                            "OpenAI not available. Please install compatible versions or configure OpenAI API."
                        ) from e

            # Ensure SentenceTransformer is available before using it
            if SentenceTransformer is None or not callable(SentenceTransformer):
                raise EmbeddingError("SentenceTransformer not properly imported")

            # Load model in thread pool to avoid blocking
            loop = asyncio.get_event_loop()

            def _load_model():
                # At this point SentenceTransformer should be properly imported
                if SentenceTransformer is None:
                    raise EmbeddingError("SentenceTransformer is None")
                return SentenceTransformer(self.config.embedding_model)

            self._embedding_model = await loop.run_in_executor(None, _load_model)

        elif self.config.embedding_provider == EmbeddingProvider.OPENAI:
            await self._initialize_openai_embeddings()

        elif self.config.embedding_provider == EmbeddingProvider.OLLAMA:
            await self._initialize_ollama_embeddings()

        else:
            raise EmbeddingError(
                f"Unsupported embedding provider: {self.config.embedding_provider}"
            )

    async def _initialize_openai_embeddings(self) -> None:
        """Initialize OpenAI embeddings (no model loading needed)."""
        if not openai_available:
            raise EmbeddingError("OpenAI not available. Install with: pip install openai")

        # Set OpenAI API key from environment
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise EmbeddingError("OPENAI_API_KEY environment variable not set")

        import openai

        openai.api_key = openai_api_key
        self._embedding_model = None  # OpenAI uses API calls, no local model
        logger.info("OpenAI embeddings initialized successfully")

    async def _initialize_ollama_embeddings(self) -> None:
        """Initialize Ollama embeddings."""
        from src.core.config import settings

        # Test connection to Ollama
        ollama_url = settings.OLLAMA_BASE_URL
        model_name = settings.OLLAMA_EMBEDDING_MODEL

        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.get(f"{ollama_url}/api/tags") as response:
                    if response.status != 200:
                        raise EmbeddingError(f"Ollama server not accessible at {ollama_url}")

                    data = await response.json()
                    available_models = [model["name"] for model in data.get("models", [])]

                    # Check if model exists (with or without tag)
                    model_found = any(
                        model.startswith(model_name.split(":")[0]) for model in available_models
                    )

                    if not model_found:
                        raise EmbeddingError(
                            f"Model '{model_name}' not found in Ollama. Available models: {available_models}"
                        )

            self._embedding_model = None  # Ollama uses API calls, no local model
            logger.info(f"Ollama embeddings initialized with model: {model_name}")

        except ImportError:
            raise EmbeddingError("aiohttp not available. Install with: pip install aiohttp")
        except Exception as e:
            raise EmbeddingError(f"Ollama initialization failed: {str(e)}")

    # ==================================================================================
    # DOCUMENT PROCESSING
    # ==================================================================================

    async def process_document(
        self,
        document_id: str,
        content: str,
        user_id: str,
        metadata: Optional[Dict[str, Any]] = None,
        overwrite: bool = False,
        file_hash: Optional[str] = None,
    ) -> List[str]:
        """
        Process document by chunking and generating embeddings.

        Args:
            document_id: Document identifier.
            content: Document content.
            user_id: User identifier for permissions.
            metadata: Additional metadata.
            overwrite: Whether to overwrite existing chunks.
            file_hash: SHA-256 hash of file content for incremental processing.

        Returns:
            List of created chunk IDs.

        Raises:
            VectorStorageError: If processing fails.
        """
        try:
            logger.info(f"Processing document {document_id} for user {user_id}")

            # Check if document exists and user has permission
            if not await self._verify_document_permission(document_id, user_id):
                raise VectorStorageError("Document not found or access denied")

            # Check if we need to process this document based on hash
            if file_hash and not overwrite:
                # Check if document has the same hash (unchanged)
                current_hash = await self._get_document_hash(document_id)
                if current_hash == file_hash:
                    logger.info(
                        f"Document {document_id} unchanged (hash: {file_hash}), skipping processing"
                    )
                    return []  # Return empty list to indicate no processing needed

            # Remove existing chunks if overwriting or hash changed
            if overwrite or (file_hash and current_hash != file_hash):
                await self._remove_document_chunks(document_id, user_id)

            # Chunk the document
            chunks = self._chunk_document(content, metadata or {})

            # Generate embeddings and store chunks
            chunk_ids = []
            for i, chunk in enumerate(chunks):
                try:
                    # Generate embedding
                    embedding = await self.generate_embedding(chunk.content)

                    # Store chunk with embedding
                    chunk_id = await self._store_document_chunk(
                        document_id=document_id,
                        content=chunk.content,
                        embedding=embedding,
                        section_index=i,
                        metadata=chunk.metadata,
                        user_id=user_id,
                    )

                    chunk_ids.append(chunk_id)

                except Exception as e:
                    logger.error(f"Failed to process chunk {i}: {str(e)}")
                    # Continue with other chunks
                    continue

            # Update document hash if provided
            if file_hash:
                await self._update_document_hash(document_id, file_hash)

            logger.info(f"Processed {len(chunk_ids)} chunks for document {document_id}")
            return chunk_ids

        except Exception as e:
            logger.error(f"Document processing failed: {str(e)}")
            raise VectorStorageError(f"Document processing failed: {str(e)}") from e

    def _chunk_document(self, content: str, metadata: Dict[str, Any]) -> List[DocumentChunk]:
        """Split document into chunks for processing."""
        chunks = []
        chunk_size = self.config.chunk_size
        chunk_overlap = self.config.chunk_overlap

        # Simple text chunking (can be enhanced with more sophisticated methods)
        words = content.split()

        for i in range(0, len(words), chunk_size - chunk_overlap):
            chunk_words = words[i : i + chunk_size]
            chunk_content = " ".join(chunk_words)

            chunk_metadata = {
                **metadata,
                "chunk_index": len(chunks),
                "word_count": len(chunk_words),
                "start_word": i,
                "end_word": min(i + chunk_size, len(words)),
            }

            chunks.append(
                DocumentChunk(
                    content=chunk_content, metadata=chunk_metadata, chunk_index=len(chunks)
                )
            )

        return chunks

    # ==================================================================================
    # EMBEDDING GENERATION
    # ==================================================================================

    async def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for text.

        Args:
            text: Text to embed.

        Returns:
            Embedding vector.

        Raises:
            EmbeddingError: If embedding generation fails.
        """
        try:
            # If initialization failed earlier, fail fast with a clear message
            if self._initialization_failed:
                raise EmbeddingError(
                    "Embedding provider unavailable (initialization failed at startup)"
                )

            # Check cache first
            if self.config.cache_embeddings:
                cache_key = hashlib.md5(text.encode("utf-8")).hexdigest()
                if cache_key in self._embedding_cache:
                    return self._embedding_cache[cache_key]

            # Generate embedding based on provider
            if self.config.embedding_provider == EmbeddingProvider.SENTENCE_TRANSFORMERS:
                embedding = await self._generate_sentence_transformer_embedding(text)
            elif self.config.embedding_provider == EmbeddingProvider.OPENAI:
                embedding = await self._generate_openai_embedding(text)
            elif self.config.embedding_provider == EmbeddingProvider.OLLAMA:
                embedding = await self._generate_ollama_embedding(text)
            else:
                raise EmbeddingError(f"Unsupported provider: {self.config.embedding_provider}")

            # Cache the embedding
            if self.config.cache_embeddings:
                self._embedding_cache[cache_key] = embedding

            return embedding

        except Exception as e:
            logger.error(f"Embedding generation failed: {str(e)}")
            raise EmbeddingError(f"Embedding generation failed: {str(e)}") from e

    async def _generate_sentence_transformer_embedding(self, text: str) -> List[float]:
        """Generate embedding using Sentence Transformers."""
        if not self._embedding_model:
            raise EmbeddingError("Sentence Transformer model not initialized")

        loop = asyncio.get_event_loop()
        embedding = await loop.run_in_executor(
            None, lambda: self._embedding_model.encode(text).tolist()
        )

        return embedding

    async def _generate_openai_embedding(self, text: str) -> List[float]:
        """Generate embedding using OpenAI API."""
        try:
            # Run OpenAI API call in executor to avoid blocking
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: openai.embeddings.create(model=self.config.embedding_model, input=text),
            )
            return response.data[0].embedding
        except Exception as e:
            raise EmbeddingError(f"OpenAI embedding failed: {str(e)}") from e

    async def _generate_ollama_embedding(self, text: str) -> List[float]:
        """Generate embedding using Ollama API."""
        from src.core.config import settings

        try:
            import aiohttp

            ollama_url = settings.OLLAMA_BASE_URL
            model_name = settings.OLLAMA_EMBEDDING_MODEL

            async with aiohttp.ClientSession() as session:
                payload = {"model": model_name, "input": text}

                async with session.post(
                    f"{ollama_url}/api/embeddings",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=settings.OLLAMA_REQUEST_TIMEOUT),
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise EmbeddingError(f"Ollama API error: {response.status} - {error_text}")

                    data = await response.json()
                    embedding = data.get("embedding", [])

                    if not embedding:
                        raise EmbeddingError("No embedding returned from Ollama")

                    return embedding

        except ImportError:
            raise EmbeddingError("aiohttp not available. Install with: pip install aiohttp")
        except Exception as e:
            raise EmbeddingError(f"Ollama embedding failed: {str(e)}") from e

    # ==================================================================================
    # VECTOR SEARCH
    # ==================================================================================

    async def search_documents(
        self,
        query: str,
        user_id: str,
        project_id: Optional[str] = None,
        similarity_threshold: Optional[float] = None,
        max_results: Optional[int] = None,
        include_metadata: bool = True,
    ) -> List[SearchResult]:
        """
        Search documents using vector similarity.

        Args:
            query: Search query.
            user_id: User identifier for permissions.
            project_id: Optional project filter.
            similarity_threshold: Minimum similarity score.
            max_results: Maximum number of results.
            include_metadata: Whether to include metadata.

        Returns:
            List of search results.

        Raises:
            SearchError: If search fails.
        """
        try:
            # Generate query embedding
            query_embedding = await self.generate_embedding(query)

            # Set defaults
            threshold = similarity_threshold or self.config.similarity_threshold
            limit = max_results or self.config.max_search_results

            # Perform search
            if project_id:
                results = await self._search_by_project(
                    query_embedding, project_id, threshold, limit, user_id
                )
            else:
                results = await self._search_all_documents(
                    query_embedding, threshold, limit, user_id
                )

            # Convert to SearchResult objects
            search_results = []
            for result in results:
                search_result = SearchResult(
                    id=result["id"],
                    document_id=result["document_id"],
                    content=result["content"],
                    similarity=result["similarity"],
                    metadata=result.get("metadata", {}) if include_metadata else {},
                    document_name=result.get("document_name"),
                    file_type=result.get("file_type"),
                )
                search_results.append(search_result)

            logger.info(f"Vector search returned {len(search_results)} results")
            return search_results

        except Exception as e:
            logger.error(f"Vector search failed: {str(e)}")
            raise SearchError(f"Vector search failed: {str(e)}") from e

    async def hybrid_search(
        self,
        query: str,
        user_id: str,
        project_id: Optional[str] = None,
        vector_weight: float = 0.7,
        text_weight: float = 0.3,
        similarity_threshold: Optional[float] = None,
        max_results: Optional[int] = None,
    ) -> List[SearchResult]:
        """
        Perform hybrid search combining vector and full-text search.

        Args:
            query: Search query.
            user_id: User identifier for permissions.
            project_id: Optional project filter.
            vector_weight: Weight for vector similarity.
            text_weight: Weight for text search.
            similarity_threshold: Minimum similarity score.
            max_results: Maximum number of results.

        Returns:
            List of search results.
        """
        try:
            if not self.config.enable_hybrid_search:
                return await self.search_documents(
                    query, user_id, project_id, similarity_threshold, max_results
                )

            # Generate query embedding
            query_embedding = await self.generate_embedding(query)

            # Set defaults
            threshold = similarity_threshold or self.config.similarity_threshold
            limit = max_results or self.config.max_search_results

            # Perform hybrid search using database function
            results = await self._hybrid_search_database(
                query, query_embedding, threshold, vector_weight, text_weight, limit, user_id
            )

            # Convert to SearchResult objects
            search_results = []
            for result in results:
                search_result = SearchResult(
                    id=result["id"],
                    document_id=result["document_id"],
                    content=result["content"],
                    similarity=result["combined_score"],  # Use combined score as similarity
                    metadata=result.get("metadata", {}),
                )
                search_results.append(search_result)

            logger.info(f"Hybrid search returned {len(search_results)} results")
            return search_results

        except Exception as e:
            logger.error(f"Hybrid search failed: {str(e)}")
            raise SearchError(f"Hybrid search failed: {str(e)}") from e

    # ==================================================================================
    # DATABASE OPERATIONS
    # ==================================================================================

    async def _store_document_chunk(
        self,
        document_id: str,
        content: str,
        embedding: List[float],
        section_index: int,
        metadata: Dict[str, Any],
        user_id: str,
    ) -> str:
        """Store document chunk with embedding in database."""
        try:
            # Use the database function for RLS enforcement
            query = """
                SELECT add_document_section($1, $2, $3, $4, $5) as chunk_id
            """

            result = await self.supabase_service.execute_query(
                query,
                document_id,
                content,
                embedding,
                section_index,
                json.dumps(metadata),
                fetch_type="one",
            )

            if isinstance(result, dict) and "chunk_id" in result:
                return result["chunk_id"]
            else:
                raise VectorStorageError("Invalid response format from database")

        except Exception as e:
            logger.error(f"Failed to store document chunk: {str(e)}")
            raise VectorStorageError(f"Failed to store chunk: {str(e)}") from e

    async def _search_all_documents(
        self, query_embedding: List[float], threshold: float, limit: int, user_id: str
    ) -> List[Dict[str, Any]]:
        """Search all accessible documents."""
        query = """
            SELECT * FROM match_document_sections($1, $2, $3)
        """

        result = await self.supabase_service.execute_query(
            query, query_embedding, threshold, limit, fetch_type="all"
        )

        # Ensure we return a list
        if isinstance(result, list):
            return result
        elif isinstance(result, dict):
            return [result]
        else:
            return []

    async def _search_by_project(
        self,
        query_embedding: List[float],
        project_id: str,
        threshold: float,
        limit: int,
        user_id: str,
    ) -> List[Dict[str, Any]]:
        """Search documents within a specific project."""
        query = """
            SELECT * FROM match_document_sections_by_project($1, $2, $3, $4)
        """

        result = await self.supabase_service.execute_query(
            query, query_embedding, project_id, threshold, limit, fetch_type="all"
        )

        # Ensure we return a list
        if isinstance(result, list):
            return result
        elif isinstance(result, dict):
            return [result]
        else:
            return []

    async def _hybrid_search_database(
        self,
        query_text: str,
        query_embedding: List[float],
        threshold: float,
        vector_weight: float,
        text_weight: float,
        limit: int,
        user_id: str,
    ) -> List[Dict[str, Any]]:
        """Perform hybrid search using database function."""
        query = """
            SELECT * FROM hybrid_search_documents($1, $2, $3, $4, $5, $6)
        """

        result = await self.supabase_service.execute_query(
            query,
            query_text,
            query_embedding,
            threshold,
            vector_weight,
            text_weight,
            limit,
            fetch_type="all",
        )

        # Ensure we return a list
        if isinstance(result, list):
            return result
        elif isinstance(result, dict):
            return [result]
        else:
            return []

    async def _verify_document_permission(self, document_id: str, user_id: str) -> bool:
        """Verify user has permission to access document."""
        try:
            query = """
                SELECT COUNT(*) as count
                FROM documents d
                JOIN projects p ON d.project_id = p.id
                WHERE d.id = $1 AND p.owner_id = $2
            """

            result = await self.supabase_service.execute_query(
                query, document_id, user_id, fetch_type="one"
            )

            if isinstance(result, dict) and "count" in result:
                return result["count"] > 0
            else:
                return False

        except Exception:
            return False

    async def _remove_document_chunks(self, document_id: str, user_id: str) -> None:
        """Remove all chunks for a document."""
        try:
            query = """
                DELETE FROM document_sections
                WHERE document_id = $1
                AND document_id IN (
                    SELECT d.id FROM documents d
                    JOIN projects p ON d.project_id = p.id
                    WHERE p.owner_id = $2
                )
            """

            await self.supabase_service.execute_query(query, document_id, user_id, fetch_type="val")

        except Exception as e:
            logger.error(f"Failed to remove document chunks: {str(e)}")

    # ==================================================================================
    # STATISTICS AND MANAGEMENT
    # ==================================================================================

    async def get_user_stats(self, user_id: str) -> DocumentStats:
        """Get document statistics for user."""
        try:
            query = """
                SELECT * FROM get_user_document_stats($1)
            """

            result = await self.supabase_service.execute_query(query, user_id, fetch_type="one")

            if isinstance(result, dict):
                return DocumentStats(
                    total_documents=result.get("total_documents", 0),
                    total_chunks=result.get("total_sections", 0),
                    total_embeddings=result.get("total_embeddings", 0),
                    avg_chunks_per_document=float(result.get("avg_sections_per_doc", 0) or 0),
                    last_updated=datetime.utcnow(),
                )
            else:
                return DocumentStats()

        except Exception as e:
            logger.error(f"Failed to get user stats: {str(e)}")
            return DocumentStats()

    async def refresh_vector_index(self) -> bool:
        """Refresh vector index for better performance."""
        try:
            query = "SELECT refresh_vector_index_stats()"
            await self.supabase_service.execute_query(query, fetch_type="val")
            return True
        except Exception as e:
            logger.error(f"Failed to refresh vector index: {str(e)}")
            return False

    # ==================================================================================
    # KNOWLEDGE TIER OPERATIONS
    # ==================================================================================

    async def store_public_knowledge(
        self,
        collection_name: str,
        document_name: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Store public knowledge document accessible to all users.

        Args:
            collection_name: Knowledge collection name (e.g., 'public_docs', 'code_snippets')
            document_name: Document identifier
            content: Document content
            metadata: Additional metadata

        Returns:
            Document ID
        """
        try:
            # Create document chunk with public tier
            chunk = DocumentChunk(
                content=content,
                metadata={
                    **(metadata or {}),
                    "collection": collection_name,
                    "document_name": document_name,
                },
                knowledge_tier=KnowledgeTier.PUBLIC,
            )

            # Generate embedding
            embedding = await self.generate_embedding(content)
            chunk.embedding = embedding

            # Store in database with public tier
            query = """
                INSERT INTO document_embeddings (
                    document_id, content, embedding, chunk_index,
                    knowledge_tier, metadata, content_hash
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7
                ) RETURNING id
            """

            result = await self.supabase_service.execute_query(
                query,
                document_name,  # Use document_name as document_id for public knowledge
                content,
                embedding,
                0,  # Single chunk for now
                KnowledgeTier.PUBLIC.value,
                json.dumps(chunk.metadata),
                chunk.metadata["content_hash"],
                fetch_type="one",
            )

            if isinstance(result, dict) and "id" in result:
                logger.info(
                    f"Stored public knowledge: {document_name} in collection {collection_name}"
                )
                return result["id"]
            else:
                raise VectorStorageError("Failed to store public knowledge")

        except Exception as e:
            logger.error(f"Failed to store public knowledge: {str(e)}")
            raise VectorStorageError(f"Failed to store public knowledge: {str(e)}") from e

    async def search_knowledge_tiers(
        self,
        query: str,
        knowledge_tier: Optional[KnowledgeTier] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        limit: int = 10,
    ) -> List[SearchResult]:
        """
        Search across knowledge tiers with proper access control.

        Args:
            query: Search query
            knowledge_tier: Specific tier to search (None for both)
            user_id: User ID for private knowledge access
            project_id: Project ID for project-specific private knowledge
            limit: Maximum results

        Returns:
            List of search results
        """
        try:
            # Generate query embedding
            query_embedding = await self.generate_embedding(query)

            # Build query based on tier
            if knowledge_tier == KnowledgeTier.PUBLIC:
                # Search only public knowledge
                sql_query = """
                    SELECT
                        id,
                        document_id,
                        content,
                        (embedding <=> $1::vector) as distance,
                        1 - (embedding <=> $1::vector) as similarity,
                        metadata,
                        knowledge_tier
                    FROM document_embeddings
                    WHERE knowledge_tier = 'public'
                    ORDER BY embedding <=> $1::vector
                    LIMIT $2
                """
                params = [query_embedding, limit]

            elif knowledge_tier == KnowledgeTier.PRIVATE:
                # Search only private knowledge with RLS
                sql_query = """
                    SELECT
                        id,
                        document_id,
                        content,
                        (embedding <=> $1::vector) as distance,
                        1 - (embedding <=> $1::vector) as similarity,
                        metadata,
                        knowledge_tier
                    FROM document_embeddings
                    WHERE knowledge_tier = 'private'
                    AND (user_id = $2 OR project_id = $3)
                    ORDER BY embedding <=> $1::vector
                    LIMIT $4
                """
                params = [query_embedding, user_id, project_id, limit]

            else:
                # Search both tiers with proper access control
                sql_query = """
                    SELECT
                        id,
                        document_id,
                        content,
                        (embedding <=> $1::vector) as distance,
                        1 - (embedding <=> $1::vector) as similarity,
                        metadata,
                        knowledge_tier
                    FROM document_embeddings
                    WHERE (
                        knowledge_tier = 'public'
                        OR (knowledge_tier = 'private' AND (user_id = $2 OR project_id = $3))
                    )
                    ORDER BY embedding <=> $1::vector
                    LIMIT $4
                """
                params = [query_embedding, user_id, project_id, limit]

            # Execute search
            results = await self.supabase_service.execute_query(
                sql_query, *params, fetch_type="all"
            )

            # Convert to SearchResult objects
            search_results = []
            if isinstance(results, list):
                for result in results:
                    search_result = SearchResult(
                        id=result["id"],
                        document_id=result["document_id"],
                        content=result["content"],
                        similarity=float(result["similarity"]),
                        metadata=json.loads(result["metadata"]) if result["metadata"] else {},
                    )
                    search_results.append(search_result)

            logger.info(f"Knowledge tier search returned {len(search_results)} results")
            return search_results

        except Exception as e:
            logger.error(f"Knowledge tier search failed: {str(e)}")
            raise SearchError(f"Knowledge tier search failed: {str(e)}") from e

    async def get_knowledge_collections(
        self, knowledge_tier: Optional[KnowledgeTier] = None
    ) -> List[Dict[str, Any]]:
        """
        Get available knowledge collections.

        Args:
            knowledge_tier: Filter by specific tier

        Returns:
            List of collection information
        """
        try:
            if knowledge_tier:
                query = """
                    SELECT
                        DISTINCT metadata->>'collection' as collection_name,
                        knowledge_tier,
                        COUNT(*) as document_count
                    FROM document_embeddings
                    WHERE knowledge_tier = $1
                    AND metadata->>'collection' IS NOT NULL
                    GROUP BY metadata->>'collection', knowledge_tier
                    ORDER BY collection_name
                """
                params = [knowledge_tier.value]
            else:
                query = """
                    SELECT
                        DISTINCT metadata->>'collection' as collection_name,
                        knowledge_tier,
                        COUNT(*) as document_count
                    FROM document_embeddings
                    WHERE metadata->>'collection' IS NOT NULL
                    GROUP BY metadata->>'collection', knowledge_tier
                    ORDER BY knowledge_tier, collection_name
                """
                params = []

            results = await self.supabase_service.execute_query(query, *params, fetch_type="all")

            return results if isinstance(results, list) else []

        except Exception as e:
            logger.error(f"Failed to get knowledge collections: {str(e)}")
            return []

    async def migrate_chromadb_data(
        self, backup_path: str, collection_mapping: Dict[str, KnowledgeTier]
    ) -> Dict[str, int]:
        """
        Migrate data from ChromaDB backup to Supabase pgvector.

        Args:
            backup_path: Path to the ChromaDB backup directory.
            collection_mapping: Mapping of collection names to knowledge tiers

        Returns:
            Migration statistics
        """
        stats = {"total_migrated": 0, "public_documents": 0, "private_documents": 0, "errors": 0}

        try:
            from langchain_community.document_loaders import ChromaDBLoader

            logger.info(f"Starting migration from ChromaDB backup at {backup_path}")

            for collection_name, knowledge_tier in collection_mapping.items():
                try:
                    loader = ChromaDBLoader(path=backup_path, collection_name=collection_name)
                    documents = loader.load()

                    for doc in documents:
                        try:
                            await self.add_document(
                                collection_name=collection_name,
                                document=doc.page_content,
                                metadata=doc.metadata,
                                knowledge_tier=knowledge_tier,
                            )
                            stats["total_migrated"] += 1
                            if knowledge_tier == KnowledgeTier.PUBLIC:
                                stats["public_documents"] += 1
                            else:
                                stats["private_documents"] += 1
                        except Exception as e:
                            logger.error(
                                f"Failed to migrate document from collection {collection_name}: {e}"
                            )
                            stats["errors"] += 1
                except Exception as e:
                    logger.error(f"Failed to load collection {collection_name} from backup: {e}")
                    stats["errors"] += 1

            logger.info(f"ChromaDB migration completed with stats: {stats}")
            return stats

        except ImportError:
            logger.error(
                "LangChain or ChromaDBLoader not installed. Please install with: pip install langchain chromadb"
            )
            stats["errors"] += 1
            return stats
        except Exception as e:
            logger.error(f"ChromaDB migration failed: {str(e)}")
            stats["errors"] += 1
            return stats

    # ---------------------------
    # Compatibility adapter methods
    # ---------------------------
    # Tests expect several older method signatures; provide thin wrappers
    # that delegate to the new implementations to remain non-breaking.

    # Adapter: store_public_knowledge(document_id=..., document_name=..., ...)
    async def store_public_knowledge(
        self,
        *,
        document_id: str = None,
        document_name: str = None,
        content: str = None,
        user_id: str = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Compatibility wrapper for older tests calling store_public_knowledge.

        Accepts either document_id or document_name and delegates to process_document or _store_document_chunk as appropriate.
        """
        # If content is provided, create a transient document id
        if content is not None and document_id is None:
            document_id = (
                document_id or f"public-{hashlib.md5(content.encode('utf-8')).hexdigest()}"
            )

        if document_id is None:
            raise VectorStorageError("document_id or content must be provided")

        # Create a single chunk document and store via process_document
        return await self.process_document(
            document_id=document_id,
            content=content or "",
            user_id=user_id or "system",
            metadata=metadata or {},
            overwrite=False,
        )

    # Adapter: store_document_chunks(...) -> delegates to process_document per-document
    async def store_document_chunks(
        self,
        document_id: str,
        chunks: List[Dict[str, Any]],
        user_id: str,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Compatibility wrapper that accepts pre-chunked documents and stores them."""
        created_ids = []
        for i, c in enumerate(chunks):
            content = c.get("content") or c.get("text") or ""
            chunk_meta = c.get("metadata", {})
            # Use per-chunk storage helper
            chunk_id = await self._store_document_chunk(
                document_id=document_id,
                content=content,
                embedding=c.get("embedding"),
                section_index=i,
                metadata={**(metadata or {}), **chunk_meta},
                user_id=user_id,
            )
            created_ids.append(chunk_id)
        return created_ids

    # Adapter: search_knowledge_tiers -> maps to search_knowledge_tier (singular) or search_vectors
    async def search_knowledge_tiers(
        self,
        *,
        knowledge_tiers: List[str] = None,
        query: str,
        user_id: Optional[str] = None,
        top_k: Optional[int] = None,
        limit: Optional[int] = None,
    ):
        """Compatibility wrapper to search across multiple knowledge tiers."""
        # Use limit if provided, otherwise top_k
        search_limit = limit if limit is not None else top_k

        # Fallback for knowledge_tiers=None
        if knowledge_tiers is None:
            knowledge_tiers = [tier.value for tier in KnowledgeTier]

        results = []
        for tier_str in knowledge_tiers:
            try:
                tier = KnowledgeTier(tier_str)
                res = await self.search_documents(
                    query=query,
                    user_id=user_id,
                    max_results=search_limit or self.config.max_search_results,
                )
                results.extend(res)
            except ValueError:
                logger.warning(
                    f"Invalid knowledge tier '{tier_str}' provided to search_knowledge_tiers."
                )
                continue

        # Sort by similarity if attribute available
        try:
            results.sort(key=lambda r: getattr(r, "similarity", 0.0), reverse=True)
        except Exception:
            pass

        # Apply final limit if needed
        if search_limit:
            results = results[:search_limit]

        return results

    # Adapter: get_knowledge_collections(user_id=...)
    async def get_knowledge_collections(
        self, *, user_id: Optional[str] = None, knowledge_tier: Optional[str] = None
    ):
        """Return list of collections filtered by user_id and/or knowledge_tier."""
        try:
            # Base query selects distinct collections from metadata
            query = "SELECT DISTINCT metadata->>'collection' as name, knowledge_tier FROM document_embeddings WHERE metadata->>'collection' IS NOT NULL"
            params = []

            # Add knowledge_tier filter if provided
            if knowledge_tier:
                query += " AND knowledge_tier = %s"
                params.append(knowledge_tier)

            # Note: user_id filtering for collections is not directly supported by this simplified query.
            # This adapter prioritizes the knowledge_tier fix. A more complex query would be needed for user-specific collections.
            if user_id:
                logger.warning(
                    "get_knowledge_collections adapter does not currently support user_id filtering."
                )

            rows = await self.supabase_service.execute_query(query, *params)
            return rows
        except Exception as e:
            logger.error(f"Failed to get knowledge collections: {e}")
            return []

    async def _get_document_hash(self, document_id: str) -> Optional[str]:
        """Get the current hash for a document."""
        try:
            query = """
                SELECT file_hash FROM documents WHERE id = $1
            """
            result = await self.supabase_service.execute_query(query, document_id, fetch_type="one")
            return result.get("file_hash") if isinstance(result, dict) else None
        except Exception as e:
            logger.error(f"Failed to get document hash: {str(e)}")
            return None

    async def _update_document_hash(self, document_id: str, file_hash: str) -> None:
        """Update the hash for a document."""
        try:
            query = """
                UPDATE documents SET file_hash = $1 WHERE id = $2
            """
            await self.supabase_service.execute_query(
                query, file_hash, document_id, fetch_type="val"
            )
        except Exception as e:
            logger.error(f"Failed to update document hash: {str(e)}")


from fastapi import HTTPException, Request


async def get_vector_service(request: Request) -> "VectorStorageService":
    """
    Retrieves the singleton instance of the VectorStorageService from the
    application state. This ensures that the same service instance is used
    across all requests, preventing re-initialization.
    """
    vector_service = getattr(request.app.state, "vector_service", None)
    if not vector_service:
        logger.error("VectorStorageService not initialized or not found in app state.")
        raise HTTPException(
            status_code=503,
            detail="The Vector Storage Service is currently unavailable. Please try again later.",
        )
    return vector_service
