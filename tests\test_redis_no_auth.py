#!/usr/bin/env python3
"""
Test Redis connection without authentication.
This script tests the Redis configuration to ensure it's accessible without passwords.
"""

import redis
import sys

def test_redis_connection():
    """Test Redis connection with various configurations."""

    # Test configurations
    test_configs = [
        {
            "name": "Development (localhost:9379)",
            "host": "127.0.0.1",
            "port": 9379,
            "db": 0
        },
        {
            "name": "Container internal (redis:6379)",
            "host": "redis",
            "port": 6379,
            "db": 0
        }
    ]

    results = []

    for config in test_configs:
        print(f"\n=== Testing {config['name']} ===")

        try:
            # Create Redis client without password
            client = redis.Redis(
                host=config["host"],
                port=config["port"],
                db=config["db"],
                decode_responses=True
            )

            # Test ping
            pong = client.ping()
            print(f"✅ PING: {pong}")

            # Test set/get
            client.set("test_no_auth", "success")
            value = client.get("test_no_auth")
            print(f"✅ SET/GET: {value}")

            # Test delete
            client.delete("test_no_auth")
            print("✅ DELETE: success")

            # Test info
            info = client.info("server")
            redis_version = info.get("redis_version", "unknown")
            print(f"✅ Redis version: {redis_version}")

            results.append({"config": config["name"], "status": "SUCCESS"})

        except redis.ConnectionError as e:
            print(f"❌ Connection failed: {e}")
            results.append({"config": config["name"], "status": "CONNECTION_FAILED", "error": str(e)})

        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({"config": config["name"], "status": "ERROR", "error": str(e)})

    # Summary
    print("\n" + "="*50)
    print("REDIS CONNECTION TEST SUMMARY")
    print("="*50)

    for result in results:
        status_icon = "✅" if result["status"] == "SUCCESS" else "❌"
        print(f"{status_icon} {result['config']}: {result['status']}")
        if "error" in result:
            print(f"   Error: {result['error']}")

    success_count = sum(1 for r in results if r["status"] == "SUCCESS")
    total_count = len(results)

    print(f"\nSuccessful connections: {success_count}/{total_count}")

    if success_count > 0:
        print("\n🎉 Redis is successfully configured without authentication!")
        return True
    else:
        print("\n⚠️  No successful Redis connections. Check configuration.")
        return False

if __name__ == "__main__":
    try:
        success = test_redis_connection()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error: {e}")
        sys.exit(1)
