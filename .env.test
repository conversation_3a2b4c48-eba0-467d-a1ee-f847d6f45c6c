# .env.test
ENVIRONMENT=testing
TEST_API_BASE_URL=http://127.0.0.1:9003

# Use an in-memory SQLite database for tests for speed and isolation.
# The +aiosqlite driver is required for async SQLAlchemy tests.
DATABASE_URL="sqlite+aiosqlite:///./test.db"
DATABASE_URL_TEST="sqlite+aiosqlite:///./test_async.db"

REDIS_URL=redis://localhost:6379/1 # Use a different DB for tests
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=test_anon_key
SUPABASE_SERVICE_KEY=test_service_key
SECRET_KEY="test_secret_key_for_testing_purposes_only"
