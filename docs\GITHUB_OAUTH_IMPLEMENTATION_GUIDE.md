# GitHub + Google OAuth Implementation Guide

## Overview

This guide provides complete implementation details for the multi-provider social login system, prioritizing GitHub for our developer audience while maintaining Google OAuth support.

## Core Principle: "Meet Developers Where They Are"

Our implementation prioritizes GitHub OAuth as the primary developer-focused authentication method, with Google as a secondary option for broader accessibility.

## Phase 1: GitHub OAuth Implementation

### 1. GitHub OAuth App Setup

#### Step 1: Create GitHub OAuth App
1. Go to GitHub Settings → Developer settings → OAuth Apps
2. Click "New OAuth App"
3. Fill in the application details:
   - **Application name**: `AI Coding Agent`
   - **Homepage URL**: `http://localhost:9001` (development) / `https://your-domain.com` (production)
   - **Application description**: `AI-powered coding assistant platform`
   - **Authorization callback URL**:
     - Development: `http://localhost:9001/api/auth/callback/github`
     - Production: `https://your-domain.com/api/auth/callback/github`

#### Step 2: Get Credentials
After creating the app, you'll receive:
- **Client ID**: Copy this value
- **Client Secret**: Generate and copy this value

### 2. Supabase Configuration

#### Enable GitHub Provider
1. Go to Supabase Dashboard → Authentication → Providers
2. Find "GitHub" in the provider list
3. Enable the GitHub provider
4. Add your GitHub OAuth credentials:
   - **Client ID**: Paste from GitHub OAuth App
   - **Client Secret**: Paste from GitHub OAuth App
5. Configure redirect URL: `https://your-project-ref.supabase.co/auth/v1/callback`
6. Save the configuration

### 3. Environment Variables

Add these to your `.env.local` file:

```bash
# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Google OAuth (existing)
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# NextAuth
NEXTAUTH_URL=http://localhost:9001
NEXTAUTH_SECRET=your-secure-random-secret
```

### 4. NextAuth Configuration

The NextAuth configuration in `/api/auth/[...nextauth]/route.ts` includes:

#### Key Features:
- **Runtime Environment Validation**: Ensures all required secrets are present in production
- **Secure Redirect Handling**: Prevents open redirect vulnerabilities
- **Multi-Provider Support**: Both GitHub and Google OAuth
- **Secure Cookies**: Production-ready cookie settings
- **Backend Integration**: Syncs with Supabase Auth backend

#### GitHub Provider Configuration:
```typescript
GitHubProvider({
  clientId: process.env.GITHUB_CLIENT_ID,
  clientSecret: process.env.GITHUB_CLIENT_SECRET,
  authorization: {
    params: {
      scope: "read:user user:email", // Request user profile and email
    },
  },
})
```

#### JWT & Session Callbacks:
- **JWT Callback**: Handles GitHub OAuth tokens and backend synchronization
- **Session Callback**: Provides GitHub user data to frontend sessions
- **Redirect Callback**: Prevents open redirect attacks

### 5. Frontend Components

#### SignInWithGitHubButton Component
- **Location**: `src/components/auth/SignInWithGitHubButton.tsx`
- **Features**:
  - GitHub branding with official icon
  - Loading states
  - Error handling
  - Customizable styling

#### SignInWithGoogleButton Component
- **Location**: `src/components/auth/SignInWithGoogleButton.tsx`
- **Features**:
  - Google branding with official colors
  - Consistent interface with GitHub button
  - Responsive design

#### Updated Login Page
- **Location**: `src/app/login/page.tsx`
- **Features**:
  - GitHub sign-in button (primary placement)
  - Google sign-in button (secondary)
  - Visual divider with "Or continue with email"
  - Existing email/password form (fallback)

### 6. Backend Integration Flow

1. **Frontend**: User clicks "Continue with GitHub"
2. **NextAuth**: Redirects to GitHub OAuth
3. **GitHub**: User authorizes the application
4. **NextAuth**: Receives OAuth tokens and user data
5. **Backend Sync**: NextAuth sends tokens to `/auth/github` endpoint
6. **Supabase**: Creates/updates user record with RLS
7. **Session**: User session includes both NextAuth and Supabase tokens

### 7. Security Features

#### Production Environment Validation
```typescript
if (process.env.NODE_ENV === "production") {
  requireEnv("NEXTAUTH_SECRET");
  requireEnv("NEXTAUTH_URL");
  requireEnv("GITHUB_CLIENT_ID");
  requireEnv("GITHUB_CLIENT_SECRET");
  requireEnv("GOOGLE_CLIENT_ID");
  requireEnv("GOOGLE_CLIENT_SECRET");
}
```

#### Secure Cookies
```typescript
cookies: process.env.NODE_ENV === "production"
  ? {
      sessionToken: {
        name: `__Secure-next-auth.session-token`,
        options: { httpOnly: true, sameSite: "lax", path: "/", secure: true },
      },
    }
  : {}
```

#### Redirect Protection
```typescript
async redirect({ url, baseUrl }) {
  if (url.startsWith("/")) return `${baseUrl}${url}`;
  else if (new URL(url).origin === baseUrl) return url;
  return baseUrl;
}
```

## Required Backend Endpoints

Your backend should implement these endpoints:

### `/auth/github` (POST)
```python
@app.post("/auth/github")
async def github_auth(
    access_token: str,
    user: dict,
    github_account: dict
):
    # Create/update user in Supabase
    # Return: { success: bool, user: dict, access_token: str }
```

### `/auth/google` (POST)
```python
@app.post("/auth/google")
async def google_auth(
    access_token: str,
    user: dict,
    google_account: dict
):
    # Create/update user in Supabase
    # Return: { success: bool, user: dict, access_token: str }
```

## File Structure

```
containers/user-portal/src/
├── app/
│   ├── api/auth/[...nextauth]/
│   │   └── route.ts                    # NextAuth configuration
│   └── login/
│       └── page.tsx                    # Updated login page
├── components/auth/
│   ├── SignInWithGitHubButton.tsx      # GitHub sign-in button
│   └── SignInWithGoogleButton.tsx      # Google sign-in button
├── types/
│   └── next-auth.d.ts                  # TypeScript augmentation
└── .env.example                        # Environment variables template
```

## Testing the Implementation

### Development Testing
1. Start the user-portal container
2. Navigate to `http://localhost:9001/login`
3. Click "Continue with GitHub"
4. Authorize the application
5. Verify redirect to dashboard
6. Check session data includes GitHub user information

### Production Checklist
- [ ] GitHub OAuth App configured with production URLs
- [ ] Supabase GitHub provider enabled
- [ ] All environment variables set
- [ ] HTTPS enabled for secure cookies
- [ ] Backend endpoints implemented
- [ ] Error monitoring configured

## Security Considerations

1. **Secrets Management**: Never commit OAuth secrets to version control
2. **HTTPS Required**: GitHub OAuth requires HTTPS in production
3. **Scope Limitation**: Only request necessary GitHub permissions
4. **Token Security**: Store tokens securely in backend
5. **Session Management**: Use secure cookie settings
6. **Error Handling**: Don't expose sensitive errors to users

## Developer Experience Benefits

1. **One-Click Authentication**: Developers can sign in with their GitHub account
2. **No Password Required**: Leverages existing GitHub trust relationship
3. **Profile Integration**: Access to GitHub profile information
4. **Repository Access**: Future integration with user's repositories
5. **Developer Community**: Aligns with developer workflow and tools

## Future Enhancements

1. **GitHub Repository Integration**: Connect user projects
2. **GitHub Actions Integration**: CI/CD pipeline integration
3. **Additional Providers**: GitLab, Bitbucket for broader developer coverage
4. **Team Management**: GitHub organization-based team features
5. **Repository Analytics**: Code analysis and insights

This implementation provides a robust, secure, and developer-focused authentication system that meets developers where they are while maintaining flexibility for other user types.
