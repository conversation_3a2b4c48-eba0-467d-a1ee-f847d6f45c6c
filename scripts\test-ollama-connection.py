#!/usr/bin/env python3
# Project: AI Coding Agent
# Purpose: Test Ollama connection from host

import asyncio
import aiohttp
import sys

async def test_ollama_connection():
    """Test connection to Ollama on localhost"""
    ollama_url = "http://localhost:11434"

    print(" Testing Ollama Connection...")
    print(f"   URL: {ollama_url}")

    try:
        async with aiohttp.ClientSession() as session:
            # Test basic connection
            print("\n1⃣  Testing basic connection...")
            async with session.get(f"{ollama_url}/api/tags") as response:
                if response.status == 200:
                    print("    Successfully connected to Ollama!")

                    data = await response.json()
                    models = data.get("models", [])
                    print(f"    Found {len(models)} models:")

                    if models:
                        for model in models:
                            name = model.get("name", "unknown")
                            size = model.get("size", 0)
                            size_mb = round(size / (1024*1024), 1) if size else 0
                            print(f"      - {name} ({size_mb} MB)")
                    else:
                        print("      No models found. You may need to pull some models.")
                        print("      Try: ollama pull llama3.2")

                    return True
                else:
                    print(f"    HTTP Error: {response.status}")
                    error_text = await response.text()
                    print(f"   Error details: {error_text}")
                    return False

    except aiohttp.ClientConnectorError:
        print("    Connection refused - Ollama is not running or not accessible")
        print("    Make sure Ollama is running on your host:")
        print("      - Windows/Mac: Open Ollama app")
        print("      - Linux: sudo systemctl start ollama")
        return False
    except Exception as e:
        print(f"    Unexpected error: {str(e)}")
        return False

async def test_model_pull():
    """Test pulling a small model"""
    ollama_url = "http://localhost:11434"
    model_name = "llama3.2:1b"  # Smallest Llama model

    print(f"\n2⃣  Testing model pull ({model_name})...")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{ollama_url}/api/pull",
                json={"name": model_name}
            ) as response:
                if response.status == 200:
                    print(f"    Successfully initiated pull for {model_name}")
                    print("   ⏳ This may take a few minutes for first-time downloads...")
                    return True
                else:
                    print(f"    Failed to pull model: HTTP {response.status}")
                    return False

    except Exception as e:
        print(f"    Error pulling model: {str(e)}")
        return False

async def test_generation():
    """Test text generation"""
    ollama_url = "http://localhost:11434"

    print("\n3⃣  Testing text generation...")

    # First, check what models are available
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{ollama_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    models = data.get("models", [])

                    if not models:
                        print("     No models available for testing generation")
                        return False

                    # Use the first available model
                    model_name = models[0].get("name", "")
                    print(f"   Using model: {model_name}")

                    # Test generation
                    payload = {
                        "model": model_name,
                        "messages": [
                            {"role": "user", "content": "Hello! Please respond with just 'OK' to confirm you're working."}
                        ],
                        "stream": False
                    }

                    async with session.post(
                        f"{ollama_url}/api/chat",
                        json=payload
                    ) as gen_response:
                        if gen_response.status == 200:
                            gen_data = await gen_response.json()
                            content = gen_data.get("message", {}).get("content", "")
                            print("    Generation successful!")
                            print(f"    Model response: {content}")
                            return True
                        else:
                            print(f"    Generation failed: HTTP {gen_response.status}")
                            return False
                else:
                    return False

    except Exception as e:
        print(f"    Error testing generation: {str(e)}")
        return False

async def main():
    print(" Ollama Connection Test for AI Coding Agent")
    print("=" * 50)

    # Test connection
    connection_ok = await test_ollama_connection()

    if not connection_ok:
        print("\n Connection test failed. Please check your Ollama setup.")
        print("\n Setup Instructions:")
        print("1. Download and install Ollama from https://ollama.ai")
        print("2. Start Ollama service")
        print("3. Pull at least one model: ollama pull llama3.2")
        print("4. Verify with: curl http://localhost:11434/api/tags")
        return 1

    # Test generation if models are available
    async with aiohttp.ClientSession() as session:
        async with session.get("http://localhost:11434/api/tags") as response:
            if response.status == 200:
                data = await response.json()
                if data.get("models"):
                    await test_generation()
                else:
                    print("\n  No models found for generation testing")
                    print("   Consider pulling a model: ollama pull llama3.2")

    print("\n Ollama setup verification complete!")
    print("   Your host Ollama is ready to use with the AI Coding Agent.")

    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))