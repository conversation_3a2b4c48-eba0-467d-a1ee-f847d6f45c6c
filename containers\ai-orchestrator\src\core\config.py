"""
Centralized Configuration Management for AI Coding Agent.

This module provides a single source of truth for all configuration values
using Pydantic settings with proper type validation, environment variable
parsing, and development/production environment support.

Author: AI Coding Agent
Version: 1.0.0
"""

import os
from enum import Enum
from pathlib import Path
from typing import List, Optional, Union

from pydantic import AnyHttpUrl, Field, PostgresDsn, RedisDsn, field_validator, model_validator
from pydantic_settings import BaseSettings


class Environment(str, Enum):
    """Application environment types."""

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class EmbeddingProvider(str, Enum):
    """Supported embedding providers."""

    SENTENCE_TRANSFORMERS = "sentence_transformers"
    OPENAI = "openai"
    HUGGINGFACE = "huggingface"
    OLLAMA = "ollama"


class LLMProvider(str, Enum):
    """Supported LLM providers."""

    OLLAMA = "ollama"
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"


def get_secret(secret_name: str) -> Optional[str]:
    """Read secret from file."""
    try:
        with open(f"/run/secrets/{secret_name}", "r") as f:
            return f.read().strip()
    except IOError:
        return None


class Settings(BaseSettings):
    """
    Centralized application settings.

    All configuration values are defined here with proper types,
    validation, and environment variable mapping.
    """

    # =====================================================================================
    # CORE APPLICATION SETTINGS
    # =====================================================================================

    APP_NAME: str = Field(default="AI Coding Agent Orchestrator", description="Application name")
    APP_VERSION: str = Field(default="1.0.0", description="Application version")
    ENVIRONMENT: Environment = Field(
        default=Environment.DEVELOPMENT, description="Application environment"
    )
    DEBUG: bool = Field(default=False, description="Debug mode flag")

    # API Configuration
    API_V1_PREFIX: str = Field(default="/api/v1", description="API version 1 prefix")
    DOCS_URL: str = Field(default="/api/v1/docs", description="API documentation URL")
    REDOC_URL: str = Field(default="/api/v1/redoc", description="ReDoc documentation URL")

    # CORS Configuration
    CORS_ORIGINS: Union[List[str], str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="Allowed CORS origins",
    )
    CORS_ALLOW_CREDENTIALS: bool = Field(default=True, description="Allow CORS credentials")
    CORS_ALLOW_METHODS: List[str] = Field(default=["*"], description="Allowed CORS methods")
    CORS_ALLOW_HEADERS: List[str] = Field(default=["*"], description="Allowed CORS headers")

    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def parse_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        """Parse CORS origins from comma-separated string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v

    # =====================================================================================
    # DATABASE CONFIGURATION
    # =====================================================================================

    # Allow both PostgreSQL and SQLite URLs for local/dev/test convenience.
    # Keep PostgresDsn import for compatibility and future stricter validation.
    DATABASE_URL: str = Field(..., description="Database URL (Postgres or SQLite for local tests)")
    DATABASE_POOL_SIZE: int = Field(default=20, description="Database connection pool size")
    DATABASE_POOL_OVERFLOW: int = Field(default=0, description="Database pool overflow")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, description="Database pool timeout in seconds")
    DATABASE_ECHO: bool = Field(default=False, description="Echo SQL queries (debug mode)")

    # =====================================================================================
    # REDIS CONFIGURATION
    # =====================================================================================

    REDIS_URL: RedisDsn = Field(..., description="Redis server URL for caching and sessions")
    REDIS_MAX_CONNECTIONS: int = Field(default=20, description="Redis connection pool size")
    REDIS_RETRY_ON_TIMEOUT: bool = Field(
        default=True, description="Retry Redis operations on timeout"
    )
    REDIS_SOCKET_KEEPALIVE: bool = Field(default=True, description="Enable Redis socket keepalive")
    REDIS_SOCKET_KEEPALIVE_OPTIONS: dict = Field(
        default_factory=lambda: {}, description="Redis socket keepalive options"
    )

    # =====================================================================================
    # SUPABASE CONFIGURATION
    # =====================================================================================

    USE_SUPABASE: bool = Field(default=False, description="Enable Supabase integration")
    SUPABASE_URL: Optional[AnyHttpUrl] = Field(default=None, description="Supabase project URL")
    SUPABASE_ANON_KEY: Optional[str] = Field(default=None, description="Supabase anonymous key")
    SUPABASE_SERVICE_KEY: Optional[str] = Field(
        default=None, description="Supabase service role key"
    )
    SUPABASE_DB_URL: Optional[PostgresDsn] = Field(
        default=None, description="Supabase database URL"
    )

    # Supabase Connection Pool Settings
    SUPABASE_MAX_CONNECTIONS: int = Field(default=20, description="Supabase max connections")
    SUPABASE_MIN_CONNECTIONS: int = Field(default=5, description="Supabase min connections")
    SUPABASE_CONNECTION_TIMEOUT: float = Field(
        default=30.0, description="Supabase connection timeout"
    )
    SUPABASE_COMMAND_TIMEOUT: float = Field(default=60.0, description="Supabase command timeout")
    SUPABASE_RETRY_ATTEMPTS: int = Field(default=3, description="Supabase retry attempts")
    SUPABASE_RETRY_DELAY: float = Field(default=1.0, description="Supabase retry delay")
    SUPABASE_ENABLE_SSL: bool = Field(default=False, description="Enable SSL for Supabase")

    @field_validator("SUPABASE_URL", "SUPABASE_ANON_KEY", "SUPABASE_SERVICE_KEY", "SUPABASE_DB_URL")
    @classmethod
    def validate_supabase_config(cls, v, info):
        """Validate Supabase configuration consistency."""
        return v

    # =====================================================================================
    # AUTHENTICATION & SECURITY
    # =====================================================================================

    JWT_SECRET_FILE: Optional[str] = Field(default=None, description="Path to JWT secret file")
    JWT_SECRET: Optional[str] = Field(default=None, description="JWT signing secret key")
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT signing algorithm")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30, description="Access token expiration in minutes"
    )
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(
        default=7, description="Refresh token expiration in days"
    )

    # Password Policy
    PASSWORD_MIN_LENGTH: int = Field(default=8, description="Minimum password length")
    REQUIRE_EMAIL_VERIFICATION: bool = Field(default=True, description="Require email verification")
    ALLOW_SIGNUP: bool = Field(default=True, description="Allow user registration")
    MAX_LOGIN_ATTEMPTS: int = Field(default=5, description="Maximum login attempts")
    LOGIN_ATTEMPT_WINDOW_MINUTES: int = Field(
        default=15, description="Login attempt window in minutes"
    )

    # OAuth Configuration
    GITHUB_CLIENT_ID: Optional[str] = Field(default=None, description="GitHub OAuth client ID")
    GITHUB_CLIENT_SECRET: Optional[str] = Field(
        default=None, description="GitHub OAuth client secret"
    )
    GITHUB_REDIRECT_URI: str = Field(
        default="http://localhost:8000/auth/github/callback",
        description="GitHub OAuth redirect URI",
    )

    # =====================================================================================
    # LLM PROVIDER CONFIGURATION
    # =====================================================================================

    # Ollama (Local LLM)
    OLLAMA_BASE_URL: AnyHttpUrl = Field(
        default="http://ollama:11434", description="Ollama server base URL"
    )
    OLLAMA_MODEL: str = Field(
        default="llama3.1:7b", description="Default Ollama model (7B for 4GB GPU)"
    )
    OLLAMA_TIMEOUT: int = Field(default=300, description="Ollama request timeout in seconds")

    # OpenRouter (Cloud LLM)
    OPENROUTER_API_KEY: Optional[str] = Field(
        default=get_secret("openrouter_api_key"), description="OpenRouter API key"
    )
    OPENROUTER_BASE_URL: AnyHttpUrl = Field(
        default="https://openrouter.ai/api/v1", description="OpenRouter API base URL"
    )
    OPENROUTER_MODEL: str = Field(
        default="anthropic/claude-3.5-sonnet", description="Default OpenRouter model"
    )

    # OpenAI
    OPENAI_API_KEY: Optional[str] = Field(
        default=get_secret("openai_api_key"), description="OpenAI API key"
    )
    OPENAI_BASE_URL: AnyHttpUrl = Field(
        default="https://api.openai.com/v1", description="OpenAI API base URL"
    )
    OPENAI_MODEL: str = Field(default="gpt-4", description="Default OpenAI model")

    # Anthropic
    ANTHROPIC_API_KEY: Optional[str] = Field(
        default=get_secret("anthropic_api_key"), description="Anthropic API key"
    )
    ANTHROPIC_BASE_URL: AnyHttpUrl = Field(
        default="https://api.anthropic.com", description="Anthropic API base URL"
    )
    ANTHROPIC_MODEL: str = Field(
        default="claude-3-5-sonnet-20241022", description="Default Anthropic model"
    )

    # LLM Configuration
    DEFAULT_LOCAL_PROVIDER: LLMProvider = Field(
        default=LLMProvider.OLLAMA, description="Default local LLM provider"
    )
    DEFAULT_CLOUD_PROVIDER: LLMProvider = Field(
        default=LLMProvider.OPENROUTER, description="Default cloud LLM provider"
    )
    ENABLE_CLOUD_FALLBACK: bool = Field(
        default=True, description="Enable fallback to cloud providers"
    )
    LLM_REQUEST_TIMEOUT: int = Field(default=300, description="LLM request timeout in seconds")
    LLM_MAX_RETRIES: int = Field(default=3, description="Maximum LLM request retries")

    # =====================================================================================
    # VECTOR & EMBEDDING CONFIGURATION
    # =====================================================================================

    EMBEDDING_PROVIDER: EmbeddingProvider = Field(
        default=EmbeddingProvider.OLLAMA, description="Embedding provider to use"
    )
    EMBEDDING_MODEL: str = Field(
        default="bge-large-en-v1.5",
        description="Embedding model name (BGE optimized for retrieval)",
    )
    EMBEDDING_DIMENSION: int = Field(default=1024, description="Embedding vector dimension")
    CHUNK_SIZE: int = Field(default=512, description="Document chunk size")
    CHUNK_OVERLAP: int = Field(default=50, description="Document chunk overlap")
    SIMILARITY_THRESHOLD: float = Field(default=0.7, description="Similarity search threshold")
    MAX_SEARCH_RESULTS: int = Field(default=10, description="Maximum search results")
    ENABLE_HYBRID_SEARCH: bool = Field(default=True, description="Enable hybrid search")
    CACHE_EMBEDDINGS: bool = Field(default=True, description="Cache embeddings")

    # Ollama Embedding Configuration
    OLLAMA_EMBEDDING_MODEL: str = Field(
        default="bge-large-en-v1.5",
        description="Ollama embedding model name (BGE optimized for retrieval)",
    )
    OLLAMA_REQUEST_TIMEOUT: int = Field(
        default=120, description="Ollama request timeout in seconds"
    )

    # =====================================================================================
    # MEMORY MANAGEMENT CONFIGURATION
    # =====================================================================================

    MEMORY_FILE_PATH: Path = Field(
        default=Path("/app/data/memories.json"), description="Path to memory storage file"
    )
    MEMORY_BACKUP_PATH: Path = Field(
        default=Path("/app/data/memories_backup.json"), description="Path to memory backup file"
    )
    MAX_MEMORY_ENTRIES: int = Field(default=10000, description="Maximum number of memory entries")
    MEMORY_CLEANUP_INTERVAL_HOURS: int = Field(
        default=24, description="Memory cleanup interval in hours"
    )
    MEMORY_RETENTION_DAYS: int = Field(default=30, description="Memory retention period in days")
    MEMORY_COMPRESSION_THRESHOLD: int = Field(
        default=1000, description="Threshold for memory compression"
    )
    MEMORY_SEARCH_SIMILARITY_THRESHOLD: float = Field(
        default=0.8, description="Memory search similarity threshold"
    )
    MEMORY_MAX_SEARCH_RESULTS: int = Field(default=50, description="Maximum memory search results")
    MEMORY_SEARCH_TIMEOUT: int = Field(default=30, description="Memory search timeout in seconds")
    ENABLE_LEARNING_LOOPS: bool = Field(default=True, description="Enable learning loops")
    LEARNING_UPDATE_INTERVAL_MINUTES: int = Field(
        default=60, description="Learning update interval in minutes"
    )
    LEARNING_CONFIDENCE_THRESHOLD: float = Field(
        default=0.7, description="Learning confidence threshold"
    )
    LEARNING_PATTERN_MIN_OCCURRENCES: int = Field(
        default=3, description="Minimum pattern occurrences for learning"
    )
    CODE_PATTERN_SIMILARITY_THRESHOLD: float = Field(
        default=0.85, description="Code pattern similarity threshold"
    )
    CODE_PATTERN_MAX_PATTERNS: int = Field(
        default=500, description="Maximum code patterns to store"
    )
    CODE_PATTERN_UPDATE_FREQUENCY: int = Field(
        default=100, description="Code pattern update frequency"
    )
    MEMORY_AUTO_SAVE: bool = Field(default=True, description="Enable automatic memory saving")
    MEMORY_SAVE_INTERVAL_SECONDS: int = Field(
        default=300, description="Memory save interval in seconds"
    )
    MEMORY_ENABLE_BACKUP: bool = Field(default=True, description="Enable memory backup")
    MEMORY_BACKUP_INTERVAL_HOURS: int = Field(
        default=6, description="Memory backup interval in hours"
    )

    # =====================================================================================
    # MEMORY MANAGEMENT CONFIGURATION
    # =====================================================================================

    # Memory Storage Configuration
    MEMORY_FILE_PATH: Path = Field(
        default=Path("/app/data/memories.json"), description="Path to memory storage file"
    )
    MEMORY_BACKUP_PATH: Path = Field(
        default=Path("/app/data/memories_backup.json"), description="Path to memory backup file"
    )

    # Memory Limits and Cleanup
    MAX_MEMORY_ENTRIES: int = Field(default=10000, description="Maximum number of memory entries")
    MEMORY_CLEANUP_INTERVAL_HOURS: int = Field(
        default=24, description="Memory cleanup interval in hours"
    )
    MEMORY_RETENTION_DAYS: int = Field(default=90, description="Memory retention period in days")
    MEMORY_COMPRESSION_THRESHOLD: int = Field(
        default=5000, description="Memory compression threshold"
    )

    # Memory Search Configuration
    MEMORY_SEARCH_SIMILARITY_THRESHOLD: float = Field(
        default=0.8, description="Memory search similarity threshold"
    )
    MEMORY_MAX_SEARCH_RESULTS: int = Field(default=20, description="Maximum memory search results")
    MEMORY_SEARCH_TIMEOUT: int = Field(default=30, description="Memory search timeout in seconds")

    # Learning Configuration
    ENABLE_LEARNING_LOOPS: bool = Field(default=True, description="Enable learning loops")
    LEARNING_UPDATE_INTERVAL_MINUTES: int = Field(
        default=60, description="Learning update interval in minutes"
    )
    LEARNING_CONFIDENCE_THRESHOLD: float = Field(
        default=0.7, description="Learning confidence threshold"
    )
    LEARNING_PATTERN_MIN_OCCURRENCES: int = Field(
        default=3, description="Minimum pattern occurrences for learning"
    )

    # Code Pattern Configuration
    CODE_PATTERN_SIMILARITY_THRESHOLD: float = Field(
        default=0.85, description="Code pattern similarity threshold"
    )
    CODE_PATTERN_MAX_PATTERNS: int = Field(
        default=1000, description="Maximum number of code patterns"
    )
    CODE_PATTERN_UPDATE_FREQUENCY: int = Field(
        default=50, description="Code pattern update frequency"
    )

    # Memory Persistence
    MEMORY_AUTO_SAVE: bool = Field(default=True, description="Auto-save memories")
    MEMORY_SAVE_INTERVAL_SECONDS: int = Field(
        default=300, description="Memory save interval in seconds"
    )
    MEMORY_ENABLE_BACKUP: bool = Field(default=True, description="Enable memory backup")
    MEMORY_BACKUP_INTERVAL_HOURS: int = Field(
        default=6, description="Memory backup interval in hours"
    )

    # =====================================================================================
    # APPROVAL SYSTEM CONFIGURATION
    # =====================================================================================

    APPROVAL_ENABLED: bool = Field(default=True, description="Enable approval system")
    AUTO_APPROVE_LOW_RISK: bool = Field(
        default=False, description="Auto-approve low-risk operations"
    )
    APPROVAL_TIMEOUT_MINUTES: int = Field(default=60, description="Approval timeout in minutes")
    APPROVAL_SERVICE_URL: AnyHttpUrl = Field(
        default="http://localhost:8000/api/v1/approvals", description="Approval service URL"
    )
    APPROVAL_TOKEN_TTL_SECONDS: int = Field(
        default=86400, description="Approval token TTL in seconds (default: 24 hours)"
    )

    # =====================================================================================
    # WORKSPACE & PROJECT CONFIGURATION
    # =====================================================================================

    WORKSPACE_PATH: Path = Field(
        default=Path("/home/<USER>/workspace"), description="Base workspace directory path"
    )
    CODE_SERVER_URL: AnyHttpUrl = Field(
        default="http://localhost:8080", description="Code server URL"
    )

    @field_validator("WORKSPACE_PATH", mode="before")
    @classmethod
    def parse_workspace_path(cls, v: Union[str, Path]) -> Path:
        """Parse workspace path from string or Path object."""
        return Path(v)

    # =====================================================================================
    # MONITORING & LOGGING CONFIGURATION
    # =====================================================================================

    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string",
    )
    ENABLE_METRICS: bool = Field(default=True, description="Enable Prometheus metrics")
    METRICS_PORT: int = Field(default=9090, description="Metrics server port")

    # Health Check Configuration
    HEALTH_CHECK_INTERVAL: int = Field(default=30, description="Health check interval in seconds")
    HEALTH_CHECK_TIMEOUT: int = Field(default=10, description="Health check timeout in seconds")

    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = Field(default=True, description="Enable rate limiting")
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Rate limit requests per window")
    RATE_LIMIT_WINDOW: int = Field(default=60, description="Rate limit window in seconds")

    # Feature flags
    ENABLE_WEBSOCKET_CHAT: bool = Field(
        default=True, description="Enable WebSocket chat initialization on startup"
    )
    ENABLE_ROLE_CONFIG_INIT: bool = Field(
        default=True, description="Enable role configuration initialization on startup"
    )
    ENABLE_GITHUB_AUTH: bool = Field(
        default=False, description="Enable GitHub authentication router"
    )
    ENABLE_PROJECT_MANAGEMENT: bool = Field(
        default=False, description="Enable project management router"
    )
    ENABLE_LANGGRAPH: bool = Field(
        default=True, description="Enable LangGraph feature for advanced orchestration"
    )
    ENABLE_CUDA: bool = Field(
        default=False, description="Enable CUDA-accelerated features"
    )

    # Role Configuration
    ROLE_CONFIG_PATH: Path = Field(
        default=Path("/app/data/role_config.json"),
        description="Path to role configuration JSON file",
    )

    # =====================================================================================
    # DOCKER & CONTAINER CONFIGURATION
    # =====================================================================================

    DOCKER_SOCKET: str = Field(default="/var/run/docker.sock", description="Docker socket path")
    DOCKER_PROXY_URL: str = Field(
        default="http://docker-proxy:2375", description="Docker proxy service URL"
    )
    CONTAINER_MEMORY_LIMIT: str = Field(default="2g", description="Default container memory limit")
    CONTAINER_CPU_LIMIT: float = Field(default=1.0, description="Default container CPU limit (e.g., 1.0 for one core)")

    # =====================================================================================
    # CONFIGURATION VALIDATION & COMPUTED PROPERTIES
    # =====================================================================================

    @model_validator(mode="after")
    def _validate_jwt_secret(self) -> "Settings":
        """
        Validate the JWT secret, ensuring it's secure for production.

        This validator will:
        1. Skip validation for the 'TESTING' environment.
        2. Attempt to load the secret from `JWT_SECRET_FILE` if `JWT_SECRET` is not set.
        3. In 'PRODUCTION', enforce that the secret exists and is at least 32 characters long.
        4. In other environments (like 'DEVELOPMENT'), ensure a secret is present but without a length requirement.
        5. Raise a `ValueError` to prevent application startup if validation fails.
        """
        # Don't perform this validation in a testing environment
        if self.ENVIRONMENT == Environment.TESTING:
            return self

        # Attempt to load secret from file if not directly provided
        secret = self.JWT_SECRET
        if not secret and self.JWT_SECRET_FILE:
            try:
                with open(self.JWT_SECRET_FILE, "r") as f:
                    secret_from_file = f.read().strip()
                    if secret_from_file:
                        # Directly assign to the model field
                        self.JWT_SECRET = secret_from_file
                        secret = secret_from_file
            except (FileNotFoundError, IOError):
                # If file is not found or unreadable, we'll proceed with secret as None
                pass

        # Production environment requires a strong secret
        if self.ENVIRONMENT == Environment.PRODUCTION:
            if not secret:
                raise ValueError(
                    "FATAL: JWT_SECRET is not configured for the production environment. "
                    "A secure secret must be provided via the JWT_SECRET environment "
                    "variable or through a file specified by JWT_SECRET_FILE."
                )
            if len(secret) < 32:
                raise ValueError(
                    f"FATAL: JWT_SECRET is too short for the production environment. "
                    f"It must be at least 32 characters long, but its current length is {len(secret)}."
                )
        # Non-production, non-test environments still require a secret
        else:
            if not secret:
                raise ValueError(
                    "JWT_SECRET is not configured for the development environment. "
                    "Please set it via the JWT_SECRET environment variable or JWT_SECRET_FILE."
                )

        return self

    @property
    def redis_available(self) -> bool:
        """Check if Redis is configured and available."""
        return bool(self.REDIS_URL)

    @property
    def supabase_available(self) -> bool:
        """Check if Supabase is properly configured."""
        if not self.USE_SUPABASE:
            return False
        return all([self.SUPABASE_URL, self.SUPABASE_ANON_KEY, self.SUPABASE_SERVICE_KEY])

    @property
    def openrouter_available(self) -> bool:
        """Check if OpenRouter is configured."""
        return bool(self.OPENROUTER_API_KEY)

    @property
    def openai_available(self) -> bool:
        """Check if OpenAI is configured."""
        return bool(self.OPENAI_API_KEY)

    @property
    def anthropic_available(self) -> bool:
        """Check if Anthropic is configured."""
        return bool(self.ANTHROPIC_API_KEY)

    @property
    def github_oauth_available(self) -> bool:
        """Check if GitHub OAuth is configured."""
        return bool(self.GITHUB_CLIENT_ID and self.GITHUB_CLIENT_SECRET)

    @property
    def db_dialect(self) -> str:
        """Get the database dialect from DATABASE_URL."""
        url_str = str(self.DATABASE_URL)
        return url_str.split(":", 1)[0].split("+", 1)[0]

    # =====================================================================================
    # PYDANTIC SETTINGS CONFIGURATION
    # =====================================================================================

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True,
        "validate_assignment": True,
        "extra": "allow",  # Allow extra environment variables
    }


# =====================================================================================
# GLOBAL SETTINGS INSTANCE
# =====================================================================================

# Create global settings instance (singleton pattern)
# Auto-detect test environment
is_test_env = (
    os.getenv("TESTING") == "1"
    or os.getenv("PYTEST_CURRENT_TEST") == "true"
    or "pytest" in os.environ.get("_", "").lower()
)

if is_test_env:

    class TestSettings(BaseSettings):
        """Test-specific settings allowing lightweight overrides (e.g., SQLite)."""

        # Hardcode test values to avoid environment variable issues
        APP_NAME: str = "AI Coding Agent Orchestrator"
        APP_VERSION: str = "1.0.0"
        ENVIRONMENT: Environment = Environment.TESTING
        DEBUG: bool = True
        API_V1_PREFIX: str = "/api/v1"
        DOCS_URL: str = "/api/v1/docs"
        REDOC_URL: str = "/api/v1/redoc"
        CORS_ORIGINS: Union[List[str], str] = ["http://localhost:3000", "http://localhost:8080"]
        CORS_ALLOW_CREDENTIALS: bool = True
        CORS_ALLOW_METHODS: List[str] = ["*"]
        CORS_ALLOW_HEADERS: List[str] = ["*"]

        # Database
        DATABASE_URL: str = "sqlite+aiosqlite:///./test.db"
        DATABASE_POOL_SIZE: int = 20
        DATABASE_POOL_OVERFLOW: int = 0
        DATABASE_POOL_TIMEOUT: int = 30
        DATABASE_ECHO: bool = False

        # Redis
        REDIS_URL: str = "redis://localhost:6379"
        REDIS_MAX_CONNECTIONS: int = 20
        REDIS_RETRY_ON_TIMEOUT: bool = True
        REDIS_SOCKET_KEEPALIVE: bool = True
        REDIS_SOCKET_KEEPALIVE_OPTIONS: dict = {}

        # Supabase
        USE_SUPABASE: bool = False
        SUPABASE_URL: Optional[AnyHttpUrl] = None
        SUPABASE_ANON_KEY: Optional[str] = None
        SUPABASE_SERVICE_KEY: Optional[str] = None
        SUPABASE_DB_URL: Optional[PostgresDsn] = None
        SUPABASE_MAX_CONNECTIONS: int = 20
        SUPABASE_MIN_CONNECTIONS: int = 5
        SUPABASE_CONNECTION_TIMEOUT: float = 30.0
        SUPABASE_COMMAND_TIMEOUT: float = 60.0
        SUPABASE_RETRY_ATTEMPTS: int = 3
        SUPABASE_RETRY_DELAY: float = 1.0
        SUPABASE_ENABLE_SSL: bool = False

        # Auth
        JWT_SECRET_FILE: Optional[str] = None
        JWT_SECRET: str = "test_jwt_secret_key_for_testing_only_32_chars_minimum"
        JWT_ALGORITHM: str = "HS256"
        JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
        JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 7
        PASSWORD_MIN_LENGTH: int = 8
        REQUIRE_EMAIL_VERIFICATION: bool = True
        ALLOW_SIGNUP: bool = True
        MAX_LOGIN_ATTEMPTS: int = 5
        LOGIN_ATTEMPT_WINDOW_MINUTES: int = 15

        # LLM
        OLLAMA_BASE_URL: str = "http://ollama:11434"
        OLLAMA_MODEL: str = "llama3.1"
        OLLAMA_TIMEOUT: int = 300
        DEFAULT_LOCAL_PROVIDER: LLMProvider = LLMProvider.OLLAMA
        DEFAULT_CLOUD_PROVIDER: LLMProvider = LLMProvider.OPENROUTER
        ENABLE_CLOUD_FALLBACK: bool = True
        LLM_REQUEST_TIMEOUT: int = 300
        LLM_MAX_RETRIES: int = 3

        # API Keys for testing
        OPENROUTER_API_KEY: Optional[str] = "mock_openrouter_key_for_tests"
        OPENAI_API_KEY: Optional[str] = "mock_openai_key_for_tests"
        ANTHROPIC_API_KEY: Optional[str] = "mock_anthropic_key_for_tests"

        # Embeddings
        EMBEDDING_PROVIDER: EmbeddingProvider = EmbeddingProvider.OLLAMA
        EMBEDDING_MODEL: str = "nomic-embed-text"
        OLLAMA_EMBEDDING_MODEL: str = "nomic-embed-text"
        EMBEDDING_DIMENSION: int = 768
        CHUNK_SIZE: int = 512
        CHUNK_OVERLAP: int = 50
        SIMILARITY_THRESHOLD: float = 0.7
        MAX_SEARCH_RESULTS: int = 10
        ENABLE_HYBRID_SEARCH: bool = True
        CACHE_EMBEDDINGS: bool = True

        # Memory Management
        MEMORY_FILE_PATH: Path = Path("/tmp/test_memories.json")
        MEMORY_BACKUP_PATH: Path = Path("/tmp/test_memories_backup.json")
        MAX_MEMORY_ENTRIES: int = 1000
        MEMORY_CLEANUP_INTERVAL_HOURS: int = 1
        MEMORY_RETENTION_DAYS: int = 7
        MEMORY_COMPRESSION_THRESHOLD: int = 500
        MEMORY_SEARCH_SIMILARITY_THRESHOLD: float = 0.8
        MEMORY_MAX_SEARCH_RESULTS: int = 20
        MEMORY_SEARCH_TIMEOUT: int = 30
        ENABLE_LEARNING_LOOPS: bool = True
        LEARNING_UPDATE_INTERVAL_MINUTES: int = 5
        LEARNING_CONFIDENCE_THRESHOLD: float = 0.7
        LEARNING_PATTERN_MIN_OCCURRENCES: int = 2
        CODE_PATTERN_SIMILARITY_THRESHOLD: float = 0.85
        CODE_PATTERN_MAX_PATTERNS: int = 100
        CODE_PATTERN_UPDATE_FREQUENCY: int = 10
        MEMORY_AUTO_SAVE: bool = True
        MEMORY_SAVE_INTERVAL_SECONDS: int = 60
        MEMORY_ENABLE_BACKUP: bool = True
        MEMORY_BACKUP_INTERVAL_HOURS: int = 1

        # Approval
        APPROVAL_ENABLED: bool = True
        AUTO_APPROVE_LOW_RISK: bool = False
        APPROVAL_TIMEOUT_MINUTES: int = 60
        APPROVAL_TOKEN_TTL_SECONDS: int = 3600  # 1 hour for tests

        # Workspace
        WORKSPACE_PATH: Path = Path("/home/<USER>/workspace")
        CODE_SERVER_URL: str = "http://localhost:8080"

        # Monitoring
        LOG_LEVEL: str = "INFO"
        LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        ENABLE_METRICS: bool = True
        METRICS_PORT: int = 9090
        HEALTH_CHECK_INTERVAL: int = 30
        HEALTH_CHECK_TIMEOUT: int = 10
        RATE_LIMIT_ENABLED: bool = True
        RATE_LIMIT_REQUESTS: int = 100
        RATE_LIMIT_WINDOW: int = 60

        # Features
        ENABLE_WEBSOCKET_CHAT: bool = True
        ENABLE_ROLE_CONFIG_INIT: bool = True
        ENABLE_GITHUB_AUTH: bool = False
        ENABLE_PROJECT_MANAGEMENT: bool = False

        # Role Configuration
        ROLE_CONFIG_PATH: Path = Path("/app/data/role_config.json")

        # Docker
        DOCKER_SOCKET: str = "/var/run/docker.sock"
        DOCKER_PROXY_URL: str = "http://docker-proxy:2375"
        CONTAINER_MEMORY_LIMIT: str = "2g"
        CONTAINER_CPU_LIMIT: float = 1.0

        @property
        def supabase_available(self) -> bool:
            """Check if Supabase is properly configured."""
            if not self.USE_SUPABASE:
                return False
            return all(
                [
                    getattr(self, "SUPABASE_URL", None),
                    getattr(self, "SUPABASE_ANON_KEY", None),
                    getattr(self, "SUPABASE_SERVICE_KEY", None),
                ]
            )

        @property
        def db_dialect(self) -> str:
            """Get the database dialect from DATABASE_URL."""
            url_str = str(self.DATABASE_URL)
            return url_str.split(":", 1)[0].split("+", 1)[0]

        # Override validators for testing
        @field_validator("JWT_SECRET")
        @classmethod
        def validate_jwt_secret(cls, v: str) -> str:
            """Validate JWT secret strength (relaxed for testing)."""
            if len(v) < 10:  # Relaxed validation for testing
                return "test_jwt_secret_key_for_testing_only_32_chars_minimum"
            return v

        @field_validator("WORKSPACE_PATH", mode="before")
        @classmethod
        def parse_workspace_path(cls, v: Union[str, Path]) -> Path:
            """Parse workspace path from string or Path object."""
            return Path(v)

        @field_validator("CORS_ORIGINS", mode="before")
        @classmethod
        def parse_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
            """Parse CORS origins from comma-separated string or list."""
            if isinstance(v, str):
                return [origin.strip() for origin in v.split(",")]
            return v

        model_config = {
            "env_file": None,  # Disable .env file reading
            "env_prefix": "",  # Disable environment variable reading
            "case_sensitive": True,
            "validate_assignment": True,
            "extra": "allow",
        }

    settings = TestSettings()
else:
    settings = Settings()


# =====================================================================================
# CONFIGURATION UTILITIES
# =====================================================================================


def get_settings() -> Settings:
    """Return settings instance.

    If TESTING=1, ensure we return the TestSettings instance; otherwise production Settings.
    """
    return settings


def reload_settings() -> Settings:
    """
    Reload settings from environment variables.

    Useful for testing or when environment variables change.

    Returns:
        Settings: New settings instance with current environment
    """
    global settings
    settings = Settings()
    return settings


def validate_configuration() -> bool:
    """
    Validate the current configuration.

    Performs comprehensive validation of all settings and
    checks for required dependencies.

    Returns:
        bool: True if configuration is valid

    Raises:
        ValueError: If configuration is invalid
    """
    try:
        # Validate core settings
        if not settings.JWT_SECRET:
            raise ValueError("JWT_SECRET is required")

        if not settings.DATABASE_URL:
            raise ValueError("DATABASE_URL is required")

        if not settings.REDIS_URL:
            raise ValueError("REDIS_URL is required")

        # Validate Supabase if enabled
        if settings.USE_SUPABASE and not settings.supabase_available:
            raise ValueError("Supabase configuration incomplete")

        # Validate paths
        if not settings.WORKSPACE_PATH.is_absolute():
            raise ValueError("WORKSPACE_PATH must be an absolute path")

        return True

    except Exception as e:
        raise ValueError(f"Configuration validation failed: {str(e)}")


def get_llm_provider_config(provider: LLMProvider) -> dict:
    """
    Get configuration for a specific LLM provider.

    Args:
        provider: LLM provider to get configuration for

    Returns:
        dict: Provider configuration
    """
    provider_configs = {
        LLMProvider.OLLAMA: {
            "base_url": str(settings.OLLAMA_BASE_URL),
            "model": settings.OLLAMA_MODEL,
            "timeout": settings.OLLAMA_TIMEOUT,
            "available": True,
        },
        LLMProvider.OPENROUTER: {
            "base_url": str(settings.OPENROUTER_BASE_URL),
            "model": settings.OPENROUTER_MODEL,
            "api_key": settings.OPENROUTER_API_KEY,
            "available": settings.openrouter_available,
        },
        LLMProvider.OPENAI: {
            "base_url": str(settings.OPENAI_BASE_URL),
            "model": settings.OPENAI_MODEL,
            "api_key": settings.OPENAI_API_KEY,
            "available": settings.openai_available,
        },
        LLMProvider.ANTHROPIC: {
            "base_url": str(settings.ANTHROPIC_BASE_URL),
            "model": settings.ANTHROPIC_MODEL,
            "api_key": settings.ANTHROPIC_API_KEY,
            "available": settings.anthropic_available,
        },
    }

    return provider_configs.get(provider, {})


def get_embedding_config() -> dict:
    """
    Get embedding configuration.

    Returns:
        dict: Embedding configuration
    """
    return {
        "provider": settings.EMBEDDING_PROVIDER,
        "model": settings.EMBEDDING_MODEL,
        "dimension": settings.EMBEDDING_DIMENSION,
        "chunk_size": settings.CHUNK_SIZE,
        "chunk_overlap": settings.CHUNK_OVERLAP,
        "similarity_threshold": settings.SIMILARITY_THRESHOLD,
        "max_search_results": settings.MAX_SEARCH_RESULTS,
        "enable_hybrid_search": settings.ENABLE_HYBRID_SEARCH,
        "cache_embeddings": settings.CACHE_EMBEDDINGS,
    }
