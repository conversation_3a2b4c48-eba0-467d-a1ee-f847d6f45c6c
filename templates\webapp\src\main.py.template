"""
__PROJECT_NAME__ FastAPI Application

A production-ready FastAPI application with health checks, logging,
and proper error handling.
"""

import logging
import os
import sys
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.getenv("LOG_FILE", "./logs/__PROJECT_NAME__.log"))
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("Starting __PROJECT_NAME__ application...")
    
    # Initialize database connections, cache, etc.
    try:
        # Add your startup logic here
        logger.info("Application startup completed successfully")
        yield
    except Exception as e:
        logger.error(f"Application startup failed: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down __PROJECT_NAME__ application...")
        # Add your cleanup logic here
        logger.info("Application shutdown completed")


# Create FastAPI application
app = FastAPI(
    title="__PROJECT_NAME__",
    description="A production-ready web application built with FastAPI",
    version=os.getenv("VERSION", "1.0.0"),
    lifespan=lifespan,
    docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") != "production" else None,
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ORIGINS", "http://localhost:3000").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=os.getenv("ALLOWED_HOSTS", "localhost,127.0.0.1").split(",")
)


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred",
            "timestamp": datetime.utcnow().isoformat()
        }
    )


@app.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Health check endpoint for container orchestration.
    
    Returns:
        Dict containing health status and system information
    """
    try:
        # Add your health checks here (database, redis, external services)
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "__PROJECT_NAME__",
            "version": os.getenv("VERSION", "1.0.0"),
            "environment": os.getenv("ENVIRONMENT", "development"),
            "checks": {
                "application": "ok",
                # Add more health checks as needed
                # "database": await check_database(),
                # "redis": await check_redis(),
            }
        }
        
        logger.debug("Health check completed successfully")
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail={
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }
        )


@app.get("/")
async def root() -> Dict[str, str]:
    """Root endpoint returning basic application information."""
    return {
        "message": f"Welcome to __PROJECT_NAME__",
        "version": os.getenv("VERSION", "1.0.0"),
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/api/v1/status")
async def api_status() -> Dict[str, Any]:
    """API status endpoint for monitoring."""
    return {
        "api_version": "v1",
        "service": "__PROJECT_NAME__",
        "status": "operational",
        "timestamp": datetime.utcnow().isoformat()
    }


if __name__ == "__main__":
    # Development server configuration
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("APP_PORT", 8000)),
        reload=os.getenv("RELOAD", "false").lower() == "true",
        log_level=os.getenv("LOG_LEVEL", "info").lower(),
        workers=int(os.getenv("WORKERS", 1))
    )
