"""
Docker Service for Multi-Tenant Workspace Management.

This module provides Docker container management capabilities for creating,
starting, stopping, and monitoring user-specific code-server workspaces.

Enhanced with:
- Async operations using asyncio.to_thread for blocking Docker SDK calls
- Configurable Traefik network names and settings
- Retry decorators for reliable container operations
- Performance optimizations and resource management

Author: AI Coding Agent
Version: 2.0.0
"""

import asyncio
import logging
import os
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from functools import wraps

try:
    import docker
    from docker.models.containers import Container
    from docker.errors import DockerException, NotFound, APIError
    DOCKER_AVAILABLE = True
except ImportError:
    DOCKER_AVAILABLE = False
    docker = None
    Container = None
    DockerException = Exception
    NotFound = Exception
    APIError = Exception

from src.models import Workspace
from src.core.config import settings

# Configure logging
logger = logging.getLogger(__name__)


def retry_on_docker_error(max_retries: int = 3, delay: float = 1.0):
    """
    Decorator to retry Docker operations on failure.

    Args:
        max_retries: Maximum number of retry attempts
        delay: Delay between retries in seconds
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if not DOCKER_AVAILABLE:
                raise RuntimeError("Docker SDK not available")

            last_exception = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except (DockerException, APIError) as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"Docker operation failed (attempt {attempt + 1}/{max_retries}): {e}")
                        await asyncio.sleep(delay * (2 ** attempt))  # Exponential backoff
                    else:
                        logger.error(f"Docker operation failed after {max_retries} attempts: {e}")
                        raise last_exception
            return None
        return wrapper
    return decorator


class DockerWorkspaceService:
    """
    Service for managing Docker containers for user workspaces.

    Handles the lifecycle of user-specific code-server containers
    with proper isolation, networking, and routing configuration.

    Enhanced features:
    - Async operations for all blocking Docker SDK calls
    - Configurable network settings and resource limits
    - Retry logic for reliable container operations
    - Performance monitoring and metrics collection
    """

    def __init__(self):
        """Initialize Docker workspace service with configurable settings and validation."""
        # Configurable settings from environment or settings with validation
        self.traefik_network = os.getenv("TRAEFIK_NETWORK_NAME",
                                       getattr(settings, 'TRAEFIK_NETWORK_NAME', "ai-coding-agent_web_network"))
        self.container_cpu_limit = os.getenv("CONTAINER_CPU_LIMIT", "1.0")
        self.container_memory_limit = os.getenv("CONTAINER_MEMORY_LIMIT", "2G")
        self.enable_resource_limits = os.getenv("DOCKER_RESOURCE_LIMITS", "true").lower() == "true"
        self.container_timeout = int(os.getenv("CONTAINER_TIMEOUT", "300"))  # 5 minutes
        self.max_concurrent_operations = int(os.getenv("DOCKER_MAX_CONCURRENT", "5"))

        # Retry configuration
        self.max_retries = int(os.getenv("DOCKER_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("DOCKER_RETRY_DELAY", "1.0"))

        # Validate configuration
        self._validate_configuration()

        # Initialize Docker client with lazy loading
        self.client = None
        self._client_initialized = False
        self._initialization_error = None

        # Operation semaphore for rate limiting
        self._operation_semaphore = asyncio.Semaphore(self.max_concurrent_operations)

        logger.info(f"Docker service initialized with network: {self.traefik_network}")
        logger.info(f"Resource limits enabled: {self.enable_resource_limits}")
        logger.info(f"Max concurrent operations: {self.max_concurrent_operations}")

    def _validate_configuration(self) -> None:
        """Validate Docker service configuration at startup."""
        errors = []

        # Validate CPU limit
        try:
            cpu_limit = float(self.container_cpu_limit)
            if cpu_limit <= 0 or cpu_limit > 32:
                errors.append(f"Invalid CPU limit: {self.container_cpu_limit} (must be 0 < limit <= 32)")
        except ValueError:
            errors.append(f"Invalid CPU limit format: {self.container_cpu_limit}")

        # Validate memory limit format
        if not self._parse_memory_limit_validate(self.container_memory_limit):
            errors.append(f"Invalid memory limit format: {self.container_memory_limit}")

        # Validate timeout
        if self.container_timeout < 10 or self.container_timeout > 3600:
            errors.append(f"Invalid timeout: {self.container_timeout} (must be 10-3600 seconds)")

        if errors:
            error_msg = "Docker service configuration validation failed:\n" + "\n".join(f"  - {err}" for err in errors)
            logger.error(error_msg)
            raise ValueError(error_msg)

    def _parse_memory_limit_validate(self, memory_str: str) -> bool:
        """Validate memory limit format without parsing."""
        try:
            self._parse_memory_limit(memory_str)
            return True
        except (ValueError, TypeError):
            return False

    async def _initialize_docker_client(self) -> None:
        """Initialize Docker client asynchronously with proper error handling."""
        if self._client_initialized:
            return

        try:
            # Initialize client in thread to avoid blocking
            def _create_client():
                client = docker.from_env()
                client.ping()  # Test connection
                return client

            self.client = await asyncio.to_thread(_create_client)
            self._client_initialized = True

            logger.info("Docker client initialized and connection verified")

            # Validate Traefik network existence
            await self._validate_traefik_network()

        except Exception as e:
            self._initialization_error = e
            logger.error(f"Failed to initialize Docker client: {str(e)}")
            raise RuntimeError(f"Docker client initialization failed: {str(e)}")

    async def _validate_traefik_network(self) -> None:
        """Validate that the Traefik network exists and is accessible."""
        try:
            async with self._operation_semaphore:
                network = await asyncio.to_thread(self.client.networks.get, self.traefik_network)
                logger.info(f"Validated Traefik network: {network.name} ({network.id[:12]})")
        except Exception as e:
            logger.warning(
                f"Traefik network '{self.traefik_network}' not accessible: {e}. "
                "Containers may not be accessible via Traefik routing."
            )

    def generate_workspace_config(self, user_id: str) -> Dict[str, Any]:
        """
        Generate workspace configuration for a user.

        Args:
            user_id: User identifier

        Returns:
            Workspace configuration dictionary
        """
        # Generate unique subdomain and container name
        subdomain = f"user-{user_id.lower().replace('_', '-')}"
        container_name = f"workspace-{user_id.lower()}-{uuid.uuid4().hex[:8]}"

        # Get resource limits from environment variables
        cpu_limit = os.getenv("CONTAINER_CPU_LIMIT", "1.0")
        memory_limit = os.getenv("CONTAINER_MEMORY_LIMIT", "2G")
        enable_limits = os.getenv("DOCKER_RESOURCE_LIMITS", "true").lower() == "true"

        return {
            "subdomain": subdomain,
            "container_name": container_name,
            "image": "codingagenttwo-code-server-template:latest",  # Correct template image name
            "networks": ["ai-coding-agent_web_network"],  # Only web network for user containers (DMZ)
            # Docker secrets for secure password management
            "secrets": [
                {
                    "source": "code_server_password",
                    "target": "/run/secrets/code_server_password",
                    "mode": 0o400  # Read-only for owner
                }
            ],
            "environment": {
                "PASSWORD_FILE": "/run/secrets/code_server_password",  # Use Docker secrets consistently
                "DOCKER_USER": "coder",
                "USER_ID": user_id,
                "SHELL": "/bin/bash",
                "CODE_SERVER_BIND_ADDR": "0.0.0.0:8080",
                "CODE_SERVER_AUTH": "password"
            },
            "labels": {
                "traefik.enable": "true",
                f"traefik.http.routers.{subdomain}.rule": f"Host(`{subdomain}.localhost`)",
                f"traefik.http.routers.{subdomain}.entrypoints": "web",
                f"traefik.http.services.{subdomain}.loadbalancer.server.port": "8080",
                "traefik.docker.network": "ai-coding-agent_web_network",  # Correct network name
                # Workspace metadata
                "workspace.user_id": user_id,
                "workspace.type": "code-server",
                "workspace.managed_by": "ai-orchestrator",
                "workspace.created_at": datetime.now(timezone.utc).isoformat(),
                # Monitoring and observability
                "monitoring.enabled": "true",
                "monitoring.scrape_port": "8080",
                "monitoring.health_endpoint": "/healthz",
                "logging.driver": "json-file",
                "logging.max_size": "10m",
                "logging.max_file": "3"
            },
            "volumes": {
                f"workspace-{user_id}-data": {
                    "bind": "/home/<USER>",
                    "mode": "rw",
                    "driver_opts": {
                        "type": "none",
                        "o": "bind"
                    }
                },
                f"workspace-{user_id}-projects": {
                    "bind": "/home/<USER>/workspace",
                    "mode": "rw",
                    "driver_opts": {
                        "type": "none",
                        "o": "bind"
                    }
                },
                # Configuration volumes (read-only)
                "code-server-config": {
                    "bind": "/home/<USER>/.config/code-server",
                    "mode": "ro"
                }
            },
            "resource_limits": {
                "enabled": enable_limits,
                "cpu_limit": cpu_limit,
                "memory_limit": memory_limit,
                "memory_reservation": "512m",
                "cpu_reservation": "0.25",
                # Enhanced resource controls
                "memory_swappiness": 10,  # Prefer RAM over swap
                "oom_kill_disable": False,  # Allow OOM killer for safety
                "pids_limit": 1024,  # Limit number of processes
                "shm_size": "128m",  # Shared memory limit
                # I/O limits
                "blkio_weight": 500,  # Default I/O weight
                "device_read_bps": "100m",  # 100MB/s read limit
                "device_write_bps": "50m"   # 50MB/s write limit
            },
            "security": {
                "user": "coder",
                "no_new_privileges": True,
                "security_opt": [
                    "no-new-privileges:true"
                    # seccomp:unconfined is removed to use Docker's default seccomp profile, which is more secure.
                ],
                "cap_drop": ["ALL"],
                "cap_add": ["SETUID", "SETGID", "DAC_OVERRIDE"],  # Added DAC_OVERRIDE for file operations
                "read_only": False,  # Code-server needs write access
                "tmpfs": {
                    "/tmp": "rw,noexec,nosuid,size=200m",
                    "/var/tmp": "rw,noexec,nosuid,size=100m",
                    "/run": "rw,noexec,nosuid,size=50m"  # Added /run for runtime files
                },
                "ulimits": [
                    {"name": "nofile", "soft": 65536, "hard": 65536},  # File descriptor limits
                    {"name": "nproc", "soft": 4096, "hard": 4096}     # Process limits
                ]
            }
        }

    @retry_on_docker_error(max_retries=3, delay=1.0)
    async def create_workspace(self, user_id: str, workspace: Workspace) -> Any:
        """Create and start a new workspace container for a user with enhanced error handling.

        Args:
            user_id: User identifier
            workspace: Workspace database model

        Returns:
            Docker container instance

        Raises:
            RuntimeError: If Docker client not available
            DockerException: If container creation fails after retries
        """
        await self._ensure_docker_client()

        async with self._operation_semaphore:
            try:
                config = self.generate_workspace_config(user_id)
                container_name = config["container_name"]

                logger.info(f"Creating workspace container '{container_name}' for user {user_id}")

                # Check if container already exists
                try:
                    existing = await asyncio.to_thread(self.client.containers.get, container_name)
                    logger.warning(f"Container {container_name} already exists, removing first")
                    if existing.status == "running":
                        await asyncio.to_thread(existing.stop, timeout=10)
                    await asyncio.to_thread(existing.remove)
                except NotFound:
                    pass  # Container doesn't exist, which is expected

                # Create volumes first using async execution with error handling
                volume_creation_errors = []
                for volume_name in config["volumes"]:
                    try:
                        await asyncio.to_thread(self.client.volumes.create, name=volume_name)
                        logger.debug(f"Created/verified volume: {volume_name}")
                    except APIError as e:
                        if "already exists" not in str(e).lower():
                            volume_creation_errors.append(f"Volume {volume_name}: {str(e)}")

                if volume_creation_errors:
                    logger.warning(f"Volume creation issues: {volume_creation_errors}")

                # Prepare container configuration with enhanced security
                container_config = self._build_container_config(config)

                # Create container with timeout
                container = await asyncio.wait_for(
                    self._create_container_with_config(container_config, config),
                    timeout=self.container_timeout
                )

                container_id = getattr(container, 'id', str(container))
                logger.info(f"Successfully created container {container_id[:12]} for user {user_id}")

                return container

            except asyncio.TimeoutError:
                error_msg = f"Container creation timeout ({self.container_timeout}s) for user {user_id}"
                logger.error(error_msg)
                raise RuntimeError(error_msg)
            except DockerException as e:
                logger.error(f"Docker error creating workspace for user {user_id}: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"Unexpected error creating workspace for user {user_id}: {str(e)}")
                raise RuntimeError(f"Workspace creation failed: {str(e)}")

    async def stop_workspace(self, container_id: str) -> bool:
        """Stop a workspace container with enhanced error handling and graceful shutdown.

        Args:
            container_id: Container identifier

        Returns:
            True if successful, False otherwise
        """
        await self._ensure_docker_client()

        async with self._operation_semaphore:
            try:
                container = await asyncio.to_thread(self.client.containers.get, container_id)

                # Check current status
                await asyncio.to_thread(container.reload)
                if container.status != "running":
                    logger.info(f"Container {container_id[:12]} is not running (status: {container.status})")
                    return True

                logger.info(f"Stopping container {container_id[:12]}")

                # Graceful stop with timeout
                await asyncio.wait_for(
                    asyncio.to_thread(container.stop, timeout=30),
                    timeout=45.0  # Extra 15 seconds for the operation itself
                )

                # Verify container stopped
                await asyncio.to_thread(container.reload)
                if container.status == "exited":
                    logger.info(f"Successfully stopped container {container_id[:12]}")
                    return True
                else:
                    logger.warning(f"Container {container_id[:12]} status after stop: {container.status}")
                    return False

            except asyncio.TimeoutError:
                logger.error(f"Timeout stopping container {container_id[:12]}")
                # Try force kill as last resort
                try:
                    container = await asyncio.to_thread(self.client.containers.get, container_id)
                    await asyncio.to_thread(container.kill)
                    logger.warning(f"Force killed container {container_id[:12]} after timeout")
                    return True
                except Exception as kill_error:
                    logger.error(f"Failed to force kill container: {kill_error}")
                    return False
            except NotFound:
                logger.warning(f"Container {container_id[:12]} not found")
                return False
            except DockerException as e:
                logger.error(f"Docker error stopping container {container_id[:12]}: {str(e)}")
                return False
            except Exception as e:
                logger.error(f"Unexpected error stopping container {container_id[:12]}: {str(e)}")
                return False

    async def remove_workspace(self, container_id: str, remove_volumes: bool = False) -> bool:
        """
        Remove a workspace container and optionally its volumes.

        Args:
            container_id: Container identifier
            remove_volumes: Whether to remove associated volumes

        Returns:
            True if successful, False otherwise
        """
        if not DOCKER_AVAILABLE or self.client is None:
            raise RuntimeError("Docker client not available")

        try:
            container = await asyncio.to_thread(self.client.containers.get, container_id)

            # Get user_id from container labels for volume cleanup
            user_id = container.labels.get("workspace.user_id")

            # Stop and remove container
            if container.status == "running":
                await asyncio.to_thread(container.stop, timeout=30)
            await asyncio.to_thread(container.remove)

            logger.info(f"Removed container {container_id}")

            # Remove volumes if requested
            if remove_volumes and user_id:
                await self._cleanup_user_volumes(user_id)

            return True

        except NotFound:
            logger.warning(f"Container {container_id} not found")
            return False
        except DockerException as e:
            logger.error(f"Failed to remove container {container_id}: {str(e)}")
            return False

    async def get_container_status(self, container_id: str) -> Optional[Dict[str, Any]]:
        """
        Get container status and information.

        Args:
            container_id: Container identifier

        Returns:
            Container status dictionary or None if not found
        """
        if not DOCKER_AVAILABLE or self.client is None:
            raise RuntimeError("Docker client not available")

        try:
            container = await asyncio.to_thread(self.client.containers.get, container_id)
            await asyncio.to_thread(container.reload)

            return {
                "id": container.id,
                "name": container.name,
                "status": container.status,
                "created": container.attrs["Created"],
                "started_at": container.attrs["State"].get("StartedAt"),
                "health": container.attrs["State"].get("Health", {}).get("Status", "unknown"),
                "ports": container.attrs["NetworkSettings"]["Ports"]
            }

        except NotFound:
            return None
        except DockerException as e:
            logger.error(f"Failed to get container status {container_id}: {str(e)}")
            return None

    async def list_user_workspaces(self, user_id: str) -> List[Dict[str, Any]]:
        """
        List all containers for a specific user.

        Args:
            user_id: User identifier

        Returns:
            List of container information dictionaries
        """
        if not DOCKER_AVAILABLE or self.client is None:
            raise RuntimeError("Docker client not available")

        try:
            containers = await asyncio.to_thread(
                self.client.containers.list,
                all=True,
                filters={"label": f"workspace.user_id={user_id}"}
            )

            result = []
            for container in containers:
                result.append({
                    "id": container.id,
                    "name": container.name,
                    "status": container.status,
                    "labels": container.labels
                })

            return result

        except DockerException as e:
            logger.error(f"Failed to list containers for user {user_id}: {str(e)}")
            return []

    async def _cleanup_user_volumes(self, user_id: str) -> None:
        """
        Clean up volumes associated with a user.

        Args:
            user_id: User identifier
        """
        if not DOCKER_AVAILABLE or self.client is None:
            logger.warning("Docker client not available for volume cleanup")
            return

        try:
            volume_patterns = [
                f"workspace-{user_id}-data",
                f"workspace-{user_id}-projects"
            ]

            for pattern in volume_patterns:
                try:
                    volume = await asyncio.to_thread(self.client.volumes.get, pattern)
                    await asyncio.to_thread(volume.remove)
                    logger.info(f"Removed volume: {pattern}")
                except NotFound:
                    logger.debug(f"Volume not found: {pattern}")

        except DockerException as e:
            logger.error(f"Failed to cleanup volumes for user {user_id}: {str(e)}")

    def _parse_memory_limit(self, memory_str: str) -> int:
        """
        Parse memory limit string to bytes.

        Args:
            memory_str: Memory limit string (e.g., '2G', '512M', '1024K')

        Returns:
            Memory limit in bytes
        """
        memory_str = memory_str.upper().strip()

        if memory_str.endswith('G'):
            return int(float(memory_str[:-1]) * 1024 * 1024 * 1024)
        elif memory_str.endswith('M'):
            return int(float(memory_str[:-1]) * 1024 * 1024)
        elif memory_str.endswith('K'):
            return int(float(memory_str[:-1]) * 1024)
        else:
            return int(memory_str)  # Assume bytes

    async def validate_user_container_limits(self, user_id: str) -> Dict[str, Any]:
        """
        Validate that user containers are within resource limits.

        Args:
            user_id: User identifier

        Returns:
            Validation result dictionary
        """
        try:
            containers = await self.list_user_workspaces(user_id)
            max_containers = int(os.getenv("MAX_USER_CONTAINERS", "10"))

            running_containers = [
                c for c in containers
                if c.get("status") == "running"
            ]

            return {
                "user_id": user_id,
                "total_containers": len(containers),
                "running_containers": len(running_containers),
                "max_allowed": max_containers,
                "within_limits": len(containers) <= max_containers,
                "can_create_new": len(running_containers) < max_containers
            }

        except Exception as e:
            logger.error(f"Failed to validate container limits for user {user_id}: {e}")
            return {
                "user_id": user_id,
                "error": str(e),
                "within_limits": False,
                "can_create_new": False
            }

    async def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check on Docker service.

        Returns:
            Detailed health status dictionary with metrics
        """
        health_data = {
            "status": "unknown",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "docker_available": DOCKER_AVAILABLE,
            "client_initialized": self._client_initialized,
            "initialization_error": str(self._initialization_error) if self._initialization_error else None
        }

        if not DOCKER_AVAILABLE:
            health_data.update({
                "status": "unhealthy",
                "error": "Docker SDK not available - missing docker package"
            })
            return health_data

        try:
            await self._ensure_docker_client()

            # Test Docker daemon connection with timeout
            ping_result = await asyncio.wait_for(
                asyncio.to_thread(self.client.ping),
                timeout=5.0
            )

            # Get comprehensive system info
            info = await asyncio.wait_for(
                asyncio.to_thread(self.client.info),
                timeout=10.0
            )

            # Check Traefik network
            network_status = await self._check_network_health()

            health_data.update({
                "status": "healthy",
                "ping_successful": True,
                "docker_version": info.get("ServerVersion", "unknown"),
                "api_version": info.get("ApiVersion", "unknown"),
                "containers_running": info.get("ContainersRunning", 0),
                "containers_paused": info.get("ContainersPaused", 0),
                "containers_stopped": info.get("ContainersStopped", 0),
                "containers_total": info.get("Containers", 0),
                "images": info.get("Images", 0),
                "memory_total": info.get("MemTotal", 0),
                "cpu_count": info.get("NCPU", 0),
                "storage_driver": info.get("Driver", "unknown"),
                "network_status": network_status,
                "traefik_network": self.traefik_network,
                "resource_limits_enabled": self.enable_resource_limits,
                "max_concurrent_operations": self.max_concurrent_operations
            })

        except asyncio.TimeoutError:
            health_data.update({
                "status": "unhealthy",
                "error": "Docker daemon timeout - daemon may be unresponsive"
            })
        except Exception as e:
            health_data.update({
                "status": "unhealthy",
                "error": str(e),
                "error_type": type(e).__name__
            })

        return health_data

    async def _check_network_health(self) -> Dict[str, Any]:
        """Check health of the configured Traefik network."""
        try:
            network = await asyncio.to_thread(self.client.networks.get, self.traefik_network)
            return {
                "network_exists": True,
                "network_name": network.name,
                "network_id": network.id[:12],
                "driver": network.attrs.get("Driver", "unknown"),
                "scope": network.attrs.get("Scope", "unknown"),
                "containers_connected": len(network.attrs.get("Containers", {}))
            }
        except Exception as e:
            return {
                "network_exists": False,
                "network_name": self.traefik_network,
                "error": str(e)
            }

    async def _ensure_docker_client(self):
        """Ensures the Docker client is initialized."""
        if not self._client_initialized:
            await self._initialize_docker_client()
        if self._initialization_error:
            raise RuntimeError(f"Docker client is not available: {self._initialization_error}")

    def generate_project_environment_config(self, user_id: str, project_id: str, project_path: str) -> Dict[str, Any]:
        """
        Generate workspace configuration for a specific project.
        """
        container_name = f"codeserver-user-{user_id}-project-{project_id}"
        subdomain = f"{project_id}.dev.localhost"
        router_name = f"project-{project_id}"

        return {
            "name": container_name,
            "image": "jules/code-server:latest",
            "hostname": container_name,
            "detach": True,
            "environment": {
                "DOCKER_USER": "coder",
                "USER_ID": user_id,
                "PROJECT_ID": project_id,
            },
            "labels": {
                "traefik.enable": "true",
                f"traefik.http.routers.{router_name}.rule": f"Host(`{subdomain}`)",
                f"traefik.http.routers.{router_name}.entrypoints": "web",
                f"traefik.http.services.{router_name}.loadbalancer.server.port": "8443",
                "traefik.docker.network": self.traefik_network,
                "workspace.managed_by": "ai-orchestrator",
                "workspace.user_id": str(user_id),
                "workspace.project_id": str(project_id),
                "workspace.type": "project-environment",
                "workspace.created_at": datetime.now(timezone.utc).isoformat(),
            },
            "volumes": {
                project_path: {
                    "bind": "/home/<USER>/project",
                    "mode": "rw"
                }
            },
            "network": self.traefik_network,
            "HostConfig": {
                "NanoCpus": int(settings.CONTAINER_CPU_LIMIT * 1_000_000_000),
                "Memory": settings.CONTAINER_MEMORY_LIMIT
            }
        }

    @retry_on_docker_error(max_retries=3, delay=2.0)
    async def launch_project_environment(self, user_id: str, project_id: str, project_path: str) -> Dict[str, str]:
        """
        Launch a dedicated code-server container for a specific project.
        """
        await self._ensure_docker_client()

        # Check if a container for this project already exists
        filters = {"label": f"workspace.project_id={project_id}"}
        existing_containers = await asyncio.to_thread(self.client.containers.list, all=True, filters=filters)

        if existing_containers:
            container = existing_containers[0]
            if container.status == "running":
                logger.info(f"Container for project {project_id} is already running.")
                return {"environment_url": f"http://{project_id}.dev.localhost"}
            else:
                logger.warning(f"Removing existing but non-running container {container.id} for project {project_id}.")
                await asyncio.to_thread(container.remove)

        # Create and start a new container
        logger.info(f"Launching new environment for project {project_id}...")
        config = self.generate_project_environment_config(user_id, project_id, project_path)

        try:
            container = await asyncio.to_thread(
                self.client.containers.create,
                **config
            )
            await asyncio.to_thread(container.start)

            # Health check loop
            for _ in range(30): # Wait up to 30 seconds
                await asyncio.sleep(1)
                await asyncio.to_thread(container.reload)
                if container.status == "running":
                    # A more robust health check would check an endpoint in the container
                    logger.info(f"Container for project {project_id} started successfully.")
                    return {"environment_url": f"http://{project_id}.dev.localhost"}

            # If loop finishes without container running
            logger.error(f"Container for project {project_id} failed to start in time.")
            await asyncio.to_thread(container.stop)
            await asyncio.to_thread(container.remove)
            raise RuntimeError("Failed to start container in time.")

        except DockerException as e:
            logger.error(f"Failed to launch environment for project {project_id}: {e}")
            raise

    async def list_project_environments(self, user_id: str) -> List[Dict[str, str]]:
        """
        List all running project environments for a specific user.
        """
        await self._ensure_docker_client()

        filters = {
            "label": [
                f"workspace.user_id={user_id}",
                "workspace.type=project-environment"
            ],
            "status": "running"
        }

        try:
            containers = await asyncio.to_thread(self.client.containers.list, filters=filters)
            environments = []
            for container in containers:
                project_id = container.labels.get("workspace.project_id")
                if project_id:
                    environments.append({
                        "project_id": project_id,
                        "environment_url": f"http://{project_id}.dev.localhost"
                    })
            return environments
        except DockerException as e:
            logger.error(f"Failed to list project environments for user {user_id}: {e}")
            return []


# Global Docker service instance
_docker_service: Optional[DockerWorkspaceService] = None


def get_docker_service() -> DockerWorkspaceService:
    """
    Get Docker workspace service singleton.

    Returns:
        DockerWorkspaceService instance
    """
    global _docker_service
    if _docker_service is None:
        _docker_service = DockerWorkspaceService()
    return _docker_service