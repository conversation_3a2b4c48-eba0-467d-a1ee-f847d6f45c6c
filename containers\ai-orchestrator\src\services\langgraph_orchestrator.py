"""
LangGraph Agent Orchestrator Service

Advanced agent orchestration using LangGraph for complex multi-agent workflows,
state management, and performance optimization.

Author: AI Coding Agent Team
Version: 1.0.0
"""

import logging
from typing import Dict, List, Optional, Any, TypedDict
from datetime import datetime

try:
    from langgraph import StateGraph, END, START
    from langgraph.cache.memory import InMemoryCache
    from langgraph.cache.redis import RedisCache
    from langgraph.checkpoint.memory import MemorySaver
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False

from services.enhanced_llm_service import EnhancedLLMService
from services.redis_service import get_redis_manager
from src.models.llm_models import GenerateRequest

logger = logging.getLogger(__name__)


class WorkflowState(TypedDict):
    """State for LangGraph agent workflows."""
    task: str
    context: Dict[str, Any]
    agent_results: Dict[str, Any]
    current_agent: Optional[str]
    workflow_history: List[Dict[str, Any]]
    errors: List[str]
    performance_metrics: Dict[str, float]


class AgentWorkflowOrchestrator:
    """
    Advanced agent orchestration using LangGraph for complex workflows.

    Features:
    - State management with persistence
    - Parallel agent execution
    - Error recovery and retry logic
    - Performance monitoring
    - Caching for optimization
    """

    def __init__(self):
        if not LANGGRAPH_AVAILABLE:
            raise ImportError("LangGraph not available. Install with: pip install langgraph")

        self.llm_service = EnhancedLLMService(redis_client=None)
        self.redis_manager = None
        self.graph_cache = InMemoryCache()
        self.checkpointer = MemorySaver()

        # Initialize Redis for caching if available
        try:
            self.redis_manager = get_redis_manager()
            self.redis_cache = RedisCache(self.redis_manager)
        except Exception as e:
            logger.warning(f"Redis cache not available: {e}")
            self.redis_cache = None

    async def initialize(self):
        """Initialize the orchestrator with Redis connection."""
        if self.redis_manager:
            await self.redis_manager.initialize()

    async def create_coding_workflow(self) -> StateGraph:
        """
        Create a LangGraph workflow for coding agent orchestration.

        Workflow:
        1. Task Analysis → Multiple agents in parallel
        2. Code Generation → Sequential refinement
        3. Testing & Validation → Parallel execution
        4. Deployment → Conditional based on results
        """

        # Define the workflow graph
        workflow = StateGraph(WorkflowState)

        # Add nodes for different workflow stages
        workflow.add_node("analyze_task", self._analyze_task)
        workflow.add_node("architect_agent", self._run_architect_agent)
        workflow.add_node("backend_agent", self._run_backend_agent)
        workflow.add_node("frontend_agent", self._run_frontend_agent)
        workflow.add_node("test_agents", self._run_test_agents)
        workflow.add_node("validate_results", self._validate_results)
        workflow.add_node("deploy_solution", self._deploy_solution)

        # Define workflow edges
        workflow.add_edge(START, "analyze_task")
        workflow.add_edge("analyze_task", "architect_agent")

        # Parallel execution of specialized agents
        workflow.add_edge("architect_agent", "backend_agent")
        workflow.add_edge("architect_agent", "frontend_agent")

        # Sequential validation and deployment
        workflow.add_edge("backend_agent", "test_agents")
        workflow.add_edge("frontend_agent", "test_agents")
        workflow.add_edge("test_agents", "validate_results")
        workflow.add_edge("validate_results", "deploy_solution")
        workflow.add_edge("deploy_solution", END)

        # Add conditional edges for error handling
        workflow.add_conditional_edges(
            "validate_results",
            self._should_retry_or_deploy,
            {
                "retry": "architect_agent",
                "deploy": "deploy_solution",
                "fail": END
            }
        )

        # Compile with caching and checkpointer
        compiled_graph = workflow.compile(
            cache=self.graph_cache,
            checkpointer=self.checkpointer
        )

        return compiled_graph

    async def _run_agent_step(
        self,
        state: WorkflowState,
        agent_name: str,
        prompt: str,
        result_key: str,
        temperature: float,
        max_tokens: int,
    ) -> Dict[str, Any]:
        """Helper to run a single agent step using the LLM service."""
        logger.info(f"Running agent: {agent_name}")
        try:
            request = GenerateRequest(
                prompt=prompt,
                model="llama3.1:7b",
                temperature=temperature,
                max_tokens=max_tokens,
            )
            result_response = await self.llm_service.generate(request)
            result = result_response.content

            return {
                "agent_results": {
                    **state.get("agent_results", {}),
                    agent_name: {result_key: result, "status": "completed"},
                },
                "current_agent": agent_name,
            }
        except Exception as e:
            logger.error(f"{agent_name.capitalize()} agent failed: {e}")
            return {
                "errors": state.get("errors", []) + [f"{agent_name.capitalize()} failed: {str(e)}"],
                "agent_results": state.get("agent_results", {}),
            }

    async def _analyze_task(self, state: WorkflowState) -> Dict[str, Any]:
        """Analyze the incoming task and prepare context."""
        start_time = datetime.now()

        try:
            # Use LLM to analyze task complexity and requirements
            analysis_prompt = f"""
            Analyze this coding task and determine the required agents and approach:

            Task: {state['task']}

            Provide analysis in JSON format:
            {{
                "complexity": "simple|medium|complex",
                "required_agents": ["architect", "backend", "frontend", "testing"],
                "estimated_time": "in minutes",
                "risk_factors": ["list", "of", "risks"],
                "approach": "description of approach"
            }}
            """

            request = GenerateRequest(
                prompt=analysis_prompt,
                model="llama3.1:7b",
                temperature=0.1,
                max_tokens=1000
            )

            analysis_response = await self.llm_service.generate(request)
            analysis = analysis_response.content

            # Update performance metrics
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            return {
                "context": {
                    "analysis": analysis,
                    "start_time": start_time.isoformat(),
                    "analysis_duration": duration
                },
                "performance_metrics": {
                    "task_analysis_time": duration
                }
            }

        except Exception as e:
            logger.error(f"Task analysis failed: {e}")
            return {
                "errors": state.get("errors", []) + [f"Analysis failed: {str(e)}"],
                "context": state.get("context", {})
            }

    async def _run_architect_agent(self, state: WorkflowState) -> Dict[str, Any]:
        """Run the architect agent for high-level design."""
        architect_prompt = f"""
        As a software architect, analyze this task and provide high-level design:

        Task: {state["task"]}
        Context: {state.get("context", {}).get("analysis", "")}

        Provide architectural design in JSON format:
        {{
            "architecture_type": "web|api|desktop|mobile",
            "components": ["list", "of", "components"],
            "technologies": ["list", "of", "technologies"],
            "design_patterns": ["list", "of", "patterns"],
            "estimated_complexity": "high|medium|low"
        }}
        """
        return await self._run_agent_step(
            state, "architect", architect_prompt, "design", 0.2, 1500
        )

    async def _run_backend_agent(self, state: WorkflowState) -> Dict[str, Any]:
        """Run backend agent for implementation."""
        backend_prompt = f"""
        As a backend developer, implement the following based on the architecture:

        Task: {state["task"]}
        Architecture: {state["agent_results"].get("architect", {}).get("design", "")}

        Provide backend implementation details in JSON format:
        {{
            "api_endpoints": ["list", "of", "endpoints"],
            "database_schema": "schema description",
            "business_logic": "implementation approach",
            "security_measures": ["list", "of", "measures"],
            "code_structure": "folder/file structure"
        }}
        """
        return await self._run_agent_step(
            state, "backend", backend_prompt, "implementation", 0.3, 2000
        )

    async def _run_frontend_agent(self, state: WorkflowState) -> Dict[str, Any]:
        """Run frontend agent for UI implementation."""
        frontend_prompt = f"""
        As a frontend developer, create the user interface based on the backend API:

        Task: {state["task"]}
        Backend API: {state["agent_results"].get("backend", {}).get("implementation", "")}
        Architecture: {state["agent_results"].get("architect", {}).get("design", "")}

        Provide frontend implementation details in JSON format:
        {{
            "ui_components": ["list", "of", "components"],
            "pages_routes": ["list", "of", "routes"],
            "styling_approach": "CSS framework or approach",
            "user_experience": "UX considerations",
            "responsive_design": "mobile/desktop considerations"
        }}
        """
        return await self._run_agent_step(
            state, "frontend", frontend_prompt, "ui_design", 0.3, 2000
        )

    async def _run_test_agents(self, state: WorkflowState) -> Dict[str, Any]:
        """Run testing agents for validation."""
        test_prompt = f"""
        As a QA engineer, create comprehensive tests for the implementation:

        Task: {state["task"]}
        Backend Implementation: {state["agent_results"].get("backend", {}).get("implementation", "")}
        Frontend Design: {state["agent_results"].get("frontend", {}).get("ui_design", "")}

        Provide testing strategy in JSON format:
        {{
            "unit_tests": ["list", "of", "unit", "tests"],
            "integration_tests": ["list", "of", "integration", "tests"],
            "ui_tests": ["list", "of", "UI", "tests"],
            "performance_tests": ["list", "of", "performance", "tests"],
            "test_coverage": "expected coverage percentage"
        }}
        """
        return await self._run_agent_step(
            state, "testing", test_prompt, "test_plan", 0.2, 1500
        )

    async def _validate_results(self, state: WorkflowState) -> Dict[str, Any]:
        """Validate all agent results and determine next steps."""
        try:
            validation_prompt = f"""
            Validate the following agent results for task completion:

            Task: {state['task']}
            Architect Results: {state['agent_results'].get('architect', {})}
            Backend Results: {state['agent_results'].get('backend', {})}
            Frontend Results: {state['agent_results'].get('frontend', {})}
            Test Results: {state['agent_results'].get('testing', {})}

            Provide validation in JSON format:
            {{
                "overall_quality": "excellent|good|needs_improvement|poor",
                "completion_percentage": 0-100,
                "critical_issues": ["list", "of", "issues"],
                "recommendations": ["list", "of", "recommendations"],
                "ready_for_deployment": true|false
            }}
            """

            request = GenerateRequest(
                prompt=validation_prompt,
                model="llama3.1:7b",
                temperature=0.1,
                max_tokens=1000
            )

            validation_response = await self.llm_service.generate(request)
            validation = validation_response.content

            return {
                "context": {
                    **state.get("context", {}),
                    "validation": validation
                }
            }

        except Exception as e:
            logger.error(f"Validation failed: {e}")
            return {
                "errors": state.get("errors", []) + [f"Validation failed: {str(e)}"],
                "context": state.get("context", {})
            }

    async def _deploy_solution(self, state: WorkflowState) -> Dict[str, Any]:
        """Deploy the solution if validation passes."""
        deploy_prompt = f"""
        As a DevOps engineer, create a deployment strategy for the application:

        Task: {state["task"]}
        Architecture: {state["agent_results"].get("architect", {}).get("design", "")}
        Implementation: {state["agent_results"].get("backend", {}).get("implementation", "")}
        UI Design: {state["agent_results"].get("frontend", {}).get("ui_design", "")}
        Test Plan: {state["agent_results"].get("testing", {}).get("test_plan", "")}
        Validation: {state["context"].get("validation", "")}

        Provide deployment strategy in JSON format:
        {{
            "deployment_platform": "docker|kubernetes|cloud",
            "ci_cd_pipeline": "pipeline description",
            "infrastructure_requirements": ["list", "of", "requirements"],
            "monitoring_setup": "monitoring strategy",
            "rollback_plan": "rollback procedures"
        }}
        """
        return await self._run_agent_step(
            state, "deployment", deploy_prompt, "strategy", 0.1, 1500
        )

    def _should_retry_or_deploy(self, state: WorkflowState) -> str:
        """Determine whether to retry, deploy, or fail based on validation."""
        try:
            validation = state.get("context", {}).get("validation", "{}")
            # Simple heuristic - in production, parse the JSON validation
            if "excellent" in validation or "good" in validation:
                return "deploy"
            elif "needs_improvement" in validation:
                return "retry"
            else:
                return "fail"
        except:
            return "fail"

    async def execute_workflow(self, task: str, thread_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Execute the complete coding workflow using LangGraph.

        Args:
            task: The coding task to execute
            thread_id: Optional thread ID for persistence

        Returns:
            Dict containing workflow results
        """
        try:
            # Create the workflow graph
            graph = await self.create_coding_workflow()

            # Prepare initial state
            initial_state = WorkflowState(
                task=task,
                context={},
                agent_results={},
                current_agent=None,
                workflow_history=[],
                errors=[],
                performance_metrics={}
            )

            # Configure execution
            config = {"configurable": {"thread_id": thread_id or f"workflow_{datetime.now().isoformat()}"}}

            # Execute the workflow
            final_state = await graph.ainvoke(initial_state, config=config)

            return {
                "success": len(final_state.get("errors", [])) == 0,
                "results": final_state.get("agent_results", {}),
                "errors": final_state.get("errors", []),
                "performance": final_state.get("performance_metrics", {}),
                "thread_id": config["configurable"]["thread_id"]
            }

        except Exception as e:
            logger.error(f"Workflow execution failed: {e}")
            return {
                "success": False,
                "results": {},
                "errors": [str(e)],
                "performance": {},
                "thread_id": thread_id
            }


# Global orchestrator instance
_orchestrator_instance = None

async def get_langgraph_orchestrator() -> AgentWorkflowOrchestrator:
    """Get or create the global LangGraph orchestrator instance."""
    global _orchestrator_instance

    if _orchestrator_instance is None:
        _orchestrator_instance = AgentWorkflowOrchestrator()
        await _orchestrator_instance.initialize()

    return _orchestrator_instance