#!/bin/bash
# Enhanced VS Code extension installer for code-server
# Author: AI Coding Agent Team
# Version: 2.0.0

set -e

echo "[INFO] Starting VS Code extension installation..."

# Function to log with timestamp
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Function to install extension with retry logic
install_extension() {
    local ext=$1
    local max_retries=3
    local retry=0

    while [ $retry -lt $max_retries ]; do
        log "INFO: Installing extension: $ext (attempt $((retry + 1))/$max_retries)"

        if /usr/lib/code-server/bin/code-server --install-extension "$ext" --force; then
            log "SUCCESS: Extension $ext installed successfully"
            return 0
        else
            retry=$((retry + 1))
            if [ $retry -lt $max_retries ]; then
                log "WARNING: Failed to install $ext, retrying in 5 seconds..."
                sleep 5
            else
                log "ERROR: Failed to install $ext after $max_retries attempts"
                return 1
            fi
        fi
    done
}

# Check multiple sources for extensions
extensions_found=false

# Method 1: Check for extensions.json (new method)
if [ -f "/home/<USER>/extensions.json" ]; then
    log "INFO: Found extensions.json, parsing recommended extensions"

    # Extract recommended extensions using jq if available, fallback to grep
    if command -v jq >/dev/null 2>&1; then
        extensions=$(jq -r '.recommendations[]?' /home/<USER>/extensions.json 2>/dev/null || echo "")
    else
        # Fallback parsing without jq
        extensions=$(grep -o '"[^"]*"' /home/<USER>/extensions.json | grep -E '^"[a-zA-Z0-9\.-]+\.[a-zA-Z0-9\.-]+"$' | tr -d '"' || echo "")
    fi

    if [ -n "$extensions" ]; then
        extensions_found=true
        log "INFO: Found $(echo "$extensions" | wc -l) extensions in extensions.json"
    fi
fi

# Method 2: Check for requirements.txt (legacy method)
if [ "$extensions_found" = false ] && [ -f "/home/<USER>/extensions/requirements.txt" ]; then
    log "INFO: Found requirements.txt, reading extensions"
    extensions=$(grep -v '^#' /home/<USER>/extensions/requirements.txt | grep -v '^$' || echo "")

    if [ -n "$extensions" ]; then
        extensions_found=true
        log "INFO: Found $(echo "$extensions" | wc -l) extensions in requirements.txt"
    fi
fi

# Exit if no extensions found
if [ "$extensions_found" = false ]; then
    log "WARNING: No extension files found, skipping installation"
    exit 0
fi

# Install extensions
current=0
failed_extensions=""
total_extensions=$(echo "$extensions" | wc -l)

log "INFO: Installing $total_extensions extensions..."

for ext in $extensions; do
    # Skip empty lines
    [ -z "$ext" ] && continue

    current=$((current + 1))
    log "INFO: Processing extension $current/$total_extensions: $ext"

    if ! install_extension "$ext"; then
        failed_extensions="$failed_extensions $ext"
    fi
done

# Summary
if [ -n "$failed_extensions" ]; then
    log "WARNING: Some extensions failed to install:$failed_extensions"
    log "INFO: Extension installation completed with warnings"
    exit 1
else
    log "SUCCESS: All $current extensions installed successfully"
    exit 0
fi
