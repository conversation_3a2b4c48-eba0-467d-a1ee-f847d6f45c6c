import React, { useState, useEffect } from 'react';
import { Save, Settings, AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';

const AdminDashboard = () => {
  const [configurations, setConfigurations] = useState({});
  const [providers, setProviders] = useState([]);
  const [models, setModels] = useState({});
  const [loading, setLoading] = useState(false);
  const [saveStatus, setSaveStatus] = useState('');

  // Agent roles in your system
  const agentRoles = [
    { id: 'architect', name: 'Architect Agent', description: 'System design and planning' },
    { id: 'backend', name: 'Backend Agent', description: 'Server-side development' },
    { id: 'frontend', name: 'Frontend Agent', description: 'UI/UX development' },
    { id: 'shell', name: 'Shell Agent', description: 'Command execution' },
    { id: 'fixer', name: 'Issue Fixer Agent', description: 'Bug fixes and debugging' },
    { id: 'test', name: 'Test Agent', description: 'Testing and validation' },
    { id: 'security', name: 'Security Agent', description: 'Security analysis' },
  ];

  // Available providers and their models
  const providerModels = {
    'openai': {
      name: 'OpenAI',
      models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4o', 'gpt-4o-mini'],
      status: 'connected'
    },
    'anthropic': {
      name: 'Anthropic',
      models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku', 'claude-3.5-sonnet'],
      status: 'connected'
    },
    'openrouter': {
      name: 'OpenRouter',
      models: ['meta-llama/llama-3.1-70b', 'anthropic/claude-3.5-sonnet', 'google/gemini-pro-1.5', 'mistral/mixtral-8x7b'],
      status: 'connected'
    },
    'ollama': {
      name: 'Ollama (Local)',
      models: ['llama3.1:8b', 'codellama:7b', 'mistral:7b', 'deepseek-coder:6.7b'],
      status: 'connected'
    },
    'groq': {
      name: 'Groq',
      models: ['llama-3.1-70b', 'llama-3.1-8b', 'mixtral-8x7b', 'gemma2-9b'],
      status: 'connected'
    }
  };

  // Initialize configurations
  useEffect(() => {
    const defaultConfigs = {};
    agentRoles.forEach(role => {
      defaultConfigs[role.id] = {
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        max_tokens: 2048,
        fallback_provider: 'anthropic',
        fallback_model: 'claude-3-sonnet',
        enabled: true
      };
    });
    setConfigurations(defaultConfigs);
    setProviders(Object.keys(providerModels));
    setModels(providerModels);
  }, []);

  const handleConfigChange = (roleId, field, value) => {
    setConfigurations(prev => ({
      ...prev,
      [roleId]: {
        ...prev[roleId],
        [field]: value
      }
    }));
  };

  const handleSaveConfigurations = async () => {
    setLoading(true);
    setSaveStatus('saving');

    try {
      // Simulate API call to save configurations
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Here you would make actual API call to your backend
      // await fetch('/api/admin/agent-configurations', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(configurations)
      // });

      setSaveStatus('success');
      setTimeout(() => setSaveStatus(''), 3000);
    } catch (error) {
      setSaveStatus('error');
      setTimeout(() => setSaveStatus(''), 3000);
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async (provider) => {
    setLoading(true);
    try {
      // Simulate connection test
      await new Promise(resolve => setTimeout(resolve, 1000));
      // Update provider status
      setModels(prev => ({
        ...prev,
        [provider]: {
          ...prev[provider],
          status: 'connected'
        }
      }));
    } catch (error) {
      setModels(prev => ({
        ...prev,
        [provider]: {
          ...prev[provider],
          status: 'error'
        }
      }));
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error': return <AlertCircle className="w-4 h-4 text-red-500" />;
      default: return <RefreshCw className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Settings className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Agent Model Configuration</h1>
                <p className="text-gray-600">Manage LLM providers and models for each agent role</p>
              </div>
            </div>
            <button
              onClick={handleSaveConfigurations}
              disabled={loading}
              className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              <Save className="w-4 h-4" />
              <span>{loading ? 'Saving...' : 'Save Configuration'}</span>
            </button>
          </div>

          {/* Save Status */}
          {saveStatus && (
            <div className={`mt-4 p-3 rounded-lg flex items-center space-x-2 ${
              saveStatus === 'success' ? 'bg-green-50 text-green-800' :
              saveStatus === 'error' ? 'bg-red-50 text-red-800' :
              'bg-blue-50 text-blue-800'
            }`}>
              {saveStatus === 'success' && <CheckCircle className="w-4 h-4" />}
              {saveStatus === 'error' && <AlertCircle className="w-4 h-4" />}
              {saveStatus === 'saving' && <RefreshCw className="w-4 h-4 animate-spin" />}
              <span>
                {saveStatus === 'success' && 'Configuration saved successfully!'}
                {saveStatus === 'error' && 'Failed to save configuration. Please try again.'}
                {saveStatus === 'saving' && 'Saving configuration...'}
              </span>
            </div>
          )}
        </div>

        {/* Provider Status Panel */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Provider Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {Object.entries(models).map(([providerId, provider]) => (
              <div key={providerId} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-sm">{provider.name}</span>
                  {getStatusIcon(provider.status)}
                </div>
                <button
                  onClick={() => testConnection(providerId)}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  Test Connection
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Agent Configurations */}
        <div className="space-y-6">
          {agentRoles.map(role => {
            const config = configurations[role.id] || {};
            return (
              <div key={role.id} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{role.name}</h3>
                    <p className="text-sm text-gray-600">{role.description}</p>
                  </div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.enabled || false}
                      onChange={(e) => handleConfigChange(role.id, 'enabled', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700">Enabled</span>
                  </label>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* Primary Provider */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Primary Provider
                    </label>
                    <select
                      value={config.provider || ''}
                      onChange={(e) => handleConfigChange(role.id, 'provider', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                      {providers.map(providerId => (
                        <option key={providerId} value={providerId}>
                          {models[providerId]?.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Primary Model */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Primary Model
                    </label>
                    <select
                      value={config.model || ''}
                      onChange={(e) => handleConfigChange(role.id, 'model', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                      {config.provider && models[config.provider]?.models.map(model => (
                        <option key={model} value={model}>{model}</option>
                      ))}
                    </select>
                  </div>

                  {/* Fallback Provider */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fallback Provider
                    </label>
                    <select
                      value={config.fallback_provider || ''}
                      onChange={(e) => handleConfigChange(role.id, 'fallback_provider', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                      {providers.map(providerId => (
                        <option key={providerId} value={providerId}>
                          {models[providerId]?.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Fallback Model */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Fallback Model
                    </label>
                    <select
                      value={config.fallback_model || ''}
                      onChange={(e) => handleConfigChange(role.id, 'fallback_model', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                      {config.fallback_provider && models[config.fallback_provider]?.models.map(model => (
                        <option key={model} value={model}>{model}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Advanced Settings */}
                <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Temperature: {config.temperature || 0.7}
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="2"
                      step="0.1"
                      value={config.temperature || 0.7}
                      onChange={(e) => handleConfigChange(role.id, 'temperature', parseFloat(e.target.value))}
                      className="w-full"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Max Tokens
                    </label>
                    <input
                      type="number"
                      value={config.max_tokens || 2048}
                      onChange={(e) => handleConfigChange(role.id, 'max_tokens', parseInt(e.target.value))}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                      min="256"
                      max="8192"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Cost Priority
                    </label>
                    <select
                      value={config.cost_priority || 'balanced'}
                      onChange={(e) => handleConfigChange(role.id, 'cost_priority', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                      <option value="cost_effective">Cost Effective</option>
                      <option value="balanced">Balanced</option>
                      <option value="performance">Performance</option>
                    </select>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Export/Import Configuration */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Configuration Management</h2>
          <div className="flex space-x-4">
            <button
              onClick={() => {
                const dataStr = JSON.stringify(configurations, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = 'agent-configurations.json';
                link.click();
              }}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              Export Configuration
            </button>
            <input
              type="file"
              accept=".json"
              onChange={(e) => {
                const file = e.target.files[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = (event) => {
                    try {
                      const imported = JSON.parse(event.target.result);
                      setConfigurations(imported);
                    } catch (error) {
                      alert('Invalid configuration file');
                    }
                  };
                  reader.readAsText(file);
                }
              }}
              className="hidden"
              id="import-config"
            />
            <label
              htmlFor="import-config"
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 cursor-pointer"
            >
              Import Configuration
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;