/**
 * useRoles Hook
 * Custom hook for managing role configurations state and API interactions
 */

import { useCallback, useEffect, useState } from "react";
import { roleApi } from "../lib/api";
import type {
  ApiError,
  RoleConfiguration,
  RoleConfigurationUpdate,
} from "../types/role";
import { LoadingState } from "../types/api";
import type { AsyncState } from "../types/api";

interface UseRolesReturn {
  // State
  roles: Record<string, RoleConfiguration>;
  loading: LoadingState;
  error: ApiError | null;

  // Actions
  refreshRoles: () => Promise<void>;
  createRole: (
    roleName: string,
    configuration: Omit<RoleConfiguration, "created_at" | "updated_at">,
  ) => Promise<boolean>;
  updateRole: (
    roleName: string,
    updates: RoleConfigurationUpdate,
  ) => Promise<boolean>;
  deleteRole: (roleName: string) => Promise<boolean>;
  clearError: () => void;

  // Utilities
  getRoleNames: () => string[];
  getRoleByName: (roleName: string) => RoleConfiguration | null;
  isRoleEnabled: (roleName: string) => boolean;
}

/**
 * Custom hook for role management
 */
export const useRoles = (): UseRolesReturn => {
  const [state, setState] = useState<
    AsyncState<Record<string, RoleConfiguration>>
  >({
    data: {},
    loading: LoadingState.IDLE,
    error: null,
  });

  /**
   * Load all roles from the API
   */
  const refreshRoles = useCallback(async (): Promise<void> => {
    setState((prev) => ({
      ...prev,
      loading: LoadingState.LOADING,
      error: null,
    }));

    try {
      const response = await roleApi.getRoles();

      if (response.error) {
        setState((prev) => ({
          ...prev,
          loading: LoadingState.ERROR,
          error: response.error!,
        }));
        return;
      }

      setState((prev) => ({
        ...prev,
        data: response.data?.roles || {},
        loading: LoadingState.SUCCESS,
        error: null,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: LoadingState.ERROR,
        error: {
          message: "Failed to fetch roles",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
    }
  }, []);

  /**
   * Create a new role
   */
  const createRole = useCallback(async (
    roleName: string,
    configuration: Omit<RoleConfiguration, "created_at" | "updated_at">,
  ): Promise<boolean> => {
    try {
      const response = await roleApi.createRole(roleName, configuration);

      if (response.error) {
        setState((prev) => ({ ...prev, error: response.error! }));
        return false;
      }

      // Add the new role to local state
      if (response.data?.configuration) {
        setState((prev) => ({
          ...prev,
          data: {
            ...prev.data,
            [roleName]: response.data!.configuration!,
          },
          error: null,
        }));
      }

      return true;
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: {
          message: "Failed to create role",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
      return false;
    }
  }, []);

  /**
   * Update an existing role
   */
  const updateRole = useCallback(async (
    roleName: string,
    updates: RoleConfigurationUpdate,
  ): Promise<boolean> => {
    try {
      const response = await roleApi.updateRole(roleName, updates);

      if (response.error) {
        setState((prev) => ({ ...prev, error: response.error! }));
        return false;
      }

      // Update the role in local state
      if (response.data?.configuration) {
        setState((prev) => ({
          ...prev,
          data: {
            ...prev.data,
            [roleName]: response.data!.configuration!,
          },
          error: null,
        }));
      }

      return true;
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: {
          message: "Failed to update role",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
      return false;
    }
  }, []);

  /**
   * Delete a role
   */
  const deleteRole = useCallback(async (roleName: string): Promise<boolean> => {
    try {
      const response = await roleApi.deleteRole(roleName);

      if (response.error) {
        setState((prev) => ({ ...prev, error: response.error! }));
        return false;
      }

      // Remove the role from local state
      setState((prev) => {
        const newData = { ...prev.data };
        delete newData[roleName];
        return {
          ...prev,
          data: newData,
          error: null,
        };
      });

      return true;
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: {
          message: "Failed to delete role",
          details: error instanceof Error ? error.message : "Unknown error",
        },
      }));
      return false;
    }
  }, []);

  /**
   * Clear the current error
   */
  const clearError = useCallback((): void => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  /**
   * Get list of role names
   */
  const getRoleNames = useCallback((): string[] => {
    return Object.keys(state.data || {});
  }, [state.data]);

  /**
   * Get a specific role by name
   */
  const getRoleByName = useCallback(
    (roleName: string): RoleConfiguration | null => {
      return state.data?.[roleName] || null;
    },
    [state.data],
  );

  /**
   * Check if a role is enabled
   */
  const isRoleEnabled = useCallback((roleName: string): boolean => {
    const role = state.data?.[roleName];
    return role?.enabled !== false; // Default to true if undefined
  }, [state.data]);

  // Load roles on mount
  useEffect(() => {
    refreshRoles();
  }, [refreshRoles]);

  return {
    // State
    roles: state.data || {},
    loading: state.loading,
    error: state.error,

    // Actions
    refreshRoles,
    createRole,
    updateRole,
    deleteRole,
    clearError,

    // Utilities
    getRoleNames,
    getRoleByName,
    isRoleEnabled,
  };
};

export default useRoles;
