#!/usr/bin/env python3
"""
Check all tables in PostgreSQL database
"""
from sqlalchemy import create_engine, text
import os

def main():
    url = os.environ.get('DATABASE_URL')
    if not url:
        print("DATABASE_URL not found")
        return

    engine = create_engine(url)
    with engine.connect() as conn:
        result = conn.execute(text("SELECT tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename"))
        print('Tables in database:')
        for row in result:
            print(f'  {row[0]}')

if __name__ == '__main__':
    main()
