#!/usr/bin/env bash

# Jules Environment Setup Script - Fixed for sandbox compatibility
# Handles BASH_SOURCE unbound variable issues

set -euo pipefail

# Configuration with safe defaults
PYTHON_MIN_MAJOR=3
PYTHON_MIN_MINOR=10

# Safe directory detection that works in all environments
if [[ "${BASH_SOURCE:-}" ]]; then
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
else
    SCRIPT_DIR="$(pwd)"
fi

# Handle Jules environment where we're already in /app
if [[ "$SCRIPT_DIR" == "/app" ]]; then
    PROJECT_ROOT="/app"
elif [[ -f "$SCRIPT_DIR/../AGENTS.md" ]]; then
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
else
    PROJECT_ROOT="$SCRIPT_DIR"
fi

LOG_FILE="${PROJECT_ROOT}/setup.log"

# Simple logging functions
log_info() {
    echo "[INFO] $*" | tee -a "$LOG_FILE"
}

log_success() {
    echo "[OK] $*" | tee -a "$LOG_FILE"
}

log_warn() {
    echo "[WARN] $*" | tee -a "$LOG_FILE"
}

log_error() {
    echo "[ERROR] $*" | tee -a "$LOG_FILE"
    return 1
}

# Utility functions
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

python_version_check() {
    local python_cmd="$1"
    if ! $python_cmd -c "import sys; exit(0 if sys.version_info >= ($PYTHON_MIN_MAJOR, $PYTHON_MIN_MINOR) else 1)" 2>/dev/null; then
        return 1
    fi
    return 0
}

# Main setup function
setup_jules_environment() {
    log_info "Starting Jules Environment Setup"
    log_info "Project root: $PROJECT_ROOT"
    log_info "Script directory: $SCRIPT_DIR"

    cd "$PROJECT_ROOT" || {
        log_error "Cannot access project root directory: $PROJECT_ROOT"
        return 1
    }

    # Check if we're in the right place
    if [[ ! -f "AGENTS.md" ]] && [[ ! -d "containers" ]]; then
        log_warn "Project structure not found in $PROJECT_ROOT"
        log_info "Looking for project files..."
        if [[ -f "/app/AGENTS.md" ]]; then
            PROJECT_ROOT="/app"
            cd "$PROJECT_ROOT"
            log_info "Found project in /app"
        fi
    fi

    setup_python_environment || return 1
    install_dependencies || return 1
    setup_environment_config || return 1
    validate_environment || return 1

    log_success "Jules environment setup completed successfully"
    print_usage_info
    return 0
}

setup_python_environment() {
    log_info "Setting up Python environment..."

    # Find suitable Python interpreter
    local python_cmd=""
    for cmd in python3.12 python3.11 python3.10 python3 python; do
        if command_exists "$cmd" && python_version_check "$cmd"; then
            python_cmd="$cmd"
            local version
            version=$($python_cmd -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}')" 2>/dev/null)
            log_success "Found suitable Python: $cmd (version $version)"
            break
        fi
    done

    if [[ -z "$python_cmd" ]]; then
        log_error "No suitable Python interpreter found (>= $PYTHON_MIN_MAJOR.$PYTHON_MIN_MINOR required)"
        return 1
    fi

    # In Jules, we might already have the right Python active
    if command -v python >/dev/null 2>&1 && python_version_check python; then
        log_success "Using active Python environment"
        return 0
    fi

    # Create virtual environment if it doesn't exist and we're not in one
    if [[ -z "${VIRTUAL_ENV:-}" ]] && [[ ! -d ".venv" ]]; then
        log_info "Creating virtual environment..."
        $python_cmd -m venv .venv || {
            log_warn "Failed to create virtual environment, continuing with system Python"
            return 0
        }
        log_success "Virtual environment created"
    fi

    # Activate virtual environment if it exists and we're not in one
    if [[ -z "${VIRTUAL_ENV:-}" ]] && [[ -f ".venv/bin/activate" ]]; then
        source .venv/bin/activate || {
            log_warn "Failed to activate virtual environment, using system Python"
        }
        log_success "Virtual environment activated"
    fi

    # Upgrade pip
    python -m pip install --upgrade pip --quiet || {
        log_warn "pip upgrade failed, continuing anyway"
    }

    return 0
}

install_dependencies() {
    log_info "Installing dependencies..."

    # Install minimal required tools
    log_info "Installing core tools..."
    python -m pip install --quiet --no-warn-script-location \
        wheel \
        python-dotenv || {
        log_warn "Some core tools failed to install"
    }

    # Try to install development tools
    python -m pip install --quiet --no-warn-script-location \
        pytest \
        black \
        ruff || {
        log_warn "Some development tools failed to install, continuing anyway"
    }

    # Install project dependencies if they exist
    local requirements_files=(
        "containers/ai-orchestrator/requirements.txt"
        "requirements-dev.txt"
    )

    for req_file in "${requirements_files[@]}"; do
        if [[ -f "$req_file" ]]; then
            log_info "Installing dependencies from $req_file..."
            python -m pip install --quiet --no-warn-script-location -r "$req_file" || {
                log_warn "Some dependencies from $req_file failed to install"
            }
        else
            log_info "Requirements file not found: $req_file"
        fi
    done

    return 0
}

setup_environment_config() {
    log_info "Setting up environment configuration..."

    # Create minimal .env file
    if [[ ! -f ".env" ]]; then
        cat > .env << 'EOF'
ENVIRONMENT=development
LOG_LEVEL=INFO
PYTHONPATH=./containers/ai-orchestrator/src
DATABASE_URL=sqlite:///./data/dev.db
REDIS_URL=redis://localhost:6379/0
JWT_SECRET=dev_jwt_secret
USE_SUPABASE=false
ENABLE_CUDA=false
EOF
        log_success "Environment file created"
    else
        log_info "Environment file already exists"
    fi

    # Ensure PYTHONPATH is set
    if ! grep -q "PYTHONPATH=" .env 2>/dev/null; then
        echo "PYTHONPATH=./containers/ai-orchestrator/src" >> .env
        log_success "PYTHONPATH added to environment"
    fi

    # Create necessary directories
    for dir in data logs; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir" 2>/dev/null || log_warn "Could not create $dir directory"
        fi
    done

    return 0
}

validate_environment() {
    log_info "Validating environment setup..."

    # Test basic Python functionality
    if python -c "import sys, os, json" 2>/dev/null; then
        log_success "Python basic imports working"
    else
        log_warn "Python basic imports failed"
    fi

    # Check for project structure
    if [[ -f "AGENTS.md" ]]; then
        log_success "Found AGENTS.md - project structure confirmed"
    else
        log_warn "AGENTS.md not found - may not be in project root"
    fi

    if [[ -d "containers/ai-orchestrator" ]]; then
        log_success "Found ai-orchestrator directory"
    else
        log_warn "ai-orchestrator directory not found"
    fi

    # Test environment file
    if [[ -f ".env" ]]; then
        log_success "Environment configuration file exists"
    else
        log_warn "Environment configuration file missing"
    fi

    return 0
}

print_usage_info() {
    echo
    log_info "Environment setup completed!"
    echo
    echo "Project is ready for development."
    echo "Key files and directories:"
    echo "  - AGENTS.md (architecture documentation)"
    echo "  - containers/ai-orchestrator/ (main application)"
    echo "  - .env (environment configuration)"
    echo
    if [[ -f ".venv/bin/activate" ]]; then
        echo "To activate virtual environment:"
        echo "  source .venv/bin/activate"
        echo
    fi
    echo "Ready to proceed with development tasks!"
}

# Safe execution that won't break in different environments
main() {
    setup_jules_environment
    local setup_exit_code=$?
    if [[ $setup_exit_code -eq 0 ]]; then
        echo "[COMPLETE] Setup finished successfully"
    else
        echo "[COMPLETE] Setup finished with warnings (code: $setup_exit_code)"
    fi
    # Never exit the shell - always return to prompt
}

# Only run if script is executed directly, handle BASH_SOURCE safely
if [[ "${BASH_SOURCE[0]:-$0}" == "${0}" ]]; then
    main
fi