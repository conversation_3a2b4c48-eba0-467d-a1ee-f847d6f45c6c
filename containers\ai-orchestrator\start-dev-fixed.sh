#!/bin/bash
#
# Corrected startup script for the AI Orchestrator
#
# This script addresses two critical issues:
# 1. PYTHONPATH: It sets the PYTHONPATH to the current directory, allowing
#    the application to use absolute imports from the 'src' directory as
#    required by the AGENTS.md development conventions.
# 2. Environment Variables: It ensures that the Python application can find
#    and load the .env file.

# Set the script to exit immediately if a command exits with a non-zero status.
set -e

echo "[INFO] Installing pysqlite3-binary for ChromaDB compatibility..."
pip install pysqlite3-binary --quiet

# Get the directory where the script is located, which should be the root of the ai-orchestrator container.
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Set PYTHONPATH to the script's directory. This makes 'src' a top-level package.
export PYTHONPATH=$SCRIPT_DIR

echo "[INFO] PYTHONPATH set to: $PYTHONPATH"
echo "[INFO] Current working directory: $(pwd)"
echo "[INFO] Launching AI Orchestrator..."

# Run the main application using uvicorn.
# The application itself is responsible for loading the .env file.
exec python3 -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
