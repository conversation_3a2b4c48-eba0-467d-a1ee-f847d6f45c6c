# Docker Hub Rate Limit Avoidance Guide

This guide provides strategies to avoid Docker Hub rate limits when using the AI
Coding Agent project.

## Quick Start

1. **Use the provided environment file**:
   ```bash
   cp .env.dockerhub .env
   ```

2. **Authenticate with Docker Hub** (if you have an account):
   ```bash
   docker login
   ```

3. **Start services with alternative registries**:
   ```bash
   docker-compose --env-file .env.dockerhub up -d
   ```

## Alternative Container Registries

The project supports alternative container registries to avoid Docker Hub rate
limits:

### Redis

- **Docker Hub**: `redis:7-alpine` (default)
- **Bitnami**: `bitnami/redis:7.2` (alternative)

### PostgreSQL

- **Docker Hub**: `postgres:15-alpine` (default)
- **CrunchyData**: `crunchydata/crunchy-postgres:15` (alternative - may require
  authentication)
- **Specific Version**: `postgres:15.5-alpine` (better caching than latest tag)

### Nginx

- **Docker Hub**: `nginx:alpine` (default)
- **Specific version**: `nginx:1.25-alpine` (better caching)

### Monitoring Stack

- **Promtail**: `grafana/promtail:2.9.0` (default)
- Can be configured via environment variables

## Environment Variables

Create a `.env` file or use `.env.dockerhub` with these variables:

```bash
# Redis - Use Bitnami instead of official Docker Hub image
REDIS_IMAGE=bitnami/redis:7.2

# PostgreSQL - Use specific version for better caching instead of CrunchyData
POSTGRES_IMAGE=postgres:15.5-alpine

# Nginx - Use specific version instead of latest/alpine
NGINX_IMAGE=nginx:1.25-alpine

# Python base images - Use specific versions for better caching
PYTHON_BASE_IMAGE=python:3.11.7-slim-bullseye

# Monitoring images
GRAFANA_IMAGE=grafana/grafana:9.5.2
PROMTAIL_IMAGE=grafana/promtail:2.9.0
LOKI_IMAGE=grafana/loki:2.9.0
```

## Docker Hub Authentication

The most effective way to avoid rate limits is to authenticate with Docker Hub:

```bash
# Login to Docker Hub (requires account)
docker login

# Your credentials will be stored and used for all pulls
# This provides significantly higher rate limits
```

## Local Registry Mirror

For advanced users, you can set up a local registry mirror:

### 1. Add registry service to docker-compose.yml

```yaml
services:
  registry:
    image: registry:2
    container_name: local-registry
    ports:
      - "5000:5000"
    volumes:
      - registry_data:/var/lib/registry
    networks:
      - internal
```

### 2. Configure Docker daemon

Add to `/etc/docker/daemon.json`:

```json
{
  "registry-mirrors": ["http://localhost:5000"]
}
```

### 3. Restart Docker daemon

```bash
sudo systemctl restart docker
```

## Docker Build Optimization

Optimize your Dockerfiles to reduce external pulls:

### Use specific tags instead of latest

```dockerfile
FROM python:3.11.7-slim-bullseye  # Specific version
```

### Use multi-stage builds

```dockerfile
# Build stage
FROM python:3.11.7-slim-bullseye AS builder
COPY requirements.txt .
RUN pip install --user -r requirements.txt

# Final stage
FROM python:3.11.7-slim-bullseye
COPY --from=builder /root/.local /root/.local
```

## GitHub Container Registry

If you have GitHub, use GitHub Container Registry:

```yaml
# In your .env file
REDIS_IMAGE=ghcr.io/your-username/redis:7.2
```

## Testing Your Configuration

Test that your alternative registries work:

```bash
# Test Redis image pull
docker pull bitnami/redis:7.2

# Test PostgreSQL image pull
docker pull crunchydata/crunchy-postgres:15

# Test with environment variables
docker-compose --env-file .env.dockerhub config
```

## Troubleshooting

### Common Issues

1. **Image not found**: Ensure the alternative registry image exists
2. **Permission denied**: You may need to authenticate with the alternative
   registry
3. **Version mismatch**: Some alternative images may have different
   configuration requirements

### Fallback Behavior

The configuration uses default fallback values:

```yaml
image: ${REDIS_IMAGE:-redis:7-alpine}
```

If the alternative image fails to pull, it will fall back to the Docker Hub
image.

## Best Practices

1. **Use authenticated pulls** whenever possible
2. **Pin specific versions** for better caching
3. **Use multi-stage builds** to reduce final image size
4. **Consider self-hosting** frequently used images
5. **Monitor pull rates** and adjust strategies as needed

## Performance Considerations

- **Bitnami images**: May be larger but often include better security practices
- **CrunchyData PostgreSQL**: Enterprise features but may have different
  configuration
- **Specific versions**: Better caching but may require updates for security
  patches

## Security Notes

- Always verify the integrity of images from alternative registries
- Use signed images when available
- Regularly update to receive security patches
- Monitor for vulnerabilities in all container images

This configuration helps avoid Docker Hub rate limits while maintaining the
security and functionality of the AI Coding Agent project.
