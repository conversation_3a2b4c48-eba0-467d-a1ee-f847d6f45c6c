# Supabase Google OAuth Integration Implementation Guide

## Overview

This guide provides a complete implementation for integrating Supabase Social Login (Google OAuth) into your existing Next.js application with NextAuth and FastAPI backend.

## Architecture

```
Frontend (Next.js + NextAuth)
    ↓ OAuth Flow
Supabase Auth (Google Provider)
    ↓ JWT Validation
FastAPI Backend (ai-orchestrator)
```

## Implementation Steps

### 1. Dependencies Installation

The following dependency has been added to `package.json`:

```json
"@auth/supabase-adapter": "^1.6.0"
```

**Action Required**: Rebuild the Docker container to install new dependencies:

```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d --build
```

### 2. Environment Variables Setup

Copy the `.env.example` file to `.env.local` and fill in your credentials:

```bash
cp containers/user-portal/.env.example containers/user-portal/.env.local
```

Required environment variables:
- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key (keep secure!)
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret
- `NEXTAUTH_SECRET`: Random secure string for JWT signing

### 3. Supabase Configuration

Follow the detailed setup in: `docs/SUPABASE_GOOGLE_OAUTH_SETUP.md`

Key steps:
1. Enable Google provider in Supabase Dashboard
2. Create Google OAuth credentials in Google Cloud Console
3. Configure authorized origins and redirect URIs
4. Add credentials to Supabase settings

### 4. NextAuth Configuration Update

The new NextAuth configuration (`route-with-google.ts`) includes:

- **SupabaseAdapter**: Handles user data storage in Supabase
- **GoogleProvider**: Enables Google OAuth sign-in
- **Hybrid JWT Strategy**: Supports both OAuth and credentials
- **Backend Integration**: Forwards Supabase JWTs to FastAPI backend

#### Key Features:

1. **Dual Authentication Support**:
   - Google OAuth → Supabase JWT → Backend validation
   - Credentials → Direct backend authentication

2. **JWT Callback Logic**:
   - Google sign-ins get Supabase session tokens
   - Credentials sign-ins use existing backend tokens
   - Automatic backend user synchronization

3. **Session Management**:
   - Provider-aware session handling
   - Proper token forwarding to backend
   - Automatic Supabase sign-out on session end

### 5. Frontend Components

#### LoginButton Component

Located at: `src/components/auth/LoginButton.tsx`

Features:
- Dynamic provider detection
- Google OAuth initiation
- Credentials fallback
- Loading states and error handling
- Customizable variants

Usage examples:

```tsx
// Both Google and Credentials options
<LoginButton variant="both" redirectTo="/dashboard" />

// Google only
<LoginButton variant="google" />

// Credentials only
<LoginButton variant="credentials" />
```

### 6. Backend Integration Requirements

Your FastAPI backend needs a new endpoint to handle Supabase JWT validation:

```python
@app.post("/auth/supabase")
async def validate_supabase_auth(
    request: SupabaseAuthRequest,
    authorization: str = Header(..., description="Bearer token")
):
    """
    Validate Supabase JWT and create/update user in backend.

    This endpoint receives:
    - Supabase access_token in Authorization header
    - User data from Supabase in request body

    Should return:
    - success: bool
    - user: User object matching your backend schema
    """
    # Validate Supabase JWT
    # Create or update user in your backend
    # Return standardized user object
    pass
```

### 7. Updated Login Page

Update your existing login page to include the new LoginButton:

```tsx
import LoginButton from '@/components/auth/LoginButton';

export default function LoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="text-3xl font-bold text-center">
            Sign in to AI Coding Agent
          </h2>
        </div>

        {/* New integrated login options */}
        <LoginButton variant="both" redirectTo="/dashboard" />

        {/* Or keep your existing form and add Google as option */}
        <LoginButton variant="google" className="w-full" />
      </div>
    </div>
  );
}
```

### 8. Docker Compose Updates

Add the new environment variables to your docker-compose files:

```yaml
services:
  user-portal:
    environment:
      # Existing variables...
      NEXT_PUBLIC_SUPABASE_URL: ${NEXT_PUBLIC_SUPABASE_URL}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
      GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
```

## Testing the Integration

### 1. Development Testing

1. Start your containers:
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
   ```

2. Visit: `http://portal.localhost/login`

3. Test both authentication methods:
   - Click "Continue with Google" → Should redirect to Google OAuth
   - Use existing credentials form → Should work as before

### 2. Google OAuth Flow Testing

1. Click "Continue with Google"
2. Complete Google authorization
3. Should redirect back to your app
4. Check that session contains Supabase JWT
5. Verify backend receives and validates the token

### 3. Session Verification

Use the browser developer tools to inspect the session:

```javascript
// In browser console
import { getSession } from 'next-auth/react';
const session = await getSession();
console.log(session);

// Should show:
// - provider: "google" or "credentials"
// - accessToken: Supabase JWT or backend token
// - user: User object
```

## Security Considerations

1. **Environment Variables**: Never commit `.env.local` files
2. **HTTPS Required**: Use HTTPS in production for OAuth flows
3. **Token Validation**: Always validate JWTs on the backend
4. **CORS Configuration**: Properly configure CORS in Supabase
5. **Rate Limiting**: Implement rate limiting for auth endpoints

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI"**:
   - Check Google Cloud Console redirect URIs
   - Ensure Supabase callback URL is correct

2. **"CORS error"**:
   - Configure allowed origins in Supabase settings
   - Check NEXTAUTH_URL environment variable

3. **"JWT validation failed"**:
   - Verify Supabase service role key
   - Check backend JWT validation logic

4. **"Session not persisting"**:
   - Ensure NEXTAUTH_SECRET is set
   - Check cookie configuration

### Debug Steps

1. Check container logs:
   ```bash
   docker logs ai-coding-agent-dev-user-portal-1
   ```

2. Verify environment variables:
   ```bash
   docker exec ai-coding-agent-dev-user-portal-1 printenv | grep -E "(SUPABASE|GOOGLE|NEXTAUTH)"
   ```

3. Test NextAuth endpoints:
   ```bash
   curl http://portal.localhost/api/auth/providers
   curl http://portal.localhost/api/auth/session
   ```

## Migration Strategy

### Phase 1: Add Google OAuth (Current)
- Install dependencies and configure OAuth
- Test Google sign-in alongside existing credentials
- Verify backend integration

### Phase 2: User Migration (Future)
- Add migration endpoint for existing users
- Allow linking Google accounts to existing credentials
- Provide account merge functionality

### Phase 3: Optimize (Future)
- Consider removing credentials provider if not needed
- Implement additional OAuth providers (GitHub, Microsoft)
- Add advanced security features (MFA, device tracking)

## Files Modified/Created

### New Files:
- `src/app/api/auth/[...nextauth]/route-with-google.ts` - Updated NextAuth config
- `src/components/auth/LoginButton.tsx` - New login component
- `.env.example` - Environment variables template
- `docs/SUPABASE_GOOGLE_OAUTH_SETUP.md` - Supabase setup guide

### Modified Files:
- `package.json` - Added @auth/supabase-adapter dependency

### Next Steps:
1. Rebuild containers with new dependencies
2. Configure Supabase and Google OAuth credentials
3. Test the authentication flow
4. Update backend to handle Supabase JWT validation
5. Deploy to production with proper environment variables
