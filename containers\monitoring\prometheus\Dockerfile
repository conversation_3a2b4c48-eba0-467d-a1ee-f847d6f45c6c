# Custom Prometheus image with curl for health checks
FROM prom/prometheus:latest

# Use built-in curl for health checks (BusyBox-based image)

# Security: Run as non-root user to prevent privilege escalation
USER nobody

# Health check for Prometheus using built-in curl
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s \
  CMD curl --silent --fail --max-time 10 http://localhost:9090/-/healthy || exit 1

# Metadata labels for traceability
LABEL maintainer="<EMAIL>"
LABEL description="Custom Prometheus image with curl for health checks"
