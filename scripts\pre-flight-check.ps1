# Pre-flight check script for AI Coding Agent (Windows PowerShell)
# Validates Docker Compose configuration, secrets, and host resources

Write-Host " Running pre-flight checks for AI Coding Agent..." -ForegroundColor Cyan

# Check if Docker is running
try {
  docker info >$null 2>&1
  if ($LASTEXITCODE -eq 0) {
    Write-Host " Docker is running" -ForegroundColor Green
  }
  else {
    Write-Host " Docker is not running. Please start Docker and try again." -ForegroundColor Red
    exit 1
  }
}
catch {
  Write-Host " Docker is not available. Please install Docker and try again." -ForegroundColor Red
  exit 1
}

# Validate docker-compose.yml syntax
Write-Host " Validating docker-compose.yml configuration..." -ForegroundColor Yellow
try {
  docker compose config >$null 2>&1
  if ($LASTEXITCODE -eq 0) {
    Write-Host " docker-compose.yml is valid" -ForegroundColor Green
  }
  else {
    Write-Host " docker-compose.yml has invalid syntax" -ForegroundColor Red
    exit 1
  }
}
catch {
  Write-Host " Failed to validate docker-compose.yml" -ForegroundColor Red
  exit 1
}

# Check for required secret files
$requiredSecrets = @(
  "secrets/postgres_password.txt",
  "secrets/jwt_secret.txt",
  "secrets/supabase_url.txt",
  "secrets/supabase_key.txt",
  "secrets/supabase_service_key.txt",
  "secrets/code_server_password.txt",
  "secrets/redis_password.txt"
)

Write-Host " Checking for required secret files..." -ForegroundColor Yellow
$missingSecrets = @()
foreach ($secret in $requiredSecrets) {
  if (-not (Test-Path $secret)) {
    $missingSecrets += $secret
  }
}

if ($missingSecrets.Count -eq 0) {
  Write-Host " All secret files are present" -ForegroundColor Green
}
else {
  Write-Host " Missing secret files:" -ForegroundColor Red
  foreach ($secret in $missingSecrets) {
    Write-Host "   $secret" -ForegroundColor Red
    $example = "$secret.example"
    if (Test-Path $example) {
      Write-Host "   Please copy from $example and populate with actual values" -ForegroundColor Yellow
    }
  }
  exit 1
}

# Check host resources
Write-Host " Checking host resources..." -ForegroundColor Yellow

# Get total memory in GB
try {
  $computerSystem = Get-CimInstance -ClassName Win32_ComputerSystem
  $totalMemGB = [math]::Round($computerSystem.TotalPhysicalMemory / 1GB)
  if ($totalMemGB -lt 8) {
    Write-Host "  Warning: Host has ${totalMemGB}GB RAM. Recommended minimum is 8GB for full stack." -ForegroundColor Yellow
  }
  else {
    Write-Host " Host memory: ${totalMemGB}GB (sufficient)" -ForegroundColor Green
  }
}
catch {
  Write-Host "  Could not determine host memory" -ForegroundColor Yellow
}

# Get CPU cores
try {
  $cpuInfo = Get-CimInstance -ClassName Win32_Processor
  $cpuCores = $cpuInfo.NumberOfCores
  if ($cpuCores -lt 4) {
    Write-Host "  Warning: Host has ${cpuCores} CPU cores. Recommended minimum is 4 cores." -ForegroundColor Yellow
  }
  else {
    Write-Host " Host CPU cores: ${cpuCores} (sufficient)" -ForegroundColor Green
  }
}
catch {
  Write-Host "  Could not determine CPU cores" -ForegroundColor Yellow
}

# Check available disk space (in GB)
try {
  $diskInfo = Get-CimInstance -ClassName Win32_LogicalDisk -Filter "DeviceID='C:'"
  $availableDiskGB = [math]::Round($diskInfo.FreeSpace / 1GB)
  if ($availableDiskGB -lt 20) {
    Write-Host "  Warning: Only ${availableDiskGB}GB disk space available. Recommended minimum is 20GB." -ForegroundColor Yellow
  }
  else {
    Write-Host " Available disk space: ${availableDiskGB}GB (sufficient)" -ForegroundColor Green
  }
}
catch {
  Write-Host "  Could not determine available disk space" -ForegroundColor Yellow
}

Write-Host ""
Write-Host " Pre-flight checks completed successfully!" -ForegroundColor Green
Write-Host "   You can now run: docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d" -ForegroundColor Cyan
