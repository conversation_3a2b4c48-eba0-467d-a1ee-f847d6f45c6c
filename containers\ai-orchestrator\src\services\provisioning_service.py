"""
Provisioning Service for Multi-Tenant Database Schema Management.

This service handles the creation and management of isolated database schemas
for each user project, implementing the Schema-per-Tenant architecture.

Key Features:
- Creates unique database schemas for each project
- Generates schema-specific database connection URLs
- Ensures proper isolation between user projects
- Handles schema creation with proper error handling

Author: AI Coding Agent
Version: 1.0.0
"""

import logging
from typing import Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger(__name__)


class ProvisioningServiceError(Exception):
    """Base exception for provisioning service operations."""
    pass


class SchemaCreationError(ProvisioningServiceError):
    """Exception raised when schema creation fails."""
    pass


class ProvisioningService:
    """
    Service for provisioning project-specific database schemas.

    This service implements the Schema-per-Tenant pattern by creating
    isolated database schemas for each user project and providing
    schema-specific connection URLs for secure multi-tenant operations.

    The service ensures that each project operates in its own schema,
    providing complete data isolation between different user projects.
    """

    def __init__(self):
        """Initialize the ProvisioningService."""
        logger.info("ProvisioningService initialized")

    async def create_project_schema(
        self,
        db: Session,
        project_id: int
    ) -> Tuple[str, str]:
        """
        Create a unique database schema for a project and return connection details.

        This method creates a PostgreSQL schema named 'project_{project_id}'
        and constructs a schema-specific DATABASE_URL that includes the
        search_path parameter to ensure all operations use the correct schema.

        Args:
            db: SQLAlchemy database session
            project_id: Unique identifier for the project

        Returns:
            Tuple containing:
            - schema_name: The name of the created schema (e.g., 'project_123')
            - connection_url: Schema-specific DATABASE_URL with search_path

        Raises:
            SchemaCreationError: If schema creation fails
            ProvisioningServiceError: For other provisioning errors

        Example:
            >>> schema_name, conn_url = await service.create_project_schema(db, 123)
            >>> print(schema_name)  # 'project_123'
            >>> print(conn_url)     # 'postgresql://.../postgres?options=-csearch_path%3Dproject_123'
        """
        try:
            # Generate schema name using project ID
            schema_name = f"project_{project_id}"

            # Create the schema if it doesn't exist
            create_schema_sql = f"CREATE SCHEMA IF NOT EXISTS {schema_name};"

            logger.info(f"Creating database schema: {schema_name}")

            # Execute schema creation
            db.execute(text(create_schema_sql))
            db.commit()

            # Construct schema-specific connection URL
            # Get the base database URL from the current session
            base_url = str(db.bind.url)

            # Remove any existing options parameter
            if '?' in base_url:
                base_url = base_url.split('?')[0]

            # Add schema-specific search_path option
            # URL encode the equals sign as %3D
            connection_url = f"{base_url}?options=-csearch_path%3D{schema_name}"

            logger.info(f"Successfully created schema '{schema_name}' with connection URL")

            return schema_name, connection_url

        except Exception as e:
            db.rollback()
            error_msg = f"Failed to create schema for project {project_id}: {str(e)}"
            logger.error(error_msg)
            raise SchemaCreationError(error_msg) from e
