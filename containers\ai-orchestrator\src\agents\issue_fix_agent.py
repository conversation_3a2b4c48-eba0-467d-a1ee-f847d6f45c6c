"""
Sequential Agents - Issue<PERSON><PERSON><PERSON><PERSON> (Code Repair Specialist)

Conforms to the simple BaseAgent interface. Accepts broken code and an error message,
prompts the LLM to produce a fixed version of the code, and returns the corrected output.
"""

from __future__ import annotations

import re
import textwrap
from typing import Any, Dict

from src.agents.base_agent import BaseAgent
from src.models.llm_models import GenerateRequest, LLMResponse
from src.services.enhanced_llm_service import get_llm_service


class IssueFixAgent(BaseAgent):
    """Agent that uses the LLM to repair code based on an error message."""

    def __init__(self) -> None:
        super().__init__()
        self._llm = None

    async def _get_llm(self):
        if self._llm is None:
            self._llm = await get_llm_service()
        return self._llm

    @staticmethod
    def _extract_code(text: str) -> str:
        """Extract code from fenced blocks if present."""
        fence = re.findall(r"```[a-zA-Z0-9_\-]*\n(.*?)```", text, flags=re.DOTALL)
        if fence:
            return fence[0].strip()
        return text.strip()

    def _fix_code_prompt(self, file_path: str, broken_code: str, error_message: str) -> str:
        """Create a prompt that instructs the LLM to fix the provided code.

        Requirements:
        - Provide only the corrected code in a single block
        - Do not include explanations
        - Keep the style and structure consistent with the project
        """
        return textwrap.dedent(
            f"""
            You are a senior software engineer. You will be given a file path, code that fails validation,
            and the error message. Return a corrected version of the code only. Do not include any explanation.

            File path: {file_path}

            Error message:
            {error_message}

            Broken code:
            ```
            {broken_code}
            ```

            Provide the corrected code below. Output only the full fixed code content for this file.
            """
        ).strip()

    async def execute(self, task_input: Dict[str, Any]) -> Dict[str, Any]:
        """Fix code using the LLM based on the provided error message."""
        file_path = task_input.get("file_path") or "unknown_file"
        code = task_input.get("code")
        error_message = task_input.get("error_message")

        if not isinstance(code, str) or not isinstance(error_message, str):
            return {"error": "Both 'code' and 'error_message' must be provided as strings"}

        llm = await self._get_llm()
        prompt = self._fix_code_prompt(str(file_path), code, error_message)
        req = GenerateRequest(prompt=prompt)
        resp: LLMResponse = await llm.generate(req, user_id="issue-fix-agent")
        fixed_code = self._extract_code(resp.content)

        return {
            "fixed_code": fixed_code,
        }
