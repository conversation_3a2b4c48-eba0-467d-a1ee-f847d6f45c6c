#!/bin/bash

# Secrets Management Script for AI Coding Agent
# This script helps set up secure credential storage using Docker secrets

set -e

SECRETS_DIR="./secrets"
ENV_FILE=".env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== AI Coding Agent Secrets Management ===${NC}"
echo ""

# Function to generate secure random password
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d '\n'
}

# Function to create secret file
create_secret() {
    local secret_name=$1
    local secret_value=$2
    local secret_file="$SECRETS_DIR/${secret_name}.txt"

    echo -n "$secret_value" > "$secret_file"
    chmod 600 "$secret_file"
    echo -e "${GREEN}${NC} Created secret: $secret_name"
}

# Create secrets directory
if [ ! -d "$SECRETS_DIR" ]; then
    mkdir -p "$SECRETS_DIR"
    chmod 700 "$SECRETS_DIR"
    echo -e "${GREEN}${NC} Created secrets directory"
else
    echo -e "${YELLOW}${NC} Secrets directory already exists"
fi

echo ""
echo -e "${BLUE}Setting up secrets...${NC}"

# Check if .env file exists for reference
if [ -f "$ENV_FILE" ]; then
    echo -e "${BLUE}Found .env file, extracting values...${NC}"

    # Extract values from .env file (if they exist)
    POSTGRES_PASSWORD=$(grep "^POSTGRES_PASSWORD=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2- || echo "")
    JWT_SECRET=$(grep "^JWT_SECRET=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2- || echo "")
    SUPABASE_SERVICE_KEY=$(grep "^SUPABASE_SERVICE_KEY=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2- || echo "")
    OPENROUTER_API_KEY=$(grep "^OPENROUTER_API_KEY=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2- || echo "")
    OPENAI_API_KEY=$(grep "^OPENAI_API_KEY=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2- || echo "")
    ANTHROPIC_API_KEY=$(grep "^ANTHROPIC_API_KEY=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2- || echo "")
    CODE_SERVER_PASSWORD=$(grep "^CODE_SERVER_PASSWORD=" "$ENV_FILE" 2>/dev/null | cut -d'=' -f2- || echo "")
else
    echo -e "${YELLOW}${NC} No .env file found, will generate new secrets"
fi

# Create or update secrets
echo ""

# PostgreSQL Password
if [ -n "$POSTGRES_PASSWORD" ] && [ "$POSTGRES_PASSWORD" != "postgres_password" ]; then
    create_secret "postgres_password" "$POSTGRES_PASSWORD"
else
    NEW_PG_PASSWORD=$(generate_password 16)
    create_secret "postgres_password" "$NEW_PG_PASSWORD"
    echo -e "${YELLOW}  Generated new PostgreSQL password${NC}"
fi

# JWT Secret
if [ -n "$JWT_SECRET" ]; then
    create_secret "jwt_secret" "$JWT_SECRET"
else
    NEW_JWT_SECRET=$(generate_password 32)
    create_secret "jwt_secret" "$NEW_JWT_SECRET"
    echo -e "${YELLOW}  Generated new JWT secret${NC}"
fi

# Supabase Service Key
if [ -n "$SUPABASE_SERVICE_KEY" ]; then
    create_secret "supabase_service_key" "$SUPABASE_SERVICE_KEY"
else
    echo -e "${YELLOW}  Supabase service key not found in .env - please set manually${NC}"
    create_secret "supabase_service_key" "your_supabase_service_key_here"
fi

# API Keys
if [ -n "$OPENROUTER_API_KEY" ]; then
    create_secret "openrouter_api_key" "$OPENROUTER_API_KEY"
else
    create_secret "openrouter_api_key" "your_openrouter_api_key_here"
fi

if [ -n "$OPENAI_API_KEY" ]; then
    create_secret "openai_api_key" "$OPENAI_API_KEY"
else
    create_secret "openai_api_key" "your_openai_api_key_here"
fi

if [ -n "$ANTHROPIC_API_KEY" ]; then
    create_secret "anthropic_api_key" "$ANTHROPIC_API_KEY"
else
    create_secret "anthropic_api_key" "your_anthropic_api_key_here"
fi

# Code Server Password
if [ -n "$CODE_SERVER_PASSWORD" ]; then
    create_secret "code_server_password" "$CODE_SERVER_PASSWORD"
else
    NEW_CODE_PASSWORD=$(generate_password 12)
    create_secret "code_server_password" "$NEW_CODE_PASSWORD"
    echo -e "${YELLOW}  Generated new code-server password: $NEW_CODE_PASSWORD${NC}"
fi

echo ""
echo -e "${GREEN}=== Secrets Setup Complete! ===${NC}"
echo ""
echo -e "${BLUE}Usage:${NC}"
echo "  # Use with secrets (recommended for production):"
echo "  docker-compose -f docker-compose.yml -f docker-compose.secrets.yml up -d"
echo ""
echo "  # Use with development (includes port bindings):"
echo "  docker-compose -f docker-compose.yml -f docker-compose.dev.yml -f docker-compose.secrets.yml up -d"
echo ""
echo -e "${YELLOW}Important:${NC}"
echo "  • Add ./secrets/ to your .gitignore file"
echo "  • Never commit secret files to version control"
echo "  • Review and update placeholder API keys in ./secrets/"
echo "  • Update your application code to read from /run/secrets/ instead of environment variables"
echo ""
echo -e "${BLUE}Security Notes:${NC}"
echo "  • Secret files have 600 permissions (owner read/write only)"
echo "  • Secrets directory has 700 permissions (owner access only)"
echo "  • Secrets are mounted as read-only files in containers"
echo ""