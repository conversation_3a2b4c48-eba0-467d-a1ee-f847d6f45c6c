# __PROJECT_NAME__

A production-ready web application built with FastAPI, PostgreSQL, and Redis.

## Features

- 🚀 **FastAPI** - Modern, fast web framework for building APIs
- 🐘 **PostgreSQL** - Robust relational database with full ACID compliance
- 🔴 **Redis** - In-memory data structure store for caching and sessions
- 🐳 **Docker** - Containerized deployment with Docker Compose
- 🔒 **Security** - JWT authentication, password hashing, CORS protection
- 📊 **Health Checks** - Comprehensive health monitoring for all services
- 📝 **Logging** - Structured logging with configurable levels
- 🧪 **Testing** - Unit and integration tests with pytest

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Git (for version control)

### Setup

1. **<PERSON>lone and setup the project:**
   ```bash
   cd __PROJECT_NAME__
   cp .env.example .env
   ```

2. **Configure environment variables:**
   Edit `.env` file and update the following:
   ```bash
   # Security - CHANGE THESE IN PRODUCTION
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
   POSTGRES_PASSWORD=your-secure-db-password
   REDIS_PASSWORD=your-secure-redis-password
   ```

3. **Start the application:**
   ```bash
   docker-compose up -d --build
   ```

4. **Verify the deployment:**
   ```bash
   # Check service health
   docker-compose ps
   
   # Test the health endpoint
   curl http://localhost:8000/health
   ```

## API Documentation

Once the application is running, you can access:

- **API Documentation (Swagger):** http://localhost:8000/docs
- **Alternative Documentation (ReDoc):** http://localhost:8000/redoc
- **Health Check:** http://localhost:8000/health

## Development

### Local Development

```bash
# Start in development mode with hot reload
docker-compose up --build

# View logs
docker-compose logs -f __PROJECT_NAME__-app

# Run tests
docker-compose exec __PROJECT_NAME__-app pytest

# Access the database
docker-compose exec __PROJECT_NAME__-db psql -U postgres -d __PROJECT_NAME__
```

### Project Structure

```
__PROJECT_NAME__/
├── src/
│   └── main.py              # FastAPI application
├── logs/                    # Application logs
├── data/                    # Application data
├── Dockerfile              # Container definition
├── docker-compose.yml      # Service orchestration
├── requirements.txt        # Python dependencies
├── init-db.sql            # Database initialization
├── .env.example           # Environment template
└── README.md              # This file
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `APP_PORT` | Application port | `8000` |
| `DATABASE_URL` | PostgreSQL connection string | Auto-configured |
| `REDIS_URL` | Redis connection string | Auto-configured |
| `JWT_SECRET` | JWT signing secret | **Must be set** |
| `LOG_LEVEL` | Logging level | `INFO` |
| `ENVIRONMENT` | Runtime environment | `development` |

### Health Checks

The application includes comprehensive health checks:

- **Application Health:** `/health` endpoint
- **Database Health:** PostgreSQL connection test
- **Redis Health:** Redis connection test
- **Container Health:** Docker health check integration

## Production Deployment

### Security Checklist

- [ ] Change all default passwords
- [ ] Set strong JWT secret
- [ ] Configure CORS origins
- [ ] Set up SSL/TLS certificates
- [ ] Configure firewall rules
- [ ] Enable log monitoring
- [ ] Set up backup procedures

### Scaling

```bash
# Scale the application
docker-compose up -d --scale __PROJECT_NAME__-app=3

# Use a load balancer (nginx, traefik, etc.)
# Configure database connection pooling
# Set up Redis clustering if needed
```

## Monitoring

### Logs

```bash
# View application logs
docker-compose logs -f __PROJECT_NAME__-app

# View all service logs
docker-compose logs -f
```

### Metrics

The application exposes health and status endpoints for monitoring:

- `/health` - Service health status
- `/api/v1/status` - API operational status

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Change APP_PORT in .env file
   APP_PORT=8001
   ```

2. **Database connection failed:**
   ```bash
   # Check database health
   docker-compose exec __PROJECT_NAME__-db pg_isready
   ```

3. **Redis connection failed:**
   ```bash
   # Check Redis health
   docker-compose exec __PROJECT_NAME__-redis redis-cli ping
   ```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
