# Code Server Container

VS Code in the browser for AI Coding Agent development and debugging.

## 🎯 Overview

This container provides code-server (VS Code in the browser) with pre-configured extensions and settings optimized for AI Coding Agent development, including Python, Docker, and AI-specific tooling.

## 🏗️ Architecture

### Features

- **VS Code Interface**: Full VS Code experience in the browser
- **Pre-installed Extensions**: Python, Docker, Git, and AI development tools
- **Workspace Integration**: Direct access to AI Coding Agent codebase
- **Remote Development**: SSH and container-based development support
- **Live Share**: Collaborative coding capabilities
- **Git Integration**: Built-in version control with GitLens
- **Terminal Access**: Integrated terminal with development tools
- **Extension Marketplace**: Access to VS Code marketplace extensions

### Pre-installed Extensions

- **Python Development**: <PERSON>ylance, Python, Jupyter, Black formatter
- **Docker Support**: Docker, Dev Containers
- **Git Tools**: GitLens, Git Graph
- **AI Assistance**: GitHub Copilot, Tabnine
- **Code Quality**: <PERSON><PERSON><PERSON>, Prettier, Pylint
- **Database Tools**: SQL Server, PostgreSQL
- **API Development**: REST Client, Thunder Client
- **Documentation**: Markdown Preview, Draw.io

## 🚀 Configuration

### Environment Variables

```bash
# Code Server Configuration
CODE_SERVER_PORT=8080
CODE_SERVER_HOST=0.0.0.0
CODE_SERVER_PASSWORD=secure_password
CODE_SERVER_DISABLE_TELEMETRY=true

# User Configuration
USER_UID=1000
USER_GID=1000
USER_NAME=developer

# Workspace Configuration
WORKSPACE_DIR=/home/<USER>/workspace
DEFAULT_WORKSPACE=/home/<USER>/workspace/ai-coding-agent

# Extension Configuration
EXTENSIONS_GALLERY='{"serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery"}'
```

### Volume Mounts

```yaml
volumes:
  - ../..:/home/<USER>/workspace/ai-coding-agent
  - ./settings.json:/home/<USER>/.local/share/code-server/User/settings.json
  - ./extensions:/home/<USER>/.local/share/code-server/extensions
  - ./ssh:/home/<USER>/.ssh
  - ./gitconfig:/home/<USER>/.gitconfig
```

## 🔧 Usage

### Building the Container

```bash
docker build -t ai-coding-agent-code-server .
```

### Running with Docker Compose

```bash
docker-compose up code-server
```

### Accessing Code Server

```bash
# Web interface
open http://localhost:8080

# With password authentication
open http://localhost:8080/?folder=/home/<USER>/workspace/ai-coding-agent
```

## ⚙️ VS Code Configuration

### Settings Configuration

```json
{
  "python.defaultInterpreterPath": "/usr/local/bin/python",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": true,
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": ["--line-length", "120"],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "files.associations": {
    "*.env": "properties",
    "Dockerfile*": "dockerfile",
    "*.yml": "yaml",
    "*.yaml": "yaml"
  },
  "git.autofetch": true,
  "git.confirmSync": false,
  "terminal.integrated.shell.linux": "/bin/bash",
  "docker.languageserver.diagnostics.deprecatedMaintainer": "ignore"
}
```

### Extension Recommendations

```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.pylance",
    "ms-python.black-formatter",
    "ms-toolsai.jupyter",
    "ms-vscode.vscode-json",
    "ms-vscode-remote.remote-ssh",
    "ms-vscode-remote.remote-containers",
    "ms-azuretools.vscode-docker",
    "eamodio.gitlens",
    "mhutchie.git-graph",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "github.copilot",
    "tabnine.tabnine-vscode",
    "ms-vscode-remote.vscode-remote-extensionpack"
  ]
}
```

## 🐍 Python Development Setup

### Virtual Environment

```bash
# Create virtual environment
python -m venv .venv

# Activate virtual environment
source .venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### Testing Configuration

```json
{
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": [
    "--verbose",
    "--tb=short",
    "--cov=src",
    "--cov-report=html"
  ],
  "python.testing.unittestEnabled": false
}
```

### Debugging Configuration

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: FastAPI",
      "type": "python",
      "request": "launch",
      "module": "uvicorn",
      "args": [
        "src.main:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
      ],
      "cwd": "${workspaceFolder}",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/src"
      }
    },
    {
      "name": "Python: pytest",
      "type": "python",
      "request": "launch",
      "module": "pytest",
      "args": [
        "-v",
        "tests/"
      ],
      "cwd": "${workspaceFolder}"
    }
  ]
}
```

## 🐳 Docker Development

### Dev Container Configuration

```json
{
  "name": "AI Coding Agent Development",
  "dockerComposeFile": [
    "../docker-compose.yml",
    "../docker-compose.dev.yml"
  ],
  "service": "code-server",
  "workspaceFolder": "/workspaces/ai-coding-agent",
  "shutdownAction": "stopCompose",
  "extensions": [
    "ms-python.python",
    "ms-azuretools.vscode-docker",
    "ms-vscode.vscode-remote-containers"
  ]
}
```

### Docker Tasks

```json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Build AI Orchestrator",
      "type": "shell",
      "command": "docker build -t ai-orchestrator ./containers/ai-orchestrator",
      "group": "build"
    },
    {
      "label": "Start Development Stack",
      "type": "shell",
      "command": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d",
      "group": "build"
    },
    {
      "label": "Run Tests",
      "type": "shell",
      "command": "docker-compose exec ai-orchestrator python -m pytest",
      "group": "test"
    }
  ]
}
```

## 🔧 Development Workflow

### Git Integration

```json
{
  "git.enableSmartCommit": true,
  "git.autofetch": true,
  "git.confirmSync": false,
  "gitlens.advanced.messages": {
    "suppressCommitHasNoChanges": false,
    "suppressFileNotUnderSourceControl": false
  }
}
```

### Code Quality Tools

```json
{
  "eslint.workingDirectories": [
    {
      "directory": "containers/user-portal",
      "changeProcessCWD": true
    }
  ],
  "prettier.configPath": ".prettierrc",
  "python.linting.pylintArgs": [
    "--rcfile", "pyproject.toml"
  ]
}
```

## 🔒 Security Configuration

### Authentication

```bash
# Password authentication
export PASSWORD="secure-development-password"

# Or use hashed password
export HASHED_PASSWORD="$(echo 'secure-password' | htpasswd -bnBC 10 '' | tr -d ':\n' | sed 's/$2y/$2a/')"
```

### Network Security

```yaml
# Internal network only
networks:
  - development

# No external ports in production
ports:
  - "127.0.0.1:8080:8080"

# Reverse proxy configuration
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.code-server.rule=Host(`code.example.com`)"
  - "traefik.http.routers.code-server.entrypoints=websecure"
  - "traefik.http.routers.code-server.tls.certresolver=letsencrypt"
  - "traefik.http.services.code-server.loadbalancer.server.port=8080"
  - "traefik.http.middlewares.code-server-auth.basicAuth.users=user:$$2y$$10$$..."
```

## 📊 Monitoring & Health Checks

### Health Check Configuration

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
```

### Resource Limits

```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 4G
    reservations:
      cpus: '0.5'
      memory: 1G
```

## 🐛 Troubleshooting

### Common Issues

1. **Extension Installation Failures**
   - Check network connectivity
   - Verify marketplace access
   - Clear extension cache

2. **Workspace Mounting Issues**
   - Verify volume permissions
   - Check Docker user mapping
   - Validate workspace path

3. **Performance Problems**
   - Adjust resource limits
   - Enable GPU acceleration for extensions
   - Optimize VS Code settings

### Debug Commands

```bash
# Check code-server status
docker exec code-server ps aux | grep code-server

# View logs
docker logs code-server

# Access container shell
docker exec -it code-server bash

# Check extension status
docker exec code-server code-server --list-extensions
```

## 📚 Additional Resources

- [code-server Documentation](https://github.com/coder/code-server)
- [VS Code Documentation](https://code.visualstudio.com/docs)
- [Dev Containers](https://containers.dev/)
- [VS Code Marketplace](https://marketplace.visualstudio.com/)

## 🤝 Contributing

When modifying this container:

1. Update extension recommendations for new tools
2. Test workspace mounting and permissions
3. Validate development workflow configurations
4. Update security configurations for new requirements
5. Test performance with different resource allocations
