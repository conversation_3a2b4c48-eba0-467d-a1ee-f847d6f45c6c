#!/usr/bin/env pwsh
#
# Unicode Character Detection and Cleanup Tool
# Scans files for Unicode characters and optionally replaces them with ASCII alternatives
#
param(
  [Parameter(Mandatory = $false)]
  [string]$Path = ".",

  [Parameter(Mandatory = $false)]
  [switch]$Fix = $false,

  [Parameter(Mandatory = $false)]
  [switch]$ShowDetails = $false
)

# File patterns to scan
$filePatterns = @("*.py", "*.ps1", "*.yml", "*.yaml", "*.sh", "*.js", "*.ts", "*.dockerfile")

# Common Unicode replacements - using only ASCII characters in this script
$unicodeMap = @{
  # Checkmarks and status symbols
  "2705"     = "[OK]"      # Check mark
  "274C"     = "[FAIL]"    # Cross mark
  "26A0"     = "[WARN]"    # Warning sign
  "2713"     = "[CHECK]"   # Check mark
  "2717"     = "[X]"       # Cross mark

  # Emoji replacements
  "1F512"    = "[LOCK]"     # Lock
  "1F6AB"    = "[BLOCK]"    # No entry sign
  "1F389"    = "[SUCCESS]"  # Party popper
  "1F50D"    = "[SEARCH]"   # Magnifying glass
  "1F4CA"    = "[STATS]"    # Bar chart
  "1F310"    = "[NETWORK]"  # Globe
  "1F4F1"    = "[WEB]"      # Mobile phone
  "1F510"    = "[INTERNAL]" # Closed lock with key
  "1F3AF"    = "[TARGET]"   # Direct hit
  "1F527"    = "[TOOLS]"    # Wrench
  "1F680"    = "[ROCKET]"   # Rocket
  "1F4A5"    = "[BOOM]"     # Boom
  "1F4DD"    = "[NOTE]"     # Note
  "1F501"    = "[RELOAD]"   # Reload
  "23F3"     = "[WAIT]"      # Hourglass
  "1F3C1"    = "[FINISH]"   # Checkered flag
  "1F9EA"    = "[TEST]"     # Test tube
  "1F4BE"    = "[SAVE]"     # Floppy disk
  "1F916"    = "[BOT]"      # Robot
  "1F511"    = "[KEY]"      # Key
  "1F4E6"    = "[PACKAGE]"  # Package
  "1F517"    = "[LINK]"     # Link
  "1F4CB"    = "[CLIPBOARD]"# Clipboard
  "1F4A1"    = "[IDEA]"     # Light bulb
  "1F6D1"    = "[STOP]"     # Stop sign
  "26A1"     = "[FAST]"      # Lightning
  "1F3ED"    = "[FACTORY]"  # Factory
  "1FA7A"    = "[HEALTH]"   # Stethoscope
  "1F4E1"    = "[SIGNAL]"   # Satellite
  "1F525"    = "[FIRE]"     # Fire
  "1F4BB"    = "[LAPTOP]"   # Laptop
  "1F9F9"    = "[CLEAN]"    # Broom
  "1F4C8"    = "[CHART]"    # Chart
  "2B50"     = "[STAR]"      # Star
  "1F48E"    = "[GEM]"      # Gem
  "1F4C4"    = "[DOC]"      # Document

  # Arrows and symbols
  "2192"     = "->"          # Right arrow
  "2190"     = "<-"          # Left arrow
  "2191"     = "^"           # Up arrow
  "2193"     = "v"           # Down arrow
  "00BB"     = ">>"          # Right double angle
  "00AB"     = "<<"          # Left double angle
  "2022"     = "*"           # Bullet
  "00B0"     = "deg"         # Degree symbol

  # Information symbols
  "2139"     = "[INFO]"      # Information
  "2139FE0F" = "[INFO]"  # Information with variant
}

# Unicode detection pattern (matches any character above ASCII 127)
$unicodePattern = "[^\x00-\x7F]"

function Write-Status {
  param([string]$Message, [string]$Type = "INFO")

  $prefix = switch ($Type) {
    "ERROR" { "[ERROR]" }
    "WARN" { "[WARN]" }
    "INFO" { "[INFO]" }
    "OK" { "[OK]" }
    "FAIL" { "[FAIL]" }
    default { "[INFO]" }
  }

  Write-Host "$prefix $Message"
}

function Get-UnicodeDetails {
  param([string]$UnicodeChar)

  $codePoint = [System.Text.Encoding]::UTF32.GetBytes($UnicodeChar)
  $hexCode = [System.BitConverter]::ToString($codePoint).Replace("-", "")
  return $hexCode
}

function Replace-UnicodeCharacters {
  param(
    [string]$Content,
    [hashtable]$ReplacementMap
  )

  $result = $Content
  $replacements = 0

  # Find all Unicode characters
  $unicodeMatches = [regex]::Matches($Content, $unicodePattern)

  foreach ($match in $unicodeMatches) {
    $unicodeChar = $match.Value
    $hexCode = Get-UnicodeDetails $unicodeChar

    # Try to find a replacement
    if ($ReplacementMap.ContainsKey($hexCode)) {
      $replacement = $ReplacementMap[$hexCode]
      $result = $result.Replace($unicodeChar, $replacement)
      $replacements++

      if ($ShowDetails) {
        Write-Status "Replaced '$unicodeChar' (U+$hexCode) with '$replacement'" "OK"
      }
    }
  }

  return @{
    Content      = $result
    Replacements = $replacements
  }
}

function Scan-FileForUnicode {
  param(
    [string]$FilePath,
    [hashtable]$ReplacementMap,
    [bool]$FixMode = $false
  )

  try {
    $content = Get-Content -Path $FilePath -Raw -Encoding UTF8
    if (-not $content) {
      return @{ HasUnicode = $false; Locations = @(); Fixed = 0 }
    }

    $lines = $content -split "`n"
    $unicodeLocations = @()
    $totalFixed = 0

    for ($i = 0; $i -lt $lines.Length; $i++) {
      $line = $lines[$i]
      $unicodeMatches = [regex]::Matches($line, $unicodePattern)

      if ($unicodeMatches.Count -gt 0) {
        foreach ($match in $unicodeMatches) {
          $unicodeLocations += @{
            LineNumber = $i + 1
            Character  = $match.Value
            Position   = $match.Index
            Context    = $line.Trim()
          }
        }
      }
    }

    if ($FixMode -and $unicodeLocations.Count -gt 0) {
      $replaceResult = Replace-UnicodeCharacters -Content $content -ReplacementMap $ReplacementMap

      if ($replaceResult.Replacements -gt 0) {
        Set-Content -Path $FilePath -Value $replaceResult.Content -Encoding UTF8 -NoNewline
        $totalFixed = $replaceResult.Replacements
        Write-Status "Fixed $totalFixed Unicode characters in $FilePath" "OK"
      }
    }

    return @{
      HasUnicode = $unicodeLocations.Count -gt 0
      Locations  = $unicodeLocations
      Fixed      = $totalFixed
    }

  }
  catch {
    Write-Status "Error processing file ${FilePath}: $($_.Exception.Message)" "ERROR"
    return @{ HasUnicode = $false; Locations = @(); Fixed = 0 }
  }
}

# Main execution
Write-Status "Unicode Character Detection Tool" "INFO"
Write-Host "====================================="

if ($Fix) {
  Write-Status "Running in FIX mode - will attempt to replace Unicode characters" "WARN"
}
else {
  Write-Status "Running in SCAN mode - will only detect Unicode characters" "INFO"
}

# Get all files to scan
$allFiles = @()
foreach ($pattern in $filePatterns) {
  $files = Get-ChildItem -Path $Path -Recurse -Filter $pattern -File | Where-Object {
    $_.FullName -notmatch '\\(\.git|__pycache__|node_modules|\.pytest_cache|\.vscode)\\'
  }
  $allFiles += $files
}

if ($allFiles.Count -eq 0) {
  Write-Status "No files found matching patterns: $($filePatterns -join ', ')" "WARN"
  exit 0
}

Write-Status "Scanning $($allFiles.Count) files..." "INFO"

$totalUnicodeFiles = 0
$totalUnicodeLocations = 0
$totalFixed = 0

foreach ($file in $allFiles) {
  $result = Scan-FileForUnicode -FilePath $file.FullName -ReplacementMap $unicodeMap -FixMode $Fix

  if ($result.HasUnicode) {
    $totalUnicodeFiles++
    $totalUnicodeLocations += $result.Locations.Count
    $totalFixed += $result.Fixed

    Write-Host ""
    Write-Status "Found in: $($file.FullName)" "WARN"

    foreach ($location in $result.Locations) {
      $charHex = Get-UnicodeDetails $location.Character
      Write-Host "  Line $($location.LineNumber): $($location.Context)"

      if ($ShowDetails) {
        Write-Host "    Character: '$($location.Character)' (U+$charHex)"
      }
    }
  }
}

Write-Host ""
Write-Host "[RESULTS] Scan Complete"
Write-Host "========================"
Write-Status "Unicode characters detected in $totalUnicodeLocations locations" "INFO"

if ($Fix) {
  Write-Status "Automatically fixed $totalFixed issues" "OK"
  $remaining = $totalUnicodeLocations - $totalFixed
  Write-Status "$remaining issues require manual fixing" "WARN"
}
else {
  Write-Status "Use -Fix flag to automatically replace common Unicode characters" "INFO"
}

Write-Host ""
Write-Status "Use ASCII alternatives instead of Unicode symbols" "INFO"
Write-Host "  Examples: [OK] [FAIL] [WARN] [ERROR] [INFO] [SUCCESS]"

if ($totalUnicodeLocations -gt 0) {
  exit 1
}
else {
  Write-Status "No Unicode characters found - all files are ASCII compatible!" "OK"
  exit 0
}
