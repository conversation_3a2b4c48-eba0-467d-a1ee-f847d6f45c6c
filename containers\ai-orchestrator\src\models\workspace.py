"""
Workspace Model for Multi-Tenant AI Coding Agent.

This module defines the SQLAlchemy model for managing user workspaces,
including container lifecycle and routing information.

Author: AI Coding Agent
Version: 1.0.0
"""

from datetime import datetime, timezone
from typing import Optional, Any, Dict
from enum import Enum
import json

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Text, Enum as SQLEnum

from src.models.database import Base

class WorkspaceStatus(str, Enum):
    """
    Workspace status enumeration.

    Defines the possible states of a workspace container:
    - CREATING: Workspace is being initialized
    - RUNNING: Workspace is active and accessible
    - STOPPING: Workspace is being shut down
    - STOPPED: Workspace is inactive
    - ERROR: Workspace encountered an error
    - DELETED: Workspace has been removed
    """
    CREATING = "creating"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"
    DELETED = "deleted"


class Workspace(Base):
    """
    Workspace model for managing user-specific development environments.

    Each workspace represents a containerized development environment
    associated with a specific user, including code-server instance
    and routing configuration. This model follows the multi-tenant
    workspace specification with proper status tracking and container
    lifecycle management.

    Attributes:
        id: Primary key for the workspace
        user_id: Supabase Auth user identifier (foreign key)
        container_id: Docker container ID (unique, nullable)
        container_name: Human-readable container name (unique)
        subdomain: Unique subdomain for workspace access
        status: Current workspace status (WorkspaceStatus enum)
        error_message: Error details when status is ERROR
        created_at: Workspace creation timestamp
        updated_at: Last modification timestamp
        workspace_config: JSON configuration for workspace settings

    Examples:
        Creating a new workspace:
            workspace = Workspace(
                user_id="user123",
                container_name="user123-workspace-1",
                subdomain="user123-ws1",
                status=WorkspaceStatus.CREATING
            )

        Updating workspace status:
            workspace.update_status(WorkspaceStatus.RUNNING)
    """

    __tablename__ = "workspaces"

    # Primary key
    id = Column(Integer, primary_key=True, index=True)

    # User relationship (from Supabase Auth)
    user_id = Column(String, index=True, nullable=False)

    # Container information
    container_id = Column(String, unique=True, nullable=True, index=True)
    container_name = Column(String, unique=True, nullable=False, index=True)

    # Routing information
    subdomain = Column(String, unique=True, nullable=False, index=True)

    # Workspace status using proper Enum
    status = Column(
        SQLEnum(WorkspaceStatus),
        default=WorkspaceStatus.STOPPED,
        nullable=False,
        index=True
    )

    # Error information
    error_message = Column(Text, nullable=True)

    # Metadata with proper timezone handling
    created_at = Column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        nullable=False
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False
    )

    # Workspace configuration as JSON text
    workspace_config = Column(Text, nullable=True)

    def __repr__(self) -> str:
        """
        String representation of workspace.

        Returns:
            String representation including key identifiers and status
        """
        return f"<Workspace(id={self.id}, user_id='{self.user_id}', subdomain='{self.subdomain}', status='{self.status.value}')>"

    @property
    def is_active(self) -> bool:
        """
        Check if workspace is in an active state.

        A workspace is considered active if it's currently being created
        or is already running.

        Returns:
            True if workspace is in CREATING or RUNNING state, False otherwise
        """
        current_status = getattr(self, 'status', WorkspaceStatus.STOPPED)
        return current_status in [WorkspaceStatus.CREATING, WorkspaceStatus.RUNNING]

    @property
    def is_running(self) -> bool:
        """
        Check if workspace is currently running.

        Returns:
            True if workspace status is RUNNING, False otherwise
        """
        current_status = getattr(self, 'status', WorkspaceStatus.STOPPED)
        return current_status == WorkspaceStatus.RUNNING

    @property
    def workspace_url(self) -> str:
        """
        Get the workspace access URL.

        Constructs the full URL for accessing the workspace based on
        the assigned subdomain.

        Returns:
            Complete URL for workspace access
        """
        return f"http://{self.subdomain}.localhost"

    @property
    def config_dict(self) -> Dict[str, Any]:
        """
        Get workspace configuration as dictionary.

        Parses the JSON workspace_config field into a Python dictionary.
        Returns empty dict if config is None or invalid JSON.

        Returns:
            Dictionary containing workspace configuration
        """
        config_value = getattr(self, 'workspace_config', None)
        if not config_value:
            return {}

        try:
            return json.loads(config_value)
        except (json.JSONDecodeError, TypeError):
            return {}

    def set_config(self, config: Dict[str, Any]) -> None:
        """
        Set workspace configuration from dictionary.

        Serializes the configuration dictionary to JSON and stores it
        in the workspace_config field.

        Args:
            config: Configuration dictionary to store

        Raises:
            TypeError: If config cannot be serialized to JSON
        """
        try:
            self.workspace_config = json.dumps(config)
            self.updated_at = datetime.now(timezone.utc)
        except TypeError as e:
            raise TypeError(f"Cannot serialize config to JSON: {e}")

    def update_status(
        self,
        status: WorkspaceStatus,
        error_message: Optional[str] = None
    ) -> None:
        """
        Update workspace status with optional error message.

        This method properly tracks status transitions and synchronizes
        with container state as required by the workspace specification.

        Args:
            status: New workspace status
            error_message: Optional error message (cleared if None and status is not ERROR)

        Examples:
            # Mark workspace as running
            workspace.update_status(WorkspaceStatus.RUNNING)

            # Mark workspace as error with message
            workspace.update_status(WorkspaceStatus.ERROR, "Container failed to start")
        """
        self.status = status

        # Clear error message unless we're setting an error status
        if status == WorkspaceStatus.ERROR:
            self.error_message = error_message
        elif error_message is None:
            self.error_message = None
        else:
            self.error_message = error_message

        self.updated_at = datetime.now(timezone.utc)

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert workspace to dictionary representation.

        Useful for API responses and serialization.

        Returns:
            Dictionary containing all workspace fields
        """
        # Get actual instance values safely
        created_at_val = getattr(self, 'created_at', None)
        updated_at_val = getattr(self, 'updated_at', None)
        status_val = getattr(self, 'status', WorkspaceStatus.STOPPED)

        return {
            "id": getattr(self, 'id', None),
            "user_id": getattr(self, 'user_id', None),
            "container_id": getattr(self, 'container_id', None),
            "container_name": getattr(self, 'container_name', None),
            "subdomain": getattr(self, 'subdomain', None),
            "status": status_val.value if hasattr(status_val, 'value') else str(status_val),
            "error_message": getattr(self, 'error_message', None),
            "created_at": created_at_val.isoformat() if created_at_val else None,
            "updated_at": updated_at_val.isoformat() if updated_at_val else None,
            "workspace_config": self.config_dict,
            "workspace_url": self.workspace_url,
            "is_active": self.is_active,
            "is_running": self.is_running
        }