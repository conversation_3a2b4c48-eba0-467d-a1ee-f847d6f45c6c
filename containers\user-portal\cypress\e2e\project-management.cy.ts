/// <reference types="cypress" />

describe('Project Management UI', () => {
  // Define mock data for a consistent test environment
  const mockProjects = [
    {
      id: '1',
      name: 'Test Project Alpha',
      description: 'This is a sample project for E2E testing.',
      status: 'Not Scanned',
      file_counts: {
        total: 100,
        scanned: 0,
        indexed: 0,
      },
    },
  ];

  beforeEach(() => {
    // Intercept the API call to fetch projects and return our mock data
    cy.intercept('GET', '/api/projects', {
      statusCode: 200,
      body: mockProjects,
    }).as('getProjects');

    // Intercept the scan API call
    cy.intercept('POST', '/api/projects/1/scan', (req) => {
      req.reply({
        statusCode: 202,
        body: { message: '<PERSON><PERSON> initiated successfully' },
      });
    }).as('startScan');

    // Visit the projects page before each test
    cy.visit('/projects');
    cy.wait('@getProjects'); // Wait for the initial project data to load
  });

  it('1. Navigation Verification: should load the page and confirm the URL', () => {
    cy.url().should('include', '/projects');
    cy.contains('h1', 'Projects').should('be.visible');
  });

  it('2. Initial State Verification: should display projects correctly', () => {
    // Check for the main components of the project card
    cy.get('[data-cy=project-card-1]').should('be.visible');
    cy.get('[data-cy=project-card-1]').within(() => {
      cy.contains(mockProjects[0].name).should('be.visible');
      cy.contains(mockProjects[0].description).should('be.visible');
      cy.get('[data-cy=status-badge]').should('contain', 'Not Scanned');
      cy.get('[data-cy=progress-bar]').should('have.attr', 'style').and('include', 'width: 0%');
      cy.get('[data-cy=start-scan-button]').should('be.enabled').and('contain', 'Start Scan');
    });
  });

  it('3. WebSocket Connection Test: should show live updates indicator', () => {
    // This test assumes a UI element indicates WebSocket status
    cy.get('[data-cy=live-updates-indicator]').should('be.visible').and('contain', 'Live');
  });

  it('4. Scan Initiation Test: should update UI when a scan starts', () => {
    // Click the start scan button
    cy.get('[data-cy=start-scan-button]').click();

    // Verify the API call was made
    cy.wait('@startScan');

    // Check for the toast notification
    cy.get('[data-cy=toast-notification]').should('be.visible').and('contain', 'Initiating project scan...');

    // Check that the card UI updates to a "Scanning" state
    cy.get('[data-cy=project-card-1]').within(() => {
      cy.get('[data-cy=status-badge]').should('contain', 'Scanning...');
      cy.get('[data-cy=start-scan-button]').should('be.disabled');
      // A loading spinner should be present inside the button
      cy.get('[data-cy=start-scan-button] svg').should('be.visible');
    });
  });

  it('5 & 6. Progress & Completion Test: should show real-time progress and completion', () => {
    // For this test, we simulate the backend sending updates.
    // We'll update the project data and re-trigger the fetch.

    // Start the scan
    cy.get('[data-cy=start-scan-button]').click();
    cy.wait('@startScan');

    // --- Simulate Progress Update ---
    const progressUpdate = { ...mockProjects[0], status: 'Scanning...', file_counts: { total: 100, scanned: 50, indexed: 20 } };
    cy.intercept('GET', '/api/projects', [progressUpdate]).as('getProjectsProgress');
    cy.wait(1000); // Simulate time passing for scan progress
    cy.visit('/projects'); // Re-visit to trigger fetch with new data
    cy.wait('@getProjectsProgress');

    cy.get('[data-cy=project-card-1]').within(() => {
      cy.get('[data-cy=progress-bar]').should('have.attr', 'style').and('include', 'width: 50%');
      cy.get('[data-cy=status-badge]').should('contain', 'Scanning...');
    });
    cy.get('[data-cy=toast-notification]').should('contain', 'Scanning file 50/100...');

    // --- Simulate Completion ---
    const completionUpdate = { ...mockProjects[0], status: 'Ready', file_counts: { total: 100, scanned: 100, indexed: 100 } };
    cy.intercept('GET', '/api/projects', [completionUpdate]).as('getProjectsComplete');
    cy.wait(1000); // Simulate more time
    cy.visit('/projects');
    cy.wait('@getProjectsComplete');

    cy.get('[data-cy=toast-notification]').should('be.visible').and('contain', 'Success');
    cy.get('[data-cy=project-card-1]').within(() => {
      cy.get('[data-cy=status-badge]').should('contain', 'Ready');
      cy.get('[data-cy=progress-bar]').should('have.attr', 'style').and('include', 'width: 100%');
      cy.get('[data-cy=start-scan-button]').should('be.enabled').and('contain', 'Re-Scan');
    });
  });

  it('7. Error Handling Test: should show an error toast on API failure', () => {
    // Override the scan interceptor to return a server error
    cy.intercept('POST', '/api/projects/1/scan', {
      statusCode: 500,
      body: { message: 'Internal Server Error' },
    }).as('startScanError');

    cy.get('[data-cy=start-scan-button]').click();
    cy.wait('@startScanError');

    // Check for a generic error toast
    cy.get('[data-cy=toast-notification]').should('be.visible').and('contain', 'An error occurred');

    // Ensure the card state has not changed
    cy.get('[data-cy=project-card-1]').within(() => {
        cy.get('[data-cy=status-badge]').should('contain', 'Not Scanned');
        cy.get('[data-cy=start-scan-button]').should('be.enabled');
    });
  });

  it('8. UI Responsiveness Test: should adapt to different screen sizes', () => {
    const viewports = ['macbook-15', 'ipad-2', 'iphone-x'];
    viewports.forEach(viewport => {
      cy.viewport(viewport);
      cy.get('[data-cy=project-grid]').should('be.visible');
      cy.get('[data-cy=project-card-1]').should('be.visible');
      cy.wait(200); // Wait for layout to settle
    });
  });
});
