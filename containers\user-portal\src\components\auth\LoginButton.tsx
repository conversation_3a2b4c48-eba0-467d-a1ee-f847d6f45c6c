"use client";

import React from 'react';
import { signIn, getProviders } from 'next-auth/react';
import { useState, useEffect } from 'react';

interface Provider {
  id: string;
  name: string;
  type: string;
  signinUrl: string;
  callbackUrl: string;
}

interface LoginButtonProps {
  className?: string;
  variant?: 'google' | 'credentials' | 'both';
  redirectTo?: string;
}

export const LoginButton: React.FC<LoginButtonProps> = ({
  className = "",
  variant = "both",
  redirectTo = "/dashboard"
}) => {
  const [providers, setProviders] = useState<Record<string, Provider> | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const loadProviders = async () => {
      const res = await getProviders();
      setProviders(res);
    };
    loadProviders();
  }, []);

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      await signIn('google', {
        callbackUrl: redirectTo,
        redirect: true,
      });
    } catch (error) {
      // Google sign-in error
      setIsLoading(false);
    }
  };

  const handleCredentialsSignIn = () => {
    // Redirect to the credentials login page
    window.location.href = '/login';
  };

  if (!providers) {
    return (
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded-md w-48"></div>
      </div>
    );
  }

  const GoogleButton = () => (
    <button
      onClick={handleGoogleSignIn}
      disabled={isLoading}
      className={`
        flex items-center justify-center gap-3 px-6 py-3
        border border-gray-300 rounded-lg
        bg-white hover:bg-gray-50
        text-gray-700 font-medium
        transition-colors duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
        ${className}
      `}
    >
      {isLoading ? (
        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-600"></div>
      ) : (
        <svg className="w-5 h-5" viewBox="0 0 24 24">
          <path
            fill="#4285F4"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="#34A853"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="#FBBC05"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="#EA4335"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
      )}
      <span>Continue with Google</span>
    </button>
  );

  const CredentialsButton = () => (
    <button
      onClick={handleCredentialsSignIn}
      className={`
        flex items-center justify-center gap-3 px-6 py-3
        border border-blue-600 rounded-lg
        bg-blue-600 hover:bg-blue-700
        text-white font-medium
        transition-colors duration-200
        ${className}
      `}
    >
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
        />
      </svg>
      <span>Sign in with Credentials</span>
    </button>
  );

  const renderButtons = () => {
    switch (variant) {
      case 'google':
        return providers?.google ? <GoogleButton /> : null;
      case 'credentials':
        return providers?.credentials ? <CredentialsButton /> : null;
      case 'both':
      default:
        return (
          <div className="space-y-3">
            {providers?.google && <GoogleButton />}
            {providers?.credentials && (
              <>
                {providers?.google && (
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-300" />
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-2 bg-white text-gray-500">or</span>
                    </div>
                  </div>
                )}
                <CredentialsButton />
              </>
            )}
          </div>
        );
    }
  };

  return <div>{renderButtons()}</div>;
};

export default LoginButton;
