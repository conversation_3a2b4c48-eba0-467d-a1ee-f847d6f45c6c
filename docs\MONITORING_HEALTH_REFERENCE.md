# Monitoring Stack Health Check Reference

## Service Endpoints & Health Checks

### Prometheus (Port 9090)

- **Web UI**: `http://localhost:9090`
- **Readiness**: `http://localhost:9090/-/ready`
- **Health**: `http://localhost:9090/-/healthy`
- **Metrics**: `http://localhost:9090/metrics`
- **Targets**: `http://localhost:9090/targets`

### Grafana (Port 3000)

- **Web UI**: `http://localhost:3000`
- **Health**: `http://localhost:3000/api/health`
- **Metrics**: `http://localhost:3000/metrics`

### Loki (Port 3100)

- **Web UI**: `http://localhost:3100`
- **Readiness**: `http://localhost:3100/ready`
- **Health**: `http://localhost:3100/health`
- **Metrics**: `http://localhost:3100/metrics`

### Promtail (Port 9080)

- **Readiness**: `http://localhost:9080/ready`
- **Health**: `http://localhost:9080/health`
- **Metrics**: `http://localhost:9080/metrics`

## Docker Health Check Commands

```bash
# Check individual service health
docker exec prometheus curl -s http://localhost:9090/-/ready
docker exec grafana curl -s http://localhost:3000/api/health
docker exec loki curl -s http://localhost:3100/ready
docker exec promtail curl -s http://localhost:9080/ready

# Run comprehensive health check
./check_monitoring_health.sh
```

## Prometheus Log Markers

- **Starting**: `msg="Start listening for connections" component=web address=0.0.0.0:9090`
- **Ready**: `msg="Server is ready to receive web requests."`
- **Healthy**: Container health check passes on `/-/ready` endpoint

## Troubleshooting

1. **Service not starting**: Check logs with `docker logs <service_name>`
2. **Health check failing**: Verify endpoint is responding
3. **Network issues**: Check `docker network ls` and service connectivity
4. **Configuration errors**: Validate config files in `./configs/` directory

## Quick Health Check

```bash
# One-liner health check
for service in prometheus grafana loki promtail; do
  echo "Checking $service..."
  docker exec $service curl -s http://localhost:9090/-/ready 2>/dev/null && echo "✅ $service OK" || echo "❌ $service FAIL"
done
```
