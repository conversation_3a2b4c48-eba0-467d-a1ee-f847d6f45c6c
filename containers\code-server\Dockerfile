# syntax=docker/dockerfile:1
# Multi-stage Dockerfile for code-server with VS Code extension
# ============================================================================
# BUILDER STAGE: Compile the VS Code extension
# ============================================================================
FROM node:20-alpine AS builder

# Layer 1: Install build dependencies (rarely changes)
RUN apk add --no-cache python3 make g++

# Set working directory for extension build
WORKDIR /build/extension

# Layer 2: Copy package files first for better Docker layer caching
COPY extensions/ai-chat-extension/package*.json ./

# Layer 3: Install dependencies with BuildKit cache mounting
RUN --mount=type=cache,target=/root/.npm \
  npm ci --only=production=false

# Layer 4: Copy extension source code (changes most frequently)
COPY extensions/ai-chat-extension/ ./

# Layer 5: Build the extension
RUN npm run compile && npm run package

# ============================================================================
# FINAL STAGE: code-server with pre-built extension
# ============================================================================
FROM codercom/code-server:4.91.1

# Switch to root for system setup
USER root

# Layer 1: System dependencies with cache mounts
RUN --mount=type=cache,target=/var/cache/apt \
  --mount=type=cache,target=/var/lib/apt/lists \
  curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
  apt-get update && \
  apt-get install -y \
  nodejs \
  curl \
  ca-certificates \
  dos2unix \
  gosu \
  && apt-get upgrade -y \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# Layer 2: User setup (rarely changes)
RUN (getent group coder || groupadd -r -g 1000 coder) && \
  (getent passwd coder || useradd -r -g coder -u 1000 -d /home/<USER>/bin/bash coder) && \
  mkdir -p /home/<USER>/.local/share/code-server/extensions && \
  chown -R coder:coder /home/<USER>

# Layer 3: Copy configuration files (changes occasionally)
COPY --chown=coder:coder install-extensions.sh /home/<USER>/install-extensions.sh
COPY --chown=coder:coder extensions.json /home/<USER>/extensions.json
COPY --chown=coder:coder settings.json /home/<USER>/settings.json
RUN chmod +x /home/<USER>/install-extensions.sh

# Layer 4: Copy entrypoint script
COPY --chown=root:root entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh && \
  dos2unix /usr/local/bin/entrypoint.sh 2>/dev/null || true

# Layer 5: Copy compiled extension from builder (changes with code)
COPY --link --from=builder --chown=coder:coder \
  /build/extension/package.json \
  /home/<USER>/.local/share/code-server/extensions/ai-chat-extension/package.json
COPY --link --from=builder --chown=coder:coder \
  /build/extension/dist/ \
  /home/<USER>/.local/share/code-server/extensions/ai-chat-extension/dist/

# Switch to non-root user
USER coder
WORKDIR /home/<USER>

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=90s --retries=3 \
  CMD curl -f http://localhost:8080/healthz || curl -f http://localhost:8080/login || exit 1

# Labels
LABEL org.opencontainers.image.title="AI Coding Agent - Code Server" \
  org.opencontainers.image.description="Browser-based VS Code with AI chat extension" \
  org.opencontainers.image.vendor="AI Coding Agent Project" \
  security.non-root="true" \
  security.user="coder"

# Entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
CMD ["code-server", "--bind-addr", "0.0.0.0:8080", "--auth", "password", "--disable-telemetry", "--disable-update-check"]