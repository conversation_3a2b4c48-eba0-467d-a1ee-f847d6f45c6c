# Supabase Configuration
# Get these from your Supabase project settings
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Google OAuth Configuration
# Get these from Google Cloud Console OAuth 2.0 credentials
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# GitHub OAuth Configuration
# Get these from GitHub Developer Settings OAuth Apps
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# NextAuth Configuration
NEXTAUTH_URL=http://portal.localhost
NEXTAUTH_SECRET=your-secure-random-secret-here

# Backend API Configuration (existing)
API_BASE_URL=http://ai-orchestrator:8000
NEXT_PUBLIC_API_BASE_URL=http://api.localhost

# Instructions:
# 1. Copy this file to .env.local in the user-portal directory
# 2. Fill in all the values with your actual credentials
# 3. Never commit the .env.local file to version control
# 4. Use different values for development and production environments
