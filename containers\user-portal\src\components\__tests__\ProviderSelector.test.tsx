/**
 * ProviderSelector Component Tests
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ProviderSelector from '../ProviderSelector';
import { LLMProvider } from '@/types/role';

// Mock react-select
jest.mock('react-select', () => {
  return ({ value, onChange, options, placeholder, isDisabled, formatOptionLabel }: any) => {
    return (
      <div data-testid="provider-select">
        <select
          value={value?.value || ''}
          onChange={(e) => {
            const selectedOption = options.find((opt: any) => opt.value === e.target.value);
            onChange(selectedOption);
          }}
          disabled={isDisabled}
        >
          <option value="" disabled>
            {placeholder}
          </option>
          {options.map((option: any) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {value && formatOptionLabel && (
          <div data-testid="formatted-option">
            {formatOptionLabel(value)}
          </div>
        )}
      </div>
    );
  };
});

const mockProviders = [
  {
    value: LLMProvider.OLLAMA,
    label: 'Ollama',
    requiresApiKey: false,
    description: 'Local LLM serving',
  },
  {
    value: LLMProvider.OPENAI,
    label: 'OpenAI',
    requiresApiKey: true,
    description: 'GPT models',
  },
];

const defaultProps = {
  value: LLMProvider.OLLAMA,
  onChange: jest.fn(),
  providers: mockProviders,
};

describe('ProviderSelector', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with proper label', () => {
    render(<ProviderSelector {...defaultProps} />);

    expect(screen.getByLabelText(/LLM Provider/)).toBeInTheDocument();
    expect(screen.getByText('LLM Provider')).toBeInTheDocument();
    expect(screen.getByText('*')).toBeInTheDocument(); // Required indicator
  });

  it('displays the selected provider', () => {
    render(<ProviderSelector {...defaultProps} />);

    const select = screen.getByTestId('provider-select').querySelector('select');
    expect(select).toHaveValue(LLMProvider.OLLAMA);
  });

  it('calls onChange when selection changes', async () => {
    const user = userEvent.setup();
    const mockOnChange = jest.fn();

    render(<ProviderSelector {...defaultProps} onChange={mockOnChange} />);

    const select = screen.getByTestId('provider-select').querySelector('select')!;
    await user.selectOptions(select, LLMProvider.OPENAI);

    expect(mockOnChange).toHaveBeenCalledWith(LLMProvider.OPENAI);
  });

  it('displays error message when provided', () => {
    const errorMessage = 'Provider is required';
    render(<ProviderSelector {...defaultProps} error={errorMessage} />);

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('is disabled when disabled prop is true', () => {
    render(<ProviderSelector {...defaultProps} disabled />);

    const select = screen.getByTestId('provider-select').querySelector('select');
    expect(select).toBeDisabled();
  });

  it('shows provider information when a provider is selected', () => {
    render(<ProviderSelector {...defaultProps} value={LLMProvider.OLLAMA} />);

    expect(screen.getByText('Selected Provider: Ollama')).toBeInTheDocument();
    expect(screen.getByText('Local LLM serving')).toBeInTheDocument();
  });

  it('shows API key requirement notice for cloud providers', () => {
    render(<ProviderSelector {...defaultProps} value={LLMProvider.OPENAI} />);

    expect(screen.getByText(/This provider requires an API key/)).toBeInTheDocument();
  });

  it('does not show API key notice for Ollama', () => {
    render(<ProviderSelector {...defaultProps} value={LLMProvider.OLLAMA} />);

    expect(screen.queryByText(/This provider requires an API key/)).not.toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    const errorMessage = 'Test error';
    render(<ProviderSelector {...defaultProps} error={errorMessage} />);

    const label = screen.getByLabelText(/LLM Provider/);
    expect(label).toHaveAttribute('aria-describedby', 'provider-error');
  });

  it('uses default providers when none provided', () => {
    const { providers, ...propsWithoutProviders } = defaultProps;
    render(<ProviderSelector {...propsWithoutProviders} />);

    // Should render without error and show default providers
    expect(screen.getByTestId('provider-select')).toBeInTheDocument();
  });
});