"""
Task repository for CRUD and query operations on Task entities.
"""
from __future__ import annotations

from typing import Optional, Dict, Any

from sqlalchemy.orm import Session
from sqlalchemy import select

from src.models import Task


class TaskRepository:
    """Encapsulates DB operations for Task entities."""

    @staticmethod
    async def create_task(
        db: Session,
        *,
        project_id: int,
        agent_role: str,
        input_data: Dict[str, Any],
    ) -> Task:
        """Create and persist a new Task.

        Note: SQLAlchemy ORM session is synchronous; this function is async to
        conform with async service layers but uses blocking operations.
        """
        task = Task(project_id=project_id, agent_role=agent_role, input_data=input_data)
        db.add(task)
        db.commit()
        db.refresh(task)
        return task

    @staticmethod
    async def get_next_pending_task_for_project(db: Session, project_id: int) -> Optional[Task]:
        """Fetch next pending task by created_at ascending for the given project."""
        stmt = select(Task).where(Task.project_id == project_id, Task.status == "pending").order_by(Task.created_at.asc())
        result = db.execute(stmt)
        row = result.scalars().first()
        return row

    @staticmethod
    async def update_task_status(db: Session, task_id: int, status: str) -> Task:
        """Update a task's status and return the updated entity."""
        stmt = select(Task).where(Task.id == task_id)
        task = db.execute(stmt).scalars().first()
        if not task:
            raise ValueError(f"Task {task_id} not found")

        task.status = status
        db.add(task)
        db.commit()
        db.refresh(task)
        return task

    @staticmethod
    async def record_task_output(db: Session, task_id: int, output_data: Dict[str, Any]) -> Task:
        """Store output_data on the task and return the updated entity."""
        stmt = select(Task).where(Task.id == task_id)
        task = db.execute(stmt).scalars().first()
        if not task:
            raise ValueError(f"Task {task_id} not found")

        task.output_data = output_data
        db.add(task)
        db.commit()
        db.refresh(task)
        return task