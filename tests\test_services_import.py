"""
Unit test for services import resilience.

Ensures that importing services does not raise ModuleNotFoundError for 'src'
when the package is loaded in different ways.
"""

import os
import sys
import pytest
from pathlib import Path


def test_services_import_resilience():
    """Test that services can be imported without 'src' ModuleNotFoundError."""
    # Save original sys.path
    original_path = sys.path[:]

    try:
        # Simulate running with 'containers/ai-orchestrator/src' on sys.path
        src_dir = Path(__file__).resolve().parents[1] / 'containers' / 'ai-orchestrator' / 'src'
        if src_dir.exists():
            sys.path.insert(0, str(src_dir))

        # Set minimal env vars for import
        os.environ['TESTING'] = '1'
        os.environ['REDIS_URL'] = 'redis://localhost:6379/0'
        os.environ['DATABASE_URL'] = 'sqlite:///./test.db'
        os.environ['JWT_SECRET'] = 'testsecret'
        os.environ['SUPABASE_URL'] = 'http://localhost'
        os.environ['SUPABASE_KEY'] = 'testkey'

        # Attempt import
        from services import EnhancedLLMService, TaskValidator, ErrorRecoverySystem, CheckpointManager

        # Assert they are not None (imported successfully)
        assert EnhancedLLMService is not None
        assert TaskValidator is not None
        assert ErrorRecoverySystem is not None
        assert CheckpointManager is not None

    finally:
        # Restore sys.path
        sys.path[:] = original_path
        # Clean up env vars
        for key in ['TESTING', 'REDIS_URL', 'DATABASE_URL', 'JWT_SECRET', 'SUPABASE_URL', 'SUPABASE_KEY']:
            os.environ.pop(key, None)


if __name__ == '__main__':
    test_services_import_resilience()
    print("Test passed: services import is resilient")
