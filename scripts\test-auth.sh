#!/bin/bash
# Test authentication flow for AI Coding Agent

set -e

echo " Testing Authentication Flow"
echo "=============================="

# Check if required environment variables are set
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_KEY" ]; then
    echo " Error: SUPABASE_URL and SUPABASE_KEY must be set"
    exit 1
fi

# Test health endpoint (should work without auth)
echo " Testing health endpoint..."
curl -s -f http://localhost:8000/health > /dev/null && echo " Health endpoint accessible" || echo " Health endpoint failed"

# Test registration endpoint
echo " Testing user registration..."
REGISTRATION_RESPONSE=$(curl -s -X POST http://localhost:8000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123",
    "username": "testuser"
  }')

if echo "$REGISTRATION_RESPONSE" | grep -q "access_token"; then
    echo " User registration successful"
    ACCESS_TOKEN=$(echo "$REGISTRATION_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access_token'])")
else
    echo " User registration failed"
    echo "Response: $REGISTRATION_RESPONSE"
fi

# Test login endpoint
echo " Testing user login..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123"
  }')

if echo "$LOGIN_RESPONSE" | grep -q "access_token"; then
    echo " User login successful"
    ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | python3 -c "import sys, json; print(json.load(sys.stdin)['access_token'])")
else
    echo " User login failed"
    echo "Response: $LOGIN_RESPONSE"
fi

# Test protected endpoint with token
if [ ! -z "$ACCESS_TOKEN" ]; then
    echo "  Testing protected endpoint with authentication..."
    PROTECTED_RESPONSE=$(curl -s -X GET http://localhost:8000/api/agents \
      -H "Authorization: Bearer $ACCESS_TOKEN")

    if echo "$PROTECTED_RESPONSE" | grep -q "agents"; then
        echo " Protected endpoint accessible with valid token"
    else
        echo " Protected endpoint failed with valid token"
        echo "Response: $PROTECTED_RESPONSE"
    fi
else
    echo "  Skipping protected endpoint test (no access token)"
fi

# Test protected endpoint without token
echo " Testing protected endpoint without authentication..."
PROTECTED_RESPONSE_NO_AUTH=$(curl -s -X GET http://localhost:8000/api/agents -w "%{http_code}" -o /dev/null)

if [ "$PROTECTED_RESPONSE_NO_AUTH" = "403" ] || [ "$PROTECTED_RESPONSE_NO_AUTH" = "401" ]; then
    echo " Protected endpoint correctly rejects unauthenticated requests"
else
    echo " Protected endpoint should reject unauthenticated requests"
    echo "Status code: $PROTECTED_RESPONSE_NO_AUTH"
fi

echo ""
echo " Authentication tests completed!"
