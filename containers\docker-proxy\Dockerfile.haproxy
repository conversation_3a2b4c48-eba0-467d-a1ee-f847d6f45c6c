FROM haproxy:2.8-alpine

# Copy HAProxy configuration
COPY haproxy.cfg /usr/local/etc/haproxy/haproxy.cfg

# Health check using HAProxy's built-in stats
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD echo "GET /stats" | nc localhost 8080 || exit 1

# Expose Docker API port
EXPOSE 2375

# Expose stats port for health checks
EXPOSE 8080

# HAProxy runs as non-root by default, but needs access to Docker socket
USER root
