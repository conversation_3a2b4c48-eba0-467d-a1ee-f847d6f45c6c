"""
Template Registry Service for AI Coding Agent

Manages multiple project template types with validation, versioning,
and extensibility features.
"""

import logging
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    yaml = None
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class TemplateType(Enum):
    """Supported template types."""
    WEBAPP = "webapp"
    REACT_FRONTEND = "react-frontend"
    MICROSERVICE = "microservice"
    NEXTJS_APP = "nextjs-app"
    DJANGO_API = "django-api"
    MOBILE_APP = "mobile-app"


@dataclass
class TemplateMetadata:
    """Template metadata and configuration."""
    name: str
    type: TemplateType
    version: str
    description: str
    required_placeholders: List[str]
    optional_placeholders: List[str]
    default_port: int
    health_endpoint: str
    startup_timeout: int
    dependencies: List[str]
    tags: List[str]


class TemplateRegistry:
    """Registry for managing project templates."""

    def __init__(self, templates_root: Optional[Path] = None):
        self.templates_root = templates_root or Path(__file__).parent.parent.parent.parent.parent / "templates"
        self._registry: Dict[TemplateType, TemplateMetadata] = {}
        self._initialize_registry()

    def _initialize_registry(self):
        """Initialize the template registry from YAML configuration."""

        yaml_file = Path(__file__).parent / "templates.yaml"

        if not yaml_file.exists():
            logger.warning(f"Templates YAML not found at {yaml_file}, falling back to built-in templates")
            self._initialize_builtin_templates()
            return

        if not YAML_AVAILABLE or yaml is None:
            logger.error("PyYAML not available, cannot load templates from YAML file")
            self._initialize_builtin_templates()
            return

        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                # Use the yaml import we added
                config = yaml.safe_load(f)

            templates_data = config.get('templates', {})

            for template_key, template_config in templates_data.items():
                template_type_str = template_config.get('type', template_key)

                # Convert string to TemplateType enum
                try:
                    template_type = TemplateType(template_type_str)
                except ValueError:
                    logger.warning(f"Unknown template type: {template_type_str}, skipping")
                    continue

                metadata = TemplateMetadata(
                    name=template_config.get('name', ''),
                    type=template_type,
                    version=template_config.get('version', '1.0.0'),
                    description=template_config.get('description', ''),
                    required_placeholders=template_config.get('required_placeholders', []),
                    optional_placeholders=template_config.get('optional_placeholders', []),
                    default_port=template_config.get('default_port', 8000),
                    health_endpoint=template_config.get('health_endpoint', '/health'),
                    startup_timeout=template_config.get('startup_timeout', 60),
                    dependencies=template_config.get('dependencies', []),
                    tags=template_config.get('tags', [])
                )

                self._registry[template_type] = metadata

            logger.info(f"Loaded {len(self._registry)} templates from {yaml_file}")

        except Exception as e:
            logger.error(f"Failed to load templates from {yaml_file}: {e}")
            logger.info("Falling back to built-in templates")
            self._initialize_builtin_templates()

    def _initialize_builtin_templates(self):
        """Initialize the template registry with built-in templates as fallback."""

        # WebApp Template (FastAPI + PostgreSQL + Redis)
        self._registry[TemplateType.WEBAPP] = TemplateMetadata(
            name="Full-Stack Web Application",
            type=TemplateType.WEBAPP,
            version="1.0.0",
            description="Production-ready FastAPI application with PostgreSQL and Redis",
            required_placeholders=["__PROJECT_NAME__"],
            optional_placeholders=["__AUTHOR__", "__VERSION__"],
            default_port=8000,
            health_endpoint="/health",
            startup_timeout=300,
            dependencies=["docker", "docker-compose"],
            tags=["fastapi", "postgresql", "redis", "production"]
        )

        # React Frontend Template
        self._registry[TemplateType.REACT_FRONTEND] = TemplateMetadata(
            name="React Frontend Application",
            type=TemplateType.REACT_FRONTEND,
            version="1.0.0",
            description="Modern React application with TypeScript and Tailwind CSS",
            required_placeholders=["__PROJECT_NAME__"],
            optional_placeholders=["__AUTHOR__", "__VERSION__", "__API_URL__"],
            default_port=3000,
            health_endpoint="/",
            startup_timeout=180,
            dependencies=["node", "npm"],
            tags=["react", "typescript", "tailwind", "frontend"]
        )

        # Microservice Template
        self._registry[TemplateType.MICROSERVICE] = TemplateMetadata(
            name="Microservice API",
            type=TemplateType.MICROSERVICE,
            version="1.0.0",
            description="Lightweight FastAPI microservice with minimal dependencies",
            required_placeholders=["__PROJECT_NAME__", "__SERVICE_NAME__"],
            optional_placeholders=["__VERSION__", "__NAMESPACE__"],
            default_port=8080,
            health_endpoint="/health",
            startup_timeout=120,
            dependencies=["docker"],
            tags=["microservice", "fastapi", "lightweight"]
        )

        logger.info(f"Initialized template registry with {len(self._registry)} templates")

    def get_template_metadata(self, template_type: TemplateType) -> Optional[TemplateMetadata]:
        """Get metadata for a specific template type."""
        return self._registry.get(template_type)

    def list_available_templates(self) -> List[TemplateMetadata]:
        """List all available template types."""
        return list(self._registry.values())

    def get_template_path(self, template_type: TemplateType) -> Path:
        """Get the filesystem path for a template."""
        return self.templates_root / template_type.value

    def validate_template_exists(self, template_type: TemplateType) -> bool:
        """Validate that a template directory exists."""
        template_path = self.get_template_path(template_type)
        return template_path.exists() and template_path.is_dir()

    def register_custom_template(self, metadata: TemplateMetadata) -> bool:
        """Register a custom template type."""
        try:
            # Validate template directory exists
            if not self.validate_template_exists(metadata.type):
                logger.error(f"Template directory not found: {metadata.type.value}")
                return False

            # Register the template
            self._registry[metadata.type] = metadata
            logger.info(f"Registered custom template: {metadata.name}")
            return True

        except Exception as e:
            logger.error(f"Failed to register custom template: {e}")
            return False

    def get_template_placeholders(self, template_type: TemplateType) -> Dict[str, List[str]]:
        """Get required and optional placeholders for a template."""
        metadata = self.get_template_metadata(template_type)
        if not metadata:
            return {"required": [], "optional": []}

        return {
            "required": metadata.required_placeholders,
            "optional": metadata.optional_placeholders
        }

    def validate_template_structure(self, template_type: TemplateType) -> Dict[str, Any]:
        """Validate template structure and return validation results."""
        metadata = self.get_template_metadata(template_type)
        if not metadata:
            return {"valid": False, "error": "Template type not found"}

        template_path = self.get_template_path(template_type)
        if not template_path.exists():
            return {"valid": False, "error": f"Template directory not found: {template_path}"}

        # Check for required files
        required_files = ["Dockerfile.template", "docker-compose.yml.template"]
        missing_files = []

        for file_name in required_files:
            if not (template_path / file_name).exists():
                missing_files.append(file_name)

        if missing_files:
            return {
                "valid": False,
                "error": f"Missing required files: {missing_files}"
            }

        # Count template files and placeholders
        template_files = list(template_path.rglob("*.template"))
        placeholder_count = 0

        for template_file in template_files:
            try:
                content = template_file.read_text(encoding='utf-8')
                for placeholder in metadata.required_placeholders:
                    placeholder_count += content.count(placeholder)
            except Exception:
                continue

        return {
            "valid": True,
            "template_files": len(template_files),
            "placeholder_count": placeholder_count,
            "metadata": metadata
        }

    def get_template_for_project_type(self, project_description: str) -> TemplateType:
        """Intelligently select template based on project description."""
        description_lower = project_description.lower()

        # Simple keyword matching (can be enhanced with ML)
        if any(keyword in description_lower for keyword in ["react", "frontend", "ui", "client"]):
            return TemplateType.REACT_FRONTEND
        elif any(keyword in description_lower for keyword in ["microservice", "api", "service"]):
            return TemplateType.MICROSERVICE
        elif any(keyword in description_lower for keyword in ["nextjs", "next.js"]):
            return TemplateType.NEXTJS_APP
        else:
            # Default to full-stack webapp
            return TemplateType.WEBAPP


# Global template registry instance
template_registry = TemplateRegistry()


def get_template_registry() -> TemplateRegistry:
    """Get the global template registry instance."""
    return template_registry
