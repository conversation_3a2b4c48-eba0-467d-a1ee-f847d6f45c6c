# Vector Storage Consolidation Plan
## Migration from ChromaDB + Supabase pgvector to Supabase pgvector Only

### Overview

This document outlines the plan to consolidate vector storage from the current dual-system (ChromaDB for public knowledge + Supabase pgvector for user data) to a single Supabase pgvector implementation.

## Current Architecture

```
┌─────────────────────────────────────┐
│         ai-orchestrator-chroma      │
│                                     │
│  ┌─────────────┐ ┌─────────────────┐│
│  │ FastAPI     │ │ ChromaDB        ││
│  │ (Port 8000) │ │ (Embedded)      ││
│  │             │ │ - Public docs   ││
│  │             │ │ - Code snippets ││
│  └─────────────┘ └─────────────────┘│
└─────────────────────────────────────┘
           │
           ▼
┌─────────────────────────────────────┐
│            Supabase                 │
│  ┌─────────────────────────────────┐│
│  │ PostgreSQL + pgvector           ││
│  │ - User project embeddings      ││
│  │ - Private documents             ││
│  │ - RLS-protected data            ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
```

## Target Architecture

```
┌─────────────────────────────────────┐
│         ai-orchestrator             │
│                                     │
│  ┌─────────────┐                    │
│  │ FastAPI     │                    │
│  │ (Port 8000) │                    │
│  │             │                    │
│  └─────────────┘                    │
└─────────────────────────────────────┘
           │
           ▼
┌─────────────────────────────────────┐
│            Supabase                 │
│  ┌─────────────────────────────────┐│
│  │ PostgreSQL + pgvector           ││
│  │                                 ││
│  │ Collections:                    ││
│  │ ├─ public_knowledge             ││
│  │ │  ├─ documentation             ││
│  │ │  ├─ code_snippets             ││
│  │ │  └─ tutorials                 ││
│  │ └─ user_projects (RLS)          ││
│  │    ├─ project_embeddings       ││
│  │    ├─ user_documents           ││
│  │    └─ private_data             ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
```

## Database Schema Design

### Enhanced Schema for Two-Tier Knowledge

```sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Knowledge tier enumeration
CREATE TYPE knowledge_tier AS ENUM ('public', 'private');

-- Enhanced documents table with tier support
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    file_type VARCHAR(50),
    file_size BIGINT,
    knowledge_tier knowledge_tier NOT NULL DEFAULT 'private',

    -- User/Project association (NULL for public knowledge)
    user_id UUID REFERENCES auth.users(id),
    project_id UUID REFERENCES projects(id),

    -- Metadata
    metadata JSONB DEFAULT '{}',
    tags TEXT[],

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Full-text search
    search_vector tsvector GENERATED ALWAYS AS (
        to_tsvector('english', name || ' ' || content)
    ) STORED
);

-- Enhanced embeddings table with tier support
CREATE TABLE document_embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL DEFAULT 0,
    content TEXT NOT NULL,
    embedding vector(1024), -- BGE-large-en-v1.5 dimension

    -- Inherit tier from document
    knowledge_tier knowledge_tier NOT NULL,

    -- User/Project association (inherited from document)
    user_id UUID,
    project_id UUID,

    -- Metadata
    metadata JSONB DEFAULT '{}',
    content_hash VARCHAR(32) NOT NULL, -- MD5 for deduplication

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    UNIQUE(document_id, chunk_index),
    UNIQUE(content_hash) -- Prevent duplicate content
);

-- Collections for organizing knowledge
CREATE TABLE knowledge_collections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    knowledge_tier knowledge_tier NOT NULL,

    -- User/Project association (NULL for public collections)
    user_id UUID REFERENCES auth.users(id),
    project_id UUID REFERENCES projects(id),

    -- Metadata
    metadata JSONB DEFAULT '{}',

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure unique names per tier/user/project
    UNIQUE(name, knowledge_tier, COALESCE(user_id, '00000000-0000-0000-0000-000000000000'), COALESCE(project_id, '00000000-0000-0000-0000-000000000000'))
);

-- Document-Collection relationship
CREATE TABLE document_collections (
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    collection_id UUID REFERENCES knowledge_collections(id) ON DELETE CASCADE,
    PRIMARY KEY (document_id, collection_id)
);

-- Indexes for performance
CREATE INDEX idx_documents_tier_user ON documents(knowledge_tier, user_id);
CREATE INDEX idx_documents_tier_project ON documents(knowledge_tier, project_id);
CREATE INDEX idx_documents_search_vector ON documents USING GIN(search_vector);
CREATE INDEX idx_documents_tags ON documents USING GIN(tags);

CREATE INDEX idx_embeddings_tier_user ON document_embeddings(knowledge_tier, user_id);
CREATE INDEX idx_embeddings_tier_project ON document_embeddings(knowledge_tier, project_id);
CREATE INDEX idx_embeddings_vector ON document_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX idx_embeddings_document ON document_embeddings(document_id);

CREATE INDEX idx_collections_tier_user ON knowledge_collections(knowledge_tier, user_id);
CREATE INDEX idx_collections_tier_project ON knowledge_collections(knowledge_tier, project_id);
```

## Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE knowledge_collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_collections ENABLE ROW LEVEL SECURITY;

-- Public knowledge access policies
CREATE POLICY "Public knowledge readable by all authenticated users"
ON documents FOR SELECT
TO authenticated
USING (knowledge_tier = 'public');

CREATE POLICY "Public embeddings readable by all authenticated users"
ON document_embeddings FOR SELECT
TO authenticated
USING (knowledge_tier = 'public');

CREATE POLICY "Public collections readable by all authenticated users"
ON knowledge_collections FOR SELECT
TO authenticated
USING (knowledge_tier = 'public');

-- Private knowledge access policies
CREATE POLICY "Private documents accessible by owner"
ON documents FOR ALL
TO authenticated
USING (knowledge_tier = 'private' AND (user_id = auth.uid() OR project_id IN (
    SELECT id FROM projects WHERE user_id = auth.uid()
)));

CREATE POLICY "Private embeddings accessible by owner"
ON document_embeddings FOR ALL
TO authenticated
USING (knowledge_tier = 'private' AND (user_id = auth.uid() OR project_id IN (
    SELECT id FROM projects WHERE user_id = auth.uid()
)));

CREATE POLICY "Private collections accessible by owner"
ON knowledge_collections FOR ALL
TO authenticated
USING (knowledge_tier = 'private' AND (user_id = auth.uid() OR project_id IN (
    SELECT id FROM projects WHERE user_id = auth.uid()
)));

-- Service role policies (for AI orchestrator operations)
CREATE POLICY "Service role full access"
ON documents FOR ALL
TO service_role
USING (true);

CREATE POLICY "Service role full access"
ON document_embeddings FOR ALL
TO service_role
USING (true);

CREATE POLICY "Service role full access"
ON knowledge_collections FOR ALL
TO service_role
USING (true);
```

## Migration Steps

### Phase 1: Preparation
1. **Database Schema**: Deploy enhanced schema to Supabase
2. **Code Updates**: Modify VectorService to support knowledge tiers
3. **Configuration**: Update environment variables

### Phase 2: Public Knowledge Migration
1. **Export ChromaDB Data**: Extract all public knowledge embeddings
2. **Transform Data**: Convert to Supabase schema format
3. **Import to Supabase**: Bulk insert with knowledge_tier='public'
4. **Validation**: Verify data integrity and search functionality

### Phase 3: Code Consolidation
1. **Remove ChromaDB Dependencies**: Update requirements.txt
2. **Update AI Orchestrator**: Remove ChromaDB initialization
3. **Simplify Docker**: Update Dockerfiles and compose files
4. **Update Documentation**: Reflect new architecture

### Phase 4: Testing & Validation
1. **Integration Tests**: Verify both tiers work correctly
2. **Performance Testing**: Ensure search performance meets requirements
3. **Security Testing**: Validate RLS policies work correctly
4. **Load Testing**: Test with realistic data volumes

## Implementation Details

### Enhanced VectorService

```python
class UnifiedVectorService:
    """Unified vector service using only Supabase pgvector."""

    async def store_document(
        self,
        document: DocumentChunk,
        knowledge_tier: KnowledgeTier = KnowledgeTier.PRIVATE,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        collection_name: Optional[str] = None
    ) -> str:
        """Store document with appropriate tier and permissions."""

    async def search_knowledge(
        self,
        query: str,
        knowledge_tier: Optional[KnowledgeTier] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        limit: int = 10
    ) -> List[SearchResult]:
        """Search across knowledge tiers with RLS enforcement."""

    async def hybrid_search(
        self,
        query: str,
        knowledge_tier: Optional[KnowledgeTier] = None,
        user_id: Optional[str] = None,
        project_id: Optional[str] = None,
        vector_weight: float = 0.7,
        text_weight: float = 0.3,
        limit: int = 10
    ) -> List[SearchResult]:
        """Hybrid vector + full-text search."""
```

### Knowledge Tier Management

```python
class KnowledgeTier(str, Enum):
    PUBLIC = "public"
    PRIVATE = "private"

class KnowledgeManager:
    """Manages knowledge tier operations."""

    async def create_public_collection(
        self,
        name: str,
        description: str
    ) -> str:
        """Create a public knowledge collection."""

    async def migrate_public_knowledge(
        self,
        source_data: List[Dict[str, Any]]
    ) -> None:
        """Migrate public knowledge from ChromaDB."""
```

## Performance Considerations

### Query Optimization

```sql
-- Optimized search query for both tiers
WITH vector_search AS (
    SELECT
        de.*,
        d.name as document_name,
        d.knowledge_tier,
        (de.embedding <=> $1::vector) as distance
    FROM document_embeddings de
    JOIN documents d ON de.document_id = d.id
    WHERE
        ($2::knowledge_tier IS NULL OR d.knowledge_tier = $2::knowledge_tier)
        AND ($3::uuid IS NULL OR d.user_id = $3::uuid OR d.project_id IN (
            SELECT id FROM projects WHERE user_id = $3::uuid
        ))
    ORDER BY de.embedding <=> $1::vector
    LIMIT $4
),
text_search AS (
    SELECT
        d.*,
        ts_rank(d.search_vector, plainto_tsquery($5)) as rank
    FROM documents d
    WHERE
        d.search_vector @@ plainto_tsquery($5)
        AND ($2::knowledge_tier IS NULL OR d.knowledge_tier = $2::knowledge_tier)
        AND ($3::uuid IS NULL OR d.user_id = $3::uuid OR d.project_id IN (
            SELECT id FROM projects WHERE user_id = $3::uuid
        ))
    ORDER BY rank DESC
    LIMIT $4
)
-- Combine results with weighted scoring
SELECT * FROM vector_search
UNION ALL
SELECT * FROM text_search
ORDER BY (distance * $6 + rank * $7) -- vector_weight, text_weight
LIMIT $4;
```

## Benefits of Consolidation

### Resource Savings
- **Memory**: ~200-300MB reduction (no ChromaDB)
- **CPU**: Eliminate HTTP API overhead
- **Storage**: Single vector storage system
- **Network**: No internal service communication

### Operational Benefits
- **Simplified Monitoring**: Single vector database to monitor
- **Unified Backups**: All vector data in one place
- **Easier Scaling**: PostgreSQL horizontal scaling options
- **Better Debugging**: All operations in SQL logs

### Development Benefits
- **Single API**: Consistent vector operations
- **Better Testing**: Use same test infrastructure
- **Simplified Deployment**: One less service to manage
- **Cleaner Architecture**: Clear separation of concerns

## Risks and Mitigation

### Risk: PostgreSQL Performance
- **Mitigation**: Use pgvector optimizations, proper indexing
- **Monitoring**: Track query performance and resource usage

### Risk: Data Migration Issues
- **Mitigation**: Thorough testing, rollback plan
- **Validation**: Compare search results before/after migration

### Risk: RLS Complexity
- **Mitigation**: Comprehensive policy testing
- **Documentation**: Clear policy documentation and examples

## Timeline

- **Week 1**: Database schema preparation and testing
- **Week 2**: VectorService refactoring and unit tests
- **Week 3**: Data migration and validation
- **Week 4**: Integration testing and performance optimization
- **Week 5**: Documentation updates and deployment

## Success Metrics

- **Performance**: Search latency < 100ms (95th percentile)
- **Resource Usage**: Memory reduction of 200MB+
- **Functionality**: All existing vector operations work correctly
- **Security**: RLS policies prevent unauthorized access
- **Data Integrity**: 100% data migration success rate
