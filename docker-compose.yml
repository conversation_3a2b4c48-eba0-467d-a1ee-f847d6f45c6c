# Production Docker Compose Configuration
# For AI Coding Agent - Multi-container-first development environment
# Usage: docker-compose up -d
# For development: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --watch

# =============================================================================
# DOCKER SECRETS CONFIGURATION
# =============================================================================
# Secrets are stored in ./secrets/ directory and mounted as files in containers
# This provides enterprise-grade security for sensitive configuration
secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt
  jwt_secret:
    file: ./secrets/jwt_secret.txt
  supabase_url:
    file: ./secrets/supabase_url.txt
  supabase_key:
    file: ./secrets/supabase_key.txt
  supabase_service_key:
    file: ./secrets/supabase_service_key.txt
  code_server_password:
    file: ./secrets/code_server_password.txt
  grafana_admin_password:
    file: ./secrets/grafana_admin_password.txt
  openai_api_key:
    file: ./secrets/openai_api_key.txt
  anthropic_api_key:
    file: ./secrets/anthropic_api_key.txt
  openrouter_api_key:
    file: ./secrets/openrouter_api_key.txt
  redis_password:
    file: ./secrets/redis_password.txt

# =============================================================================
# NETWORK CONFIGURATION - TWO-NETWORK SECURITY MODEL
# =============================================================================
# Implements a dual-network architecture for enhanced security:
# 1. WEB NETWORK (DMZ): Public-facing services accessible via Traefik
# 2. INTERNAL NETWORK: Secure backend services (database, cache, etc.)

networks:
  # WEB NETWORK (DMZ) - Public-facing services that need to be reached by Traefik
  # Members: Traefik, ai-orchestrator, user-portal, code-server instances, monitoring web UIs
  # Security Zone: Demilitarized Zone (DMZ) - External access controlled by reverse proxy
  web:
    external: true
    name: ai-coding-agent_web_network
    # Labels removed for external network - managed externally

    # INTERNAL NETWORK - Secure backend services (never exposed externally)
    # Members: ai-orchestrator (bridge), postgresql, redis, ollama, docker-proxy
    # Security Zone: Secure Backend - No external access, internal service communication only
  internal:
    external: true
    name: ai-coding-agent_internal_network
    # Labels removed for external network - managed externally

    # MONITORING NETWORK - Observability stack isolation
    # Members: Prometheus, Loki, Promtail, Grafana
    # Security Zone: Monitoring - Isolated monitoring infrastructure
  monitoring-network:
    external: true
    name: ai-coding-agent-dev_monitoring-network
    # Labels removed for external network - managed externally

    # External Supabase network (created by Supabase CLI)
  supabase-network:
    external: true
    name: supabase_network_codingagenttwo

  # DEPRECATED NETWORKS (for backward compatibility - will be removed)
  # These are kept for gradual migration but should not be used for new services
  ai-coding-agent-network:
    external: true
    name: ai-coding-agent-dev_ai-coding-agent-network
    # Labels removed for external network - managed externally
  traefik-network:
    external: true
    name: ai-coding-agent-dev_traefik-network
    # Labels removed for external network - managed externally

volumes:
  supabase_db_data:
    driver: local
  redis_data:
    driver: local
  code_server_data:
    driver: local
  ai_orchestrator_data:
    driver: local
  ollama_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
  prometheus_data:
    driver: local
  user_workspace:
    driver: local
  hosted_sites_data:
    driver: local

services:
  # Docker Socket Proxy - Secure access to Docker daemon
  # Provides limited, controlled access to Docker operations instead of direct socket mounting
  # SECURITY: Internal network only - never expose Docker socket access externally
  docker-proxy:
    build:
      context: ./containers/docker-proxy
      dockerfile: Dockerfile
    container_name: docker-proxy
    ports:
      - "127.0.0.1:9376:2375" # Docker proxy for production (avoid 2376 conflict)
    volumes:
      # Mount Docker socket in read-only mode for maximum security
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - web # Docker proxy needs web network to communicate with Traefik
      - internal # Also needs internal network for other services
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 128M
        reservations:
          cpus: '0.1'
          memory: 32M
      labels:
        - "com.docker.compose.project=codingagenttwo"
        - "com.docker.compose.service=docker-proxy"
        - "security.access-level=internal-only"
        - "security.privilege-level=docker-proxy"
        - "security.isolation=container-hardened"
        - "security.zone=secure-backend"
        - "network.zone=secure-backend"
    security_opt:
      - no-new-privileges:true
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:2377/health" ]
      interval: 30s
      timeout: 10s
      start_period: 10s
      retries: 3
  # Traefik Reverse Proxy - Entry point for all web traffic
  # Production-hardened configuration using secure docker-proxy
  traefik:
    build:
      context: ./containers/traefik
      dockerfile: Dockerfile
    container_name: traefik
    command:
      - "--api=true" # Enable API for ping endpoint
      - "--api.dashboard=false" # Disabled for production security
      - "--api.insecure=false" # Disabled for production security
      - "--providers.docker=true" # Enabled - using secure docker-proxy
      - "--providers.docker.endpoint=tcp://docker-proxy:2375" # HTTP proxy endpoint
      - "--providers.docker.exposedbydefault=false" # Only manage containers with traefik.enable=true
      - "--providers.docker.watch=true" # Enable watching for container changes
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--entrypoints.ping.address=:8080" # Dedicated ping entrypoint
      - "--log.level=WARN" # Reduced log verbosity for production
      - "--accesslog=false" # Disabled for production (enable if needed for monitoring)
      - "--metrics.prometheus=true"
      - "--ping=true" # Enable health check endpoint
      - "--ping.entryPoint=ping" # Use dedicated ping entrypoint
      - "--global.sendAnonymousUsage=false" # Privacy enhancement
      - "--providers.file.directory=/etc/traefik/dynamic"
      - "--providers.file.watch=true"
    ports:
      - "80:80" # Bind to all interfaces for external access
      - "443:443" # Bind to all interfaces for external access
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik-config:/etc/traefik/dynamic
    depends_on:
      - docker-proxy # Remove health check condition to avoid blocking traefik
    networks:
      - web # Traefik only needs access to the web DMZ network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.ai-orchestrator.rule=Host(`api.localhost`)"
        - "traefik.http.routers.ai-orchestrator.entrypoints=web"
        - "traefik.http.services.ai-orchestrator.loadbalancer.server.port=8000"
        - "traefik.docker.network=ai-coding-agent_web_network" # Updated to use new web network
        - "com.docker.compose.project=codingagenttwo"
        - "com.docker.compose.service=traefik"
        - "security.access-level=external-facing"
        - "security.privilege-level=reverse-proxy"
        - "security.isolation=container-hardened"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
    healthcheck:
      test: [ "CMD", "sh", "-c", "test -f /usr/local/bin/traefik && echo 'healthy'" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Supabase services removed here. Supabase is now managed by the Supabase CLI (supabase start)
  # If you need to run a local Supabase stack via Docker Compose, reintroduce the services
  # or use the `docker-compose.supabase.yml` file. For local development we expect the
  # Supabase CLI to be running and services to be available on the host (127.0.0.1 / host.docker.internal).

  # Redis Cache Service - High-performance caching and session storage - WITH AUTHENTICATION
  # SECURITY: Internal network only - never expose Docker socket access externally
  redis:
    image: ${REDIS_IMAGE:-redis:7-alpine}
    container_name: redis
    command: >
      redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - redis_data:/data
      - ./secrets/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - internal # Redis only needs secure internal network access
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 768M
        reservations:
          cpus: '0.25'
          memory: 256M
      labels:
        - "com.docker.compose.project=codingagenttwo"
        - "com.docker.compose.service=redis"
        - "security.access-level=internal-only"
        - "security.privilege-level=cache"
        - "security.isolation=network-isolated"
        - "security.zone=secure-backend"
        - "network.zone=secure-backend"
        - "service.type=cache-database"
    security_opt:
      - no-new-privileges:true
    user: redis
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=50m
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # ============================================================================
  # OLLAMA (GPU / CPU PROFILES)
  # ============================================================================
  # Use docker compose --profile gpu up -d   (desktop w/ NVIDIA GPU)
  #     docker compose --profile cpu up -d   (laptop / no GPU)
  # ai-orchestrator always reaches service via alias 'ollama'

  ollama-gpu:
    build:
      context: ./containers/ollama
      dockerfile: Dockerfile
    container_name: ollama-gpu
    profiles: [ "gpu" ]
    volumes:
      - ollama_data:/home/<USER>/.ollama:rw
    networks:
      internal:
        aliases:
          - ollama
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
      labels:
        - "com.docker.compose.project=codingagenttwo"
        - "com.docker.compose.service=ollama-gpu"
        - "security.access-level=internal-only"
        - "security.privilege-level=ai-inference-vector-db"
        - "security.isolation=network-isolated"
        - "network.zone=secure-backend"
        - "service.type=combined-ai-vector"
    runtime: nvidia
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_PORT=11434
      - OLLAMA_MODELS=/home/<USER>/.ollama/models
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - OLLAMA_MAX_LOADED_MODELS=3
      - OLLAMA_NUM_THREAD=4
      - OLLAMA_NUM_GPU=1
      - OLLAMA_GPU_LAYERS=35
      - OLLAMA_NUM_CTX=4096
      - OLLAMA_NUM_BATCH=512
      - OLLAMA_NUM_GQA=8
      - OLLAMA_FLASH_ATTENTION=true
      - OLLAMA_ROPE_SCALING=linear
      - OLLAMA_COMPRESSION=auto
    security_opt:
      - no-new-privileges:true
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:11434/api/tags" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 300s

  ollama-cpu:
    build:
      context: ./containers/ollama
      dockerfile: Dockerfile
    container_name: ollama-cpu
    profiles: [ "cpu" ]
    volumes:
      - ollama_data:/home/<USER>/.ollama:rw
    networks:
      internal:
        aliases:
          - ollama
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 512M
      labels:
        - "com.docker.compose.project=codingagenttwo"
        - "com.docker.compose.service=ollama-cpu"
        - "security.access-level=internal-only"
        - "security.privilege-level=ai-inference-vector-db"
        - "security.isolation=network-isolated"
        - "network.zone=secure-backend"
        - "service.type=combined-ai-vector"
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_PORT=11434
      - OLLAMA_MODELS=/home/<USER>/.ollama/models
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - OLLAMA_MAX_LOADED_MODELS=3
      - OLLAMA_NUM_THREAD=4
      - OLLAMA_NUM_GPU=1
      - OLLAMA_GPU_LAYERS=35
      - OLLAMA_NUM_CTX=4096
      - OLLAMA_NUM_BATCH=512
      - OLLAMA_NUM_GQA=8
      - OLLAMA_FLASH_ATTENTION=true
      - OLLAMA_ROPE_SCALING=linear
      - OLLAMA_COMPRESSION=auto
    security_opt:
      - no-new-privileges:true
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:11434/api/tags" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 300s

  # AI Orchestrator - Production (ChromaDB removed, using Supabase pgvector only)
  ai-orchestrator:
    build:
      context: ./containers/ai-orchestrator
      dockerfile: Dockerfile
    container_name: ai-orchestrator
    secrets:
      - postgres_password
      - jwt_secret
      - supabase_url
      - supabase_key
      - supabase_service_key
      - openai_api_key
      - anthropic_api_key
      - openrouter_api_key
    volumes:
      - ./templates:/app/templates
      - ai_orchestrator_data:/app/data
      - user_workspace:/workspace
      - hosted_sites_data:/hosted_sites
      - ./traefik-config:/traefik-config
    ports:
      - "127.0.0.1:9003:8000" # FastAPI port (production, avoid conflicts)
    environment:
      - SUPABASE_URL=http://127.0.0.1:54321
      - SUPABASE_ANON_KEY=${ANON_KEY}
      - SUPABASE_SERVICE_KEY=${SERVICE_ROLE_KEY}
      - DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
      - DATABASE_HOST=127.0.0.1
      - DATABASE_PORT=54322
      - DATABASE_NAME=postgres
      - DATABASE_USER=postgres
      # Other services
      # Redis connection with authentication
      - REDIS_URL=redis://:ebubulbul1986@redis:6379/0

      # External Service URLs
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://ollama:11434}
      # ChromaDB removed - using Supabase pgvector for all vector operations

      # AI Service API Keys (now using Docker secrets)
      - OPENROUTER_API_KEY_FILE=/run/secrets/openrouter_api_key
      - OPENAI_API_KEY_FILE=/run/secrets/openai_api_key
      - ANTHROPIC_API_KEY_FILE=/run/secrets/anthropic_api_key

      # LLM Provider Configuration
      - DEFAULT_LOCAL_PROVIDER=${DEFAULT_LOCAL_PROVIDER:-ollama}
      - DEFAULT_CLOUD_PROVIDER=${DEFAULT_CLOUD_PROVIDER:-openrouter}
      - ENABLE_CLOUD_FALLBACK=${ENABLE_CLOUD_FALLBACK:-true}

      # Embedding Configuration
      - EMBEDDING_PROVIDER=${EMBEDDING_PROVIDER:-ollama}
      - OLLAMA_EMBEDDING_MODEL=${OLLAMA_EMBEDDING_MODEL:-bge-large-en-v1.5}

      # Authentication
      # JWT secret is provided via Docker secrets file to avoid plaintext defaults
      - JWT_SECRET_FILE=/run/secrets/jwt_secret
      - JWT_ALGORITHM=HS256
      - JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

      - USE_SUPABASE=false
      - ENABLE_ROLE_CONFIG_INIT=false
      - ENABLE_WEBSOCKET_CHAT=false
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - DEBUG=false
      - LOG_LEVEL=info

      - POSTGRES_PASSWORD_FILE=/run/secrets/postgres_password
      - REDIS_PASSWORD_FILE=/run/secrets/redis_password

      - DOCKER_HOST=tcp://docker-proxy:2375 # Internal container communication uses port 2375

      - SUPABASE_URL_FILE=/run/secrets/supabase_url
      - SUPABASE_KEY_FILE=/run/secrets/supabase_key
      - SUPABASE_SERVICE_KEY_FILE=/run/secrets/supabase_service_key
      # JWT secret file already referenced above (JWT_SECRET_FILE)

      - PYTHONPATH=/app

      # =========================================================================
      # SECURITY CONFIGURATION - Container Isolation and Resource Management

      # =========================================================================

      # Resource Limits: Control user container creation and resource consumption
      - DOCKER_RESOURCE_LIMITS=${DOCKER_RESOURCE_LIMITS:-true}
      - MAX_USER_CONTAINERS=${MAX_USER_CONTAINERS:-10}
      - CONTAINER_CPU_LIMIT=${CONTAINER_CPU_LIMIT:-1.0}
      - CONTAINER_MEMORY_LIMIT=${CONTAINER_MEMORY_LIMIT:-2G}

      # Advanced Security Controls
      - ENABLE_CONTAINER_SANDBOXING=${ENABLE_CONTAINER_SANDBOXING:-true}
      - CONTAINER_READONLY_ROOTFS=${CONTAINER_READONLY_ROOTFS:-true}
      - ENABLE_USER_NAMESPACE_REMAPPING=${ENABLE_USER_NAMESPACE_REMAPPING:-true}
      - CONTAINER_SECURITY_PROFILE=${CONTAINER_SECURITY_PROFILE:-restricted}

      # Network Security
      - ENABLE_NETWORK_ISOLATION=${ENABLE_NETWORK_ISOLATION:-true}
      - MAX_NETWORKS_PER_USER=${MAX_NETWORKS_PER_USER:-2}
      - ENABLE_TRAFFIC_SHAPING=${ENABLE_TRAFFIC_SHAPING:-true}

      # Process and Capability Restrictions
      - DROP_UNSAFE_CAPABILITIES=${DROP_UNSAFE_CAPABILITIES:-true}
      - ENABLE_SECCOMP_FILTERING=${ENABLE_SECCOMP_FILTERING:-true}
      - RESTRICTED_USER_MODE=${RESTRICTED_USER_MODE:-true}
    networks:
      - web # Needs web network for Traefik access (public API)
      - internal # Needs internal network for database/Redis/Ollama access
      - supabase-network # External Supabase connectivity
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.ai-orchestrator.rule=Host(`api.localhost`)"
        - "traefik.http.routers.ai-orchestrator.entrypoints=web"
        - "traefik.http.services.ai-orchestrator.loadbalancer.server.port=8000"
        - "traefik.docker.network=ai-coding-agent_web_network" # Updated to use new web network
        - "com.docker.compose.project=codingagenttwo"
        - "com.docker.compose.service=ai-orchestrator"
        - "security.access-level=bridge-service" # Updated to reflect bridge role
        - "security.privilege-level=orchestrator"
        - "security.isolation=dual-network-bridge" # Updated to reflect dual network access
    security_opt:
      - no-new-privileges:true
      - apparmor:docker-workspace
    cap_drop:
      - ALL
    user: appuser
    healthcheck:
      test: [ "CMD", "sh", "-lc", "curl -fsS http://localhost:8000/health >/dev/null" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 300s
  user-portal:
    build:
      context: ./containers/user-portal
      dockerfile: Dockerfile
    container_name: user-portal
    secrets:
      - supabase_url
      - supabase_key
      # user-portal should also be able to read jwt-related secrets if needed
      - jwt_secret
    environment:
      # Server-side API URL (for internal container communication)
      API_BASE_URL: http://ai-orchestrator:8000
      # Client-side API URL (accessible from browser) - Use Traefik routing
      NEXT_PUBLIC_API_BASE_URL: http://api.localhost
      # NextAuth configuration
      NEXTAUTH_URL: http://portal.localhost
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET:-dev-insecure-change-me}
      NEXT_PUBLIC_SUPABASE_URL: ${SUPABASE_URL}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${SUPABASE_KEY}
      SUPABASE_URL_FILE: /run/secrets/supabase_url
      SUPABASE_KEY_FILE: /run/secrets/supabase_key
      NODE_ENV: production
      NEXT_TELEMETRY_DISABLED: "1"
      # Health check configuration
      HEALTH_CHECK_TIMEOUT: "3000"
    depends_on:
      ai-orchestrator:
        condition: service_healthy
    networks:
      - web # User portal only needs web network access (DMZ)
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.user-portal.rule=Host(`portal.localhost`)"
        - "traefik.http.routers.user-portal.entrypoints=web"
        - "traefik.http.services.user-portal.loadbalancer.server.port=3000"
        - "traefik.docker.network=ai-coding-agent_web_network" # Updated to use new web network
    security_opt:
      - no-new-privileges:true
    user: nextjs
    read_only: true
    tmpfs:
      - /tmp:rw,noexec,nosuid,size=100m
      - /home/<USER>/.next/cache:rw,noexec,nosuid,size=200m
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3000/api/health" ]
      timeout: 15s
      interval: 30s
      retries: 5
      start_period: 60s

  hosting-server:
    image: ${NGINX_IMAGE:-nginx:alpine}
    restart: unless-stopped
    volumes:
      - hosted_sites_data:/var/www:ro
      - ./containers/hosting-server/nginx.dynamic.conf:/etc/nginx/conf.d/default.conf:ro
    networks:
      - web
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.hosted-sites.service=hosted-sites-service"
      - "traefik.http.services.hosted-sites-service.loadbalancer.server.port=80"
      - "traefik.http.routers.hosted-sites.rule=HostRegexp(`{name:.+}.localhost`)"

  # Monitoring Stack
  prometheus:
    build:
      context: ./containers/monitoring/prometheus
      dockerfile: Dockerfile
    container_name: prometheus
    volumes:
      - ./configs/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    ports:
      - "127.0.0.1:9090:9090" # Expose Prometheus web UI
    networks:
      - monitoring-network
      - web # Add web network for Traefik access
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.1'
          memory: 256M
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.prometheus.rule=Host(`prometheus.localhost`)"
        - "traefik.http.routers.prometheus.entrypoints=web"
        - "traefik.http.services.prometheus.loadbalancer.server.port=9090"
        - "traefik.docker.network=ai-coding-agent_web_network" # Updated to use new web network
        - "com.docker.compose.project=codingagenttwo"
        - "com.docker.compose.service=prometheus"
        - "security.access-level=monitoring-web" # Updated for web access
        - "security.privilege-level=monitoring"
        - "security.isolation=monitoring-dmz" # Updated for DMZ access
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:9090/-/ready" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  loki:
    build:
      context: ./containers/monitoring/loki
      dockerfile: Dockerfile
    container_name: loki
    volumes:
      - loki_data:/loki
      - ./configs/loki-config.yaml:/etc/loki/local-config.yaml:ro
    command: -config.file=/etc/loki/local-config.yaml
    ports:
      - "127.0.0.1:9100:3100" # Loki logs (avoid 3100 conflict)
    networks:
      - monitoring-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3100/ready" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  promtail:
    image: ${PROMTAIL_IMAGE:-grafana/promtail:2.9.0}
    container_name: promtail
    volumes:
      - ./configs/promtail-config.yml:/etc/promtail/config.yml:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring-network
      - internal
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
        reservations:
          cpus: '0.05'
          memory: 64M
    healthcheck:
      test: [ "CMD", "sh", "-c", "test -f /usr/bin/promtail && echo 'healthy'" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  grafana:
    build:
      context: ./containers/monitoring/grafana
      dockerfile: Dockerfile
    container_name: grafana
    secrets:
      - grafana_admin_password
    ports:
      - "127.0.0.1:9000:3000" # Grafana dashboard (avoid 3001 conflict)
    environment:
      GF_SECURITY_ADMIN_PASSWORD__FILE: /run/secrets/grafana_admin_password
      GF_USERS_ALLOW_SIGN_UP: "false"
      GF_INSTALL_PLUGINS: "grafana-piechart-panel"
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - monitoring-network
      - web # Add web network for Traefik access
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "sh", "-lc", "curl -fsS http://localhost:3000/api/health >/dev/null" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.grafana.rule=Host(`grafana.localhost`)"
        - "traefik.http.routers.grafana.entrypoints=web"
        - "traefik.http.services.grafana.loadbalancer.server.port=3001"
        - "traefik.docker.network=ai-coding-agent_web_network" # Updated to use new web network

  # Template code-server instance (builds image only, doesn't run)
  code-server-template:
    build:
      context: ./containers/code-server
      dockerfile: Dockerfile
    container_name: code-server-template
    profiles:
      - template-only
    # Note: Runtime configurations below are for documentation/reference only
    # since this container uses template-only profile and won't run
    secrets:
      - code_server_password
    volumes:
      - code_server_data:/home/<USER>
