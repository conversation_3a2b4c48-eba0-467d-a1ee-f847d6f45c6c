# Services Issues TODO List

Below are identified issues and recommended fixes for each service module. No code changes have been applied.

## __init__.py
- **Issue**: Optional imports set names to None but DI functions unconditionally reference them.
  - **Fix**: Guard DI factories with explicit checks and raise clear startup errors, or ensure imports are required. Add type-safe fallbacks.
- **Issue**: DI uses `Depends(get_redis_client)` which is async; ensure FastAPI context only.
  - **Fix**: Document usage context; provide sync wrapper if needed outside FastAPI.

## auth_dependencies.py
- **Issue**: JWT secret and anon key sourced from settings/env; missing validation on startup.
  - **Fix**: Validate presence at import/startup and fail fast with actionable logs.
- **Issue**: Hardcoded audience `authenticated` may not match Supabase config variations.
  - **Fix**: Make audience configurable via settings.
- **Issue**: Logs include token prefix (first 20 chars); may leak sensitive info in dev logs.
  - **Fix**: Remove token snippet logging; log token presence only.
- **Issue**: `httpx` use for health checks lacks timeouts and retries in some spots.
  - **Fix**: Always set reasonable timeout and retry strategy for external calls.

## checkpoint_manager.py
- **Issue**: Likely performs file IO; ensure all IO is async or offloaded to threadpool to avoid blocking.
  - **Fix**: Wrap blocking IO with `asyncio.to_thread` and add error handling.

## crypto_service.py
- **Issue**: Potential direct handling of secrets without key rotation or KDF hardening.
  - **Fix**: Use strong KDF (Argon2/scrypt) and document rotation; keep keys in secret store.

## dispatcher.py
- **Issue**: Depends on `LockManager`; ensure consistent async contract and Redis locking fairness/TTL.
  - **Fix**: Add TTL renewal (heartbeat) and deadlock prevention; document single-flight patterns.

## docker_service.py
- **Issue**: Uses `docker` SDK in process; blocking calls in async context can stall event loop.
  - **Fix**: Run Docker SDK calls via `asyncio.to_thread` or switch to HTTP proxy pattern like `ProjectService`.
- **Issue**: Hardcoded Traefik network label `ai-coding-agent_web_network` couples to compose name.
  - **Fix**: Make network name configurable via settings; validate existence.
- **Issue**: No rate limiting or backoff when creating containers.
  - **Fix**: Implement retry with exponential backoff on transient Docker errors.

## enhanced_llm_service.py
- **Issue**: Ensure provider fallbacks (Ollama -> OpenRouter/OpenAI/Anthropic) are robust and rate-limited.
  - **Fix**: Add circuit breaker, per-provider timeouts, and structured error reporting. Redact keys in logs.

## error_recovery.py
- **Issue**: May catch broad exceptions and swallow root cause.
  - **Fix**: Use specific exceptions, attach context, and re-raise after logging when appropriate.

## lock_manager.py
- **Issue**: Redis-based locks must set unique tokens, TTL, and safe release (compare-and-del).
  - **Fix**: Implement Redlock-like semantics or use redis-py `Lock` with `blocking_timeout`. Add jitter.
- **Issue**: No observability of lock contention.
  - **Fix**: Emit metrics for wait time, ownership, TTL extensions.

## port_manager.py
- **Issue**: Port allocation may race under concurrency.
  - **Fix**: Guard with Redis lock or DB transactional reservation; validate port availability at OS level.

## project_service.py
- **Issue**: Uses `httpx.AsyncClient` to talk to Docker proxy; timeouts present but no retries.
  - **Fix**: Add retry policy on 5xx/connection errors with jitter; centralize client creation.
- **Issue**: Returns generic Exceptions in places.
  - **Fix**: Raise specific error types and map at API layer; include error codes.
- **Issue**: DB operations likely sync `Session` in async context.
  - **Fix**: Wrap with `run_in_threadpool` or migrate to SQLAlchemy async session.
- **Issue**: Network and volume names derived from project IDs; no normalization length/charset guards.
  - **Fix**: Slugify and cap length (e.g., Docker 63 char name limit); validate before calling API.
- **Issue**: Cleanup paths ignore partial failures beyond logs.
  - **Fix**: Aggregate cleanup results and surface in response for operator action.

## provisioning_service.py
- **Issue**: Async methods accept sync `Session`; potential blocking calls.
  - **Fix**: Use async SQLAlchemy or threadpool wrappers. Validate schema existence idempotently.

## rag_service.py / vector_service.py
- **Issue**: Embedding/vector ops may run heavy CPU/GPU sync workloads.
  - **Fix**: Offload to worker threads/process pool; set timeouts and batch sizes; cache embeddings.

## redis_service.py
- **Issue**: Ensure a single async Redis client is reused; avoid creating per-request connections.
  - **Fix**: Provide startup/shutdown lifecycle and pool sizing; health check pings.
- **Issue**: Key namespace/versioning not enforced.
  - **Fix**: Prefix keys per tenant and environment; add helper functions.

## resource_manager.py
- **Issue**: Uses `docker.from_env()` on import/init; may fail in test or non-docker envs.
  - **Fix**: Lazy-init client with capability checks; guard when Docker unavailable.
- **Issue**: Filesystem cleanup uses `shutil` without sandbox checks.
  - **Fix**: Enforce workspace root allowlist and safe path joins.

## roadmap_service.py
- **Issue**: If using SQLAlchemy sync session in async routes, same blocking concerns.
  - **Fix**: Async session or threadpool; import `sqlalchemy.func` for DB-side timestamps.

## rollback_manager.py
- **Issue**: Direct `docker` SDK usage in multiple methods; blocking and error handling variances.
  - **Fix**: Unify through helper that wraps Docker calls with retries/timeouts in threadpool.
- **Issue**: Template file checks assume specific names.
  - **Fix**: Use registry metadata from `template_registry` for validation.

## security_service.py
- **Issue**: Initializes `docker.from_env()` at __init__; blocking and fragile.
  - **Fix**: Lazy-init in first use; wrap calls in threadpool.
- **Issue**: Dangerous command list static; may cause false positives.
  - **Fix**: Make configurable; record evidence and thresholds; add allowlist per container.
- **Issue**: Uses many env reads at runtime.
  - **Fix**: Centralize in settings and validate on startup.

## storage_cleanup_service.py
- **Issue**: Potentially deletes files; ensure tenant/project scoping and dry-run mode.
  - **Fix**: Implement safety checks and logging with metrics.

## supabase_service.py
- **Issue**: Supabase Python client is sync-like; using in async context can block.
  - **Fix**: Run in threadpool or switch to HTTP client with async.
- **Issue**: Service and service-key usage may bypass RLS.
  - **Fix**: Default to user-context client; only use service key for admin ops with audit logging.

## task_validator.py
- **Issue**: LLM or heavy IO operations should have timeouts and cancellation points.
  - **Fix**: Add `asyncio.timeout` guards and cooperative cancellation.

## template_registry.py
- **Issue**: Hardcoded dependencies and required files.
  - **Fix**: Make registry data-driven via YAML/JSON and validate templates on load.

## vector_service.py
- **Issue**: Model downloads/loads may block startup.
  - **Fix**: Lazy-load models; background warmup with readiness indicator and cache.

---

# Cross-cutting Improvements
- **Configuration**: Move all magic strings (networks, labels, timeouts) to `settings` with defaults; validate on startup.
- **Async vs Sync**: Audit every external call (DB, Docker, Redis, Supabase, filesystem) and ensure non-blocking behavior.
- **Error Model**: Introduce typed service exceptions with error codes; map to API responses uniformly.
- **Observability**: Add structured logging, metrics (latency, retries, failures), and trace IDs per request/project.
- **Security**: Redact secrets in logs; enforce RBAC and tenant scoping in service methods; avoid leaking identifiers.