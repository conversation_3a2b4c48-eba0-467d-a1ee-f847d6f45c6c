"""
Integration Tests for User Portal and Backend API
Tests the complete role management workflow from frontend API calls to backend persistence
"""

import pytest
import requests
import time
from typing import Dict, Any

# Test configuration
API_BASE_URL = "http://localhost:8000"
USER_PORTAL_URL = "http://user-portal:3000"  # Container-to-container communication
TEST_TIMEOUT = 30
POLL_INTERVAL = 1

class ApiClient:
    """Test API client for making requests to the backend"""

    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'IntegrationTest/1.0'
        })

    def wait_for_service(self, timeout: int = TEST_TIMEOUT) -> bool:
        """Wait for the API service to be available"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(f"{self.base_url}/health", timeout=5)
                if response.status_code == 200:
                    return True
            except requests.RequestException:
                pass
            time.sleep(POLL_INTERVAL)
        return False

    def get_roles(self) -> Dict[str, Any]:
        """Get all role configurations"""
        response = self.session.get(f"{self.base_url}/api/roles")
        response.raise_for_status()
        return response.json()

    def get_role(self, role_name: str) -> Dict[str, Any]:
        """Get specific role configuration"""
        response = self.session.get(f"{self.base_url}/api/roles/{role_name}")
        response.raise_for_status()
        return response.json()

    def create_role(self, role_name: str, configuration: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new role configuration"""
        response = self.session.post(
            f"{self.base_url}/api/roles/{role_name}",
            json=configuration
        )
        response.raise_for_status()
        return response.json()

    def update_role(self, role_name: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing role configuration"""
        response = self.session.put(
            f"{self.base_url}/api/roles/{role_name}",
            json=updates
        )
        response.raise_for_status()
        return response.json()

    def delete_role(self, role_name: str) -> Dict[str, Any]:
        """Delete a role configuration"""
        response = self.session.delete(f"{self.base_url}/api/roles/{role_name}")
        response.raise_for_status()
        return response.json()

    def get_provider_models(self) -> Dict[str, Any]:
        """Get available models by provider"""
        response = self.session.get(f"{self.base_url}/api/roles/providers/models")
        response.raise_for_status()
        return response.json()


class FrontendClient:
    """Test client for interacting with the frontend"""

    def __init__(self, base_url: str = USER_PORTAL_URL):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()

    def wait_for_service(self, timeout: int = TEST_TIMEOUT) -> bool:
        """Wait for the frontend service to be available"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(f"{self.base_url}/api/health", timeout=5)
                if response.status_code == 200:
                    return True
            except requests.RequestException:
                pass
            time.sleep(POLL_INTERVAL)
        return False

    def health_check(self) -> Dict[str, Any]:
        """Get frontend health status"""
        response = self.session.get(f"{self.base_url}/api/health")
        response.raise_for_status()
        return response.json()


@pytest.fixture(scope="session")
def api_client():
    """API client fixture"""
    client = ApiClient()

    # Wait for services to be available
    assert client.wait_for_service(), "API service is not available"

    yield client


@pytest.fixture(scope="session")
def frontend_client():
    """Frontend client fixture"""
    client = FrontendClient()

    # Wait for services to be available
    assert client.wait_for_service(), "Frontend service is not available"

    yield client


@pytest.fixture
def test_role_config():
    """Sample role configuration for testing"""
    return {
        "provider": "openrouter",
        "available_models": ["anthropic/claude-3-sonnet", "openai/gpt-4"],
        "selected_model": "anthropic/claude-3-sonnet",
        "api_key": "sk-or-v1-test-key-for-integration-testing",
        "cost_limit": 50.0,
        "max_tokens": 4096,
        "temperature": 0.7,
        "enabled": True
    }


@pytest.fixture
def cleanup_test_roles(api_client):
    """Cleanup fixture to remove test roles after tests"""
    test_roles = []

    yield test_roles

    # Cleanup: delete any test roles created during testing
    for role_name in test_roles:
        try:
            api_client.delete_role(role_name)
        except requests.HTTPError:
            pass  # Role might not exist


class TestServiceHealth:
    """Test service health and connectivity"""

    def test_backend_health(self, api_client):
        """Test that backend API is healthy"""
        try:
            response = api_client.session.get(f"{api_client.base_url}/health")
            assert response.status_code == 200

            health_data = response.json()
            assert health_data["status"] == "healthy"
            assert "timestamp" in health_data
            assert health_data["uptime"] > 0

        except requests.RequestException as e:
            pytest.fail(f"Backend health check failed: {e}")

    def test_frontend_health(self, frontend_client):
        """Test that frontend is healthy"""
        try:
            health_data = frontend_client.health_check()
            assert health_data["status"] == "healthy"
            assert "timestamp" in health_data
            assert health_data["uptime"] > 0
            assert "checks" in health_data

            # Check that API connectivity is working
            checks = health_data["checks"]
            assert checks["dependencies"] == "healthy"
            # API check can be degraded in test environment
            assert checks["api"] in ["healthy", "degraded"]

        except requests.RequestException as e:
            pytest.fail(f"Frontend health check failed: {e}")


class TestRoleManagementWorkflow:
    """Test complete role management workflow"""

    def test_get_initial_roles(self, api_client):
        """Test fetching initial role configurations"""
        response = api_client.get_roles()

        assert response["success"] is True
        assert "roles" in response
        assert "total_roles" in response
        assert isinstance(response["roles"], dict)
        assert response["total_roles"] >= 0

        # Should have at least the default roles
        expected_roles = {"architect", "backend", "frontend", "issue_fix", "shell"}
        actual_roles = set(response["roles"].keys())

        # At least some default roles should exist
        assert len(actual_roles.intersection(expected_roles)) > 0

    def test_create_new_role(self, api_client, test_role_config, cleanup_test_roles):
        """Test creating a new role configuration"""
        role_name = "test_integration_role"
        cleanup_test_roles.append(role_name)

        # Create the role
        response = api_client.create_role(role_name, test_role_config)

        assert response["success"] is True
        assert response["role_name"] == role_name
        assert "configuration" in response

        config = response["configuration"]
        assert config["provider"] == test_role_config["provider"]
        assert config["selected_model"] == test_role_config["selected_model"]
        assert config["enabled"] == test_role_config["enabled"]
        assert "created_at" in config
        assert "updated_at" in config

    def test_get_specific_role(self, api_client, test_role_config, cleanup_test_roles):
        """Test fetching a specific role configuration"""
        role_name = "test_get_role"
        cleanup_test_roles.append(role_name)

        # Create a role first
        api_client.create_role(role_name, test_role_config)

        # Fetch the specific role
        response = api_client.get_role(role_name)

        assert response["success"] is True
        assert response["role_name"] == role_name
        assert "configuration" in response

        config = response["configuration"]
        assert config["provider"] == test_role_config["provider"]
        assert config["selected_model"] == test_role_config["selected_model"]

    def test_update_role_configuration(self, api_client, test_role_config, cleanup_test_roles):
        """Test updating an existing role configuration"""
        role_name = "test_update_role"
        cleanup_test_roles.append(role_name)

        # Create a role first
        api_client.create_role(role_name, test_role_config)

        # Update the role
        updates = {
            "temperature": 0.9,
            "max_tokens": 8192,
            "cost_limit": 75.0
        }

        response = api_client.update_role(role_name, updates)

        assert response["success"] is True
        assert response["role_name"] == role_name

        config = response["configuration"]
        assert config["temperature"] == 0.9
        assert config["max_tokens"] == 8192
        assert config["cost_limit"] == 75.0
        # Other fields should remain unchanged
        assert config["provider"] == test_role_config["provider"]
        assert config["selected_model"] == test_role_config["selected_model"]

    def test_delete_role(self, api_client, test_role_config):
        """Test deleting a role configuration"""
        role_name = "test_delete_role"

        # Create a role first
        api_client.create_role(role_name, test_role_config)

        # Delete the role
        response = api_client.delete_role(role_name)

        assert response["success"] is True
        assert response["message"] == "Role deleted successfully"

        # Verify role is deleted
        with pytest.raises(requests.HTTPError) as exc_info:
            api_client.get_role(role_name)
        assert exc_info.value.response.status_code == 404

    def test_get_provider_models(self, api_client):
        """Test fetching available models by provider"""
        response = api_client.get_provider_models()

        assert isinstance(response, list)

        # Should have entries for supported providers
        providers = {item["provider"] for item in response if "provider" in item}
        expected_providers = {"ollama", "openrouter", "openai", "anthropic"}

        # At least some providers should be available
        assert len(providers.intersection(expected_providers)) > 0

        # Each provider entry should have models
        for item in response:
            if "models" in item:
                assert isinstance(item["models"], list)
                assert len(item["models"]) > 0


class TestErrorHandling:
    """Test error handling and edge cases"""

    def test_create_duplicate_role(self, api_client, test_role_config, cleanup_test_roles):
        """Test creating a role with duplicate name"""
        role_name = "test_duplicate_role"
        cleanup_test_roles.append(role_name)

        # Create the role first time
        api_client.create_role(role_name, test_role_config)

        # Try to create again with same name
        with pytest.raises(requests.HTTPError) as exc_info:
            api_client.create_role(role_name, test_role_config)
        assert exc_info.value.response.status_code in [400, 409]

    def test_get_nonexistent_role(self, api_client):
        """Test fetching a role that doesn't exist"""
        with pytest.raises(requests.HTTPError) as exc_info:
            api_client.get_role("nonexistent_role_12345")
        assert exc_info.value.response.status_code == 404

    def test_update_nonexistent_role(self, api_client):
        """Test updating a role that doesn't exist"""
        with pytest.raises(requests.HTTPError) as exc_info:
            api_client.update_role("nonexistent_role_12345", {"temperature": 0.5})
        assert exc_info.value.response.status_code == 404

    def test_delete_nonexistent_role(self, api_client):
        """Test deleting a role that doesn't exist"""
        with pytest.raises(requests.HTTPError) as exc_info:
            api_client.delete_role("nonexistent_role_12345")
        assert exc_info.value.response.status_code == 404

    def test_invalid_role_configuration(self, api_client):
        """Test creating role with invalid configuration"""
        invalid_config = {
            "provider": "invalid_provider",
            "selected_model": "",  # Empty model
            "temperature": 2.5,    # Invalid temperature
            "max_tokens": -1,      # Invalid max_tokens
        }

        with pytest.raises(requests.HTTPError) as exc_info:
            api_client.create_role("invalid_role", invalid_config)
        assert exc_info.value.response.status_code == 422


class TestDataPersistence:
    """Test data persistence and consistency"""

    def test_role_persists_after_restart(self, api_client, test_role_config, cleanup_test_roles):
        """Test that role configurations persist (requires manual restart testing)"""
        role_name = "test_persistence_role"
        cleanup_test_roles.append(role_name)

        # Create role
        api_client.create_role(role_name, test_role_config)

        # Verify it exists and has correct data
        response = api_client.get_role(role_name)
        original_created_at = response["configuration"]["created_at"]

        # In a real test, we would restart services here
        # For now, just verify the data is still accessible

        response2 = api_client.get_role(role_name)
        assert response2["configuration"]["created_at"] == original_created_at
        assert response2["configuration"]["provider"] == test_role_config["provider"]

    def test_concurrent_updates(self, api_client, test_role_config, cleanup_test_roles):
        """Test handling of concurrent updates to the same role"""
        role_name = "test_concurrent_role"
        cleanup_test_roles.append(role_name)

        # Create initial role
        api_client.create_role(role_name, test_role_config)

        # Simulate concurrent updates
        update1 = {"temperature": 0.5}
        update2 = {"max_tokens": 2048}

        # Both updates should succeed
        api_client.update_role(role_name, update1)
        api_client.update_role(role_name, update2)

        # Final state should have both updates
        final_response = api_client.get_role(role_name)
        config = final_response["configuration"]
        assert config["temperature"] == 0.5
        assert config["max_tokens"] == 2048


@pytest.mark.performance
class TestPerformance:
    """Performance and load testing"""

    def test_api_response_time(self, api_client):
        """Test that API responses are within acceptable time limits"""
        start_time = time.time()
        api_client.get_roles()
        response_time = time.time() - start_time

        # Should respond within 2 seconds
        assert response_time < 2.0, f"API response time too slow: {response_time}s"

    def test_multiple_role_operations(self, api_client, test_role_config, cleanup_test_roles):
        """Test creating and managing multiple roles"""
        num_roles = 5
        role_names = [f"perf_test_role_{i}" for i in range(num_roles)]
        cleanup_test_roles.extend(role_names)

        start_time = time.time()

        # Create multiple roles
        for role_name in role_names:
            api_client.create_role(role_name, test_role_config)

        # Fetch all roles
        response = api_client.get_roles()

        total_time = time.time() - start_time

        # Should complete within reasonable time
        assert total_time < 10.0, f"Multiple role operations took too long: {total_time}s"

        # Verify all roles were created
        assert response["total_roles"] >= num_roles

        # Cleanup
        for role_name in role_names:
            api_client.delete_role(role_name)


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v", "--tb=short"])