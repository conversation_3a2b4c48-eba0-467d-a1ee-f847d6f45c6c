# Project: AI Coding Agent - Integration Tests
# Purpose: Comprehensive integration tests for validation framework

import pytest
import asyncio
import tempfile
import time
from pathlib import Path
from unittest.mock import patch
from typing import Dict, cast, Any

from src.validation.validation_rules import ValidationRuleEngine
from src.services.task_validator import TaskValidator
from src.services.error_recovery import ErrorRecoverySystem, ErrorType
from src.execution.pipeline_manager import PipelineManager
from src.execution.validation_gates import ValidationGateManager
from src.state.state_serializer import StateSerializer
from src.state.rollback_engine import RollbackEngine
from src.approval.approval_manager import ApprovalManager
from src.models.task import Task
from src.models.validation_models import (
    TaskResult,
    ValidationResult,
    TaskType,
    AgentType,
    ExecutionPipeline,
    ApprovalStatus,
)
from src.approval.approval_models import (
    ApprovalType,
)


class TestValidationFrameworkIntegration:
    """Integration tests for the complete validation framework"""

    @pytest.fixture
    async def test_environment(self):
        """Set up a complete test environment with all components"""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_root = Path(temp_dir)

            # Initialize all components
            validation_engine = ValidationRuleEngine(str(project_root))
            task_validator = TaskValidator(str(project_root))
            error_recovery = ErrorRecoverySystem(str(project_root))
            pipeline_manager = PipelineManager(str(project_root))
            validation_gates = ValidationGateManager()
            state_serializer = StateSerializer(str(project_root))
            rollback_engine = RollbackEngine(str(project_root))
            approval_manager = ApprovalManager(str(project_root))

            await approval_manager.initialize()

            yield {
                'project_root': project_root,
                'validation_engine': validation_engine,
                'task_validator': task_validator,
                'error_recovery': error_recovery,
                'pipeline_manager': pipeline_manager,
                'validation_gates': validation_gates,
                'state_serializer': state_serializer,
                'rollback_engine': rollback_engine,
                'approval_manager': approval_manager
            }

            # Cleanup
            await approval_manager.shutdown()

    @pytest.fixture
    def sample_project_structure(self):
        """Create a sample project structure for testing"""
        return {
            'src/components/Header.tsx': '''
                import React from 'react';

                interface HeaderProps {
                    title: string;
                    onMenuClick: () => void;
                }

                const Header: React.FC<HeaderProps> = ({ title, onMenuClick }) => {
                    return (
                        <header className="header">
                            <h1>{title}</h1>
                            <button onClick={onMenuClick}>Menu</button>
                        </header>
                    );
                };

                export default Header;
            ''',
            'src/api/users.py': '''
                from fastapi import APIRouter, HTTPException
                from typing import List

                router = APIRouter(prefix="/users", tags=["users"])

                @router.get("/", response_model=List[dict])
                async def get_users():
                    return [{"id": 1, "name": "John Doe"}]

                @router.get("/{user_id}")
                async def get_user(user_id: int):
                    if user_id == 1:
                        return {"id": 1, "name": "John Doe"}
                    raise HTTPException(status_code=404, detail="User not found")
            ''',
            'tests/test_header.tsx': '''
                import React from 'react';
                import { render, screen, fireEvent } from '@testing-library/react';
                import Header from '../src/components/Header';

                test('renders header with title', () => {
                    const mockOnMenuClick = jest.fn();
                    render(<Header title="Test App" onMenuClick={mockOnMenuClick} />);

                    expect(screen.getByText('Test App')).toBeInTheDocument();
                    expect(screen.getByText('Menu')).toBeInTheDocument();
                });

                test('calls onMenuClick when menu button is clicked', () => {
                    const mockOnMenuClick = jest.fn();
                    render(<Header title="Test App" onMenuClick={mockOnMenuClick} />);

                    fireEvent.click(screen.getByText('Menu'));
                    expect(mockOnMenuClick).toHaveBeenCalledTimes(1);
                });
            ''',
            'package.json': '''
                {
                    "name": "test-app",
                    "version": "1.0.0",
                    "scripts": {
                        "test": "jest",
                        "build": "react-scripts build"
                    },
                    "dependencies": {
                        "react": "^18.0.0"
                    }
                }
            '''
        }

    async def create_project_files(self, project_root: Path, file_structure: Dict[str, str]):
        """Create project files from structure dictionary"""
        for file_path, content in file_structure.items():
            full_path = project_root / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text(content.strip())

    # End-to-End Pipeline Tests

    @pytest.mark.asyncio
    async def test_complete_validation_pipeline_success(self, test_environment, sample_project_structure):
        """Test complete validation pipeline with successful execution"""
        env = test_environment
        await self.create_project_files(env['project_root'], sample_project_structure)

        # Create a task for component creation
        task = Task(
            title="Create Header Component",
            description="Create a reusable header component",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND,
            expected_files=["src/components/Header.tsx"],
            code_files=["src/components/Header.tsx"],
            test_command="npm test test_header.tsx",
            integration_checks=[]
        )

        # Create task result
        task_result = TaskResult(
            success=True,
            output="Component created successfully",
            files_created=["src/components/Header.tsx"],
            duration_seconds=30.5
        )

        # Mock subprocess for test execution
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "All tests passed"

            # Run validation
            validation_result = await env['task_validator'].validate_task_completion(task, task_result)

            assert validation_result.is_valid
            assert validation_result.error is None
            assert 'validation_time_seconds' in validation_result.metrics

    @pytest.mark.asyncio
    async def test_validation_pipeline_with_error_recovery(self, test_environment, sample_project_structure):
        """Test validation pipeline with error recovery"""
        env = test_environment
        await self.create_project_files(env['project_root'], sample_project_structure)

        # Create task with syntax error in the file
        invalid_structure = sample_project_structure.copy()
        invalid_structure['src/components/Header.tsx'] = '''
            import React from 'react';

            const Header = ({ title }) => {
                return (
                    <header>
                        <h1>{title}</h1>
                    </header>
                // Missing closing brace and semicolon
        '''

        await self.create_project_files(env['project_root'], invalid_structure)

        task = Task(
            title="Create Header Component",
            description="Create header component with syntax error",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND,
            expected_files=["src/components/Header.tsx"],
            code_files=["src/components/Header.tsx"]
        )

        # First validation should fail due to syntax error
        task_result = TaskResult(success=True, files_created=["src/components/Header.tsx"])
        validation_result = await env['task_validator'].validate_task_completion(task, task_result)

        assert not validation_result.is_valid
        assert "syntax error" in validation_result.error.lower()

        # Attempt error recovery
        syntax_error = SyntaxError("Invalid syntax in Header.tsx")

        # Mock LLM service for syntax fixing
        with patch.object(env['error_recovery'], '_fix_syntax_with_llm') as mock_llm:
            mock_llm.return_value = sample_project_structure['src/components/Header.tsx']

            with patch.object(env['error_recovery'], '_apply_code_fix_with_backup') as mock_apply:
                mock_apply.return_value = True

                with patch.object(env['error_recovery'], '_validate_syntax_fix') as mock_validate:
                    mock_validate.return_value = True

                    recovery_result = await env['error_recovery'].handle_task_failure(task, syntax_error)

                    assert recovery_result.success
                    assert recovery_result.retry_recommended

    @pytest.mark.asyncio
    async def test_pipeline_execution_with_validation_gates(self, test_environment, sample_project_structure):
        """Test pipeline execution with validation gates"""
        env = test_environment
        await self.create_project_files(env['project_root'], sample_project_structure)

        # Create execution pipeline
        pipeline = ExecutionPipeline(
            id="test-pipeline",
            name="Component Creation Pipeline",
            description="Pipeline for creating React components",
            stages=[],  # Will be populated by pipeline manager
            user_id="test-user"
        )

        # Create validation gate
        gate_id = await env['validation_gates'].create_gate(
            gate_type="automatic",
            pipeline_id=pipeline.id,
            stage_id="component-creation",
            validation_rules={
                'file_exists': True,
                'syntax_valid': True,
                'tests_pass': False  # Skip tests for this test
            }
        )

        # Mock validation execution
        with patch.object(env['validation_gates'], '_execute_validation_rules') as mock_validate:
            mock_validate.return_value = ValidationResult.success("All validations passed")

            # Execute gate
            gate_result = await env['validation_gates'].execute_gate(gate_id)

            assert gate_result.is_valid
            assert gate_result.details == "All validations passed"

    @pytest.mark.asyncio
    async def test_state_serialization_and_rollback(self, test_environment, sample_project_structure):
        """Test state serialization and rollback functionality"""
        env = test_environment
        await self.create_project_files(env['project_root'], sample_project_structure)

        # Create execution state
        execution_state = {
            'pipeline_id': 'test-pipeline',
            'current_stage': 0,
            'completed_tasks': [],
            'project_files': list(sample_project_structure.keys()),
            'timestamp': time.time()
        }

        # Serialize state
        checkpoint_id = await env['state_serializer'].serialize_execution_state(
            execution_state,
            checkpoint_name="initial_state"
        )

        assert checkpoint_id is not None

        # Modify project (simulate task execution)
        new_file = env['project_root'] / "src/components/Footer.tsx"
        new_file.write_text("const Footer = () => <footer>Test</footer>;")

        # Create rollback plan
        rollback_plan = await env['rollback_engine'].create_rollback_plan(
            checkpoint_id,
            target_state="initial_state",
            user_id="test-user"
        )

        assert rollback_plan.rollback_steps
        assert rollback_plan.safety_checks

        # Execute rollback (mocked file operations)
        with patch.object(env['rollback_engine'], '_execute_file_rollback') as mock_rollback:
            mock_rollback.return_value = True

            rollback_result = await env['rollback_engine'].execute_rollback(rollback_plan.id)

            assert rollback_result.success

    @pytest.mark.asyncio
    async def test_approval_workflow_integration(self, test_environment):
        """Test approval workflow integration with validation framework"""
        env = test_environment

        # Create approval request for phase completion
        approval_request = await env['approval_manager'].create_approval_request(
            user_id="test-user",
            approval_type=ApprovalType.PHASE_COMPLETION,
            title="Phase 1 Completion Approval",
            description="Component creation phase has completed successfully",
            item_type="phase",
            item_id="phase-1",
            files_affected=["src/components/Header.tsx"],
            changes_summary=["Created Header component", "Added component tests"],
            rollback_plan="Phase can be rolled back to previous checkpoint"
        )

        assert approval_request.status == ApprovalStatus.PENDING
        assert approval_request.risk_level  # Should have been assessed

        # Simulate user approval
        approval_success = await env['approval_manager'].respond_to_approval(
            approval_request.id,
            "test-user",
            ApprovalStatus.APPROVED,
            "Phase looks good, approved for continuation"
        )

        assert approval_success

        # Verify approval is no longer pending
        pending_approvals = env['approval_manager'].get_pending_approvals("test-user")
        assert len(pending_approvals) == 0

    # Multi-Component Integration Tests

    @pytest.mark.asyncio
    async def test_validation_engine_with_task_validator(self, test_environment, sample_project_structure):
        """Test integration between validation engine and task validator"""
        env = test_environment
        await self.create_project_files(env['project_root'], sample_project_structure)

        # Create task with validation requirements
        task = Task(
            title="Backend API Endpoint",
            description="Create user API endpoint",
            type=TaskType.CREATE_API_ENDPOINT,
            agent_type=AgentType.BACKEND,
            expected_files=["src/api/users.py"],
            code_files=["src/api/users.py"],
            integration_checks=[]  # No external services for this test
        )

        # Test task validation using validation engine
        task_validation = await env['validation_engine'].validate_task(task)
        assert task_validation.is_valid

        # Test with task validator
        task_result = TaskResult(
            success=True,
            files_created=["src/api/users.py"],
            duration_seconds=25.3
        )

        validator_result = await env['task_validator'].validate_task_completion(task, task_result)
        assert validator_result.is_valid

        # Both should produce similar results for valid scenarios
        assert task_validation.is_valid == validator_result.is_valid

    @pytest.mark.asyncio
    async def test_error_recovery_with_approval_system(self, test_environment):
        """Test integration between error recovery and approval system"""
        env = test_environment

        # Create a task that will fail
        failing_task = Task(
            title="Failing Task",
            description="A task that will require manual intervention",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND
        )

        # Simulate critical error that requires approval
        critical_error = Exception("Critical system error requiring manual review")

        # Mock error recovery to require approval
        with patch.object(env['error_recovery'], '_classify_error') as mock_classify:
            mock_classify.return_value = ErrorType.UNKNOWN_ERROR  # Unrecoverable

            # Attempt recovery
            recovery_result = await env['error_recovery'].handle_task_failure(failing_task, critical_error)

            # Should fail automatic recovery
            assert not recovery_result.success

            # Create approval request for manual intervention
            approval_request = await env['approval_manager'].create_approval_request(
                user_id="test-user",
                approval_type=ApprovalType.DESTRUCTIVE_OPERATION,
                title="Manual Intervention Required",
                description=f"Task {failing_task.title} failed with critical error",
                item_type="task",
                item_id=failing_task.id,
                impact_summary=[recovery_result.actions_taken],
                recovery_suggestions=recovery_result.recovery_suggestions
            )

            assert approval_request.status == ApprovalStatus.PENDING
            assert approval_request.risk_level.value in ['high', 'critical']

    @pytest.mark.asyncio
    async def test_pipeline_with_rollback_on_failure(self, test_environment, sample_project_structure):
        """Test complete pipeline with rollback on failure"""
        env = test_environment
        await self.create_project_files(env['project_root'], sample_project_structure)

        # Create initial checkpoint
        initial_state = {
            'pipeline_id': 'rollback-test',
            'files': list(sample_project_structure.keys()),
            'stage': 'initial'
        }

        checkpoint_id = await env['state_serializer'].serialize_execution_state(
            initial_state,
            checkpoint_name="pipeline_start"
        )

        # Simulate pipeline execution that fails midway
        pipeline = ExecutionPipeline(
            id="rollback-test",
            name="Test Pipeline with Rollback",
            description="Pipeline that will fail and rollback",
            stages=[],
            user_id="test-user"
        )

        # Mock pipeline failure
        with patch.object(env['pipeline_manager'], 'execute_pipeline') as mock_execute:
            mock_execute.side_effect = Exception("Pipeline execution failed")

            try:
                await env['pipeline_manager'].execute_pipeline(pipeline.id)
                assert False, "Should have raised exception"
            except Exception as e:
                assert "Pipeline execution failed" in str(e)

                # Create and execute rollback plan
                rollback_plan = await env['rollback_engine'].create_rollback_plan(
                    checkpoint_id,
                    target_state="pipeline_start",
                    user_id="test-user"
                )

                # Mock rollback execution
                with patch.object(env['rollback_engine'], '_execute_file_rollback') as mock_rollback:
                    mock_rollback.return_value = True

                    rollback_result = await env['rollback_engine'].execute_rollback(rollback_plan.id)

                    assert rollback_result.success
                    assert "rollback completed" in rollback_result.summary.lower()

    # Performance and Stress Tests

    @pytest.mark.asyncio
    async def test_validation_performance_multiple_tasks(self, test_environment, sample_project_structure):
        """Test validation performance with multiple concurrent tasks"""
        env = test_environment
        await self.create_project_files(env['project_root'], sample_project_structure)

        # Create multiple tasks
        tasks = []
        for i in range(10):
            task = Task(
                title=f"Task {i}",
                description=f"Test task number {i}",
                type=TaskType.CREATE_COMPONENT,
                agent_type=AgentType.FRONTEND,
                expected_files=[f"src/component{i}.tsx"],
                code_files=[f"src/component{i}.tsx"]
            )
            tasks.append(task)

            # Create corresponding files
            file_path = env['project_root'] / f"src/component{i}.tsx"
            file_path.parent.mkdir(exist_ok=True)
            file_path.write_text(f"const Component{i} = () => <div>Component {i}</div>;")

        # Validate all tasks concurrently
        start_time = time.time()

        validation_coroutines = []
        for task in tasks:
            task_result = TaskResult(success=True, files_created=[task.code_files[0]])
            validation_coroutines.append(
                env['task_validator'].validate_task_completion(task, task_result)
            )

        results = await asyncio.gather(*validation_coroutines)

        end_time = time.time()
        total_time = end_time - start_time

        # All validations should succeed
        assert all(result.is_valid for result in results)

        # Performance check - should complete within reasonable time
        assert total_time < 10.0  # 10 seconds for 10 tasks should be reasonable

        # Average validation time should be recorded
        avg_validation_time = sum(
            result.metrics.get('validation_time_seconds', 0) for result in results
        ) / len(results)

        assert avg_validation_time > 0

    @pytest.mark.asyncio
    async def test_approval_system_under_load(self, test_environment):
        """Test approval system performance under load"""
        env = test_environment

        # Create multiple approval requests simultaneously
        approval_requests = []
        for i in range(20):
            request = await env['approval_manager'].create_approval_request(
                user_id="test-user",
                approval_type=ApprovalType.TASK_EXECUTION,
                title=f"Approval Request {i}",
                description=f"Test approval request number {i}",
                item_type="task",
                item_id=f"task-{i}"
            )
            approval_requests.append(request)

        # All should be created successfully
        assert len(approval_requests) == 20

        # Verify all are pending (except any auto-approved low-risk ones)
        pending_approvals = env['approval_manager'].get_pending_approvals("test-user")
        assert len(pending_approvals) >= 15  # Some might be auto-approved

        # Bulk approve some requests
        approved_count = 0
        for request in approval_requests[:10]:
            if request.status == ApprovalStatus.PENDING:
                success = await env['approval_manager'].respond_to_approval(
                    request.id, "test-user", ApprovalStatus.APPROVED
                )
                if success:
                    approved_count += 1

        assert approved_count > 0

        # Check metrics
        metrics = env['approval_manager'].get_approval_metrics()
        assert metrics.total_requests >= 20
        assert metrics.approved_count >= approved_count

    # Error Handling and Edge Cases

    @pytest.mark.asyncio
    async def test_framework_resilience_component_failure(self, test_environment):
        """Test framework resilience when individual components fail"""
        env = test_environment

        # Test validation engine failure
        task = Task(
            title="Test Task",
            description="Task for testing component failure",
            type=TaskType.CREATE_COMPONENT,
            agent_type=AgentType.FRONTEND,
            expected_files=["nonexistent.tsx"]
        )

        # Mock validation engine to raise exception
        with patch.object(env['validation_engine'], 'validate_task', side_effect=Exception("Engine failure")):
            # Task validator should handle the exception gracefully
            task_result = TaskResult(success=True)

            # This should not crash the entire system
            try:
                result = await env['task_validator'].validate_task_completion(task, task_result)
                # Should return a failure result rather than crashing
                assert not result.is_valid
                assert "validation system error" in result.error.lower()
            except Exception as e:
                # If it does raise, it should be a controlled failure
                assert "Engine failure" in str(e)

    @pytest.mark.asyncio
    async def test_concurrent_access_thread_safety(self, test_environment):
        """Test thread safety with concurrent access to shared components"""
        env = test_environment

        # Test concurrent approval requests
        async def create_approval():
            return await env['approval_manager'].create_approval_request(
                user_id="concurrent-user",
                approval_type=ApprovalType.TASK_EXECUTION,
                title="Concurrent Request",
                description="Testing concurrent access",
                item_type="task",
                item_id=f"concurrent-{asyncio.current_task().get_name()}"
            )

        # Create multiple concurrent approval requests
        tasks = [create_approval() for _ in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # All should succeed (no exceptions)
        assert all(not isinstance(result, Exception) for result in results)
        assert len(results) == 5

        # Type assertion: results are not exceptions after the above check
        valid_results = cast(list, results)

        # All should have unique IDs
        request_ids = [result.id for result in valid_results]
        assert len(set(request_ids)) == 5

    @pytest.mark.asyncio
    async def test_cleanup_and_resource_management(self, test_environment):
        """Test proper cleanup and resource management"""
        env = test_environment

        # Create some resources
        checkpoint_id = await env['state_serializer'].serialize_execution_state(
            {'test': 'data'},
            checkpoint_name="cleanup_test"
        )

        approval_request = await env['approval_manager'].create_approval_request(
            user_id="cleanup-user",
            approval_type=ApprovalType.CONFIGURATION_CHANGE,
            title="Cleanup Test",
            description="Testing resource cleanup",
            item_type="config",
            item_id="cleanup-config"
        )

        # Verify resources exist
        assert checkpoint_id is not None
        assert approval_request.id is not None

        # Test cleanup (this would be done automatically in the fixture teardown)
        # But we can test explicit cleanup methods

        # Cancel pending approval
        cancel_success = await env['approval_manager'].cancel_approval_request(
            approval_request.id,
            "cleanup-user",
            "Testing cleanup"
        )

        assert cancel_success

        # Verify cleanup
        pending_after_cleanup = env['approval_manager'].get_pending_approvals("cleanup-user")
        assert len(pending_after_cleanup) == 0