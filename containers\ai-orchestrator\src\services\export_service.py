import shutil
from pathlib import Path
from services.export_registry import Exporter, register_exporter

@register_exporter("zip")
class ZipExporter(Exporter):
    def export(self, project_id: str, **kwargs) -> str:
        # This is a simplified implementation. A real implementation would
        # need to get the project path from the project_id.
        source_dir = Path(f"workspace/{project_id}")
        output_filename = f"workspace/{project_id}.zip"
        shutil.make_archive(str(source_dir), 'zip', str(source_dir))
        return output_filename

class ExportService:
    def __init__(self, registry):
        self.registry = registry

    def export_project(self, format_name: str, project_id: str, **kwargs) -> str:
        exporter_class = self.registry.get_exporter(format_name)
        exporter = exporter_class()
        return exporter.export(project_id, **kwargs)
