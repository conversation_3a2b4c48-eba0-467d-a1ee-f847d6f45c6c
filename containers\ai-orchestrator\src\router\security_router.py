"""
Security Monitoring Router for AI Orchestrator Service.

This module provides API endpoints for security monitoring,
container behavior validation, and security event management.

Author: AI Coding Agent
Version: 1.0.0
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel

from src.services.security_service import (
    get_security_monitoring_service,
    SecurityMonitoringService,
    SecurityLevel,
    SecurityEventType
)
from src.utils.auth import get_current_user

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/security", tags=["security"])


class SecurityEventResponse(BaseModel):
    """Security event response model."""
    event_type: str
    severity: str
    timestamp: datetime
    user_id: Optional[str]
    container_id: Optional[str]
    description: str
    details: Dict[str, Any]
    action_taken: Optional[str]


class SecurityMetricsResponse(BaseModel):
    """Security metrics response model."""
    monitoring_enabled: bool
    monitoring_interval: int
    events_24h: int
    event_types: Dict[str, int]
    severity_breakdown: Dict[str, int]
    alert_thresholds: Dict[str, Any]
    last_check: str


class SecurityStatusResponse(BaseModel):
    """Security status response model."""
    status: str
    active_alerts: int
    critical_events: int
    monitoring_uptime: str
    last_scan: str


async def start_security_monitoring_background(security_service: SecurityMonitoringService):
    """
    Background task to start security monitoring.

    Args:
        security_service: Security monitoring service instance
    """
    try:
        await security_service.start_monitoring()
    except Exception as e:
        logger.error(f"Security monitoring background task failed: {e}")


@router.get("/status", response_model=SecurityStatusResponse)
async def get_security_status(
    current_user: Dict[str, Any] = Depends(get_current_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service)
):
    """
    Get current security monitoring status.

    Requires authentication.
    """
    try:
        # Get recent events for status calculation
        recent_events = await security_service.get_recent_events(hours=24)

        # Count critical events
        critical_events = sum(
            1 for event in recent_events
            if event.get("severity") == SecurityLevel.CRITICAL.value
        )

        # Count active alerts (high and critical events in last hour)
        recent_alerts = await security_service.get_recent_events(hours=1)
        active_alerts = sum(
            1 for event in recent_alerts
            if event.get("severity") in [SecurityLevel.HIGH.value, SecurityLevel.CRITICAL.value]
        )

        # Determine overall status
        if critical_events > 0:
            status = "critical"
        elif active_alerts > 5:
            status = "warning"
        elif security_service.monitoring_enabled:
            status = "healthy"
        else:
            status = "disabled"

        return SecurityStatusResponse(
            status=status,
            active_alerts=active_alerts,
            critical_events=critical_events,
            monitoring_uptime="monitoring_active" if security_service.monitoring_enabled else "disabled",
            last_scan=datetime.now(timezone.utc).isoformat()
        )

    except Exception as e:
        logger.error(f"Failed to get security status: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve security status")


@router.get("/events", response_model=List[SecurityEventResponse])
async def get_security_events(
    hours: int = Query(24, ge=1, le=168, description="Hours to look back (1-168)"),
    severity: Optional[str] = Query(None, description="Filter by severity level"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of events to return"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service)
):
    """
    Get security events with optional filtering.

    Requires authentication. Only admins can see all users' events.
    """
    try:
        # Validate severity filter
        severity_filter = None
        if severity:
            try:
                severity_filter = SecurityLevel(severity.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid severity level. Must be one of: {[s.value for s in SecurityLevel]}"
                )

        # Check if user is admin (implement your admin check logic)
        is_admin = current_user.get("role") == "admin"  # Adjust based on your auth system

        # Get events
        events = await security_service.get_recent_events(hours=hours, severity=severity_filter)

        # Filter by event type if specified
        if event_type:
            events = [e for e in events if e.get("event_type") == event_type]

        # Filter by user ID if specified or if not admin
        if user_id and is_admin:
            events = [e for e in events if e.get("user_id") == user_id]
        elif not is_admin:
            # Non-admin users can only see their own events
            current_user_id = current_user.get("sub") or current_user.get("id")
            events = [e for e in events if e.get("user_id") == current_user_id]

        # Limit results
        events = events[:limit]

        # Convert to response models
        response_events = []
        for event in events:
            response_events.append(SecurityEventResponse(
                event_type=event["event_type"],
                severity=event["severity"],
                timestamp=datetime.fromisoformat(event["timestamp"]),
                user_id=event.get("user_id"),
                container_id=event.get("container_id"),
                description=event["description"],
                details=event["details"],
                action_taken=event.get("action_taken")
            ))

        return response_events

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get security events: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve security events")


@router.get("/metrics", response_model=SecurityMetricsResponse)
async def get_security_metrics(
    current_user: Dict[str, Any] = Depends(get_current_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service)
):
    """
    Get security monitoring metrics and statistics.

    Requires authentication.
    """
    try:
        metrics = await security_service.get_security_metrics()

        return SecurityMetricsResponse(
            monitoring_enabled=metrics["monitoring_enabled"],
            monitoring_interval=metrics["monitoring_interval"],
            events_24h=metrics["events_24h"],
            event_types=metrics["event_types"],
            severity_breakdown=metrics["severity_breakdown"],
            alert_thresholds=metrics["alert_thresholds"],
            last_check=metrics["last_check"]
        )

    except Exception as e:
        logger.error(f"Failed to get security metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve security metrics")


@router.post("/start-monitoring")
async def start_security_monitoring(
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service)
):
    """
    Start security monitoring as a background task.

    Requires admin authentication.
    """
    # Check if user is admin
    is_admin = current_user.get("role") == "admin"  # Adjust based on your auth system
    if not is_admin:
        raise HTTPException(
            status_code=403,
            detail="Admin privileges required to control security monitoring"
        )

    try:
        if not security_service.monitoring_enabled:
            raise HTTPException(
                status_code=400,
                detail="Security monitoring is disabled via configuration"
            )

        # Start monitoring in the background
        background_tasks.add_task(
            start_security_monitoring_background,
            security_service
        )

        return {
            "message": "Security monitoring started",
            "monitoring_interval": security_service.monitoring_interval,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start security monitoring: {e}")
        raise HTTPException(status_code=500, detail="Failed to start security monitoring")


@router.get("/alerts")
async def get_active_alerts(
    current_user: Dict[str, Any] = Depends(get_current_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service)
):
    """
    Get currently active security alerts.

    Returns high and critical severity events from the last hour.
    """
    try:
        # Get recent high-severity events
        recent_events = await security_service.get_recent_events(hours=1)

        active_alerts = [
            event for event in recent_events
            if event.get("severity") in [SecurityLevel.HIGH.value, SecurityLevel.CRITICAL.value]
        ]

        # Group by severity
        alerts_by_severity = {
            SecurityLevel.CRITICAL.value: [],
            SecurityLevel.HIGH.value: []
        }

        for alert in active_alerts:
            severity = alert.get("severity")
            if severity in alerts_by_severity:
                alerts_by_severity[severity].append(alert)

        return {
            "total_alerts": len(active_alerts),
            "critical_alerts": len(alerts_by_severity[SecurityLevel.CRITICAL.value]),
            "high_alerts": len(alerts_by_severity[SecurityLevel.HIGH.value]),
            "alerts_by_severity": alerts_by_severity,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get active alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve active alerts")


@router.get("/event-types")
async def get_event_types():
    """
    Get available security event types.

    Public endpoint for reference.
    """
    return {
        "event_types": [event_type.value for event_type in SecurityEventType],
        "severity_levels": [level.value for level in SecurityLevel],
        "description": "Available security event types and severity levels"
    }


@router.get("/container/{container_id}/security")
async def get_container_security_info(
    container_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    security_service: SecurityMonitoringService = Depends(get_security_monitoring_service)
):
    """
    Get security information for a specific container.

    Users can only access their own containers unless they're admin.
    """
    try:
        # Get events related to this container
        all_events = await security_service.get_recent_events(hours=168)  # Last week
        container_events = [
            event for event in all_events
            if event.get("container_id") == container_id
        ]

        # Check if user has access to this container
        is_admin = current_user.get("role") == "admin"
        current_user_id = current_user.get("sub") or current_user.get("id")

        if not is_admin:
            # Filter to only user's own container events
            container_events = [
                event for event in container_events
                if event.get("user_id") == current_user_id
            ]

        # Analyze security status
        security_score = 100  # Start with perfect score
        issues = []

        for event in container_events:
            severity = event.get("severity")
            if severity == SecurityLevel.CRITICAL.value:
                security_score -= 30
                issues.append(f"Critical: {event['description']}")
            elif severity == SecurityLevel.HIGH.value:
                security_score -= 15
                issues.append(f"High: {event['description']}")
            elif severity == SecurityLevel.MEDIUM.value:
                security_score -= 5
                issues.append(f"Medium: {event['description']}")

        security_score = max(0, security_score)  # Don't go below 0

        # Determine security status
        if security_score >= 90:
            status = "excellent"
        elif security_score >= 75:
            status = "good"
        elif security_score >= 50:
            status = "fair"
        else:
            status = "poor"

        return {
            "container_id": container_id,
            "security_score": security_score,
            "security_status": status,
            "total_events": len(container_events),
            "recent_issues": issues[:10],  # Last 10 issues
            "events_by_severity": {
                level.value: sum(1 for e in container_events if e.get("severity") == level.value)
                for level in SecurityLevel
            },
            "last_event": container_events[0]["timestamp"] if container_events else None,
            "analysis_period": "7 days"
        }

    except Exception as e:
        logger.error(f"Failed to get container security info: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve container security information")